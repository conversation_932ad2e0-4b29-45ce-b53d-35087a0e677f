// const fs = require("fs");
// const path = require("path");

// const dfPkgPath = path.join(
//   path.resolve(__dirname, "..", "..", ".."),
//   "delight-formily/package.json",
// );
// const dfPkg = require(dfPkgPath);

// const sourcePath = path.join(
//   path.resolve(__dirname, "..", "..", ".."),
//   "delight-formily/CHANGELOG.md",
// );
// const targetPath = path.resolve(__dirname, "..", "docs/changelog.md");

// 复制 changelog.md
// try {
//   const sourceData = fs.readFileSync(sourcePath, "utf-8");
//   fs.writeFileSync(targetPath, sourceData, "utf-8");
// } catch (err) {
//   console.log(err);
// }

// base + nav
// const configPath = path.join(
//   path.resolve(__dirname, ".."),
//   ".vitepress/config.mts",
// );
// try {
//   let configData = fs.readFileSync(configPath, "utf-8");
//   configData = configData.replace(
//     /\/\/ insert version/g,`
//       { text: 'v${dfPkg.version}', link: '' },
//       { text: 'CHANGELOG', link: '/docs/changelog.md' }`,
//   );
//   configData = configData.replace(/0_0_0/g, dfPkg.version);

//   fs.writeFileSync(configPath, configData, "utf-8");
// } catch (err) {
//   console.error(err);
// }

// 修改完成后
// 写配置文件 version path
// npm run build

// 修改完成后
// 写配置文件 无version

// dist 目录内容在每次构建后，是否会清理目录

// ls dist
// index.html xxx.
// vx.x.x/
