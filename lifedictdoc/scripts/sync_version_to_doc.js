// const fs = require('fs')
// const path = require('path')

// const dfPkgPath = path.join(
//   path.resolve(__dirname, '..', '..', '..'),
//   'delight-formily/package.json',
// )
// const dfPkg = require(dfPkgPath)

// const configPath = path.join(
//   path.resolve(__dirname, '..'),
//   '.vitepress/config.mts',
// )

// try {
//   let configData = fs.readFileSync(configPath, 'utf-8')
//   configData = configData
//     .replace(/base: "\/form\/"/g, `base: "/form/v${dfPkg.version}/"`)
//     .replace(/\/dist/, `/dist/v${dfPkg.version}`)
//   fs.writeFileSync(configPath, configData)
// } catch (err) {
//   console.error(err)
// }
