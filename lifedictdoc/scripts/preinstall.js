// @ts-nocheck
/** 将外部依赖同步到doc */
const fs = require('fs')
const path = require('path')

// 读取上级和当前目录的package.json
// eslint-disable-next-line import/no-dynamic-require
const upperPackageJson = require(path.join(__dirname, '../..', 'package.json'))
// eslint-disable-next-line import/no-dynamic-require
const currentPackageJson = require(path.join(__dirname, '..', 'package.json'))

// 合并dependencies
const mergedDependencies = { ...currentPackageJson.dependencies, ...upperPackageJson.dependencies }

// 写入更新后的package.json
fs.writeFileSync(path.join(__dirname, '..', 'package.json'), JSON.stringify({ ...currentPackageJson, dependencies: mergedDependencies }, null, 2))

/** 准备alias.ts文件内容 */
let aliasContent = 'export const aliasDataList = [\n'
Object.keys(mergedDependencies).forEach(dep => {
  aliasContent += `  "${dep}",\n`
})
aliasContent += '];\n'

// 写入到.vitepress/alias.ts文件
const aliasTsPath = path.join(__dirname, '..', '.vitepress', 'alias.mts')
console.log(aliasTsPath, aliasContent)

fs.writeFileSync(aliasTsPath, aliasContent)
