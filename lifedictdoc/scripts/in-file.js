// import getTitleText from './getTitleText'

const fs = require('fs')
const path = require('path')

function createDirectoryAndFile(directoryPath, fileName, fileContent) {
  // 创建目录
  fs.mkdirSync(directoryPath, { recursive: true })

  // 创建文件
  fs.writeFileSync(path.join(directoryPath, fileName), fileContent)
}

const getTitleText = data => {
  const titleMatch = data.match(/# (.*?)</g) // 使用正则表达式匹配第一个一级标题

  if (titleMatch) {
    return titleMatch?.[0]?.slice(2, -1)
  }
  return ''
}

function findFoldersAndFiles(fileName) {
  const startPath = `../../packages/shared/dictMaterials/${fileName}`
  const outputPath = `../docs/component/${fileName}`
  const modalFolders = fs.readdirSync(startPath, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory() && dirent.name.endsWith('Model'))
    .map(dirent => path.join(startPath, dirent.name))

  const output = {
    items: []
  }

  for (const modalFolder of modalFolders) {
    const innerFolders = fs.readdirSync(modalFolder, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => path.join(modalFolder, dirent.name))

    const currentItem = {
      text: path.parse(modalFolder).name,
      collapsed: false,
      items: []
    }

    for (const innerFolder of innerFolders) {
      const files = fs.readdirSync(innerFolder, { withFileTypes: true })
        .filter(dirent => dirent.isFile() && dirent.name.endsWith('.md'))
        .map(dirent => path.join(innerFolder, dirent.name))

      for (const file of files) {
        // 读取文件内容
        const fileContent = fs.readFileSync(file, 'utf8')

        // 获取文件名（不包含扩展名）和上两级文件夹名
        // const fileName = path.parse(file).name
        const upperFolderName = path.parse(innerFolder).name
        const uppestFolderName = path.parse(modalFolder).name

        // 创建对应的文件夹和 .md 文件，并写入内容
        createDirectoryAndFile(path.join(outputPath, uppestFolderName), `${upperFolderName}.md`, fileContent)

        const title = getTitleText(fileContent)?.trim?.() ?? ''
        currentItem.items.push({
          text: `${upperFolderName} ${title}`,
          link: `/docs/component/${fileName}/${uppestFolderName}/${upperFolderName}`
        })
      }
    }

    output.items.push(currentItem)
  }

  fs.writeFileSync(path.join('../src', `${fileName}.json`), JSON.stringify(output, null, 2), 'utf8')
}

findFoldersAndFiles('MerchantManage')
findFoldersAndFiles('MerchantOperation')
