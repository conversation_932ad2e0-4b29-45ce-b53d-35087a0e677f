{"items": [{"text": "BrandModel", "collapsed": false, "items": [{"text": "BrandIndustry 品牌行业", "link": "/docs/component/MerchantOperation/BrandModel/BrandIndustry"}, {"text": "BrandName 品牌名称", "link": "/docs/component/MerchantOperation/BrandModel/BrandName"}, {"text": "BrandSourceName 所属国家", "link": "/docs/component/MerchantOperation/BrandModel/BrandSourceName"}, {"text": "BrandType 品牌类型", "link": "/docs/component/MerchantOperation/BrandModel/BrandType"}]}, {"text": "CompanyModel", "collapsed": false, "items": [{"text": "Address 地址", "link": "/docs/component/MerchantOperation/CompanyModel/Address"}, {"text": "PrincipalType 主体类型", "link": "/docs/component/MerchantOperation/CompanyModel/PrincipalType"}, {"text": "UniformCreditCode 社会统一信用代码", "link": "/docs/component/MerchantOperation/CompanyModel/UniformCreditCode"}]}, {"text": "LegalPersonModel", "collapsed": false, "items": [{"text": "AuthorizationType 认证方式", "link": "/docs/component/MerchantOperation/LegalPersonModel/AuthorizationType"}, {"text": "IDNumber 证件号码", "link": "/docs/component/MerchantOperation/LegalPersonModel/IDNumber"}, {"text": "IdType 证件类型", "link": "/docs/component/MerchantOperation/LegalPersonModel/IdType"}, {"text": "IdentificationPhoto 证件照", "link": "/docs/component/MerchantOperation/LegalPersonModel/IdentificationPhoto"}, {"text": "UserEmail 用户邮箱", "link": "/docs/component/MerchantOperation/LegalPersonModel/UserEmail"}, {"text": "UserName 用户名称", "link": "/docs/component/MerchantOperation/LegalPersonModel/UserName"}, {"text": "UserPhone 用户手机号", "link": "/docs/component/MerchantOperation/LegalPersonModel/UserPhone"}, {"text": "WeChatCode 微信号", "link": "/docs/component/MerchantOperation/LegalPersonModel/WeChatCode"}]}, {"text": "ProfessionalModel", "collapsed": false, "items": [{"text": "ProIdentityScope 经营范围", "link": "/docs/component/MerchantOperation/ProfessionalModel/ProIdentityScope"}, {"text": "ProfessionalCode 专业号身份", "link": "/docs/component/MerchantOperation/ProfessionalModel/ProfessionalCode"}]}, {"text": "QualificationModel", "collapsed": false, "items": [{"text": "QualificationCode 资质 code", "link": "/docs/component/MerchantOperation/QualificationModel/QualificationCode"}, {"text": "ValidityPeriod 有效期", "link": "/docs/component/MerchantOperation/QualificationModel/ValidityPeriod"}]}, {"text": "ShopModel", "collapsed": false, "items": [{"text": "SettleUpload 图片上传", "link": "/docs/component/MerchantOperation/ShopModel/SettleUpload"}, {"text": "ShopName 门店名称", "link": "/docs/component/MerchantOperation/ShopModel/ShopName"}]}]}