<!-- 看板 -->
<template>
  <div class="box">
    <div ref="jsmindContainer" class="dictionaries-board"></div>
    <Drawer
      v-model:visible="visible"
      :title="drawerTitle"
      :mask="true"
      :mask-closeable="true"
      :z-index="1000"
      size="large"
    >
      <Tabs size="small" @tab-click="hiddenTitle">
        <TabPane class="tab-pane" :icon="International" label="使用方式" @click="hiddenTitle">
          <iframe
            class="iframe"
            :src="origin + iframeSrc"
          />
        </TabPane>
        <TabPane label="使用情况">
          <Result class="--space-m-top-large" status="404" title="敬请期待" />
        </TabPane>
      </Tabs>
    </Drawer>
  </div>
</template>

<script setup>
  import {
    onMounted,
    ref,
  } from 'vue'
  import 'jsmind/style/jsmind.css'
  import JsMind from 'jsmind'
  import {
    Drawer, Tabs, TabPane, Result
  } from '@xhs/delight'
  import { MerchantManageEnum, MerchantOperationEnum } from './constant'
  import { getData } from './utils'
  import MerchantManage from './MerchantManage.json'
  import MerchantOperation from './MerchantOperation.json'

  const jsmindContainer = ref(null)

  const visible = ref(false)

  function handleOpen() {
    visible.value = true
    hiddenTitle()
  }
  const hiddenTitle = () => {
    setTimeout(() => {
      const iframe = document.querySelector('.iframe')
      const innerDoc = iframe.contentDocument || iframe.contentWindow.document // 获取iframe内部的document对象
      const css = '.VPNav,.VPLocalNav { display: none !important; }' // 定义要修改的样式
      const style = innerDoc.createElement('style') // 创建style标签
      style.innerHTML = css // 设置样式内容
      innerDoc.head.appendChild(style) // 将style标签添加到iframe内部的head标签
    }, 100)
  }

  const drawerTitle = ref('模型字典')
  const origin = window.location.origin
  const iframeSrc = ref('/life-doc/docs/component/MerchantManage/GoodsModel/Price.html')

  onMounted(() => {
    const mind = new JsMind({
      container: jsmindContainer.value,
      editable: true, // 允许编辑
      theme: 'greensea',
      mode: 'full', // 显示模式
      support_html: true, // 是否支持节点里的HTML元素
      view: {
        engine: 'svg', // 思维导图各节点之间线条的绘制引擎
        hmargin: 100, // 思维导图距容器外框的最小水平距离
        vmargin: 50, // 思维导图距容器外框的最小垂直距离
        line_width: 1, // 思维导图线条的粗细
        line_color: '#000' // 思维导图线条的颜色
      },
      layout: {
        hspace: 100, // 节点之间的水平间距
        vspace: 30, // 节点之间的垂直间距
        pspace: 13 // 节点与连接线之间的水平间距（用于容纳节点收缩/展开控制器）
      },
    })
    // 商家经营
    const MerchantManageObj = getData(MerchantManage, MerchantManageEnum)
    // 商家供给
    const MerchantOperationObj = getData(MerchantOperation, MerchantOperationEnum)

    const mindData = {
      /* 元数据，定义思维导图的名称、作者、版本等信息 */
      meta: {
        name: 'dictionaries-board',
        author: 'shanjian',
        version: '0.0.1'
      },
      /* 数据格式声明 */
      format: 'node_tree',
      /* 数据内容 */
      data: {
        id: 'root',
        topic: '模型字典看板',
        children: [
          {
            id: 'MerchantOperation',
            topic: '商家供给',
            direction: 'right',
            children: MerchantOperationObj ?? []
          },
          {
            id: 'MerchantManage',
            topic: '商家经营',
            direction: 'right',
            children: MerchantManageObj ?? []
          },
          {
            id: 'Transaction',
            topic: '交易链路',
            direction: 'left',
            children: [
              {
                id: 'C-Order',
                topic: '订单模型',
                expanded: false,
                children: [
                  {
                    id: 'C-OrderNo',
                    topic: '订单号',
                  },
                  {
                    id: 'C-OrderType',
                    topic: '订单状态',
                  },
                  {
                    id: 'C-OrderTime',
                    topic: '订单时间',
                  },
                  {
                    id: 'C-OrderPrice',
                    topic: '订单价格',
                  },
                  {
                    id: 'C-OrderIndate',
                    topic: '订单有效期',
                  },
                ]
              },
              {
                id: 'C-Coupon',
                topic: '优惠券模型',
                expanded: false,
                children: [
                  {
                    id: 'C-CouponNo',
                    topic: '券码号',
                  },
                  {
                    id: 'C-CouponStatus',
                    topic: '券码状态',
                  },
                  {
                    id: 'C-CouponType',
                    topic: '券码类型',
                  },
                  {
                    id: 'C-VerificationPlatform',
                    topic: '核销平台',
                  },
                ]
              },
              {
                id: 'C-Appointment',
                topic: '预约信息',
                expanded: false,
                children: [
                  {
                    id: 'C-AppointmentUser',
                    topic: '预约人',
                  },
                  {
                    id: 'C-AppointmentTime',
                    topic: '预约时间',
                  },
                  {
                    id: 'C-TripNum',
                    topic: '出行人数',
                  },
                  {
                    id: 'C-TripDate',
                    topic: '出行日期',
                  }
                ]
              },
              {
                id: 'C-Verify',
                topic: '核销模型',
                expanded: false,
                children: [
                  {
                    id: 'C-VerifyTime',
                    topic: '核销时间',
                  },
                  {
                    id: 'C-VerifyShop',
                    topic: '核销门店',
                  },
                  {
                    id: 'C-Verifytype',
                    topic: '核销状态',
                  }
                ]
              },
              {
                id: 'C-Refund',
                topic: '退款模型',
                expanded: false,
                children: [
                  {
                    id: 'C-RefundPrice',
                    topic: '退款金额',
                  },
                  {
                    id: 'C-RefundType',
                    topic: '退款类型',
                  },
                  {
                    id: 'C-RefundTime',
                    topic: '退款时间',
                  },
                  {
                    id: 'C-RefundSource',
                    topic: '退款渠道',
                  },
                  {
                    id: 'C-RefundReason',
                    topic: '退款原因',
                  }
                ]
              },
              {
                id: 'C-UserInfo',
                topic: '用户信息',
                expanded: false,
                children: [
                  {
                    id: 'C-UserPhone',
                    topic: '手机号码',
                  },
                  {
                    id: 'C-UserName',
                    topic: '用户名称',
                  },
                ]
              },
              {
                id: 'C-Goods',
                topic: '商品模型',
                expanded: false,
                children: [
                  {
                    id: 'C-GoodsName',
                    topic: '商品名称',
                  },
                  {
                    id: 'C-GoodsDesc',
                    topic: '商品描述',
                  },
                  {
                    id: 'C-GoodsPrice',
                    topic: '商品价格',
                  },
                  {
                    id: 'C-Goodsimg',
                    topic: '商品图片',
                  },
                  {
                    id: 'C-GoodsSpec',
                    topic: '商品规格',
                  },
                  {
                    id: 'C-GoodsType',
                    topic: '商品状态',
                  },
                  {
                    id: 'C-GoodsCategory',
                    topic: '商品类目',
                  },
                  {
                    id: 'C-GoodsStock',
                    topic: '商品库存',
                  },
                  {
                    id: 'C-SaleTime',
                    topic: '售卖时间',
                  },
                ]
              },
              {
                id: 'C-ShopInfo',
                topic: '门店模型',
                expanded: false,
                children: [
                  {
                    id: 'C-ShopImg',
                    topic: '门店头图',
                  },
                  {
                    id: 'C-ShopName',
                    topic: '门店名称',
                  },
                  {
                    id: 'C-ShopAddress',
                    topic: '门店位置',
                  },
                  {
                    id: 'C-ShopDistance',
                    topic: '门店距离',
                  },
                  {
                    id: 'C-ShopPhone',
                    topic: '门店电话',
                  },
                ],
              },
            ],
            // 'background-color': 'red',
            // 'foreground-color': '#fff'
          },
          {
            id: 'powerful',
            topic: '场域活动',
            direction: 'left',
            children: [
              { id: 'Activity', topic: '活动模型' },
              { id: 'Plan', topic: '计划模型' },
              { id: 'General', topic: '通兑品模型' }
            ],
          }
        ]
      }
    }
    mind.show(mindData)
    /**
      设置主题 : 使用 jm.set_theme(theme) 方法可设置当前思维导图的主题。

      设置背景色/前景色 : 使用 jm.set_node_color(nodeid, bgcolor, fgcolor) 方法可设置指定节点的背景色与前景色。

      设置字体 : 使用 jm.set_node_font_style(nodeid, size, weight, style) 方法可设置指定节点的字体。

      设置背景图片 : 使用 jm.set_node_background_image(nodeid, image, width, height) 方法可设置指定节点的背景图片。
      */
    // mind.set_node_color('other', 'red', '#fff')
    // const node = mind?.get_node?.('other')
    // console.log(233, node)
    // node.data.style = {
    //   'background-color': '#FFD700', // 背景颜色
    // }
    // mind.update_node('other', node)
    mind.add_event_listener((_, { node }) => {
      if (node && node.includes('/docs/component/')) {
        const nodeArr = node.split('-') ?? []
        const text = nodeArr[0]
        const path = nodeArr[1]
        const pathArr = node.split('/') ?? []
        const name = `${text}-${pathArr[pathArr.length - 1]}`
        drawerTitle.value = name
        iframeSrc.value = `/life-doc${path}.html`
        handleOpen()
      }
    })
  })

</script>

<style scoped>
/* 可以添加自定义样式 */
.box {
  position: fixed;
  z-index: 99;
  top: 65px;
  left: 0;
  width: 100vw;
  height: calc(100vh - 65px);
  background-color: #fff;
}
.dictionaries-board {
  width: 100%;
  height: 100%;
}
.d-tabs {
  height: 100%;
}
.d-tabs-pane {
  height: calc(100% - 32px);
}
.tab-pane {
  overflow: hidden;
}
.iframe {
  position: relative;
  /* top: -112px; */
  width: 100%;
  height: 100%;
  border: none;
}
</style>
