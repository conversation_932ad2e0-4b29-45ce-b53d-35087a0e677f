export const MerchantManageEnum = {
  GoodsModel: {
    id: 'GoodsModel',
    name: '商品模型'
  },
  OrderModel: {
    id: 'OrderModel',
    name: '订单模型'
  },
  DataModel: {
    id: 'DataModel',
    name: '数据模型'
  },
}
export const MerchantOperationEnum = {
  AuditModel: {
    id: 'AuditModel',
    name: '审核模型',
  },
  BrandModel: {
    id: 'BrandModel',
    name: '品牌模型',
  },
  CompanyModel: {
    id: 'CompanyModel',
    name: '公司模型',
  },
  LegalPersonModel: {
    id: 'LegalPersonModel',
    name: '管理人模型',
  },
  ProfessionalModel: {
    id: 'ProfessionalModel',
    name: '专业号模型',
  },
  QualificationModel: {
    id: 'QualificationModel',
    name: '资质模型',
  },
  ShopModel: {
    id: 'ShopModel',
    name: '店铺模型'
  },
}
