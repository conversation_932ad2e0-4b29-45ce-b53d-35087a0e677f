export const getData = (domain: any, Enum: any) => {
  // 处理children数据
  const children = {}
  domain?.items?.forEach(item => {
    const modelId = Enum[item.text].id
    children[modelId] = item.items.map(child => {
      const textArr = child?.text?.split(' ') ?? []
      // const id = textArr[0]
      const topic = textArr[1]
      return {
        id: (`${topic}-${child?.link}`),
        topic,
        link: child?.link ?? ''
      }
    })
  })
  // 模型数据
  const modelArr: any = []
  Object.keys(children).forEach(key => {
    if (!children[key]) return
    modelArr.push({
      id: key,
      topic: Enum[key].name,
      expanded: false,
      children: children[key]
    })
  })
  return modelArr
}
