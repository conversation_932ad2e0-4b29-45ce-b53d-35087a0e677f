// import Vue from 'vue'
import type { Theme } from "vitepress"
import DefaultTheme from "vitepress/theme"
import DemoPreview, { useComponents } from "@xhs/vitepress-plugin-code-demo-component"
import "@xhs/vitepress-plugin-code-demo-component/dist/style.css"
import Tag from "./Tag.vue"

import "./style.css"

export default {
  ...DefaultTheme,
  enhanceApp(ctx) {
    useComponents(ctx.app, DemoPreview)
    ctx.app.component("Tag", Tag)
    DefaultTheme.enhanceApp(ctx)
  },
} satisfies Theme
