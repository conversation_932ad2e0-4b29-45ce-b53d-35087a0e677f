export const jsText = `
try {
  if (!window.process) {
    window.process = {
      env: {
        BROWSER: true
      }
    }
  };
  // insight?.init({
  //   package: {},
  //   env: window.location.href.includes(".sit") ? "development" : "production",
  //   getUserInfo: () => {
  //     return fetch("https://swm.xiaohongshu.com/api/users/name", {
  //       "body": null,
  //       "method": "GET",
  //       "mode": "cors",
  //       "credentials": "include"
  //     })
  //       .then((data) => data.json())
  //       .then((data) => {
  //         return {
  //           user: {
  //             type: "User",
  //             value: {
  //               userId: data.data.email,
  //             },
  //           },
  //         };
  //       });
  //   },
  // });
} catch (error) {
  console.error(error);
}`