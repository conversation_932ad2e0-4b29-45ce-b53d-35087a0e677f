/* delight 样式党混着用，导致 stylus 无法加载 css 文件，所以对于所有 delight 的 *.css 进行导入 */
@import "@xhs/delight/style/layout.css";
@import "@xhs/delight/style/select.css";
@import "@xhs/delight/style/cascader.css";
@import "@xhs/delight/style/radio.css";
@import "@xhs/delight/style/checkbox.css";
@import "@xhs/delight/style/datepicker.css";
@import "@xhs/delight/style/index.styl";
@import '@xhs/delight-formily/style/index.styl';

:root {
  /* --vp-layout-max-width: 100%; */
  /* .VPDoc.has-aside .content-container[data-v-3d0e2cd8] */
  --vp-layout-max-width: 100vw;
}

.VPDoc.has-aside .content-container {
  max-width: unset !important;
}

.vp-doc table {
  display: table;
  width: 100%;
}

@media (min-width: 1600px) {
  .VPDoc > .container > .content {
    padding: 0 calc(32px + ( 100% - 1300px ) / 2) 128px !important;
  }
}