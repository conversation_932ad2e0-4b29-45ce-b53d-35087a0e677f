<script setup lang="ts">
import type { VNode } from "vue";

withDefaults(
  defineProps<{
    type?: string;
    text?: string;
    vertical?: string;
  }>(),
  {
    type: "tip",
    text: "",
    vertical: undefined,
  }
);

defineSlots<{
  default?: () => VNode | VNode | string | null;
}>();
</script>

<template>
  <span
    class="vp-badge"
    :class="type"
    :style="{
      verticalAlign: vertical,
    }"
  >
    <slot>{{ text }}</slot>
  </span>
</template>

<style>
.vp-badge {
  display: inline-block;
  vertical-align: top;
  height: 18px;
  padding: 0 6px;
  border-radius: 3px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  transition: color 0.3s ease, background-color 0.3s ease;
}

.vp-badge.tip {
  background-color: #3c66ff;
}

.vp-badge.warning {
  background-color: #ecc808;
  color: #fff;
}

.vp-badge.danger {
  background-color: #dc2626;
  color: #fff;
}

.vp-badge + .vp-badge {
  margin-left: 5px;
}
</style>
