import { defineConfig, inBrowser } from "vitepress"
import { resolve } from "path"
import { readFileSync } from "fs"
import vueJsx from "@vitejs/plugin-vue-jsx"
import Inspect from "vite-plugin-inspect"
import MerchantManage from '../src/MerchantManage.json'
import MerchantOperation from '../src/MerchantOperation.json'
import { aliasDataList } from "./alias.mts"
import {
  viteDemoPreviewPlugin,
  demoPreviewPlugin,
} from "@xhs/vitepress-plugin-code-demo"
import { fileURLToPath } from "url"
import { jsText } from './theme/b-script'

const pkg = JSON.parse(
  readFileSync(resolve(__dirname, "../package.json"), "utf-8")
)

// 别名配置
const aliasData = {}
aliasDataList.forEach((alias) => {
  aliasData[alias] = resolve(__dirname, `../node_modules/${alias}`)
})

export default defineConfig({
  // 静态文件路径
  base: "/life-doc/",
  title: "模型字典",
  description: "...",
  lang: "zh",
  head: [
    [
      "script",
      {
        src: "https://fe-static.xhscdn.com/formula-static/@xhs/apm-insight@1.1.19/apm-insight.min.js",
      },
    ],
    [
      "script",
      {},
      jsText,
    ],
  ],
  themeConfig: {
    nav: [
      { text: "组件文档", link: "/docs/component/MerchantOperation/CompanyModel/PrincipalType" },
      { text: "字典看板", link: "/docs/api/dictionariesBoard" },
      {
        text: "开发指南",
        link: "/docs/guide/development-component",
      },
      // 下行作为站位勿删

      // { text: 'v0.0.1-alpha.1', link: '' },
      // { text: 'CHANGELOG', link: '/docs/changelog.md' }
    ],
    sidebar: {
      "/docs/component/": [
        {
          text: "快速开始",
          link: "/docs/component/start",
        },
        {
          text: "商家经营",
          collapsed: false,
          items: [
            ...MerchantOperation.items,
          ],
        },
        {
          text: "商家管理",
          collapsed: false,
          items: [
            ...MerchantManage.items,
          ],
        },
        {
          text: "交易链路",
        },
        {
          text: "场域活动",
        },
      ],
    },

    socialLinks: [
      {
        icon: {
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" class="icon" viewBox="0 0 1024 1024"><path fill="#FC6D26" d="m932.317 567.767-47.212-145.306-93.573-287.998c-4.813-14.817-25.777-14.817-30.591 0L667.36 422.46H356.628l-93.577-287.998c-4.813-14.817-25.776-14.817-30.593 0L138.885 422.46l-47.21 145.31a32.166 32.166 0 0 0 11.683 35.963l408.628 296.89 408.631-296.888a32.17 32.17 0 0 0 11.684-35.965"/><path fill="#E24329" d="m512.002 900.628 155.365-478.17H356.635z"/><path fill="#FC6D26" d="M512.004 900.628 356.64 422.472H138.902z"/><path fill="#FCA326" d="m138.891 422.466-47.214 145.31a32.164 32.164 0 0 0 11.686 35.962l408.629 296.89z"/><path fill="#E24329" d="M138.893 422.46h217.738l-93.578-288c-4.812-14.819-25.778-14.819-30.59 0z"/><path fill="#FC6D26" d="m512.002 900.628 155.365-478.154h217.738z"/><path fill="#FCA326" d="m885.115 422.466 47.214 145.31a32.164 32.164 0 0 1-11.685 35.962l-408.63 296.89z"/><path fill="#E24329" d="M885.096 422.46H667.361l93.577-287.999c4.815-14.819 25.779-14.819 30.591 0z"/></svg>',
        },
        link: "https://code.devops.xiaohongshu.com/fe/shopping/lifepartner/-/tree/lifedictdoc/init/lifedictdoc",
      },
    ],
    search: {
      provider: "local",
    },
    docFooter: {
      prev: "上一页",
      next: "下一页",
    },

    outline: {
      label: "页面导航",
      level: "deep",
    },
    langMenuLabel: "多语言",
    returnToTopLabel: "回到顶部",
    sidebarMenuLabel: "菜单",
    darkModeSwitchLabel: "主题",
    lightModeSwitchTitle: "切换到浅色模式",
    darkModeSwitchTitle: "切换到深色模式",
  },
  outDir: resolve(__dirname, "../") + "/public",
  markdown: {
    config(md) {
      const docRoot = fileURLToPath(new URL("../", import.meta.url))
      // @ts-ignore
      md.use(demoPreviewPlugin, { docRoot })
    },
  },
  vite: {
    build: {
      ssr: false,
    },
    plugins: [vueJsx(), viteDemoPreviewPlugin(), Inspect()],
    resolve: {
      alias: {
        [pkg.name]: resolve(__dirname, "../src/index.ts"),
        dayjs: "dayjs/esm",
        // 配置依赖别名
        ...(aliasData ?? {}),
        shared: resolve(__dirname, "../../packages/shared"),
        lifeitem: resolve(__dirname, "../../packages/lifeitem"),
        "@edith": resolve(__dirname, "../../edith"),
      },
    },
    optimizeDeps: {
      // 不参与打包
      include: [
        "dayjs",
        "dayjs/plugin/isBetween",
        "dayjs/plugin/isSameOrAfter",
        "dayjs/plugin/isSameOrBefore",
        "dayjs/plugin/customParseFormat",
        "dayjs/plugin/advancedFormat",
        "dayjs/plugin/quarterOfYear",
        "dayjs/plugin/weekOfYear",
        "dayjs/plugin/weekYear",
        "lodash.mergewith",
        "lodash.clonedeep",
        "lodash.uniq",
        "lodash.get",
        "lodash.set",
        "lodash/omit",
        "lodash/debounce",
        "lodash/cloneDeep",
        "vuedraggable",
        "cos-js-sdk-v5",
        "@xhs/fe-api-sign",
        "@xhs/launcher-legacy",
        "@xhs/http",
        "@xhs/logger",
        "process",
        "qr-code-styling"
      ],
      // 参与打包
      exclude: [
        "@xhs/delight-formily",
        "@xhs/delight",
        "@xhs/launcher",
        "@xhs/uploader"
      ],
    },
    esbuild: {},
  },
  // 重写路由-暂时不可用：受文件根目录限制，找不到shared文件
  // rewrites: {
  //   // 'packages/:pkg/src/(.*)': ':pkg/index.md',
  //   '/lifepartner/packages/shared/dictMaterials/MerchantManage/GoodsModel/Price/demo.md': 'Price/index.md',
  // },
})
