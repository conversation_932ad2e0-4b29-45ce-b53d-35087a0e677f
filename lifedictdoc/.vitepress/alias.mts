export const aliasDataList = [
  "@babel/plugin-proposal-decorators",
  "@vant/lazyload",
  "@xhs/apm-insight",
  "@xhs/ark-datacenter",
  "@xhs/biz-dict-kit-core",
  "@xhs/delight",
  "@xhs/delight-ark-indicator-card",
  "@xhs/delight-ark-qrcode",
  "@xhs/delight-formily",
  "@xhs/delight-material-ads-note-detail",
  "@xhs/delight-material-ark-goods-select",
  "@xhs/delight-material-ark-qrcode",
  "@xhs/delight-material-item-calendar",
  "@xhs/delight-material-life-identification-upload",
  "@xhs/delight-material-life-image",
  "@xhs/delight-material-life-qrcode",
  "@xhs/delight-material-life-region-cascader",
  "@xhs/delight-material-life-show-qrcode",
  "@xhs/delight-material-promotion-pc-feedback",
  "@xhs/delight-material-ultra-delight-form",
  "@xhs/delight-material-ultra-enhance-upload",
  "@xhs/delight-material-ultra-image-conversion",
  "@xhs/delight-material-ultra-outline-filter",
  "@xhs/delight-material-ultra-page-header",
  "@xhs/delight-material-ultra-table-cell",
  "@xhs/delight-material-ultra-toolbar",
  "@xhs/delight-material-ultra-tour-guide",
  "@xhs/delight-official-chart",
  "@xhs/di",
  "@xhs/launcher",
  "@xhs/launcher-ark",
  "@xhs/launcher-plugin-anti-spam",
  "@xhs/modular-startup",
  "@xhs/module-reactive",
  "@xhs/ozone-bridge",
  "@xhs/uploader",
  "@xhs/vue-di",
  "axios",
  "big.js",
  "cn-fontsource-lxgw-wen-kai-gb-screen-r",
  "dayjs",
  "dompurify",
  "echarts",
  "html-to-image",
  "js-cookie",
  "jsmind",
  "lodash",
  "lodash.clonedeep",
  "lodash.get",
  "lodash.mergewith",
  "lodash.set",
  "lodash.uniq",
  "qiankun",
  "query-string",
  "shared",
  "three",
  "vite-plugin-cjs-interop",
  "vite-plugin-inspect",
  "vue-router",
  "vuedraggable",
  "vuex",
  "@xhs/delight-material-life-enhance-input-number",
  "@xhs/delight-material-life-category-selector",
  "@xhs/delight-material-life-validity-period",
  "@formily/core",
  "@formily/reactive-vue",
  "@formily/vue",
  "@formily/reactive",
  "@formily/shared",
  "@xhs/formula-cli",
  "vue-cropperjs",
];
