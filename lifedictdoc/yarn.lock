# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@~4.3.3":
  version "4.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@adobe/css-tools/-/css-tools-4.3.3.tgz#90749bde8b89cd41764224f5aac29cd4138f75ff"
  integrity sha512-rE0Pygv0sEZ4vBWHlAgJLGDU7Pm8xoO6p3wsEceb7GYAjScrOHpEo8KK/eVkAcnSM+slAEtXjA2JpdjLp4fJQQ==

"@algolia/autocomplete-core@1.9.3":
  version "1.9.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/autocomplete-core/-/autocomplete-core-1.9.3.tgz#1d56482a768c33aae0868c8533049e02e8961be7"
  integrity sha512-009HdfugtGCdC4JdXUbVJClA0q0zh24yyePn+KUGk3rP7j8FEe/m5Yo/z65gn6nP/cM39PxpzqKrL7A6fP6PPw==
  dependencies:
    "@algolia/autocomplete-plugin-algolia-insights" "1.9.3"
    "@algolia/autocomplete-shared" "1.9.3"

"@algolia/autocomplete-plugin-algolia-insights@1.9.3":
  version "1.9.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/autocomplete-plugin-algolia-insights/-/autocomplete-plugin-algolia-insights-1.9.3.tgz#9b7f8641052c8ead6d66c1623d444cbe19dde587"
  integrity sha512-a/yTUkcO/Vyy+JffmAnTWbr4/90cLzw+CC3bRbhnULr/EM0fGNvM13oQQ14f2moLMcVDyAx/leczLlAOovhSZg==
  dependencies:
    "@algolia/autocomplete-shared" "1.9.3"

"@algolia/autocomplete-preset-algolia@1.9.3":
  version "1.9.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/autocomplete-preset-algolia/-/autocomplete-preset-algolia-1.9.3.tgz#64cca4a4304cfcad2cf730e83067e0c1b2f485da"
  integrity sha512-d4qlt6YmrLMYy95n5TB52wtNDr6EgAIPH81dvvvW8UmuWRgxEtY0NJiPwl/h95JtG2vmRM804M0DSwMCNZlzRA==
  dependencies:
    "@algolia/autocomplete-shared" "1.9.3"

"@algolia/autocomplete-shared@1.9.3":
  version "1.9.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/autocomplete-shared/-/autocomplete-shared-1.9.3.tgz#2e22e830d36f0a9cf2c0ccd3c7f6d59435b77dfa"
  integrity sha512-Wnm9E4Ye6Rl6sTTqjoymD+l8DjSTHsHboVRYrKgEt8Q7UHm9nYbqhN/i0fhUYA3OAEH7WA8x3jfpnmJm3rKvaQ==

"@algolia/cache-browser-local-storage@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/cache-browser-local-storage/-/cache-browser-local-storage-4.24.0.tgz#97bc6d067a9fd932b9c922faa6b7fd6e546e1348"
  integrity sha512-t63W9BnoXVrGy9iYHBgObNXqYXM3tYXCjDSHeNwnsc324r4o5UiVKUiAB4THQ5z9U5hTj6qUvwg/Ez43ZD85ww==
  dependencies:
    "@algolia/cache-common" "4.24.0"

"@algolia/cache-common@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/cache-common/-/cache-common-4.24.0.tgz#81a8d3a82ceb75302abb9b150a52eba9960c9744"
  integrity sha512-emi+v+DmVLpMGhp0V9q9h5CdkURsNmFC+cOS6uK9ndeJm9J4TiqSvPYVu+THUP8P/S08rxf5x2P+p3CfID0Y4g==

"@algolia/cache-in-memory@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/cache-in-memory/-/cache-in-memory-4.24.0.tgz#ffcf8872f3a10cb85c4f4641bdffd307933a6e44"
  integrity sha512-gDrt2so19jW26jY3/MkFg5mEypFIPbPoXsQGQWAi6TrCPsNOSEYepBMPlucqWigsmEy/prp5ug2jy/N3PVG/8w==
  dependencies:
    "@algolia/cache-common" "4.24.0"

"@algolia/client-account@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/client-account/-/client-account-4.24.0.tgz#eba7a921d828e7c8c40a32d4add21206c7fe12f1"
  integrity sha512-adcvyJ3KjPZFDybxlqnf+5KgxJtBjwTPTeyG2aOyoJvx0Y8dUQAEOEVOJ/GBxX0WWNbmaSrhDURMhc+QeevDsA==
  dependencies:
    "@algolia/client-common" "4.24.0"
    "@algolia/client-search" "4.24.0"
    "@algolia/transporter" "4.24.0"

"@algolia/client-analytics@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/client-analytics/-/client-analytics-4.24.0.tgz#9d2576c46a9093a14e668833c505ea697a1a3e30"
  integrity sha512-y8jOZt1OjwWU4N2qr8G4AxXAzaa8DBvyHTWlHzX/7Me1LX8OayfgHexqrsL4vSBcoMmVw2XnVW9MhL+Y2ZDJXg==
  dependencies:
    "@algolia/client-common" "4.24.0"
    "@algolia/client-search" "4.24.0"
    "@algolia/requester-common" "4.24.0"
    "@algolia/transporter" "4.24.0"

"@algolia/client-common@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/client-common/-/client-common-4.24.0.tgz#77c46eee42b9444a1d1c1583a83f7df4398a649d"
  integrity sha512-bc2ROsNL6w6rqpl5jj/UywlIYC21TwSSoFHKl01lYirGMW+9Eek6r02Tocg4gZ8HAw3iBvu6XQiM3BEbmEMoiA==
  dependencies:
    "@algolia/requester-common" "4.24.0"
    "@algolia/transporter" "4.24.0"

"@algolia/client-personalization@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/client-personalization/-/client-personalization-4.24.0.tgz#8b47789fb1cb0f8efbea0f79295b7c5a3850f6ae"
  integrity sha512-l5FRFm/yngztweU0HdUzz1rC4yoWCFo3IF+dVIVTfEPg906eZg5BOd1k0K6rZx5JzyyoP4LdmOikfkfGsKVE9w==
  dependencies:
    "@algolia/client-common" "4.24.0"
    "@algolia/requester-common" "4.24.0"
    "@algolia/transporter" "4.24.0"

"@algolia/client-search@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/client-search/-/client-search-4.24.0.tgz#75e6c02d33ef3e0f34afd9962c085b856fc4a55f"
  integrity sha512-uRW6EpNapmLAD0mW47OXqTP8eiIx5F6qN9/x/7HHO6owL3N1IXqydGwW5nhDFBrV+ldouro2W1VX3XlcUXEFCA==
  dependencies:
    "@algolia/client-common" "4.24.0"
    "@algolia/requester-common" "4.24.0"
    "@algolia/transporter" "4.24.0"

"@algolia/logger-common@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/logger-common/-/logger-common-4.24.0.tgz#28d439976019ec0a46ba7a1a739ef493d4ef8123"
  integrity sha512-LLUNjkahj9KtKYrQhFKCzMx0BY3RnNP4FEtO+sBybCjJ73E8jNdaKJ/Dd8A/VA4imVHP5tADZ8pn5B8Ga/wTMA==

"@algolia/logger-console@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/logger-console/-/logger-console-4.24.0.tgz#c6ff486036cd90b81d07a95aaba04461da7e1c65"
  integrity sha512-X4C8IoHgHfiUROfoRCV+lzSy+LHMgkoEEU1BbKcsfnV0i0S20zyy0NLww9dwVHUWNfPPxdMU+/wKmLGYf96yTg==
  dependencies:
    "@algolia/logger-common" "4.24.0"

"@algolia/recommend@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/recommend/-/recommend-4.24.0.tgz#8a3f78aea471ee0a4836b78fd2aad4e9abcaaf34"
  integrity sha512-P9kcgerfVBpfYHDfVZDvvdJv0lEoCvzNlOy2nykyt5bK8TyieYyiD0lguIJdRZZYGre03WIAFf14pgE+V+IBlw==
  dependencies:
    "@algolia/cache-browser-local-storage" "4.24.0"
    "@algolia/cache-common" "4.24.0"
    "@algolia/cache-in-memory" "4.24.0"
    "@algolia/client-common" "4.24.0"
    "@algolia/client-search" "4.24.0"
    "@algolia/logger-common" "4.24.0"
    "@algolia/logger-console" "4.24.0"
    "@algolia/requester-browser-xhr" "4.24.0"
    "@algolia/requester-common" "4.24.0"
    "@algolia/requester-node-http" "4.24.0"
    "@algolia/transporter" "4.24.0"

"@algolia/requester-browser-xhr@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/requester-browser-xhr/-/requester-browser-xhr-4.24.0.tgz#313c5edab4ed73a052e75803855833b62dd19c16"
  integrity sha512-Z2NxZMb6+nVXSjF13YpjYTdvV3032YTBSGm2vnYvYPA6mMxzM3v5rsCiSspndn9rzIW4Qp1lPHBvuoKJV6jnAA==
  dependencies:
    "@algolia/requester-common" "4.24.0"

"@algolia/requester-common@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/requester-common/-/requester-common-4.24.0.tgz#1c60c198031f48fcdb9e34c4057a3ea987b9a436"
  integrity sha512-k3CXJ2OVnvgE3HMwcojpvY6d9kgKMPRxs/kVohrwF5WMr2fnqojnycZkxPoEg+bXm8fi5BBfFmOqgYztRtHsQA==

"@algolia/requester-node-http@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/requester-node-http/-/requester-node-http-4.24.0.tgz#4461593714031d02aa7da221c49df675212f482f"
  integrity sha512-JF18yTjNOVYvU/L3UosRcvbPMGT9B+/GQWNWnenIImglzNVGpyzChkXLnrSf6uxwVNO6ESGu6oN8MqcGQcjQJw==
  dependencies:
    "@algolia/requester-common" "4.24.0"

"@algolia/transporter@4.24.0":
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@algolia/transporter/-/transporter-4.24.0.tgz#226bb1f8af62430374c1972b2e5c8580ab275102"
  integrity sha512-86nI7w6NzWxd1Zp9q3413dRshDqAzSbsQjhcDhPIatEFiZrL1/TjnHL8S7jVKFePlIMzDsZWXAXwXzcok9c5oA==
  dependencies:
    "@algolia/cache-common" "4.24.0"
    "@algolia/logger-common" "4.24.0"
    "@algolia/requester-common" "4.24.0"

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@ampproject/remapping/-/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/utils@^0.7.10":
  version "0.7.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@antfu/utils/-/utils-0.7.10.tgz#ae829f170158e297a9b6a28f161a8e487d00814d"
  integrity sha512-+562v9k4aI80m1+VuMHehNJWLOFjBnXn3tdOitzD0il5b7smkSBal4+a3oKiQTbrwMmN/TBUMDvbdoWDehgOww==

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.23.5", "@babel/code-frame@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/code-frame/-/code-frame-7.24.7.tgz#882fd9e09e8ee324e496bd040401c6f046ef4465"
  integrity sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==
  dependencies:
    "@babel/highlight" "^7.24.7"
    picocolors "^1.0.0"

"@babel/compat-data@^7.24.8":
  version "7.24.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.24.9.tgz#53eee4e68f1c1d0282aa0eb05ddb02d033fc43a0"
  integrity sha512-e701mcfApCJqMMueQI0Fb68Amflj83+dvAvHawoBpAz+GDjCIyGHzNwnefjsWJ3xiYAqqiQFoWbspGYBdb2/ng==

"@babel/compat-data@^7.25.2":
  version "7.25.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/compat-data/-/compat-data-7.25.2.tgz#e41928bd33475305c586f6acbbb7e3ade7a6f7f5"
  integrity sha512-bYcppcpKBvX4znYaPEeFau03bp89ShqNMLs+rmdptMw+heSZh9+z84d2YG+K7cYLbWwzdjtDoW/uqZmPjulClQ==

"@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.23.9":
  version "7.25.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/core/-/core-7.25.2.tgz#ed8eec275118d7613e77a352894cd12ded8eba77"
  integrity sha512-BBt3opiCOxUr9euZ5/ro/Xv8/V7yJ5bjYMqG/C1YAo8MIKAnumZalCN+msbci3Pigy4lIQfPUpfMM27HMGaYEA==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.25.0"
    "@babel/helper-compilation-targets" "^7.25.2"
    "@babel/helper-module-transforms" "^7.25.2"
    "@babel/helpers" "^7.25.0"
    "@babel/parser" "^7.25.0"
    "@babel/template" "^7.25.0"
    "@babel/traverse" "^7.25.2"
    "@babel/types" "^7.25.2"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/core@^7.23.3":
  version "7.24.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/core/-/core-7.24.9.tgz#dc07c9d307162c97fa9484ea997ade65841c7c82"
  integrity sha512-5e3FI4Q3M3Pbr21+5xJwCv6ZT6KmGkI0vw3Tozy5ODAQFTIWe37iT8Cr7Ice2Ntb+M3iSKCEWMB1MBgKrW3whg==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.24.9"
    "@babel/helper-compilation-targets" "^7.24.8"
    "@babel/helper-module-transforms" "^7.24.9"
    "@babel/helpers" "^7.24.8"
    "@babel/parser" "^7.24.8"
    "@babel/template" "^7.24.7"
    "@babel/traverse" "^7.24.8"
    "@babel/types" "^7.24.9"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.24.8", "@babel/generator@^7.24.9":
  version "7.24.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.24.10.tgz#a4ab681ec2a78bbb9ba22a3941195e28a81d8e76"
  integrity sha512-o9HBZL1G2129luEUlG1hB4N/nlYNWHnpwlND9eOMclRqqu1YDy2sSYVCFUZwl8I1Gxh+QSRrP2vD7EpUmFVXxg==
  dependencies:
    "@babel/types" "^7.24.9"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/generator@^7.25.0", "@babel/generator@^7.7.2":
  version "7.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/generator/-/generator-7.25.0.tgz#f858ddfa984350bc3d3b7f125073c9af6988f18e"
  integrity sha512-3LEEcj3PVW8pW2R1SR1M89g/qrYk/m/mB/tLqn7dn4sbBUQyTqnlod+II2U4dqiGtUmkcnAmkMDralTFZttRiw==
  dependencies:
    "@babel/types" "^7.25.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.7.tgz#5373c7bc8366b12a033b4be1ac13a206c6656aab"
  integrity sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-compilation-targets@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.24.8.tgz#b607c3161cd9d1744977d4f97139572fe778c271"
  integrity sha512-oU+UoqCHdp+nWVDkpldqIQL/i/bvAv53tRqLG/s+cOXxe66zOYLU7ar/Xs3LdmBihrUMEUhwu6dMZwbNOYDwvw==
  dependencies:
    "@babel/compat-data" "^7.24.8"
    "@babel/helper-validator-option" "^7.24.8"
    browserslist "^4.23.1"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-compilation-targets@^7.25.2":
  version "7.25.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.2.tgz#e1d9410a90974a3a5a66e84ff55ef62e3c02d06c"
  integrity sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==
  dependencies:
    "@babel/compat-data" "^7.25.2"
    "@babel/helper-validator-option" "^7.24.8"
    browserslist "^4.23.1"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.24.7", "@babel/helper-create-class-features-plugin@^7.25.0":
  version "7.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.0.tgz#a109bf9c3d58dfed83aaf42e85633c89f43a6253"
  integrity sha512-GYM6BxeQsETc9mnct+nIIpf63SAyzvyYN7UB/IlTyd+MBg06afFGp0mIeUqGyWgS2mxad6vqbMrHVlaL3m70sQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.8"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/helper-replace-supers" "^7.25.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/traverse" "^7.25.0"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.8.tgz#47f546408d13c200c0867f9d935184eaa0851b09"
  integrity sha512-4f6Oqnmyp2PP3olgUMmOwC3akxSm5aBYraQ6YDdKy7NcAMkDECHWG0DEnV6M2UAkERgIBhYt8S27rURPg7SxWA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.8"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/helper-replace-supers" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    semver "^6.3.1"

"@babel/helper-environment-visitor@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-environment-visitor/-/helper-environment-visitor-7.24.7.tgz#4b31ba9551d1f90781ba83491dd59cf9b269f7d9"
  integrity sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-function-name@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-function-name/-/helper-function-name-7.24.7.tgz#75f1e1725742f39ac6584ee0b16d94513da38dd2"
  integrity sha512-FyoJTsj/PEUWu1/TYRiXTIHc8lbw+TDYkZuoE43opPS5TrI7MyONBE1oNvfguEXAD9yhQRrVBnXdXzSLQl9XnA==
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-hoist-variables@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-hoist-variables/-/helper-hoist-variables-7.24.7.tgz#b4ede1cde2fd89436397f30dc9376ee06b0f25ee"
  integrity sha512-MJJwhkoGy5c4ehfoRyrJ/owKeMl19U54h27YYftT0o2teQ3FJ3nQUf/I3LlJsX4l3qlw7WRXUmiyajvHXoTubQ==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-member-expression-to-functions@^7.24.7", "@babel/helper-member-expression-to-functions@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.24.8.tgz#6155e079c913357d24a4c20480db7c712a5c3fb6"
  integrity sha512-LABppdt+Lp/RlBxqrh4qgf1oEH/WxdzQNDJIu5gC/W1GyvPVrOBiItmmM8wan2fm4oYqFuFfkXmlGpLQhPY8CA==
  dependencies:
    "@babel/traverse" "^7.24.8"
    "@babel/types" "^7.24.8"

"@babel/helper-module-imports@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.24.7.tgz#f2f980392de5b84c3328fc71d38bd81bbb83042b"
  integrity sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-module-imports@~7.22.15":
  version "7.22.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz#16146307acdc40cc00c3b2c647713076464bdbf0"
  integrity sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-module-transforms@^7.24.8", "@babel/helper-module-transforms@^7.25.2":
  version "7.25.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.25.2.tgz#ee713c29768100f2776edf04d4eb23b8d27a66e6"
  integrity sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==
  dependencies:
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-simple-access" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"
    "@babel/traverse" "^7.25.2"

"@babel/helper-module-transforms@^7.24.9":
  version "7.24.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-module-transforms/-/helper-module-transforms-7.24.9.tgz#e13d26306b89eea569180868e652e7f514de9d29"
  integrity sha512-oYbh+rtFKj/HwBQkFlUzvcybzklmVdVV3UU+mN7n2t/q3yGHbuVdNxyFvSBO1tfvjyArpHNcWMAzsSPdyI46hw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-simple-access" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"

"@babel/helper-optimise-call-expression@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.24.7.tgz#8b0a0456c92f6b323d27cfd00d1d664e76692a0f"
  integrity sha512-jKiTsW2xmWwxT1ixIdfXUZp+P5yURx2suzLZr5Hi64rURpDYdMW0pv+Uf17EYk2Rd428Lx4tLsnjGJzYKDM/6A==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.7", "@babel/helper-plugin-utils@^7.24.8", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.8.tgz#94ee67e8ec0e5d44ea7baeb51e571bd26af07878"
  integrity sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==

"@babel/helper-replace-supers@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-replace-supers/-/helper-replace-supers-7.24.7.tgz#f933b7eed81a1c0265740edc91491ce51250f765"
  integrity sha512-qTAxxBM81VEyoAY0TtLrx1oAEJc09ZK67Q9ljQToqCnA+55eNwCORaxlKyu+rNfX86o8OXRUSNUnrtsAZXM9sg==
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.7"
    "@babel/helper-optimise-call-expression" "^7.24.7"

"@babel/helper-replace-supers@^7.25.0":
  version "7.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-replace-supers/-/helper-replace-supers-7.25.0.tgz#ff44deac1c9f619523fe2ca1fd650773792000a9"
  integrity sha512-q688zIvQVYtZu+i2PsdIu/uWGRpfxzr5WESsfpShfZECkO+d2o+WROWezCi/Q6kJ0tfPa5+pUGUlfx2HhrA3Bg==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.24.8"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/traverse" "^7.25.0"

"@babel/helper-simple-access@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-simple-access/-/helper-simple-access-7.24.7.tgz#bcade8da3aec8ed16b9c4953b74e506b51b5edb3"
  integrity sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.24.7.tgz#5f8fa83b69ed5c27adc56044f8be2b3ea96669d9"
  integrity sha512-IO+DLT3LQUElMbpzlatRASEyQtfhSE0+m465v++3jyyXeBTBUjtVZg28/gHeV5mrTJqvEKhKroBGAvhW+qPHiQ==
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-split-export-declaration@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.24.7.tgz#83949436890e07fa3d6873c61a96e3bbf692d856"
  integrity sha512-oy5V7pD+UvfkEATUKvIjvIAH/xCzfsFVw7ygW2SI6NClZzquT+mwdTfgfdbUiceh6iQO0CHtCPsyze/MZ2YbAA==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-string-parser@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-string-parser/-/helper-string-parser-7.24.8.tgz#5b3329c9a58803d5df425e5785865881a81ca48d"
  integrity sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==

"@babel/helper-validator-identifier@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.7.tgz#75b889cfaf9e35c2aaf42cf0d72c8e91719251db"
  integrity sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==

"@babel/helper-validator-option@^7.24.7", "@babel/helper-validator-option@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helper-validator-option/-/helper-validator-option-7.24.8.tgz#3725cdeea8b480e86d34df15304806a06975e33d"
  integrity sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==

"@babel/helpers@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helpers/-/helpers-7.24.8.tgz#2820d64d5d6686cca8789dd15b074cd862795873"
  integrity sha512-gV2265Nkcz7weJJfvDoAEVzC1e2OTDpkGbEsebse8koXUJUXPsCMi7sRo/+SPMuMZ9MtUPnGwITTnQnU5YjyaQ==
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.8"

"@babel/helpers@^7.25.0":
  version "7.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/helpers/-/helpers-7.25.0.tgz#e69beb7841cb93a6505531ede34f34e6a073650a"
  integrity sha512-MjgLZ42aCm0oGjJj8CtSM3DB8NOOf8h2l7DCTePJs29u+v7yO/RBX9nShlKMgFnRks/Q4tBAe7Hxnov9VkGwLw==
  dependencies:
    "@babel/template" "^7.25.0"
    "@babel/types" "^7.25.0"

"@babel/highlight@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/highlight/-/highlight-7.24.7.tgz#a05ab1df134b286558aae0ed41e6c5f731bf409d"
  integrity sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.7"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.25.0", "@babel/parser@^7.25.3":
  version "7.25.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.25.3.tgz#91fb126768d944966263f0657ab222a642b82065"
  integrity sha512-iLTJKDbJ4hMvFPgQwwsVoxtHyWpKKPBrxkANrSYewDPaPpT5py5yeVkgPIJ7XYXhndxJpaA3PyALSXQ7u8e/Dw==
  dependencies:
    "@babel/types" "^7.25.2"

"@babel/parser@^7.23.9", "@babel/parser@^7.24.7", "@babel/parser@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/parser/-/parser-7.24.8.tgz#58a4dbbcad7eb1d48930524a3fd93d93e9084c6f"
  integrity sha512-WzfbgXOkGzZiXXCqk43kKwZjzwx4oulxZi3nq2TYL9mOjQv6kYwul9mz6ID36njuL7Xkp6nJEfok848Zj10j/w==

"@babel/plugin-proposal-decorators@^7.24.6":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.7.tgz#7e2dcfeda4a42596b57c4c9de1f5176bbfc532e3"
  integrity sha512-RL9GR0pUG5Kc8BUWLNDm2T5OpYwSX15r98I0IkgmRQTXuELq/OynH8xtMTMvTJFjXbMWFVTKtYkTaYQsuAwQlQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-decorators" "^7.24.7"

"@babel/plugin-proposal-export-namespace-from@^7.18.9":
  version "7.18.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.18.9.tgz#5f7313ab348cdb19d590145f9247540e94761203"
  integrity sha1-X3MTqzSM2xnVkBRfkkdUDpR2EgM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz#4c9a6f669f5d0cdf1b90a1671e9a146be5300cea"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-decorators@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.24.7.tgz#e4f8a0a8778ccec669611cd5aed1ed8e6e3a6fcf"
  integrity sha512-Ui4uLJJrRV1lb38zg1yYTmRKmiZLiftDEvZN2iq3kd9kUFU+PttmzTbAFC2ucRk/XJmtek6G23gPsuZbhrT8fQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz#028964a9ba80dbc094c915c487ad7c4e7a66465a"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.23.3", "@babel/plugin-syntax-jsx@^7.24.7", "@babel/plugin-syntax-jsx@^7.7.2":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.7.tgz#39a1fa4a7e3d3d7f34e2acc6be585b718d30e02d"
  integrity sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.24.7", "@babel/plugin-syntax-typescript@^7.7.2":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.7.tgz#58d458271b4d3b6bb27ee6ac9525acbb259bad1c"
  integrity sha512-c/+fVeJBB0FeKsFvwytYiUD+LBvhHjGSI0g446PRGdSVGZLRNArBUno2PETbAly3tpiNAQR5XaZ+JslxkotsbA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-modules-commonjs@^7.24.7":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.24.8.tgz#ab6421e564b717cb475d6fff70ae7f103536ea3c"
  integrity sha512-WHsk9H8XxRs3JXKWFiqtQebdh9b/pTk4EgueygFzYlTKAg0Ud985mSevdNjdXdFBATSKVJGQXP1tv6aGbssLKA==
  dependencies:
    "@babel/helper-module-transforms" "^7.24.8"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/helper-simple-access" "^7.24.7"

"@babel/plugin-transform-typescript@^7.23.3":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.8.tgz#c104d6286e04bf7e44b8cba1b686d41bad57eb84"
  integrity sha512-CgFgtN61BbdOGCP4fLaAMOPkzWUh6yQZNMr5YSt8uz2cZSSiQONCQFWqsE4NeVfOIhqDOlS9CR3WD91FzMeB2Q==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-create-class-features-plugin" "^7.24.8"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/plugin-syntax-typescript" "^7.24.7"

"@babel/plugin-transform-typescript@^7.24.7":
  version "7.25.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.25.2.tgz#237c5d10de6d493be31637c6b9fa30b6c5461add"
  integrity sha512-lBwRvjSmqiMYe/pS0+1gggjJleUJi7NzjvQ1Fkqtt69hBa/0t1YuW/MLQMAPixfwaQOHUXsd6jeU3Z+vdGv3+A==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-create-class-features-plugin" "^7.25.0"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/plugin-syntax-typescript" "^7.24.7"

"@babel/preset-typescript@^7.21.4":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/preset-typescript/-/preset-typescript-7.24.7.tgz#66cd86ea8f8c014855671d5ea9a737139cbbfef1"
  integrity sha512-SyXRe3OdWwIwalxDg5UtJnJQO+YPcTfwiIY2B0Xlddh9o7jpWLvv8X1RthIeDOxQ+O1ML5BLPCONToObyVQVuQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-validator-option" "^7.24.7"
    "@babel/plugin-syntax-jsx" "^7.24.7"
    "@babel/plugin-transform-modules-commonjs" "^7.24.7"
    "@babel/plugin-transform-typescript" "^7.24.7"

"@babel/runtime@^7.10.5", "@babel/runtime@^7.7.2":
  version "7.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/runtime/-/runtime-7.25.0.tgz#3af9a91c1b739c569d5d80cc917280919c544ecb"
  integrity sha512-7dRy4DwXwtzBrPbZflqxnvfxLF8kdZXPkhymtDeFoFqE6ldzjQFgYTtYIFARcLEYDrqfBfYcZt1WqFxRoyC9Rw==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.23.9", "@babel/template@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.24.7.tgz#02efcee317d0609d2c07117cb70ef8fb17ab7315"
  integrity sha512-jYqfPrU9JTF0PmPy1tLYHW4Mp4KlgxJD9l2nP9fD6yT/ICi554DmrWBAEYpIelzjHf1msDP3PxJIRt/nFNfBig==
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/parser" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/template@^7.25.0", "@babel/template@^7.3.3":
  version "7.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/template/-/template-7.25.0.tgz#e733dc3134b4fede528c15bc95e89cb98c52592a"
  integrity sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/parser" "^7.25.0"
    "@babel/types" "^7.25.0"

"@babel/traverse@^7.23.9", "@babel/traverse@^7.24.7", "@babel/traverse@^7.24.8":
  version "7.24.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.24.8.tgz#6c14ed5232b7549df3371d820fbd9abfcd7dfab7"
  integrity sha512-t0P1xxAPzEDcEPmjprAQq19NWum4K0EQPjMwZQZbHt+GiZqvjCHjj755Weq1YRPVzBI+3zSfvScfpnuIecVFJQ==
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.24.8"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-hoist-variables" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/parser" "^7.24.8"
    "@babel/types" "^7.24.8"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/traverse@^7.25.0", "@babel/traverse@^7.25.2":
  version "7.25.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/traverse/-/traverse-7.25.3.tgz#f1b901951c83eda2f3e29450ce92743783373490"
  integrity sha512-HefgyP1x754oGCsKmV5reSmtV7IXj/kpaE1XYY+D9G5PvKKoFfSbiS4M77MdjuwlZKDIKFCffq9rPU+H/s3ZdQ==
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.25.0"
    "@babel/parser" "^7.25.3"
    "@babel/template" "^7.25.0"
    "@babel/types" "^7.25.2"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.25.0", "@babel/types@^7.25.2", "@babel/types@^7.3.3":
  version "7.25.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.25.2.tgz#55fb231f7dc958cd69ea141a4c2997e819646125"
  integrity sha512-YTnYtra7W9e6/oAZEHj0bJehPRUlLH9/fbpT5LfB0NhQXyALCRkRs3zH9v07IYhkgpqX6Z78FnuccZr/l4Fs4Q==
  dependencies:
    "@babel/helper-string-parser" "^7.24.8"
    "@babel/helper-validator-identifier" "^7.24.7"
    to-fast-properties "^2.0.0"

"@babel/types@^7.16.8", "@babel/types@^7.22.15", "@babel/types@^7.23.9", "@babel/types@^7.24.7", "@babel/types@^7.24.8", "@babel/types@^7.24.9":
  version "7.24.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@babel/types/-/types-7.24.9.tgz#228ce953d7b0d16646e755acf204f4cf3d08cc73"
  integrity sha512-xm8XrMKz0IlUdocVbYJe0Z9xEgidU7msskG8BbhnTPK/HZ2z/7FP7ykqPgrUH+C+r414mNfNWam1f2vqOjqjYQ==
  dependencies:
    "@babel/helper-string-parser" "^7.24.8"
    "@babel/helper-validator-identifier" "^7.24.7"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@bufbuild/connect-node@~0.8.5":
  version "0.8.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/connect-node/download/@bufbuild/connect-node-0.8.6.tgz#bdb6bafc5d2119dd7eb322dfaae20364b40cc0ae"
  integrity sha1-vba6/F0hGd1+syLfquIDZLQMwK4=
  dependencies:
    "@bufbuild/connect" "0.8.6"
    headers-polyfill "^3.1.2"

"@bufbuild/connect@0.8.6", "@bufbuild/connect@~0.8.5":
  version "0.8.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/connect/download/@bufbuild/connect-0.8.6.tgz#ba85ee7ad546fe380d705826b9b5bf5b8a5c0f01"
  integrity sha1-uoXuetVG/jgNcFgmubW/W4pcDwE=

"@bufbuild/protobuf@1.10.0", "@bufbuild/protobuf@^1.2.0":
  version "1.10.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/protobuf/-/protobuf-1.10.0.tgz#1a67ac889c2d464a3492b3e54c38f80517963b16"
  integrity sha512-QDdVFLoN93Zjg36NoQPZfsVH9tZew7wKDKyV5qRdj8ntT4wQCOradQjRaTdwMhWUYsgKsvCINKKm87FdEk96Ag==

"@bufbuild/protobuf@1.2.1", "@bufbuild/protobuf@~1.2.0":
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/protobuf/download/@bufbuild/protobuf-1.2.1.tgz#f8b1fbbe79726a4eafa9772ddde147b57f85d177"
  integrity sha1-+LH7vnlyak6vqXct3eFHtX+F0Xc=

"@bufbuild/protoc-gen-connect-es@~0.8.5":
  version "0.8.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/protoc-gen-connect-es/download/@bufbuild/protoc-gen-connect-es-0.8.6.tgz#326eaa71cacb1b53b8b0745bbbb2bfe98e4ce358"
  integrity sha1-Mm6qccrLG1O4sHRbu7K/6Y5M41g=
  dependencies:
    "@bufbuild/protobuf" "^1.2.0"
    "@bufbuild/protoplugin" "^1.2.0"

"@bufbuild/protoc-gen-es@~1.2.0":
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/protoc-gen-es/download/@bufbuild/protoc-gen-es-1.2.1.tgz#0eeee9175579e36e2c60755690d8ecd19a7bcade"
  integrity sha1-Du7pF1V5424sYHVWkNjs0Zp7yt4=
  dependencies:
    "@bufbuild/protoplugin" "1.2.1"

"@bufbuild/protoplugin@1.2.1":
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/protoplugin/download/@bufbuild/protoplugin-1.2.1.tgz#bdac5dba4e3397178ae2b2db304e9dc04c4ef964"
  integrity sha1-vaxduk4zlxeK4rLbME6dwExO+WQ=
  dependencies:
    "@bufbuild/protobuf" "1.2.1"
    "@typescript/vfs" "^1.4.0"
    typescript "4.5.2"

"@bufbuild/protoplugin@^1.2.0":
  version "1.10.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@bufbuild/protoplugin/-/protoplugin-1.10.0.tgz#314035fa2b80fa56e72d1aa06eb9ea6de6877c4c"
  integrity sha512-u6NE4vL0lw1+EK4/PiE/SQB7fKO4LRJNTEScIXVOi2x88K/c8WKc/k0KyEaA0asVBMpwekJQZGnRyj04ZtN5Gg==
  dependencies:
    "@bufbuild/protobuf" "1.10.0"
    "@typescript/vfs" "^1.4.0"
    typescript "4.5.2"

"@docsearch/css@3.6.1", "@docsearch/css@^3.6.0":
  version "3.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@docsearch/css/-/css-3.6.1.tgz#f0a728ecb486c81f2d282650fc1820c914913408"
  integrity sha512-VtVb5DS+0hRIprU2CO6ZQjK2Zg4QU5HrDM1+ix6rT0umsYvFvatMAnf97NHZlVWDaaLlx7GRfR/7FikANiM2Fg==

"@docsearch/js@^3.6.0":
  version "3.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@docsearch/js/-/js-3.6.1.tgz#aaf6c6427371a53c1cd46b2ed08b9c353e5cd02d"
  integrity sha512-erI3RRZurDr1xES5hvYJ3Imp7jtrXj6f1xYIzDzxiS7nNBufYWPbJwrmMqWC5g9y165PmxEmN9pklGCdLi0Iqg==
  dependencies:
    "@docsearch/react" "3.6.1"
    preact "^10.0.0"

"@docsearch/react@3.6.1":
  version "3.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@docsearch/react/-/react-3.6.1.tgz#0f826df08693293806d64277d6d9c38636211b97"
  integrity sha512-qXZkEPvybVhSXj0K7U3bXc233tk5e8PfhoZ6MhPOiik/qUQxYC+Dn9DnoS7CxHQQhHfCvTiN0eY9M12oRghEXw==
  dependencies:
    "@algolia/autocomplete-core" "1.9.3"
    "@algolia/autocomplete-preset-algolia" "1.9.3"
    "@docsearch/css" "3.6.1"
    algoliasearch "^4.19.1"

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/android-arm/-/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/android-x64/-/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz#e495b539660e51690f3928af50a76fb0a6ccff2a"
  integrity sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c"
  integrity sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==

"@fastify/deepmerge@^1.1.0":
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@fastify/deepmerge/download/@fastify/deepmerge-1.3.0.tgz#8116858108f0c7d9fd460d05a7d637a13fe3239a"
  integrity sha1-gRaFgQjwx9n9Rg0Fp9Y3oT/jI5o=

"@floating-ui/core@^0.3.0":
  version "0.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/core/download/@floating-ui/core-0.3.1.tgz#3dde0ad0724d4b730567c92f49f0950910e18871"
  integrity sha1-Pd4K0HJNS3MFZ8kvSfCVCRDhiHE=

"@floating-ui/core@^0.6.2":
  version "0.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/core/download/@floating-ui/core-0.6.2.tgz#f2813f0e5f3d5ed7af5029e1a082203dadf02b7d"
  integrity sha1-8oE/Dl89XtevUCnhoIIgPa3wK30=

"@floating-ui/core@^1.6.0":
  version "1.6.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/core/-/core-1.6.5.tgz#102335cac0d22035b04d70ca5ff092d2d1a26f2b"
  integrity sha512-8GrTWmoFhm5BsMZOTHeGD2/0FLKLQQHvO/ZmQga4tKempYRLz8aqJGqXVuQgisnMObq2YZ2SgkwctN1LOOxcqA==
  dependencies:
    "@floating-ui/utils" "^0.2.5"

"@floating-ui/dom@^0.1.10":
  version "0.1.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/dom/download/@floating-ui/dom-0.1.10.tgz#ce304136a52c71ef157826d2ebf52d68fa2deed5"
  integrity sha1-zjBBNqUsce8VeCbS6/UtaPot7tU=
  dependencies:
    "@floating-ui/core" "^0.3.0"

"@floating-ui/dom@^0.4.2":
  version "0.4.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/dom/download/@floating-ui/dom-0.4.5.tgz#2e88d16646119cc67d44683f75ee99840475bbfa"
  integrity sha1-LojRZkYRnMZ9RGg/de6ZhAR1u/o=
  dependencies:
    "@floating-ui/core" "^0.6.2"

"@floating-ui/dom@^1.0.0":
  version "1.6.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/dom/-/dom-1.6.8.tgz#45e20532b6d8a061b356a4fb336022cf2609754d"
  integrity sha512-kx62rP19VZ767Q653wsP1XZCGIirkE09E0QUGNYTM/ttbbQHqcGPdSfWFxUyyNLc/W6aoJRBajOSXhP6GXjC0Q==
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.5"

"@floating-ui/utils@^0.2.5":
  version "0.2.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/utils/-/utils-0.2.5.tgz#105c37d9d9620ce69b7f692a20c821bf1ad2cbf9"
  integrity sha512-sTcG+QZ6fdEUObICavU+aB3Mp8HY4n14wYHdxK4fXjPmv3PXZZeY5RaguJmGyeH/CJQhX3fqKUtS4qc1LoHwhQ==

"@floating-ui/vue@^1.0.2":
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@floating-ui/vue/-/vue-1.1.2.tgz#d09a23d9635f6efc7fc860271f6c2d0ecf38551d"
  integrity sha512-7pq8HfhVhxOpV6iIMKSslI51fwFYy8G0BF0GjhlhpmUhVwL8jCByvcjzTwEtRWFVRrGD/I9kLp6eUHKumiUTjw==
  dependencies:
    "@floating-ui/dom" "^1.0.0"
    "@floating-ui/utils" "^0.2.5"
    vue-demi ">=0.13.0"

"@formily/core@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/core/download/@formily/core-2.2.7.tgz#3957f96c51215b97de9f8a3f9ac7a41421a91f6f"
  integrity sha1-OVf5bFEhW5fen4o/msekFCGpH28=
  dependencies:
    "@formily/reactive" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"

"@formily/grid@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/grid/download/@formily/grid-2.2.7.tgz#ab18cfb1ad45f9b462ac72afc153c515c372de51"
  integrity sha1-qxjPsa1F+bRirHKvwVPFFcNy3lE=
  dependencies:
    "@formily/reactive" "2.2.7"
    "@juggle/resize-observer" "^3.3.1"

"@formily/json-schema@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/json-schema/download/@formily/json-schema-2.2.7.tgz#a4212b8ba0751b3c45fce7b3a1393dee87708b27"
  integrity sha1-pCEri6B1GzxF/OezoTk97odwiyc=
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/shared" "2.2.7"

"@formily/path@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/path/download/@formily/path-2.2.7.tgz#fab951658bc7dce6d5b373e3d89556f2c3a3d134"
  integrity sha1-+rlRZYvH3ObVs3Pj2JVW8sOj0TQ=

"@formily/reactive-vue@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/reactive-vue/download/@formily/reactive-vue-2.2.7.tgz#5c769124bb901c052e77aafd4fbf8fbb81d13625"
  integrity sha1-XHaRJLuQHAUud6r9T7+Pu4HRNiU=
  dependencies:
    "@formily/reactive" "2.2.7"
    vue-demi "^0.13.6"

"@formily/reactive@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/reactive/download/@formily/reactive-2.2.7.tgz#e803317abeb05802d985c388129b9e7584570717"
  integrity sha1-6AMxer6wWALZhcOIEpuedYRXBxc=

"@formily/shared@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/shared/download/@formily/shared-2.2.7.tgz#012985f6894471a9ac3fbd0451ca2efd04e69527"
  integrity sha1-ASmF9olEcamsP70EUcou/QTmlSc=
  dependencies:
    "@formily/path" "2.2.7"
    camel-case "^4.1.1"
    lower-case "^2.0.1"
    no-case "^3.0.4"
    param-case "^3.0.4"
    pascal-case "^3.1.1"
    upper-case "^2.0.1"

"@formily/validator@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/validator/download/@formily/validator-2.2.7.tgz#e8a8d13f373dc957e85828cc64158b07f258acdc"
  integrity sha1-6KjRPzc9yVfoWCjMZBWLB/JYrNw=
  dependencies:
    "@formily/shared" "2.2.7"

"@formily/vue@2.2.7":
  version "2.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@formily/vue/download/@formily/vue-2.2.7.tgz#01f4aea3a6e80aa7e395d7bf21124b08e64b3398"
  integrity sha1-AfSuo6boCqfjlde/IRJLCOZLM5g=
  dependencies:
    "@formily/core" "2.2.7"
    "@formily/json-schema" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/reactive-vue" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"
    fs-extra "^10.0.0"
    vue-demi "^0.13.6"
    vue-frag "^1.1.4"

"@hapi/bourne@^3.0.0":
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@hapi/bourne/download/@hapi/bourne-3.0.0.tgz#f11fdf7dda62fe8e336fa7c6642d9041f30356d7"
  integrity sha1-8R/ffdpi/o4zb6fGZC2QQfMDVtc=

"@icon-park/svg@1.4.1":
  version "1.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@icon-park/svg/download/@icon-park/svg-1.4.1.tgz#c73add6580136167afe45b23f3d90abd118a1749"
  integrity sha1-xzrdZYATYWev5Fsj89kKvRGKF0k=

"@icon-park/svg@^1.3.5":
  version "1.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@icon-park/svg/-/svg-1.4.2.tgz#12838a2549ec8cfc3389089ad335da632bb6285f"
  integrity sha512-1X0DA+1e0R0liYvw+Nb2BQmF1oEo/wS3o/JYkQYifPJXCGYij2vN9sJf/NNhbzDsJWTg4W2bbzZjJvC7Q4w4oQ==

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz#fd3db1d59ecf7cf121e80650bb86712f9b55eced"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz#e45e384e4b8ec16bce2fd903af78450f6bf7ec98"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/console/-/console-29.7.0.tgz#cd4822dbdb84529265c5a2bdb529a3c9cc950ffc"
  integrity sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"

"@jest/core@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/core/-/core-29.7.0.tgz#b6cccc239f30ff36609658c5a5e2291757ce448f"
  integrity sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/reporters" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^29.7.0"
    jest-config "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-resolve-dependencies "^29.7.0"
    jest-runner "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    jest-watcher "^29.7.0"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/environment/-/environment-29.7.0.tgz#24d61f54ff1f786f3cd4073b4b94416383baf2a7"
  integrity sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==
  dependencies:
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"

"@jest/expect-utils@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/expect-utils/-/expect-utils-29.7.0.tgz#023efe5d26a8a70f21677d0a1afc0f0a44e3a1c6"
  integrity sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==
  dependencies:
    jest-get-type "^29.6.3"

"@jest/expect@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/expect/-/expect-29.7.0.tgz#76a3edb0cb753b70dfbfe23283510d3d45432bf2"
  integrity sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==
  dependencies:
    expect "^29.7.0"
    jest-snapshot "^29.7.0"

"@jest/fake-timers@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/fake-timers/-/fake-timers-29.7.0.tgz#fd91bf1fffb16d7d0d24a426ab1a47a49881a565"
  integrity sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==
  dependencies:
    "@jest/types" "^29.6.3"
    "@sinonjs/fake-timers" "^10.0.2"
    "@types/node" "*"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

"@jest/globals@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/globals/-/globals-29.7.0.tgz#8d9290f9ec47ff772607fa864ca1d5a2efae1d4d"
  integrity sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/types" "^29.6.3"
    jest-mock "^29.7.0"

"@jest/reporters@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/reporters/-/reporters-29.7.0.tgz#04b262ecb3b8faa83b0b3d321623972393e8f4c7"
  integrity sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^28.1.3":
  version "28.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/schemas/download/@jest/schemas-28.1.3.tgz#ad8b86a66f11f33619e3d7e1dcddd7f2d40ff905"
  integrity sha1-rYuGpm8R8zYZ49fh3N3X8tQP+QU=
  dependencies:
    "@sinclair/typebox" "^0.24.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/schemas/-/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^29.6.3":
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/source-map/-/source-map-29.6.3.tgz#d90ba772095cf37a34a5eb9413f1b562a08554c4"
  integrity sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.18"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/test-result/-/test-result-29.7.0.tgz#8db9a80aa1a097bb2262572686734baed9b1657c"
  integrity sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz#6cef977ce1d39834a3aea887a1726628a6f072ce"
  integrity sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==
  dependencies:
    "@jest/test-result" "^29.7.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    slash "^3.0.0"

"@jest/transform@^29.7.0":
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/transform/-/transform-29.7.0.tgz#df2dd9c346c7d7768b8a06639994640c642e284c"
  integrity sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.2"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jest/types/-/types-29.6.3.tgz#1131f8cf634e7e84c5e77bab12f052af585fba59"
  integrity sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz#dcce6aff74bdf6dad1a95802b69b04a2fcb1fb36"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/set-array/-/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  version "1.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.18", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@juggle/resize-observer@^3.3.1":
  version "3.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@juggle/resize-observer/download/@juggle/resize-observer-3.4.0.tgz#08d6c5e20cf7e4cc02fd181c4b0c225cd31dbb60"
  integrity sha1-CNbF4gz35MwC/RgcSwwiXNMdu2A=

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.25"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@polka/url/-/url-1.0.0-next.25.tgz#f077fdc0b5d0078d30893396ff4827a13f99e817"
  integrity sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==

"@popperjs/core@^2.11.7":
  version "2.11.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@popperjs/core/download/@popperjs/core-2.11.8.tgz#6b79032e760a0899cd4204710beede972a3a185f"
  integrity sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=

"@prisma/client@^3.14.0":
  version "3.15.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@prisma/client/download/@prisma/client-3.15.2.tgz#2181398147afc79bfe0d83c03a88dc45b49bd365"
  integrity sha1-IYE5gUevx5v+DYPAOojcRbSb02U=
  dependencies:
    "@prisma/engines-version" "3.15.1-1.461d6a05159055555eb7dfb337c9fb271cbd4d7e"

"@prisma/engines-version@3.15.1-1.461d6a05159055555eb7dfb337c9fb271cbd4d7e":
  version "3.15.1-1.461d6a05159055555eb7dfb337c9fb271cbd4d7e"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@prisma/engines-version/download/@prisma/engines-version-3.15.1-1.461d6a05159055555eb7dfb337c9fb271cbd4d7e.tgz#bf5e2373ca68ce7556b967cb4965a7095e93fe53"
  integrity sha1-v14jc8poznVWuWfLSWWnCV6T/lM=

"@rollup/plugin-commonjs@^25.0.8":
  version "25.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/plugin-commonjs/-/plugin-commonjs-25.0.8.tgz#c77e608ab112a666b7f2a6bea625c73224f7dd34"
  integrity sha512-ZEZWTK5n6Qde0to4vS9Mr5x/0UZoqCxPVR9KRUjU4kA2sO7GEUn1fop0DAwpO6z0Nw/kJON9bDmSxdWxO/TT1A==
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    commondir "^1.0.1"
    estree-walker "^2.0.2"
    glob "^8.0.3"
    is-reference "1.2.1"
    magic-string "^0.30.3"

"@rollup/plugin-inject@^5.0.5":
  version "5.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/plugin-inject/-/plugin-inject-5.0.5.tgz#616f3a73fe075765f91c5bec90176608bed277a3"
  integrity sha512-2+DEJbNBoPROPkgTDNe8/1YXWcqxbN5DTjASVIOx8HS+pITXushyNiBV56RB08zuptzz8gT3YfkqriTBVycepg==
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    estree-walker "^2.0.2"
    magic-string "^0.30.3"

"@rollup/pluginutils@^5.0.1", "@rollup/pluginutils@^5.1.0":
  version "5.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/pluginutils/-/pluginutils-5.1.0.tgz#7e53eddc8c7f483a4ad0b94afb1f7f5fd3c771e0"
  integrity sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g==
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^2.3.1"

"@rollup/rollup-android-arm-eabi@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.19.0.tgz#3d9fd50164b94964f5de68c3c4ce61933b3a338d"
  integrity sha512-JlPfZ/C7yn5S5p0yKk7uhHTTnFlvTgLetl2VxqE518QgyM7C9bSfFTYvB/Q/ftkq0RIPY4ySxTz+/wKJ/dXC0w==

"@rollup/rollup-android-arm64@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.0.tgz#e1a6d4bca2eb08c84fd996a4bf896ce4b6f4014c"
  integrity sha512-RDxUSY8D1tWYfn00DDi5myxKgOk6RvWPxhmWexcICt/MEC6yEMr4HNCu1sXXYLw8iAsg0D44NuU+qNq7zVWCrw==

"@rollup/rollup-darwin-arm64@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.19.0.tgz#0a3fffea69489a24a96079af414b0be78df8abbc"
  integrity sha512-emvKHL4B15x6nlNTBMtIaC9tLPRpeA5jMvRLXVbl/W9Ie7HhkrE7KQjvgS9uxgatL1HmHWDXk5TTS4IaNJxbAA==

"@rollup/rollup-darwin-x64@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.19.0.tgz#13fbdb15f58f090871b0ffff047ece06ad6ad74c"
  integrity sha512-fO28cWA1dC57qCd+D0rfLC4VPbh6EOJXrreBmFLWPGI9dpMlER2YwSPZzSGfq11XgcEpPukPTfEVFtw2q2nYJg==

"@rollup/rollup-linux-arm-gnueabihf@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.19.0.tgz#e9d9219ddf6f6e946e2ee322198af12466d2c868"
  integrity sha512-2Rn36Ubxdv32NUcfm0wB1tgKqkQuft00PtM23VqLuCUR4N5jcNWDoV5iBC9jeGdgS38WK66ElncprqgMUOyomw==

"@rollup/rollup-linux-arm-musleabihf@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.19.0.tgz#4ba804a00b5e793196a622f6977e05f23e01f59a"
  integrity sha512-gJuzIVdq/X1ZA2bHeCGCISe0VWqCoNT8BvkQ+BfsixXwTOndhtLUpOg0A1Fcx/+eA6ei6rMBzlOz4JzmiDw7JQ==

"@rollup/rollup-linux-arm64-gnu@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.19.0.tgz#d871e3f41de759a6db27fc99235b782ba47c15cc"
  integrity sha512-0EkX2HYPkSADo9cfeGFoQ7R0/wTKb7q6DdwI4Yn/ULFE1wuRRCHybxpl2goQrx4c/yzK3I8OlgtBu4xvted0ug==

"@rollup/rollup-linux-arm64-musl@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.19.0.tgz#6e63f7ad4cc51bd2c693a2826fd279de9eaa05b5"
  integrity sha512-GlIQRj9px52ISomIOEUq/IojLZqzkvRpdP3cLgIE1wUWaiU5Takwlzpz002q0Nxxr1y2ZgxC2obWxjr13lvxNQ==

"@rollup/rollup-linux-powerpc64le-gnu@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.19.0.tgz#1540b284d91c440bc9fa7a1714cfb71a5597e94d"
  integrity sha512-N6cFJzssruDLUOKfEKeovCKiHcdwVYOT1Hs6dovDQ61+Y9n3Ek4zXvtghPPelt6U0AH4aDGnDLb83uiJMkWYzQ==

"@rollup/rollup-linux-riscv64-gnu@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.19.0.tgz#70ae58103b5bc7ba2e2235738b51d97022c8ef92"
  integrity sha512-2DnD3mkS2uuam/alF+I7M84koGwvn3ZVD7uG+LEWpyzo/bq8+kKnus2EVCkcvh6PlNB8QPNFOz6fWd5N8o1CYg==

"@rollup/rollup-linux-s390x-gnu@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.19.0.tgz#579ca5f271421a961d3c73d221202c79e02ff03a"
  integrity sha512-D6pkaF7OpE7lzlTOFCB2m3Ngzu2ykw40Nka9WmKGUOTS3xcIieHe82slQlNq69sVB04ch73thKYIWz/Ian8DUA==

"@rollup/rollup-linux-x64-gnu@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.19.0.tgz#f0282d761b8b4e7b92b236813475248e37231849"
  integrity sha512-HBndjQLP8OsdJNSxpNIN0einbDmRFg9+UQeZV1eiYupIRuZsDEoeGU43NQsS34Pp166DtwQOnpcbV/zQxM+rWA==

"@rollup/rollup-linux-x64-musl@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.19.0.tgz#65da807ac66c505ad14b76f1e5976006cb67dd5f"
  integrity sha512-HxfbvfCKJe/RMYJJn0a12eiOI9OOtAUF4G6ozrFUK95BNyoJaSiBjIOHjZskTUffUrB84IPKkFG9H9nEvJGW6A==

"@rollup/rollup-win32-arm64-msvc@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.19.0.tgz#1eed24b91f421c2eea8bb7ca8889ba0c867e1780"
  integrity sha512-HxDMKIhmcguGTiP5TsLNolwBUK3nGGUEoV/BO9ldUBoMLBssvh4J0X8pf11i1fTV7WShWItB1bKAKjX4RQeYmg==

"@rollup/rollup-win32-ia32-msvc@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.19.0.tgz#1ed93c9cdc84e185359797a686f4d1576afcea58"
  integrity sha512-xItlIAZZaiG/u0wooGzRsx11rokP4qyc/79LkAOdznGRAbOFc+SfEdfUOszG1odsHNgwippUJavag/+W/Etc6Q==

"@rollup/rollup-win32-x64-msvc@4.19.0":
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.19.0.tgz#baf9b65023ea2ecc5e6ec68f787a0fecfd8ee84c"
  integrity sha512-xNo5fV5ycvCCKqiZcpB65VMR11NJB+StnxHz20jdqRAktfdfzhgjTiJ2doTDQE/7dqGaV5I7ZGqKpgph6lCIag==

"@shikijs/core@1.11.1", "@shikijs/core@^1.10.3":
  version "1.11.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@shikijs/core/-/core-1.11.1.tgz#a102cf56f32fa8cf3ceb9f918f2da5511782efe7"
  integrity sha512-Qsn8h15SWgv5TDRoDmiHNzdQO2BxDe86Yq6vIHf5T0cCvmfmccJKIzHtep8bQO9HMBZYCtCBzaXdd1MnxZBPSg==
  dependencies:
    "@types/hast" "^3.0.4"

"@shikijs/transformers@^1.10.3":
  version "1.11.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@shikijs/transformers/-/transformers-1.11.1.tgz#917f542f6b1eab8e599013bb0afd2b52bd4eee4e"
  integrity sha512-e6DUvZRylv+V8htF5q3ZuNyPaxJYQnsLyTd2S/K6ePs8t132NJS82LG2vARmtfSFP3I3CcBXfJ73FaCgI9kAMg==
  dependencies:
    shiki "1.11.1"

"@sinclair/typebox@^0.24.1":
  version "0.24.51"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinclair/typebox/download/@sinclair/typebox-0.24.51.tgz#645f33fe4e02defe26f2f5c0410e1c094eac7f5f"
  integrity sha1-ZF8z/k4C3v4m8vXAQQ4cCU6sf18=

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@sinonjs/commons@^3.0.0":
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinonjs/commons/-/commons-3.0.1.tgz#1029357e44ca901a615585f6d27738dbc89084cd"
  integrity sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^10.0.2":
  version "10.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz#55fdff1ecab9f354019129daf4df0dd4d923ea66"
  integrity sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==
  dependencies:
    "@sinonjs/commons" "^3.0.0"

"@types/accepts@*":
  version "1.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/accepts/-/accepts-1.3.7.tgz#3b98b1889d2b2386604c2bbbe62e4fb51e95b265"
  integrity sha512-Pay9fq2lM2wXPWbteBsRAGiWH2hig4ZE2asK+mm7kUzlxRTfL961rj89I6zV/E3PcIkDqyuBEcMxFT7rccugeQ==
  dependencies:
    "@types/node" "*"

"@types/babel-types@*", "@types/babel-types@^7.0.0":
  version "7.0.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel-types/-/babel-types-7.0.15.tgz#11fb1ab5a4f984d00d1c80a768f6fb8d59f96966"
  integrity sha512-JUgfZHUOMbtjopxiOQaaF+Uovk5wpDqpXR+XLWiOivCWSy1FccO30lvNNpCt8geFwq8VmGT2y9OMkOpA0g5O5g==

"@types/babel__core@^7.1.14":
  version "7.20.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__core/-/babel__core-7.20.5.tgz#3df15f27ba85319caa07ba08d0721889bb39c017"
  integrity sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__generator/-/babel__generator-7.6.8.tgz#f836c61f48b1346e7d2b0d93c6dacc5b9535d3ab"
  integrity sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__template/-/babel__template-7.4.4.tgz#5672513701c1b2199bc6dad636a9d7491586766f"
  integrity sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.20.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babel__traverse/-/babel__traverse-7.20.6.tgz#8dc9f0ae0f202c08d8d4dab648912c8d6038e3f7"
  integrity sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==
  dependencies:
    "@babel/types" "^7.20.7"

"@types/babylon@^6.16.2":
  version "6.16.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/babylon/-/babylon-6.16.9.tgz#7abf03f6591a921fe3171af91433077cd2666e36"
  integrity sha512-sEKyxMVEowhcr8WLfN0jJYe4gS4Z9KC2DGz0vqfC7+MXFbmvOF7jSjALC77thvAO2TLgFUPa9vDeOak+AcUrZA==
  dependencies:
    "@types/babel-types" "*"

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/body-parser/-/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/connect/-/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/content-disposition/-/content-disposition-0.5.8.tgz#6742a5971f490dc41e59d277eee71361fea0b537"
  integrity sha512-QVSSvno3dE0MgO76pJhmv4Qyi/j0Yk9pBp0Y7TJ2Tlj+KCgJWY6qX7nnxCOLkZ3VYRSIk1WTxCvwUSdx6CCLdg==

"@types/cookies@*":
  version "0.9.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/cookies/-/cookies-0.9.0.tgz#a2290cfb325f75f0f28720939bee854d4142aee2"
  integrity sha512-40Zk8qR147RABiQ7NQnBzWzDcjKzNrntB5BAmeGCb2p/MIyOE+4BVvc17wumsUqUw00bJYqoXFHYygQnEFh4/Q==
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/debug@^4.0.0":
  version "4.1.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/debug/-/debug-4.1.12.tgz#a155f21690871953410df4b6b6f53187f0500917"
  integrity sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==
  dependencies:
    "@types/ms" "*"

"@types/detect-port@^1.3.5":
  version "1.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/detect-port/-/detect-port-1.3.5.tgz#deecde143245989dee0e82115f3caba5ee0ea747"
  integrity sha512-Rf3/lB9WkDfIL9eEKaSYKc+1L/rNVYBjThk22JTqQw0YozXarX8YljFAz+HCoC6h4B4KwCMsBPZHaFezwT4BNA==

"@types/estree@*", "@types/estree@1.0.5", "@types/estree@^1.0.0":
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/estree/-/estree-1.0.5.tgz#a6ce3e556e00fd9895dd872dd172ad0d4bd687f4"
  integrity sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==

"@types/express-serve-static-core@^4.17.33":
  version "4.19.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/express-serve-static-core/-/express-serve-static-core-4.19.5.tgz#218064e321126fcf9048d1ca25dd2465da55d9c6"
  integrity sha512-y6W03tvrACO72aijJ5uF02FRq5cgDR9lUxddQ8vyF+GvmjJQqbzDcJngEjURc+ZsG31VI3hODNZJ2URj86pzmg==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "4.17.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/express/-/express-4.17.21.tgz#c26d4a151e60efe0084b23dc3369ebc631ed192d"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/formidable@~3.4.5":
  version "3.4.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/formidable/-/formidable-3.4.5.tgz#8e45c053cac5868e2b71cc7410e2bd92872f6b9c"
  integrity sha512-s7YPsNVfnsng5L8sKnG/Gbb2tiwwJTY1conOkJzTMRvJAlLFW1nEua+ADsJQu8N1c0oTHx9+d5nqg10WuT9gHQ==
  dependencies:
    "@types/node" "*"

"@types/graceful-fs@^4.1.3":
  version "4.1.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/graceful-fs/-/graceful-fs-4.1.9.tgz#2a06bc0f68a20ab37b3e36aa238be6abdf49e8b4"
  integrity sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==
  dependencies:
    "@types/node" "*"

"@types/hast@^3.0.4":
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/hast/-/hast-3.0.4.tgz#1d6b39993b82cea6ad783945b0508c25903e15aa"
  integrity sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==
  dependencies:
    "@types/unist" "*"

"@types/http-assert@*":
  version "1.5.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/http-assert/-/http-assert-1.5.5.tgz#dfb1063eb7c240ee3d3fe213dac5671cfb6a8dbf"
  integrity sha512-4+tE/lwdAahgZT1g30Jkdm9PzFRde0xwxBNUyRsCitRvCQB90iuA2uJYdUnhnANRcqGXaWOGY4FEoxeElNAK2g==

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/http-errors/-/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz#7739c232a1fee9b4d3ce8985f314c0c6d33549d7"
  integrity sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz#53047614ae72e19fc0401d872de3ae2b4ce350bf"
  integrity sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz#0f03e3d2f670fbdac586e34b433783070cc16f54"
  integrity sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/keygrip@*":
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/keygrip/-/keygrip-1.0.6.tgz#1749535181a2a9b02ac04a797550a8787345b740"
  integrity sha512-lZuNAY9xeJt7Bx4t4dx0rYCDqGPW8RXhQZK1td7d4H6E9zYbLoOtjBvfwdTKpsyxQI/2jv+armjX/RW+ZNpXOQ==

"@types/koa-bodyparser@^4.3.3", "@types/koa-bodyparser@~4.3.7":
  version "4.3.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa-bodyparser/-/koa-bodyparser-4.3.12.tgz#c19355e504422fd2a8fdb3496a32da48cd29133c"
  integrity sha512-hKMmRMVP889gPIdLZmmtou/BijaU1tHPyMNmcK7FAHAdATnRcGQQy78EqTTxLH1D4FTsrxIzklAQCso9oGoebQ==
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*", "@types/koa-compose@~3.2.5":
  version "3.2.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa-compose/-/koa-compose-3.2.8.tgz#dec48de1f6b3d87f87320097686a915f1e954b57"
  integrity sha512-4Olc63RY+MKvxMwVknCUDhRQX1pFQoBZ/lXcRLP69PQkEpze/0cr8LNqJQe5NFb/b19DWi2a5bTi2VAlQzhJuA==
  dependencies:
    "@types/koa" "*"

"@types/koa-send@*":
  version "4.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa-send/-/koa-send-4.1.6.tgz#15d90e95e3ccce669a15b6a3c56c3a650a167cea"
  integrity sha512-vgnNGoOJkx7FrF0Jl6rbK1f8bBecqAchKpXtKuXzqIEdXTDO6dsSTjr+eZ5m7ltSjH4K/E7auNJEQCAd0McUPA==
  dependencies:
    "@types/koa" "*"

"@types/koa-static@^4.0.2", "@types/koa-static@~4.0.2":
  version "4.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa-static/-/koa-static-4.0.4.tgz#ce6f2a5d14cc7ef19f9bf6ee8e4f3eadfcc77323"
  integrity sha512-j1AUzzl7eJYEk9g01hNTlhmipFh8RFbOQmaMNLvLcNNAkPw0bdTs3XTa3V045XFlrWN0QYnblbDJv2RzawTn6A==
  dependencies:
    "@types/koa" "*"
    "@types/koa-send" "*"

"@types/koa@*", "@types/koa@^2.13.4":
  version "2.15.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa/-/koa-2.15.0.tgz#eca43d76f527c803b491731f95df575636e7b6f2"
  integrity sha512-7QFsywoE5URbuVnG3loe03QXuGajrnotr3gQkXcEBShORai23MePfFYdhz90FEtBBpkyIYQbVD+evKtloCgX3g==
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/koa@~2.13.6":
  version "2.13.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/koa/-/koa-2.13.12.tgz#70d87a9061a81909e0ee11ca50168416e8d3e795"
  integrity sha512-vAo1KuDSYWFDB4Cs80CHvfmzSQWeUb909aQib0C0aFx4sw0K9UZFz2m5jaEP+b3X1+yr904iQiruS0hXi31jbw==
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/linkify-it@^5":
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/linkify-it/-/linkify-it-5.0.0.tgz#21413001973106cda1c3a9b91eedd4ccd5469d76"
  integrity sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==

"@types/markdown-it@^14.1.1":
  version "14.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/markdown-it/-/markdown-it-14.1.2.tgz#57f2532a0800067d9b934f3521429a2e8bfb4c61"
  integrity sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==
  dependencies:
    "@types/linkify-it" "^5"
    "@types/mdurl" "^2"

"@types/mdast@^4.0.0":
  version "4.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/mdast/-/mdast-4.0.4.tgz#7ccf72edd2f1aa7dd3437e180c64373585804dd6"
  integrity sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==
  dependencies:
    "@types/unist" "*"

"@types/mdurl@^2":
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/mdurl/-/mdurl-2.0.0.tgz#d43878b5b20222682163ae6f897b20447233bdfd"
  integrity sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/mime/-/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/ms@*":
  version "0.7.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/ms/-/ms-0.7.34.tgz#10964ba0dee6ac4cd462e2795b6bebd407303433"
  integrity sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g==

"@types/node-fetch@2.5.12":
  version "2.5.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node-fetch/download/@types/node-fetch-2.5.12.tgz#8a6f779b1d4e60b7a57fb6fd48d84fb545b9cc66"
  integrity sha1-im93mx1OYLelf7b9SNhPtUW5zGY=
  dependencies:
    "@types/node" "*"
    form-data "^3.0.0"

"@types/node-int64@*", "@types/node-int64@~0.4.29":
  version "0.4.32"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node-int64/-/node-int64-0.4.32.tgz#a540bcb9e48816ca1b5329d1ab907d6ad134b856"
  integrity sha512-xf/JsSlnXQ+mzvc0IpXemcrO4BrCfpgNpMco+GLcXkFk01k/gW9lGJu+Vof0ZSvHK6DsHJDPSbjFPs36QkWXqw==
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@^20.12.11":
  version "20.14.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/-/node-20.14.12.tgz#129d7c3a822cb49fc7ff661235f19cfefd422b49"
  integrity sha512-r7wNXakLeSsGT0H1AU863vS2wa5wBOK4bWMjZz2wj+8nBx+m5PeIn0k8AloSLpRuiwdRQZwarZqHE4FNArPuJQ==
  dependencies:
    undici-types "~5.26.4"

"@types/node@18.x":
  version "18.19.42"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/-/node-18.19.42.tgz#b54ed4752c85427906aab40917b0f7f3d724bf72"
  integrity sha512-d2ZFc/3lnK2YCYhos8iaNIYu9Vfhr92nHiyJHRltXWjXUBjEE+A4I58Tdbnw4VhggSW+2j5y5gTrLs4biNnubg==
  dependencies:
    undici-types "~5.26.4"

"@types/node@^10.3.6":
  version "10.17.60"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/download/@types/node-10.17.60.tgz#35f3d6213daed95da7f0f73e75bcc6980e90597b"
  integrity sha1-NfPWIT2u2V2n8Pc+dbzGmA6QWXs=

"@types/node@^14.17.1":
  version "14.18.63"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/-/node-14.18.63.tgz#1788fa8da838dbb5f9ea994b834278205db6ca2b"
  integrity sha512-fAtCfv4jJg+ExtXhvCkCqUKZ+4ok/JQk01qDKhL5BDDoS3AxKXhV5/MAVUZyQnSEd2GT92fkgZl0pz0Q0AzcIQ==

"@types/node@^18.19.34":
  version "18.19.45"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/node/-/node-18.19.45.tgz#a9ebfe4c316a356be7ca11f753ecb2feda6d6bdf"
  integrity sha512-VZxPKNNhjKmaC1SUYowuXSRSMGyQGmQjvvA1xE4QZ0xce2kLtEhPDS+kqpCPBZYgqblCLQ2DAjSzmgCM5auvhA==
  dependencies:
    undici-types "~5.26.4"

"@types/q@*":
  version "1.5.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/q/-/q-1.5.8.tgz#95f6c6a08f2ad868ba230ead1d2d7f7be3db3837"
  integrity sha512-hroOstUScF6zhIi+5+x0dzqrHA1EJi+Irri6b1fxolMTqqHIV/Cg77EtnQcZqZCu8hR3mX2BzIxN4/GzI68Kfw==

"@types/qs@*":
  version "6.9.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/qs/-/qs-6.9.15.tgz#adde8a060ec9c305a82de1babc1056e73bd64dce"
  integrity sha512-uXHQKES6DQKKCLh441Xv/dwxOq1TVS3JPUMlEqoEglvlhR6Mxnlew/Xq/LRVHpLyk7iK3zODe1qYHIMltO7XGg==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/range-parser/-/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/send@*":
  version "0.17.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/send/-/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serialize-javascript@~5.0.2":
  version "5.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/serialize-javascript/-/serialize-javascript-5.0.4.tgz#7a7c32248e207a0d29afed88e5ee3e921999c73d"
  integrity sha512-Z2R7UKFuNWCP8eoa2o9e5rkD3hmWxx/1L0CYz0k2BZzGh0PhEVMp9kfGiqEml/0IglwNERXZ2hwNzIrSz/KHTA==

"@types/serve-static@*":
  version "1.15.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/serve-static/-/serve-static-1.15.7.tgz#22174bbd74fb97fe303109738e9b5c2f3064f714"
  integrity sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/stack-utils/-/stack-utils-2.0.3.tgz#6209321eb2c1712a7e7466422b8cb1fc0d9dd5d8"
  integrity sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==

"@types/thrift@~0.10.12":
  version "0.10.17"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/thrift/-/thrift-0.10.17.tgz#54e835b9a0cb032da9535fad534557991d13dff7"
  integrity sha512-bDX6d5a5ZDWC81tgDv224n/3PKNYfIQJTPHzlbk4vBWJrYXF6Tg1ncaVmP/c3JbGN2AK9p7zmHorJC2D6oejGQ==
  dependencies:
    "@types/node" "*"
    "@types/node-int64" "*"
    "@types/q" "*"

"@types/unist@*", "@types/unist@^3.0.0":
  version "3.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/unist/-/unist-3.0.2.tgz#6dd61e43ef60b34086287f83683a5c1b2dc53d20"
  integrity sha512-dqId9J8K/vGi5Zr7oo212BGii5m3q5Hxlkwy3WpYuKPklmBEvsbMYYyLxAQpSffdLl/gdW0XUpKWFvYmyoWCoQ==

"@types/uuid@^8.3.0":
  version "8.3.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/uuid/download/@types/uuid-8.3.4.tgz#bd86a43617df0594787d38b735f55c805becf1bc"
  integrity sha1-vYakNhffBZR4fTi3NfVcgFvs8bw=

"@types/web-bluetooth@^0.0.16":
  version "0.0.16"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/web-bluetooth/download/@types/web-bluetooth-0.0.16.tgz#1d12873a8e49567371f2a75fe3e7f7edca6662d8"
  integrity sha1-HRKHOo5JVnNx8qdf4+f37cpmYtg=

"@types/web-bluetooth@^0.0.20":
  version "0.0.20"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/web-bluetooth/-/web-bluetooth-0.0.20.tgz#f066abfcd1cbe66267cdbbf0de010d8a41b41597"
  integrity sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow==

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/yargs-parser/-/yargs-parser-21.0.3.tgz#815e30b786d2e8f0dcd85fd5bcf5e1a04d008f15"
  integrity sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==

"@types/yargs@^17.0.8":
  version "17.0.32"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@types/yargs/-/yargs-17.0.32.tgz#030774723a2f7faafebf645f4e5a48371dca6229"
  integrity sha512-xQ67Yc/laOG5uMfX/093MRlGGCIBzZMarVa+gfNKJxWAIgykYpVGkBdbqEzGDDfCrVUj6Hiff4mTZ5BA6TmAog==
  dependencies:
    "@types/yargs-parser" "*"

"@typescript/vfs@^1.4.0":
  version "1.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@typescript/vfs/-/vfs-1.6.0.tgz#9c90d8c43f7ac53cc77d5959e5c4c9b639f0959e"
  integrity sha512-hvJUjNVeBMp77qPINuUvYXj4FyWeeMMKZkxEATEU3hqBAQ7qdTBCUFT7Sp0Zu0faeEtFf+ldXxMEDr/bk73ISg==
  dependencies:
    debug "^4.1.1"

"@unhead/dom@1.6.2", "@unhead/dom@~1.6.2":
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@unhead/dom/-/dom-1.6.2.tgz#4f4df6b02fa3d411cc259f731cdc7a51e3486668"
  integrity sha512-xOFZx0kAYdgXn17G1eNpGXPbClKGuzeFP40LHZG9PDxe8W2OVTAVlhPnamaPumwSYPcif0au6libawtp1bTvxA==
  dependencies:
    "@unhead/schema" "1.6.2"
    "@unhead/shared" "1.6.2"

"@unhead/schema@1.6.2", "@unhead/schema@~1.6.2":
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@unhead/schema/-/schema-1.6.2.tgz#6aa1e06bf9906f48e8ea66d1b5d432c511b7b3cc"
  integrity sha512-yOjL4i1nm7xQ8XWOhRnO/Uf66lIJP48D4lT+Fl1tPTw3isYzopKGqkyHRrVL5PqunC37jQnwBb/ZRqZr5uGLAg==
  dependencies:
    hookable "^5.5.3"
    zhead "^2.1.1"

"@unhead/shared@1.6.2":
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@unhead/shared/-/shared-1.6.2.tgz#1710365970705899b818214bb87e8eff0d5eabc4"
  integrity sha512-4+tvNwulccD1f4NOOU1SEpfJ1YepOsetaogZzPhwdHiUud/LV7YI37FAUx4dKfr3pn1/BeFzj4BH/aPOABRdyg==
  dependencies:
    "@unhead/schema" "1.6.2"

"@unhead/ssr@~1.6.2":
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@unhead/ssr/-/ssr-1.6.2.tgz#b9e50146a494627ff81a568532da7076aaecd99c"
  integrity sha512-LDdYpHADtjdUQpuBZ8rDoLunhqmQupv8CdDV9ibyz+axmSdNp42IhzL6BHw4Oi0j++PhCoZIbpVu425aMQ14pA==
  dependencies:
    "@unhead/schema" "1.6.2"
    "@unhead/shared" "1.6.2"

"@unhead/vue@~1.6.2":
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@unhead/vue/-/vue-1.6.2.tgz#92ec3736c0eab2f5d9e219b01385af8b3a00eca5"
  integrity sha512-EpcwH+90rLUfYudKYBOphfGoVSzPa2513+I5pAl+OYhZpzSkcvyLUonw+iN0KAKxbl9ELuK0Yu/MAaeOeD2C7g==
  dependencies:
    "@unhead/schema" "1.6.2"
    "@unhead/shared" "1.6.2"
    hookable "^5.5.3"
    unhead "1.6.2"

"@vant/lazyload@^1.2.0":
  version "1.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vant/lazyload/download/@vant/lazyload-1.4.0.tgz#e0529326917d8cb2264ee2e4eb08f1a03aab2c1d"
  integrity sha1-4FKTJpF9jLImTuLk6wjxoDqrLB0=
  dependencies:
    "@vant/use" "^1.3.2"

"@vant/use@^1.3.2":
  version "1.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vant/use/-/use-1.6.0.tgz#237df3091617255519552ca311ffdfea9de59001"
  integrity sha512-PHHxeAASgiOpSmMjceweIrv2AxDZIkWXyaczksMoWvKV2YAYEhoizRuk/xFnKF+emUIi46TsQ+rvlm/t2BBCfA==

"@vitejs/plugin-vue-jsx@^3.1.0":
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vitejs/plugin-vue-jsx/-/plugin-vue-jsx-3.1.0.tgz#9953fd9456539e1f0f253bf0fcd1289e66c67cd1"
  integrity sha512-w9M6F3LSEU5kszVb9An2/MmXNxocAnUb3WhRr8bHlimhDrXNt6n6D2nJQR3UXpGlZHh/EsgouOHCsM8V3Ln+WA==
  dependencies:
    "@babel/core" "^7.23.3"
    "@babel/plugin-transform-typescript" "^7.23.3"
    "@vue/babel-plugin-jsx" "^1.1.5"

"@vitejs/plugin-vue@^5.0.5":
  version "5.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vitejs/plugin-vue/-/plugin-vue-5.1.0.tgz#d29f2aad9127c73b578e7a463e76249e89256e0b"
  integrity sha512-QMRxARyrdiwi1mj3AW4fLByoHTavreXq0itdEW696EihXglf1MB3D4C2gBvE0jMPH29ZjC3iK8aIaUMLf4EOGA==

"@vue/babel-helper-vue-transform-on@1.2.2":
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.2.2.tgz#7f1f817a4f00ad531651a8d1d22e22d9e42807ef"
  integrity sha512-nOttamHUR3YzdEqdM/XXDyCSdxMA9VizUKoroLX6yTyRtggzQMHXcmwh8a7ZErcJttIBIc9s68a1B8GZ+Dmvsw==

"@vue/babel-plugin-jsx@^1.1.5", "@vue/babel-plugin-jsx@^1.2.2":
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.2.2.tgz#eb426fb4660aa510bb8d188ff0ec140405a97d8a"
  integrity sha512-nYTkZUVTu4nhP199UoORePsql0l+wj7v/oyQjtThUVhJl1U+6qHuoVhIvR3bf7eVKjbCK+Cs2AWd7mi9Mpz9rA==
  dependencies:
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.23.3"
    "@babel/template" "^7.23.9"
    "@babel/traverse" "^7.23.9"
    "@babel/types" "^7.23.9"
    "@vue/babel-helper-vue-transform-on" "1.2.2"
    "@vue/babel-plugin-resolve-type" "1.2.2"
    camelcase "^6.3.0"
    html-tags "^3.3.1"
    svg-tags "^1.0.0"

"@vue/babel-plugin-resolve-type@1.2.2":
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.2.2.tgz#66844898561da6449e0f4a261b0c875118e0707b"
  integrity sha512-EntyroPwNg5IPVdUJupqs0CFzuf6lUrVvCspmv2J1FITLeGnUCuoGNNk78dgCusxEiYj6RMkTJflGSxk5aIC4A==
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/parser" "^7.23.9"
    "@vue/compiler-sfc" "^3.4.15"

"@vue/compat@^3.2.31":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compat/-/compat-3.4.35.tgz#5495a99d1226d384277465b50792575dfc2470f9"
  integrity sha512-Z1OpqfQXWQurtOGxxYZdkgDtgWKxdQ+ZoQZ7MmAENaCFzmX80qRKGh5PVbOFqQTyTXnv0PRk3TqcF5bxddnp+g==
  dependencies:
    "@babel/parser" "^7.24.7"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-core@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-core/-/compiler-core-3.4.34.tgz#4e6af7a00927284f1f67571e2e1a8a6e93ee2d1f"
  integrity sha512-Z0izUf32+wAnQewjHu+pQf1yw00EGOmevl1kE+ljjjMe7oEfpQ+BI3/JNK7yMB4IrUsqLDmPecUrpj3mCP+yJQ==
  dependencies:
    "@babel/parser" "^7.24.7"
    "@vue/shared" "3.4.34"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-core@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-core/-/compiler-core-3.4.35.tgz#421922a75ecabf1aabc6b7a2ce98b5acb2fc2d65"
  integrity sha512-gKp0zGoLnMYtw4uS/SJRRO7rsVggLjvot3mcctlMXunYNsX+aRJDqqw/lV5/gHK91nvaAAlWFgdVl020AW1Prg==
  dependencies:
    "@babel/parser" "^7.24.7"
    "@vue/shared" "3.4.35"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-dom/-/compiler-dom-3.4.34.tgz#fd3b8df142b063c2cc0ec3e168b76b0d7774b78c"
  integrity sha512-3PUOTS1h5cskdOJMExCu2TInXuM0j60DRPpSCJDqOCupCfUZCJoyQmKtRmA8EgDNZ5kcEE7vketamRZfrEuVDw==
  dependencies:
    "@vue/compiler-core" "3.4.34"
    "@vue/shared" "3.4.34"

"@vue/compiler-dom@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-dom/-/compiler-dom-3.4.35.tgz#cd0881f1b4ed655cd96367bce4845f87023a5a2d"
  integrity sha512-pWIZRL76/oE/VMhdv/ovZfmuooEni6JPG1BFe7oLk5DZRo/ImydXijoZl/4kh2406boRQ7lxTYzbZEEXEhj9NQ==
  dependencies:
    "@vue/compiler-core" "3.4.35"
    "@vue/shared" "3.4.35"

"@vue/compiler-sfc@3.4.34", "@vue/compiler-sfc@^3.4.15":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-sfc/-/compiler-sfc-3.4.34.tgz#9a892747f8f707183a592f2dbd359b0272749dc1"
  integrity sha512-x6lm0UrM03jjDXTPZgD9Ad8bIVD1ifWNit2EaWQIZB5CULr46+FbLQ5RpK7AXtDHGjx9rmvC7QRCTjsiGkAwRw==
  dependencies:
    "@babel/parser" "^7.24.7"
    "@vue/compiler-core" "3.4.34"
    "@vue/compiler-dom" "3.4.34"
    "@vue/compiler-ssr" "3.4.34"
    "@vue/shared" "3.4.34"
    estree-walker "^2.0.2"
    magic-string "^0.30.10"
    postcss "^8.4.39"
    source-map-js "^1.2.0"

"@vue/compiler-sfc@3.4.35", "@vue/compiler-sfc@^3.2.31":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-sfc/-/compiler-sfc-3.4.35.tgz#16f87dd3bdab64cef14d3a6fcf53f8673e404071"
  integrity sha512-xacnRS/h/FCsjsMfxBkzjoNxyxEyKyZfBch/P4vkLRvYJwe5ChXmZZrj8Dsed/752H2Q3JE8kYu9Uyha9J6PgA==
  dependencies:
    "@babel/parser" "^7.24.7"
    "@vue/compiler-core" "3.4.35"
    "@vue/compiler-dom" "3.4.35"
    "@vue/compiler-ssr" "3.4.35"
    "@vue/shared" "3.4.35"
    estree-walker "^2.0.2"
    magic-string "^0.30.10"
    postcss "^8.4.40"
    source-map-js "^1.2.0"

"@vue/compiler-ssr@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-ssr/-/compiler-ssr-3.4.34.tgz#4fac491550ddc2d8733ebb58a9c3bfbe85aa7bce"
  integrity sha512-8TDBcLaTrFm5rnF+Qm4BlliaopJgqJ28Nsrc80qazynm5aJO+Emu7y0RWw34L8dNnTRdcVBpWzJxhGYzsoVu4g==
  dependencies:
    "@vue/compiler-dom" "3.4.34"
    "@vue/shared" "3.4.34"

"@vue/compiler-ssr@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/compiler-ssr/-/compiler-ssr-3.4.35.tgz#0774c9a0afed74d71615209904b38f3fa9711adb"
  integrity sha512-7iynB+0KB1AAJKk/biENTV5cRGHRdbdaD7Mx3nWcm1W8bVD6QmnH3B4AHhQQ1qZHhqFwzEzMwiytXm3PX1e60A==
  dependencies:
    "@vue/compiler-dom" "3.4.35"
    "@vue/shared" "3.4.35"

"@vue/devtools-api@^6.0.0-beta.11", "@vue/devtools-api@^6.5.1", "@vue/devtools-api@^6.6.3":
  version "6.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/devtools-api/-/devtools-api-6.6.3.tgz#b23a588154cba8986bba82b6e1d0248bde3fd1a0"
  integrity sha512-0MiMsFma/HqA6g3KLKn+AGpL1kgKhFWszC9U29NfpWK5LE7bjeXxySWJrOJ77hBz+TBrBQ7o4QJqbPbqbs8rJw==

"@vue/devtools-api@^7.3.5":
  version "7.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/devtools-api/-/devtools-api-7.3.7.tgz#78f46e9d7af206f06392b4139045e6538cef0a19"
  integrity sha512-kvjQ6nmsqTp7SrmpwI2G0MgbC4ys0bPsgQirHXJM8y1m7siQ5RnWQUHJVfyUrHNguCySW1cevAdIw87zrPTl9g==
  dependencies:
    "@vue/devtools-kit" "^7.3.7"

"@vue/devtools-kit@^7.3.7":
  version "7.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/devtools-kit/-/devtools-kit-7.3.7.tgz#c1c19d427e3b457cf91305c86ae2e6a58830442b"
  integrity sha512-ktHhhjI4CoUrwdSUF5b/MFfjrtAtK8r4vhOkFyRN5Yp9kdXTwsRBYcwarHuP+wFPKf4/KM7DVBj2ELO8SBwdsw==
  dependencies:
    "@vue/devtools-shared" "^7.3.7"
    birpc "^0.2.17"
    hookable "^5.5.3"
    mitt "^3.0.1"
    perfect-debounce "^1.0.0"
    speakingurl "^14.0.1"
    superjson "^2.2.1"

"@vue/devtools-shared@^7.3.7":
  version "7.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/devtools-shared/-/devtools-shared-7.3.7.tgz#765d2e0e4d3def891cdead22ef2a373d1d1f4df0"
  integrity sha512-M9EU1/bWi5GNS/+IZrAhwGOVZmUTN4MH22Hvh35nUZZg9AZP2R2OhfCb+MG4EtAsrUEYlu3R43/SIj3G7EZYtQ==
  dependencies:
    rfdc "^1.4.1"

"@vue/reactivity@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/reactivity/-/reactivity-3.4.34.tgz#388ec52f55a3fbe6f9332d5d993567a1886fdc76"
  integrity sha512-ua+Lo+wBRlBEX9TtgPOShE2JwIO7p6BTZ7t1KZVPoaBRfqbC7N3c8Mpzicx173fXxx5VXeU6ykiHo7WgLzJQDA==
  dependencies:
    "@vue/shared" "3.4.34"

"@vue/reactivity@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/reactivity/-/reactivity-3.4.35.tgz#dfbb4f5371da1290ac86e3313d0e9a68bb0ab38d"
  integrity sha512-Ggtz7ZZHakriKioveJtPlStYardwQH6VCs9V13/4qjHSQb/teE30LVJNrbBVs4+aoYGtTQKJbTe4CWGxVZrvEw==
  dependencies:
    "@vue/shared" "3.4.35"

"@vue/runtime-core@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/runtime-core/-/runtime-core-3.4.34.tgz#47d2ab89c796d7012be17e2bbec40cff001317d7"
  integrity sha512-PXhkiRPwcPGJ1BnyBZFI96GfInCVskd0HPNIAZn7i3YOmLbtbTZpB7/kDTwC1W7IqdGPkTVC63IS7J2nZs4Ebg==
  dependencies:
    "@vue/reactivity" "3.4.34"
    "@vue/shared" "3.4.34"

"@vue/runtime-core@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/runtime-core/-/runtime-core-3.4.35.tgz#c036013a7b1bbe0d14a6b76eb4355dae6690d2e6"
  integrity sha512-D+BAjFoWwT5wtITpSxwqfWZiBClhBbR+bm0VQlWYFOadUUXFo+5wbe9ErXhLvwguPiLZdEF13QAWi2vP3ZD5tA==
  dependencies:
    "@vue/reactivity" "3.4.35"
    "@vue/shared" "3.4.35"

"@vue/runtime-dom@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/runtime-dom/-/runtime-dom-3.4.34.tgz#8a7f25647c3ac8d9fc2208fd5e06f70ba2dd6638"
  integrity sha512-dXqIe+RqFAK2Euak4UsvbIupalrhc67OuQKpD7HJ3W2fv8jlqvI7szfBCsAEcE8o/wyNpkloxB6J8viuF/E3gw==
  dependencies:
    "@vue/reactivity" "3.4.34"
    "@vue/runtime-core" "3.4.34"
    "@vue/shared" "3.4.34"
    csstype "^3.1.3"

"@vue/runtime-dom@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/runtime-dom/-/runtime-dom-3.4.35.tgz#74254c7c327163d692e1d7d2b6d9e92463744e90"
  integrity sha512-yGOlbos+MVhlS5NWBF2HDNgblG8e2MY3+GigHEyR/dREAluvI5tuUUgie3/9XeqhPE4LF0i2wjlduh5thnfOqw==
  dependencies:
    "@vue/reactivity" "3.4.35"
    "@vue/runtime-core" "3.4.35"
    "@vue/shared" "3.4.35"
    csstype "^3.1.3"

"@vue/server-renderer@3.4.34":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/server-renderer/-/server-renderer-3.4.34.tgz#4b3a5bc6fb818aef9713e41fb78dece256dd933c"
  integrity sha512-GeyEUfMVRZMD/mZcNONEqg7MiU10QQ1DB3O/Qr6+8uXpbwdlmVgQ5Qs1/ZUAFX1X2UUtqMoGrDRbxdWfOJFT7Q==
  dependencies:
    "@vue/compiler-ssr" "3.4.34"
    "@vue/shared" "3.4.34"

"@vue/server-renderer@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/server-renderer/-/server-renderer-3.4.35.tgz#188e94e82d8e729ba7b40dd91d10678b85f77c6b"
  integrity sha512-iZ0e/u9mRE4T8tNhlo0tbA+gzVkgv8r5BX6s1kRbOZqfpq14qoIvCZ5gIgraOmYkMYrSEZgkkojFPr+Nyq/Mnw==
  dependencies:
    "@vue/compiler-ssr" "3.4.35"
    "@vue/shared" "3.4.35"

"@vue/shared@3.4.34", "@vue/shared@^3.4.31":
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/shared/-/shared-3.4.34.tgz#130858419e634a427ca82c36e1da75c66a39ba8e"
  integrity sha512-x5LmiRLpRsd9KTjAB8MPKf0CDPMcuItjP0gbNqFCIgL1I8iYp4zglhj9w9FPCdIbHG2M91RVeIbArFfFTz9I3A==

"@vue/shared@3.4.35":
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vue/shared/-/shared-3.4.35.tgz#5432f4b1c79e763fcf78cc830faf59ff01248968"
  integrity sha512-hvuhBYYDe+b1G8KHxsQ0diDqDMA8D9laxWZhNAjE83VZb5UDaXl9Xnz7cGdDSyiHM90qqI/CyGMcpBpiDy6VVQ==

"@vueuse/core@10.11.0", "@vueuse/core@^10.11.0":
  version "10.11.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/core/-/core-10.11.0.tgz#b042585a8bf98bb29c177b33999bd0e3fcd9e65d"
  integrity sha512-x3sD4Mkm7PJ+pcq3HX8PLPBadXCAlSDR/waK87dz0gQE+qJnaaFhc/dZVfJz+IUYzTMVGum2QlR7ImiJQN4s6g==
  dependencies:
    "@types/web-bluetooth" "^0.0.20"
    "@vueuse/metadata" "10.11.0"
    "@vueuse/shared" "10.11.0"
    vue-demi ">=0.14.8"

"@vueuse/core@^6.7.3":
  version "6.9.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/core/download/@vueuse/core-6.9.2.tgz#76b16d01f33cf367dd1a2d7f2e31d106443ceb8a"
  integrity sha1-drFtAfM882fdGi1/LjHRBkQ864o=
  dependencies:
    "@vueuse/shared" "6.9.2"
    vue-demi "*"

"@vueuse/core@^9.0.0", "@vueuse/core@^9.3.1":
  version "9.13.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/core/download/@vueuse/core-9.13.0.tgz#2f69e66d1905c1e4eebc249a01759cf88ea00cf4"
  integrity sha1-L2nmbRkFweTuvCSaAXWc+I6gDPQ=
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    vue-demi "*"

"@vueuse/head@^0.7.5":
  version "0.7.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/head/download/@vueuse/head-0.7.13.tgz#a6defbe215a7f42bd65daea5b2f102588f883270"
  integrity sha1-pt774hWn9CvWXa6lsvECWI+IMnA=
  dependencies:
    "@zhead/schema-vue" "^0.7.3"

"@vueuse/integrations@^10.11.0":
  version "10.11.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/integrations/-/integrations-10.11.0.tgz#ce2746587172af9ab8faa713f42e619609ed0de1"
  integrity sha512-Pp6MtWEIr+NDOccWd8j59Kpjy5YDXogXI61Kb1JxvSfVBO8NzFQkmrKmSZz47i+ZqHnIzxaT38L358yDHTncZg==
  dependencies:
    "@vueuse/core" "10.11.0"
    "@vueuse/shared" "10.11.0"
    vue-demi ">=0.14.8"

"@vueuse/metadata@10.11.0":
  version "10.11.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/metadata/-/metadata-10.11.0.tgz#27be47cf115ee98e947a1bfcd0b1b5b35d785fb6"
  integrity sha512-kQX7l6l8dVWNqlqyN3ePW3KmjCQO3ZMgXuBMddIu83CmucrsBfXlH+JoviYyRBws/yLTQO8g3Pbw+bdIoVm4oQ==

"@vueuse/metadata@9.13.0":
  version "9.13.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/metadata/download/@vueuse/metadata-9.13.0.tgz#bc25a6cdad1b1a93c36ce30191124da6520539ff"
  integrity sha1-vCWmza0bGpPDbOMBkRJNplIFOf8=

"@vueuse/shared@10.11.0":
  version "10.11.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/shared/-/shared-10.11.0.tgz#be09262b2c5857069ed3dadd1680f22c4cb6f984"
  integrity sha512-fyNoIXEq3PfX1L3NkNhtVQUSRtqYwJtJg+Bp9rIzculIZWHTkKSysujrOk2J+NrRulLTQH9+3gGSfYLWSEWU1A==
  dependencies:
    vue-demi ">=0.14.8"

"@vueuse/shared@6.9.2":
  version "6.9.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/shared/download/@vueuse/shared-6.9.2.tgz#97e4369fa7262ebc96fe1d6e210268f30b037005"
  integrity sha1-l+Q2n6cmLryW/h1uIQJo8wsDcAU=
  dependencies:
    vue-demi "*"

"@vueuse/shared@9.13.0", "@vueuse/shared@^9.2.0":
  version "9.13.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@vueuse/shared/download/@vueuse/shared-9.13.0.tgz#089ff4cc4e2e7a4015e57a8f32e4b39d096353b9"
  integrity sha1-CJ/0zE4uekAV5XqPMuSznQljU7k=
  dependencies:
    vue-demi "*"

"@xhs/abtest@^0.6.2":
  version "0.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/abtest/-/@xhs/abtest-0.6.2.tgz#21703dae672ad25ef73f95ae1a4e16f1c5fdf128"
  integrity sha1-IXA9rmcq0l73P5WuGk4W8cX98Sg=
  dependencies:
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/preset-typescript" "^7.21.4"
    "@xhs/ozone-bridge" "^2.17.6"
    babel-preset-env "^1.7.0"
    jest "^29.5.0"

"@xhs/abtest@^0.7.0":
  version "0.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/abtest/-/@xhs/abtest-0.7.0.tgz#ac9ab4ec935378927cf263229eb60e3c8b672008"
  integrity sha512-QZ6Mt1roDO+IxZbSzDel+uG7LYmdJUl6EV5pfl35KK9PMacgxFSMaY5pqehnDw4ZKXLDkBS4M2LchGEFBuEU2A==
  dependencies:
    "@babel/plugin-proposal-export-namespace-from" "^7.18.9"
    "@babel/preset-typescript" "^7.21.4"
    "@xhs/ozone-bridge" "^2.17.6"
    babel-preset-env "^1.7.0"
    jest "^29.5.0"

"@xhs/apm-insight@^1.1.8":
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/apm-insight/-/@xhs/apm-insight-1.2.0.tgz#274ac94c27d234280e7ee0b589cf20bdc69da740"
  integrity sha512-/FIgtblwMEAbcg+iWO0uGNql8ugqxdI7yRjbHMYo8WWrZbEvR5uehjazY5WED7/MhQiHN1NQPHrUj7onRuauNA==
  dependencies:
    "@xhs/ozone-detector" "^3.11.8"
    "@xhs/ozone-schema" "^1.159.0"
    "@xhs/perf-metrics" "^1.10.5"
    uuid "^9.0.1"
    web-vitals "^3.1.1"

"@xhs/ark-datacenter@0.0.9":
  version "0.0.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ark-datacenter/-/@xhs/ark-datacenter-0.0.9.tgz#0bd8a0d7705615932f90ad8ef097a4fe54dd92f7"
  integrity sha1-C9ig13BWFZMvkK2O8Jek/lTdkvc=
  dependencies:
    "@xhs/meepo-delight-chart" "1.1.0"
    echarts "5.2.1"
    lodash-es "^4.17.21"

"@xhs/biz-dict-kit-core@0.0.3-beta.0":
  version "0.0.3-beta.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/biz-dict-kit-core/-/@xhs/biz-dict-kit-core-0.0.3-beta.0.tgz#398b5a13802796591bce0d7f0807b89d1a06ffc3"
  integrity sha512-6uG8CFsgWoqRNeaJG75M1SMqy2lpNp64YDfIFX5ynZ+8vQRe0P3FY3sjurrzjhpwg5VV6CygWoT1I3+KJlClGg==

"@xhs/bridge-shared@^1.1.0":
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/bridge-shared/-/@xhs/bridge-shared-1.1.0.tgz#c440ec5a45ce82e245a4e4c0a4dda6d03e162066"
  integrity sha1-xEDsWkXOguJFpOTApN2m0D4WIGY=

"@xhs/data-transform@^0.3.3":
  version "0.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/data-transform/-/@xhs/data-transform-0.3.3.tgz#9bc57313156ad0f638d65fa3799215bec08e8b15"
  integrity sha1-m8VzExVq0PY41l+jeZIVvsCOixU=

"@xhs/delight-ark-indicator-card@^0.3.0":
  version "0.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-ark-indicator-card/-/@xhs/delight-ark-indicator-card-0.3.0.tgz#9c787c7e0a017d99a9c088a281c7f5edd7d3e125"
  integrity sha1-nHh8fgoBfZmpwIiigcf17dfT4SU=
  dependencies:
    "@vueuse/core" "^6.7.3"
    dayjs "^1.10.6"
    jest-diff "^28.0.2"
    lodash "^4.17.21"
    mitt "^3.0.0"
    vuedraggable "^4.0.3"

"@xhs/delight-ark-qrcode@^1.0.1":
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-ark-qrcode/-/@xhs/delight-ark-qrcode-1.0.1.tgz#2452a670b47cdba4a21b185e594ef4158af9cf65"
  integrity sha1-JFKmcLR826SiGxheWU70FYr5z2U=
  dependencies:
    qr-code-styling "^1.6.0-rc.1"

"@xhs/delight-formily@0.6.6":
  version "0.6.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-formily/-/@xhs/delight-formily-0.6.6.tgz#8bfcdb4e82639d2cbb15ea14be5e35664edf38dc"
  integrity sha1-i/zbToJjnSy7FeoUvl41Zk7fONw=
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@formily/core" "2.2.7"
    "@formily/grid" "2.2.7"
    "@formily/json-schema" "2.2.7"
    "@formily/reactive" "2.2.7"
    "@formily/reactive-vue" "2.2.7"
    "@formily/shared" "2.2.7"
    "@formily/validator" "2.2.7"
    "@formily/vue" "2.2.7"
    "@icon-park/svg" "^1.3.5"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.0.0"
    "@xhs/delight" "1.0.5"
    dayjs "^1.10.7"
    lodash "^4.17.21"
    lodash-unified "^1.0.2"
    lodash.clonedeep "^4.5.0"
    lodash.mergewith "^4.6.2"
    lodash.uniq "^4.5.0"
    resize-observer-polyfill "^1.5.1"
    vue-slicksort "^2.0.0-alpha.5"

"@xhs/delight-material-ads-note-detail@^0.1.6":
  version "0.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ads-note-detail/-/@xhs/delight-material-ads-note-detail-0.1.7.tgz#57abfbf817beac7603401f4bd36c832f11bc7f33"
  integrity sha512-alR0PGAoRj/A2IKT20URQjBW3vMi3sqiLnhydaNcsY+zU4CsFS+IIr2euAn1VoyrNS8Z/b7s0XXm/epoTzx+6g==
  dependencies:
    "@xhs/delight" "^0.1.26"
    "@xhs/xgplayer" "2.32.24"
    "@xhs/xgplayer-mp4" "2.1.30"
    clipboard "^2.0.11"
    numerify "^1.2.9"

"@xhs/delight-material-ark-goods-select@0.2.15":
  version "0.2.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ark-goods-select/-/@xhs/delight-material-ark-goods-select-0.2.15.tgz#d50f07b4654d04378ac9dc47abfb05440b824a21"
  integrity sha1-1Q8HtGVNBDeKydxHq/sFRAuCSiE=
  dependencies:
    loadsh "0.0.4"

"@xhs/delight-material-ark-qrcode@^0.1.8":
  version "0.1.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ark-qrcode/-/@xhs/delight-material-ark-qrcode-0.1.8.tgz#83164df7c480d09282d65da4c639c22036cb9e43"
  integrity sha1-gxZN98SA0JKC1l2kxjnCIDbLnkM=
  dependencies:
    qr-code-styling "^1.6.0-rc.1"

"@xhs/delight-material-item-calendar@^0.0.7":
  version "0.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-item-calendar/-/@xhs/delight-material-item-calendar-0.0.7.tgz#fa3de9e7001b29530d44b8d2ac7a3b0ce3d07478"
  integrity sha512-s87RvvdF6IyLzx3AXBEaVOBZUyoDtO+tvBrKIpT9zx0/aVBmbFCyRfYbg4kk6GVpYuFNnoLDikx2Nr5S7hprgA==

"@xhs/delight-material-life-category-selector@^0.1.3":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-category-selector/-/@xhs/delight-material-life-category-selector-0.1.3.tgz#be78dcd03316fd5f984fb22c17cccd0194a5aa6f"
  integrity sha512-35Wfe+/bV6jKjMjw7I4OcNcx0j3AZUDDCrnF33ezcVBx4zHAMcGYJvv0I35Y1TfJH57hFc4fawuxhxwcWwWF0Q==

"@xhs/delight-material-life-enhance-input-number@^0.1.3":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-enhance-input-number/-/@xhs/delight-material-life-enhance-input-number-0.1.3.tgz#6987db1c889030828965316788bc579056bc196c"
  integrity sha512-STWZgg62Jha63w03tbUrRI56shue8gAFPOuQlbYSYxAkS+UdCDOMRi6Vs1+9ces9eAetkO1jectrHeaAjFYlzg==

"@xhs/delight-material-life-identification-upload@0.1.8":
  version "0.1.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-identification-upload/-/@xhs/delight-material-life-identification-upload-0.1.8.tgz#e82a83af15b9a64562dab9d5ba402465341654b9"
  integrity sha512-VzpppVOZO/OcAeCa53ermZrS4zAU4Uj5s7z5FmJtjS0idIux6UnuD0vdWkA7aLgSxJI2ia2cAOplPJW0bsEXpw==
  dependencies:
    "@xhs/delight-material-ultra-enhance-upload" "0.2.1"
    "@xhs/uploader" "^3.0.7"

"@xhs/delight-material-life-image@^0.1.2":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-image/-/@xhs/delight-material-life-image-0.1.3.tgz#7d4f7b906595c534b2395da06d32728b536aba8f"
  integrity sha512-HetG3tClyhHGcJzw7bxhKnzwMgsuuXXAzzvtQbQNhmCRtoyIFxXOgD4mtO4wqSbGaqed+Y0W0y6LGuoH7SNGwg==

"@xhs/delight-material-life-qrcode@0.1.5":
  version "0.1.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-qrcode/-/@xhs/delight-material-life-qrcode-0.1.5.tgz#078671aedb998443af3b1ce82a0f657c239b1918"
  integrity sha512-tFvh4NNeKNtG6cFIaCUcQ1TnS+ObR6qQ0SCG1BWVHBfax3P72U3J/0wlOIa+uEh9SRVSDZzN6BCUqDvRG16zcQ==
  dependencies:
    qr-code-styling "^1.6.0-rc.1"

"@xhs/delight-material-life-qrcode@^0.1.5":
  version "0.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-qrcode/-/@xhs/delight-material-life-qrcode-0.1.6.tgz#734becd6204aaac3ed17a8ad0a07b059c283567e"
  integrity sha512-zyVcKL5IOuDNJE8/0znSTGZZUyiHywYC9ilJcIdaDoo8hc6C622fobArI7SrnAasAZnjpgnxdK01A7UIWLVchg==
  dependencies:
    qr-code-styling "^1.6.0-rc.1"

"@xhs/delight-material-life-region-cascader@0.1.1":
  version "0.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-region-cascader/-/@xhs/delight-material-life-region-cascader-0.1.1.tgz#2a1a5fc4430421ef5d4e207fede37ea19cab501b"
  integrity sha512-X0XMOoOgfY9DWdwkDys0kBqbz5jcpp6G7cGn1A7niuu2wSzCm7LZSL7rZAOe92HbLQFcAfNfwcbETQiE1O7eEw==

"@xhs/delight-material-life-show-qrcode@^0.1.7":
  version "0.1.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-show-qrcode/-/@xhs/delight-material-life-show-qrcode-0.1.9.tgz#805c9e66be4103ff74a867198d235e850a4aa6c2"
  integrity sha512-Wn9DZDW76ZahjssXSCMTLhQJHhvQgBNgXCi5WOgl9fjhL0FLBUGV+ncbr7NfA4kRgU3oeAg2pwewO57tys531Q==
  dependencies:
    file-saver "^2.0.5"
    html2canvas "^1.4.1"
    jszip "^3.10.1"
    qr-code-styling "^1.6.0-rc.1"
    swiper "^11.0.5"

"@xhs/delight-material-life-validity-period@^0.1.2":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-life-validity-period/-/@xhs/delight-material-life-validity-period-0.1.3.tgz#1d22c9e77a7cb0daa51570edcb39a3b2be50a1e0"
  integrity sha512-Lq6MiKh4CcwwKIBZyBLlMHE+huOdSgfAg+t2Zyw2ncRs78ZWw8fZL26XjeHUVftoPyPlE0GAAZwTrT/+HvLKPA==

"@xhs/delight-material-promotion-pc-feedback@^0.1.5":
  version "0.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-promotion-pc-feedback/-/@xhs/delight-material-promotion-pc-feedback-0.1.6.tgz#03956e2805e8dfe530c95e00de8a7cd32c4291a3"
  integrity sha512-5YiVF1PmJ/OdBmBJc1CMfHjFzQXRs4sF8+No/cQLlaUec7/GYpk7nZ2RBRwPAx6fIVMmpr+d+64dj+/giRO1jw==
  dependencies:
    "@xhs/uploader" "^3.0.6"
    lodash.clonedeep "^4.5.0"
    lodash.debounce "^4.0.8"

"@xhs/delight-material-ultra-crop-tool@^0.1.10":
  version "0.1.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-crop-tool/-/@xhs/delight-material-ultra-crop-tool-0.1.10.tgz#aea4713a7549d8d9af355b853225ec6971a19de4"
  integrity sha512-YAjzFYdqCbq8N0BXrXLQoPaCgOvP8aB+4K9hja1BFd9TE54Tj38TL/nuCSyXtShqPbzR3NGO8N26d2R8LqxSEA==
  dependencies:
    cropperjs "^1.5.12"

"@xhs/delight-material-ultra-delight-form@^0.1.8":
  version "0.1.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-delight-form/-/@xhs/delight-material-ultra-delight-form-0.1.9.tgz#71f6706eaf755038ed940fc9e46e918745bbcd88"
  integrity sha512-a2dG3/Jh2jHRZo2KVLYUSpaf4Tyrj8FuD2SEv1pY4Y5RaWrVPaI+pbmjiuGX3ZNL4B4TJlCbk6lPQ3Y5h/WwUg==

"@xhs/delight-material-ultra-enhance-upload@0.2.1":
  version "0.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-enhance-upload/-/@xhs/delight-material-ultra-enhance-upload-0.2.1.tgz#cf6a017f7278302dedf3719aa88ee9973a9aaf59"
  integrity sha512-QX33wF+xpCG+kS/pvPtVnnG7PihxgfFFai1kWFGDDZfzZ94qXsS4lBXnNUk+avbgHwPPy7ZfFAdLurpD0SGLvA==
  dependencies:
    "@xhs/delight-material-life-qrcode" "0.1.5"
    "@xhs/delight-material-ultra-crop-tool" "^0.1.10"
    vuedraggable "4.1.0"

"@xhs/delight-material-ultra-image-conversion@^0.1.13":
  version "0.1.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-image-conversion/-/@xhs/delight-material-ultra-image-conversion-0.1.13.tgz#a654ced0fe79f567e4d4fd04e78b3a434b4326a0"
  integrity sha512-s9j1XZvulJfDGGnckBC8QS0l7dG9SiNMdQ9DS1rjR+AUv4GJ+5dqE4K6zZR8cV14eLWEBlAYEsC27v3K/FZ1BA==
  dependencies:
    browser-md5-file "^1.1.1"
    cropperjs "^1.6.1"
    exif-js "^2.3.0"

"@xhs/delight-material-ultra-outline-filter@^0.1.15":
  version "0.1.25"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-outline-filter/-/@xhs/delight-material-ultra-outline-filter-0.1.25.tgz#d3ec952ad6fddec8f953c69e97451f9676dabc32"
  integrity sha512-R9Psglw2MCTSTjXYOhlWheswTgN+tqKBd9Mw05pC0S362iGnHkym4M1lHJ8oGBsIjWHbka//oAPlVFjO8SUd0w==
  dependencies:
    vuedraggable "4.1.0"

"@xhs/delight-material-ultra-page-header@^0.2.10":
  version "0.2.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-page-header/-/@xhs/delight-material-ultra-page-header-0.2.12.tgz#90dd531fd76930a466fdd8dfdd440bfe75e7806b"
  integrity sha512-nZ6BTMqz1fFjbmUUa5IJGdVSt8o1/A+ef4RKKtp9f9lkqXGnGCh0OnyqT87bLKN1PtJvSSFk2r6g4G3U/urVBA==

"@xhs/delight-material-ultra-table-cell@^0.1.30":
  version "0.1.32"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-table-cell/-/@xhs/delight-material-ultra-table-cell-0.1.32.tgz#222eacfd7048ffd93a54f76313fb786c6df00dea"
  integrity sha512-Q08ZPMk1okya0JZi+RPFz4EU/0TF0jgpqGykzUZ0PwiWjEI7+EISal0TL+Oe/ZHrNIv1F53Ma0HKXoq2i+ffYQ==

"@xhs/delight-material-ultra-toolbar@^0.1.28":
  version "0.1.30"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-toolbar/-/@xhs/delight-material-ultra-toolbar-0.1.30.tgz#d089243bf8d5b772f7f312cebdcdd2e1f0c80aad"
  integrity sha512-4mz8Dsx21tWywQDD2TfdACjZ2Ox0XK0kZ4bNIN1+0JjTiECbFBFhUxPan6onlErJgRvv7w1KTJ+/UZirAG/JCQ==
  dependencies:
    resize-observer-polyfill "^1.5.1"

"@xhs/delight-material-ultra-tour-guide@^1.0.4":
  version "1.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-material-ultra-tour-guide/-/@xhs/delight-material-ultra-tour-guide-1.0.4.tgz#0601bd320b3ee99e1a7252c8b9439869cf7f9cbe"
  integrity sha512-RuS/ryLA/3MpKAIpAHLogwONdZBoysvo1WMIq4xIjkHIbGF0oSCFx50hHLpGXSRYx71xlbWJIdQqsEUXtr0Jxw==
  dependencies:
    "@popperjs/core" "^2.11.7"
    lodash-es "^4.17.21"

"@xhs/delight-official-chart@^1.1.3":
  version "1.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight-official-chart/-/@xhs/delight-official-chart-1.1.3.tgz#52472cd1b17b32c86fa564ac6aa534753b496489"
  integrity sha1-Ukcs0bF7MshvpWSsaqU0dTtJZIk=
  dependencies:
    echarts "^5.2.2"

"@xhs/delight@1.0.5":
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight/-/@xhs/delight-1.0.5.tgz#180f2194791c23d1f39ed74dfa316a69f565f9e6"
  integrity sha1-GA8hlHkcI9HzntdN+jFqafVl+eY=
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@icon-park/svg" "1.4.1"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.3.1"
    async-validator "^4.2.5"
    dayjs "^1.10.7"
    lodash.clonedeep "^4.5.0"
    lodash.get "^4.4.2"
    lodash.mergewith "^4.6.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    vue-virtual-scroller "^2.0.0-beta.3"

"@xhs/delight@1.2.15":
  version "1.2.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight/-/@xhs/delight-1.2.15.tgz#e3bcd1e9f3eefaacd1672a3e9c0c47c342bafdc7"
  integrity sha512-15gDpnjCiQoBGrgIcUMvQY3W31NjR45iaDQNq5HoDqB3gxL2Amx24UinU5BMo6pzSFFzqJjTpogp8NHsSoybOw==
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.4.2"
    "@icon-park/svg" "1.4.1"
    "@types/node" "^18.19.34"
    "@vueuse/core" "^9.3.1"
    async-validator "^4.2.5"
    dayjs "^1.10.7"
    lodash.clonedeep "^4.5.0"
    lodash.get "^4.4.2"
    lodash.mergewith "^4.6.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    resize-observer-polyfill "^1.5.1"
    sortablejs "^1.15.0"
    vue-virtual-scroller "^2.0.0-beta.3"

"@xhs/delight@^0.1.26":
  version "0.1.61"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/delight/-/@xhs/delight-0.1.61.tgz#e525f99ef0ed7aba965de8a2f8386b23f0c1c86e"
  integrity sha512-VT5ZlqwMYKWU4Ji3HYao36GaW5XMQvpGgKshc9R7I6fbUrJ/I3DOT1JEvmXM7rMc7Lq+MK5XjbREr/LmrMl7AA==
  dependencies:
    "@babel/types" "^7.16.8"
    "@floating-ui/dom" "^0.1.10"
    "@icon-park/svg" "1.4.1"
    "@types/node" "^14.17.1"
    "@vueuse/core" "^9.3.1"
    async-validator "^4.2.5"
    dayjs "^1.10.7"
    lodash.clonedeep "^4.5.0"
    lodash.get "^4.4.2"
    lodash.mergewith "^4.6.2"
    lodash.set "^4.3.2"
    lodash.uniq "^4.5.0"
    resize-observer-polyfill "^1.5.1"
    sortablejs "^1.15.0"
    vue-virtual-scroller "^2.0.0-beta.3"

"@xhs/di@0.1.0-beta.1":
  version "0.1.0-beta.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/di/-/@xhs/di-0.1.0-beta.1.tgz#1670c9ae534885c4d914378ca36cf7d937679827"
  integrity sha512-VwlMVd2Nu2z38eULnz7BRzHxzYfYrKAJ3I73aIAnE7W63LEr7KmLDDMbcfWYEWJRVrSq2AkRsfEIMf39Xa44Fw==
  dependencies:
    reflect-metadata "^0.2.1"

"@xhs/di@^0.1.1":
  version "0.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/di/-/@xhs/di-0.1.1.tgz#585ef7dd32d88794446091a9a2a23742d434a868"
  integrity sha512-jrmbdjKxzHinSQYhiw65kDP9t2MWGwEg8SNw5ZQYPdoILvaEHGrqzTa6J5LeJQsUjA75joIJv3YVciXLDBxXyg==
  dependencies:
    reflect-metadata "^0.2.1"

"@xhs/dockwalloper@^0.4.2":
  version "0.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/dockwalloper/-/@xhs/dockwalloper-0.4.2.tgz#c7924ce28496dc94ec6d79924b9404d952282a28"
  integrity sha1-x5JM4oSW3JTsbXmSS5QE2VIoKig=
  dependencies:
    adm-zip "^0.5.5"
    axios "^0.19.2"
    btoa "^1.2.1"
    chalk "^2.4.2"
    commander "^3.0.2"
    cos-nodejs-sdk-v5 "^2.9.13"
    dompurify "^2.0.6"
    marked "0.4.0"
    rd "^2.0.1"
    uuid "^8.3.2"

"@xhs/eaglet-emitter-base@^0.1.3":
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/eaglet-emitter-base/-/@xhs/eaglet-emitter-base-0.1.3.tgz#b86746f0272909eca0f462351e6f7c7edafb220f"
  integrity sha1-uGdG8CcpCeyg9GI1Hm98ftr7Ig8=

"@xhs/eaglet@1.1.1-1":
  version "1.1.1-1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/eaglet/-/@xhs/eaglet-1.1.1-1.tgz#eb22cb012a1bdd9610e948ad1ca2a38abf3e4c76"
  integrity sha1-6yLLASob3ZYQ6UitHKKjir8+THY=

"@xhs/fe-api-sign@^0.2.0", "@xhs/fe-api-sign@~0.2.0":
  version "0.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/fe-api-sign/-/@xhs/fe-api-sign-0.2.0.tgz#a97e0b767add2243a84bd41c2c9e2f8f89d25a32"
  integrity sha1-qX4LdnrdIkOoS9QcLJ4vj4nSWjI=
  dependencies:
    md5 "^2.2.1"

"@xhs/http@>=1.13":
  version "1.13.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/http/-/@xhs/http-1.13.1.tgz#92f28447fb3a2c8d80129e241146fa2e4a6df9d0"
  integrity sha512-PJRzhS/kMDSCJ9HNliPE4c3GwpHC+r14mJgHFgQ7cBiGv6jgfzJjMB29EytrcDgOhU5yQvUvp9+KVMgc+b5Kxg==
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^3.0.1"
    "@xhs/untrace" "^0.0.1"
    axios "^0.19.2"

"@xhs/http@^1.10.1":
  version "1.13.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/http/-/@xhs/http-1.13.3.tgz#02eb1ca1e7b946e45c6e8bea061a88906f3d90e0"
  integrity sha512-89uuMELpZQMRmQeIGiNz5A3R1n1QnnGZxcLPjG+PWPNFU1WpmDNNN4vZqVcWvgtf7edh5VkcmEPoaw3naueKeg==
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^3.0.1"
    "@xhs/untrace" "^0.0.1"
    axios "^0.19.2"

"@xhs/hulk-apm-logger-service@~1.2.1":
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-apm-logger-service/-/@xhs/hulk-apm-logger-service-1.2.1.tgz#40d0c040cd79735df6413da42b52caf8db116f0a"
  integrity sha512-r1KAblWWXsNxNdhIynr1/SeruWClfR99wAx9MGqy5w69KSuksOrQoxUcK8B+F8Jhs3Sz5vy00/NY58N6SxiIIQ==

"@xhs/hulk-decorators@^0.5.13":
  version "0.5.14"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-decorators/-/@xhs/hulk-decorators-0.5.14.tgz#7b805102e3af212ed93994230945026807c5f580"
  integrity sha1-e4BRAuOvIS7ZOZQjCUUCaAfF9YA=
  dependencies:
    "@prisma/client" "^3.14.0"
    "@types/koa" "^2.13.4"
    "@types/koa-bodyparser" "^4.3.3"
    "@types/koa-static" "^4.0.2"
    "@types/node-fetch" "2.5.12"
    abort-controller "^3.0.0"
    ajv "^8.6.3"
    find-my-way "^4.3.3"
    fluent-json-schema "^3.0.1"
    klaw-sync "^6.0.0"
    koa "^2.13.3"
    koa-bodyparser "^4.3.0"
    koa-compose "^4.1.0"
    koa-static "^5.0.0"
    lodash.merge "^4.6.2"
    node-fetch "^2.6.1"
    prom-client "^14.0.0"
    reflect-metadata "^0.1.13"

"@xhs/hulk-decorators@~1.7.2":
  version "1.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-decorators/-/@xhs/hulk-decorators-1.7.2.tgz#5346e6e655223a4a51d6ea93f46c3e2550c5e0c9"
  integrity sha512-8R2KAF7Yhxy4Xaa6FHD+hFkEf/7+BIMFWJdewrVPj+71vs7xxl04SPXm0WnDDIUoTKiM8ls5vJPyoTaCtGx/uQ==
  dependencies:
    "@types/formidable" "~3.4.5"
    "@types/koa" "~2.13.6"
    "@types/koa-bodyparser" "~4.3.7"
    "@types/koa-compose" "~3.2.5"
    "@types/koa-static" "~4.0.2"
    ajv "~8.12.0"
    find-my-way "~8.1.0"
    fluent-json-schema "~4.2.1"
    formidable "~3.5.1"
    klaw-sync "~6.0.0"
    koa "~2.14.1"
    koa-bodyparser "~4.4.0"
    koa-compose "~4.1.0"
    koa-static "~5.0.0"
    lodash.merge "~4.6.2"
    reflect-metadata "~0.1.13"

"@xhs/hulk-env@~1.0.3":
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-env/-/@xhs/hulk-env-1.0.3.tgz#a98ac25338b2d89252a2257b3a56e39401fc7e4b"
  integrity sha512-yO4msV9RZnbLKY1ddbnvrBQnRkaQWP+7ehCvdz6h7vyEhNRY8EPrq3jipG85VDJR9t3pikgFhNjDaIjUYnxXrA==

"@xhs/hulk-etag-middleware@~1.2.1":
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-etag-middleware/-/@xhs/hulk-etag-middleware-1.2.1.tgz#1d296b04553e3570126d3b64ee92f867438e995a"
  integrity sha512-L3/05VK2RflLKDuVyMxKVHnSlgttQ1tSTmOS5P9IhnsUuQQxVDPC+m/OZVSXFwbxs56JGE2um1B2ZivUeW7h2Q==

"@xhs/hulk-http-service@~2.2.3":
  version "2.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-http-service/-/@xhs/hulk-http-service-2.2.3.tgz#1ccb0831238b481164aab0ab1b8833fe8d940dd3"
  integrity sha512-2mrCiOGlkU19eIUxH15E0Vg6XwyeuGNDOQCho/OXhZfJOxKfbzlqItNwrUWD9asxDJmpaJviPc+JKlw3c534Fg==
  dependencies:
    "@types/node-fetch" "2.5.12"
    abort-controller "~3.0.0"
    node-fetch "~2.6.9"

"@xhs/hulk-logger-service@~1.1.1":
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-logger-service/-/@xhs/hulk-logger-service-1.1.1.tgz#8451a78829dcb316a0e403bb2ea958cbe04064ed"
  integrity sha512-sUNKhny1m4d1LA/+kbR8KOzNEjFRakBzUMi5FO710MKnx78dY3fG0h1kldIcZ3O4myVz7FFIeNauDDVyKonuaQ==

"@xhs/hulk-metrics@~2.3.2":
  version "2.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-metrics/-/@xhs/hulk-metrics-2.3.2.tgz#1760c745705fb9f68a62e55d715ada62e17e3c0a"
  integrity sha512-06+xC1mkCsFwPnyJpe+O5W+nGn2h+Ez9pcVHy3EHva7syLoII3k/qMIhEweUfoZ3lvZVVaOHvm86VWbooGJrYw==
  dependencies:
    prom-client "~14.1.1"

"@xhs/hulk-rate-limit-middleware@~1.2.0":
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-rate-limit-middleware/-/@xhs/hulk-rate-limit-middleware-1.2.0.tgz#556272f91136c7394b13ec16b5ff5f0164f1ce15"
  integrity sha512-YS6NP2b29Cb+IDapIfGXY4ccpTwsvR+WotIpFJEkRjlrPuco7SjF9t3WEZy9EE9nRf4lb5HkOANSyauP71Dv1A==

"@xhs/hulk-thrift-agent@~2.4.0":
  version "2.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-thrift-agent/-/@xhs/hulk-thrift-agent-2.4.0.tgz#3a88fabf06dc61d2e867e1d8cf6b70a322adb807"
  integrity sha512-RHPxmsr8mKfxaicDJbDpKpxFxXI58tBeS1qMW/zkwGeRrNe1vyMF8b7UZKUvvdiz1I0+YykDzy/r6XERf1X9GQ==
  dependencies:
    "@types/node-int64" "~0.4.29"
    "@types/thrift" "~0.10.12"
    node-int64 "~0.4.0"
    thrift "~0.18.1"

"@xhs/hulk-xds@~1.1.7":
  version "1.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk-xds/-/@xhs/hulk-xds-1.1.7.tgz#b0f622b7bde0dc7cc518c0fb3459322b73cc1657"
  integrity sha512-Y5QZGCKk8eh5a/Kz77Dxqe0gFaRYzRlC85SFGEwyGZvXm9LoIqnxOy9R4uYJLckgSyhDy5ujp7guB4AMhwA6ew==
  dependencies:
    "@bufbuild/connect" "~0.8.5"
    "@bufbuild/connect-node" "~0.8.5"
    "@bufbuild/protobuf" "~1.2.0"
    "@bufbuild/protoc-gen-connect-es" "~0.8.5"
    "@bufbuild/protoc-gen-es" "~1.2.0"

"@xhs/hulk@~2.4.27":
  version "2.4.37"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/hulk/-/@xhs/hulk-2.4.37.tgz#e90126b41ee16a54b5ebe818c372248f7a65ac3c"
  integrity sha512-Gt3nnnvUhZqc6jYTNBa7p4bOAyEdM5ZIBOBmqHZa6Kq7Nn9MkdZDIxol6DLJ+NtKjnCtdnLE5Cu1Lc473TrXrQ==
  dependencies:
    "@xhs/hulk-apm-logger-service" "~1.2.1"
    "@xhs/hulk-decorators" "~1.7.2"
    "@xhs/hulk-env" "~1.0.3"
    "@xhs/hulk-etag-middleware" "~1.2.1"
    "@xhs/hulk-http-service" "~2.2.3"
    "@xhs/hulk-logger-service" "~1.1.1"
    "@xhs/hulk-metrics" "~2.3.2"
    "@xhs/hulk-rate-limit-middleware" "~1.2.0"
    "@xhs/hulk-thrift-agent" "~2.4.0"
    "@xhs/hulk-xds" "~1.1.7"

"@xhs/launcher-ark@1.2.16":
  version "1.2.16"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-ark/-/@xhs/launcher-ark-1.2.16.tgz#f7154041dc48df270b29e21e136cc95e90883df0"
  integrity sha512-yhh74Bog0bPcuxKGIQtx6KeKBNIHkexJ3zoTpUnr6RmWfuGsW+AtVWErOfEUTrRxseSBDu+4jmaj+vCgi6lk5w==
  dependencies:
    "@xhs/launcher" "3.6.1"
    "@xhs/launcher-plugin-anti-spam" "2.2.0"
    "@xhs/launcher-plugin-auth" "2.0.0-2"
    "@xhs/launcher-plugin-eaglet" "5.4.4"
    "@xhs/launcher-plugin-goods3" "1.0.26"
    "@xhs/launcher-plugin-store" "2.0.0"
    "@xhs/launcher-plugin-tracker" "0.0.21-6"
    "@xhs/launcher-plugin-webvitals-collector" "0.0.14"
    "@xhs/launcher-plugin-worker" "3.6.4"
    "@xhs/performance-plugin-eaglet" "1.0.1"
    js-cookie "3.0.1"
    url-parse "1.5.10"

"@xhs/launcher-legacy@~1.0.1":
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-legacy/-/@xhs/launcher-legacy-1.0.1.tgz#4afb0918b3a9d968a031df02d9994348273806e9"
  integrity sha512-J/Isi23fVVNmp7E74pyu8JKsW/5ofCV7/wb5Npb0P0dbVhG+Ma23OVnTDvkxizA35LaFuVZCM4b4+a/DFgM0VA==
  dependencies:
    "@xhs/fe-api-sign" "~0.2.0"
    "@xhs/logger" "3.x"

"@xhs/launcher-plugin-anti-spam@2.2.0":
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-anti-spam/-/@xhs/launcher-plugin-anti-spam-2.2.0.tgz#b032ffa7ef525870b5327beb6f1e372546052b7e"
  integrity sha1-sDL/p+9SWHC1Mnvrbx43JUYFK34=
  dependencies:
    "@xhs/ozone-bridge" "^2.27.1"
    "@xhs/ozone-detector" "^3.4.4"
    js-cookie "^3.0.1"
    lodash.throttle "^4.1.1"

"@xhs/launcher-plugin-anti-spam@^3.6.8":
  version "3.7.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-anti-spam/-/@xhs/launcher-plugin-anti-spam-3.7.7.tgz#4920cb3c052b5eb606ed6ae6254d6521e39aa3f6"
  integrity sha512-cbFAWQbbh4nb+KvGF7fwZJS2ZqdqwOr8kRU/s8MJ9e82oboNJaU0+u/aqgSiFVTHJh9wYfvN6QAvQvio3gWONQ==
  dependencies:
    js-cookie "^3.0.1"
    lodash.throttle "^4.1.1"

"@xhs/launcher-plugin-auth@2.0.0-2":
  version "2.0.0-2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-auth/-/@xhs/launcher-plugin-auth-2.0.0-2.tgz#8791fc95ad03c838455022adba83cd1a278de582"
  integrity sha1-h5H8la0DyDhFUCKtuoPNGieN5YI=
  dependencies:
    "@xhs/logger" "^2.2.2"
    "@xhs/ozone-bridge" "^2.27.1"
    "@xhs/ozone-detector" "^3.4.4"

"@xhs/launcher-plugin-eaglet@5.4.4":
  version "5.4.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-eaglet/-/@xhs/launcher-plugin-eaglet-5.4.4.tgz#3bccb2a77d22f4732e8e418816e443f814ac932a"
  integrity sha1-O8yyp30i9HMujkGIFuRD+BSskyo=
  dependencies:
    "@xhs/abtest" "^0.6.2"
    "@xhs/eaglet" "1.1.1-1"
    "@xhs/eaglet-emitter-base" "^0.1.3"
    "@xhs/launcher-plugin-eaglet" "^5.3.17-5"
    "@xhs/ozone-bridge" "^4.0.3"
    "@xhs/ozone-schema" "^1.131.0"
    "@xhs/perf-metrics" "1.10.3-23"
    google-protobuf "^3.17.3"
    lodash.merge "^4.6.2"
    lodash.uniqueid "^4.0.1"
    uuid "^8.3.2"

"@xhs/launcher-plugin-eaglet@^5.3.17-5":
  version "5.6.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-eaglet/-/@xhs/launcher-plugin-eaglet-5.6.5.tgz#bfc230efc6c037a83070660af7151ddb28196444"
  integrity sha512-O4pf7q+cz7G5d+Rm7h2Po0MWI2gFbhkjoEy2WwczEGVLgJ0f1ddfwU727R3D0SRUjesEuca+6439nmm+tla7Kw==
  dependencies:
    "@xhs/abtest" "^0.7.0"
    "@xhs/eaglet" "1.1.1-1"
    "@xhs/eaglet-emitter-base" "^0.1.3"
    "@xhs/ozone-bridge" "^4.0.3"
    "@xhs/ozone-schema" "^1.141.0"
    "@xhs/perf-metrics" "1.10.3-23"
    google-protobuf "^3.17.3"
    lodash.merge "^4.6.2"
    lodash.uniqueid "^4.0.1"
    uuid "^8.3.2"

"@xhs/launcher-plugin-goods3@1.0.26":
  version "1.0.26"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-goods3/-/@xhs/launcher-plugin-goods3-1.0.26.tgz#e1a0e2c01fc4bd7a1a76352d5bfdb12c45ec784d"
  integrity sha1-4aDiwB/EvXoadjUtW/2xLEXseE0=

"@xhs/launcher-plugin-store@2.0.0":
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-store/-/@xhs/launcher-plugin-store-2.0.0.tgz#f55b2fdf35ae590879b102c494543e035daf8bb5"
  integrity sha1-9Vsv3zWuWQh5sQLElFQ+A12vi7U=
  dependencies:
    vuex "^4.0.0"

"@xhs/launcher-plugin-tracker@0.0.21-6":
  version "0.0.21-6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-tracker/-/@xhs/launcher-plugin-tracker-0.0.21-6.tgz#776fe4c86f23ce7b973c8345bc6db94227b41789"
  integrity sha512-F73OcKZ5aLG82R/MIybR2d3Nwr2fP4E0I4ruKv4xqCh4hfEhf7HJZEmT9OuQndD47WSVJUsFazp30A3Qt5ZegA==
  dependencies:
    "@xhs/ozone-schema" "^1.15.0"
    axios "^1.1.3"
    lodash.uniqueid "^4.0.1"
    ua-parser-js "^1.0.35"
    uuid "^8.3.2"

"@xhs/launcher-plugin-webvitals-collector@0.0.14":
  version "0.0.14"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-webvitals-collector/-/@xhs/launcher-plugin-webvitals-collector-0.0.14.tgz#7b96d879184184336eed3541e3c58fe91f53a61b"
  integrity sha1-e5bYeRhBhDNu7TVB48WP6R9Tphs=
  dependencies:
    "@xhs/perf-metrics" "^1.10.4-23"

"@xhs/launcher-plugin-worker@3.6.4":
  version "3.6.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher-plugin-worker/-/@xhs/launcher-plugin-worker-3.6.4.tgz#9becd9b2686c09038b1a3696ea2c4ef7a110d360"
  integrity sha1-m+zZsmhsCQOLGjaW6ixO96EQ02A=
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    axios "^0.26.1"
    ua-parser-js "^0.7.23"
    uuid "^8.3.2"

"@xhs/launcher@3.6.1":
  version "3.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher/-/@xhs/launcher-3.6.1.tgz#600cf613a88ea030c5d5a646286002ac9ad7d554"
  integrity sha1-YAz2E6iOoDDF1aZGKGACrJrX1VQ=
  dependencies:
    "@vue/compat" "^3.2.31"
    "@vue/compiler-sfc" "^3.2.31"
    "@vueuse/head" "^0.7.5"
    "@xhs/fe-api-sign" "^0.2.0"
    "@xhs/http" "^1.10.1"
    "@xhs/hulk-decorators" "^0.5.13"
    "@xhs/logger" "^2.6.1"
    "@xhs/ozone-bridge" "^3.18.0"
    escape-html "^1.0.3"
    serialize-javascript "^6.0.0"
    vue "^3.2.22"
    vue-router "^4.0.14"

"@xhs/launcher@^3.24.0":
  version "3.25.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/launcher/-/@xhs/launcher-3.25.7.tgz#fe67a94287bd9a252efb20409b2a66bbc8e83a0e"
  integrity sha512-enNAeEEO+YPxYsbdq5IYQ7QX75NQAns47zdI6MA7pLT9wO2ROBUhWWhzvZ3pTVu4idoh6f0gnua8vPRGtjJTiw==
  dependencies:
    "@types/node" "18.x"
    "@types/serialize-javascript" "~5.0.2"
    "@unhead/dom" "~1.6.2"
    "@unhead/schema" "~1.6.2"
    "@unhead/ssr" "~1.6.2"
    "@unhead/vue" "~1.6.2"
    "@xhs/http" ">=1.13"
    "@xhs/hulk" "~2.4.27"
    "@xhs/launcher-legacy" "~1.0.1"
    "@xhs/logger" "~3.0.1"
    "@xhs/ozone-bridge" ">=3"
    axios "0.x"
    fast-json-stable-stringify "~2.1.0"
    serialize-javascript "~6.0.1"
    vue "3.x"
    vue-router "4.x"

"@xhs/logger@3.x", "@xhs/logger@^3.0.1":
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logger/-/@xhs/logger-3.1.0.tgz#5e470b96613be89f99361e86b1424969f549c377"
  integrity sha512-+F5+0goFTgZc/k+hqOdsigWO9KifBxCEOkKGsMqPsp8nlOUV2IAueOAUrk9Y3+ThE3Zv2j7dfApYurKHidm2cg==

"@xhs/logger@^2.2.2", "@xhs/logger@^2.5.1", "@xhs/logger@^2.6.1", "@xhs/logger@^2.6.2":
  version "2.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logger/-/@xhs/logger-2.6.2.tgz#8c1d23b777785f169e1c6c934c42501cf81efb56"
  integrity sha1-jB0jt3d4XxaeHGyTTEJQHPge+1Y=

"@xhs/logger@~3.0.1":
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/logger/-/@xhs/logger-3.0.1.tgz#7a8e7504b45c3ba7d8deb89ba55c581d7f7b3dd7"
  integrity sha1-eo51BLRcO6fY3ribpVxYHX97Pdc=

"@xhs/meepo-delight-chart@1.1.0":
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/meepo-delight-chart/-/@xhs/meepo-delight-chart-1.1.0.tgz#2e621278d3f18884552012b547cc23c0b66d6316"
  integrity sha1-LmISeNPxiIRVIBK1R8wjwLZtYxY=
  dependencies:
    echarts "^5.2.2"

"@xhs/modular-core@^3.25.0", "@xhs/modular-core@^3.3.0":
  version "3.25.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/modular-core/-/@xhs/modular-core-3.25.0.tgz#11c52dbe434733c6095b1070a34e7fe27fcf9591"
  integrity sha512-/AjPpGehCLGi93MP+bBIZC4IqEk8tbkhwYrRKRBKcF0WJL7OUn7LE1rFTHIP1peheJyQv4yWjZUHcg0wMtCxkQ==
  dependencies:
    "@xhs/modular-rpc" "^1.26.0"
    express "^4.8.5"
    open "^7.0.4"

"@xhs/modular-mock@^1.3.0":
  version "1.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/modular-mock/-/@xhs/modular-mock-1.26.0.tgz#425f273c4e9957560e52ed411fd4ac965dfac04a"
  integrity sha512-5d2hKZZnCxC5+nmLm+xK3bKm6kw4t8+45fqS8DNs8aTntEMpGIs+CJWLGslOZuU7r+9lQ1YWfAZupqS/FKmUvg==
  dependencies:
    "@xhs/modular-core" "^3.25.0"
    jsonlint "^1.6.3"
    nanoid "3.3.4"
    path-to-regexp "^6.2.1"

"@xhs/modular-proxy-core@^1.8.0":
  version "1.8.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/modular-proxy-core/-/@xhs/modular-proxy-core-1.8.0.tgz#0af1daed54b11abbb296db07f745a3f849c4f589"
  integrity sha512-BeiaNp/R3ux3iB8mxw7/qzy6WhYOKLufhvEk/kCBTmbyVYw5jeHwl2H/E7YYuLv/XyWrYb6S+RKqH7YbFREhjA==
  dependencies:
    async "~0.9.0"
    async-task-mgr ">=1.1.0"
    body-parser "^1.13.1"
    brotli "^1.3.2"
    classnames "^2.2.5"
    clipboard-js "^0.3.3"
    co "^4.6.0"
    colorful "^2.1.0"
    commander "~2.11.0"
    component-emitter "^1.2.1"
    compression "^1.4.4"
    es6-promise "^3.3.1"
    express "^4.8.5"
    fast-json-stringify "^0.17.0"
    iconv-lite "^0.4.6"
    inquirer "^5.2.0"
    ip "^0.3.2"
    juicer "^0.6.6-stable"
    mime-types "2.1.11"
    moment "^2.15.1"
    nedb "^1.8.0"
    node-easy-cert "^1.0.0"
    pug "^2.0.0-beta6"
    qrcode-npm "0.0.3"
    request "^2.74.0"
    stream-throttle "^0.1.3"
    svg-inline-react "^1.0.2"
    thunkify "^2.1.2"
    whatwg-fetch "^1.0.0"
    ws "^5.1.0"

"@xhs/modular-proxy@^1.4.0":
  version "1.27.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/modular-proxy/-/@xhs/modular-proxy-1.27.0.tgz#22696f0db2d11c5e6aea0101258b83544b5e0965"
  integrity sha512-w8O9EcvQM1WFaUFQkjPUBJ5qJNN3HYJUoC5nHRrBjy5ia5mzQO4H5rpfRBiatPNBaxLk4sB9SjqF8yUMuoFjNw==
  dependencies:
    "@xhs/modular-core" "^3.25.0"
    "@xhs/modular-proxy-core" "^1.8.0"
    "@xhs/modular-rpc" "^1.26.0"
    url-parse "^1.5.10"

"@xhs/modular-rpc@^1.26.0", "@xhs/modular-rpc@^1.3.0":
  version "1.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/modular-rpc/-/@xhs/modular-rpc-1.26.0.tgz#60baea1fcb3b9a9493cf129d782136870cafc44b"
  integrity sha512-N9K36TbbMPfgwnbcQnxiokLP8dpUvnUFKG1hV50E2SPyHm4nZKEKJjfAUwMOy7OAOaJ/124E254yKHhenKI1lg==
  dependencies:
    web-events "npm:events@^3.3.0"
    ws "7.4.0"

"@xhs/modular-startup@4.2.0":
  version "4.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/modular-startup/-/@xhs/modular-startup-4.2.0.tgz#596aa81ffcf2c0d05b0bcc0214551a9155cf200e"
  integrity sha512-KRfKWyuG3jKy3MCGZQ/833+5dasvOvjPtqxkDqmfqqGEhu3aeKM0ClFdRxpAz0krwtE/i28G01go1vhO7gscXQ==
  dependencies:
    "@types/detect-port" "^1.3.5"
    "@xhs/di" "0.1.0-beta.1"
    "@xhs/modular-core" "^3.3.0"
    "@xhs/modular-mock" "^1.3.0"
    "@xhs/modular-proxy" "^1.4.0"
    "@xhs/modular-rpc" "^1.3.0"
    chokidar "^3.5.3"
    commander "~2.11.0"
    detect-port "^1.5.1"
    fs-extra "^11.2.0"

"@xhs/module-reactive@^0.1.0":
  version "0.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/module-reactive/-/@xhs/module-reactive-0.1.0.tgz#d64dab7198e339ef9a8462e3b36b1c6170769d37"
  integrity sha512-RdX8uIfq7ox7xRYJbD1mlqr4me0OwbpSO/oIb1pddf/dH01WO1OdcSkcfGfgHW41gaygzlPusI8BwGE+x6Ef2A==

"@xhs/ozone-bridge@3.18.0":
  version "3.18.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-3.18.0.tgz#fd5c2985b57ae60fdba847a27042a0835a72e09e"
  integrity sha1-/VwphbV65g/bqEeicEKgg1py4J4=
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.2.2"
    "@xhs/ozone-detector" "^3.4.4"
    "@xhs/ozone-schema" "^0.33.0"
    prop-types "^15.7.2"
    shared "^1.8.5"
    uuid "^7.0.2"

"@xhs/ozone-bridge@>=3", "@xhs/ozone-bridge@^4.0.3":
  version "4.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-4.0.4.tgz#6f0dbb70fb7ce5701c5a57d2716ff4caf90fc150"
  integrity sha1-bw27cPt85XAcWlfScW/0yvkPwVA=
  dependencies:
    "@xhs/bridge-shared" "^1.1.0"
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.6.2"
    "@xhs/ozone-detector" "^3.4.4"
    "@xhs/ozone-schema" "^1.131.0"
    prop-types "^15.7.2"
    uuid "^8.3.2"

"@xhs/ozone-bridge@^2.17.6", "@xhs/ozone-bridge@^2.27.1":
  version "2.37.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-2.37.0.tgz#3f03d27bececd47de175f196cb416e51ac0b7f42"
  integrity sha1-PwPSe+zs1H3hdfGWy0FuUawLf0I=
  dependencies:
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.2.2"
    "@xhs/ozone-detector" "^3.4.4"
    prop-types "^15.7.2"
    uuid "^7.0.2"

"@xhs/ozone-bridge@^3.18.0":
  version "3.26.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-bridge/-/@xhs/ozone-bridge-3.26.3.tgz#be8d241a0d89ebfe98abf341ada4a8d234337ae1"
  integrity sha512-biiAdAx6jkncQipy75mtBnd29j1I2Zo4ibMqsSNse8FP0KoGYgZTRFOfmXbaAmjbZqPEBUcdupCK5WwecumgPw==
  dependencies:
    "@xhs/bridge-shared" "^1.1.0"
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.2.2"
    "@xhs/ozone-detector" "^3.15.3"
    "@xhs/ozone-schema" "^1.163.0"
    prop-types "^15.7.2"
    uuid "^7.0.2"

"@xhs/ozone-detector@^3.11.8", "@xhs/ozone-detector@^3.15.3", "@xhs/ozone-detector@^3.4.4", "@xhs/ozone-detector@^3.4.5":
  version "3.15.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-detector/-/@xhs/ozone-detector-3.15.3.tgz#6480c1a37ac33755528a848d7ea52ba4c8e5a1ca"
  integrity sha512-mhb2v2c7CNwCESWz9BQZ2RrBEJkEwPSk77DaGaBC9PSQBJOtzxiMmEZMXIuAT3tqhZ2ZgqqXbZWh2MZC5J3hJA==

"@xhs/ozone-schema@^0.33.0":
  version "0.33.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-schema/-/@xhs/ozone-schema-0.33.1.tgz#73c7d9d6a36bb06380d79f77afcc5a7ac2a35228"
  integrity sha1-c8fZ1qNrsGOA1593r8xaesKjUig=
  dependencies:
    "@types/uuid" "^8.3.0"
    "@xhs/logger" "^2.5.1"
    "@xhs/ozone-detector" "^3.4.5"
    jsonschema "^1.2.6"
    url "^0.11.0"
    uuid "^7.0.2"

"@xhs/ozone-schema@^1.131.0":
  version "1.216.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-schema/-/@xhs/ozone-schema-1.216.0.tgz#d00d4581d2b16655fd3c43c5c05161697ddeab05"
  integrity sha512-tpBEsfmscLawHIAp7GHRmA5Ynv3Hml3Yl2FiLDXs+u8qXetrDqWMd4leVDEj8T/uH9qE1sAOcGvR1ZJNHPGeGQ==
  dependencies:
    "@types/uuid" "^8.3.0"
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.6.2"
    "@xhs/ozone-detector" "^3.15.3"
    jsonschema "^1.2.6"
    prop-types "^15.8.1"
    url "^0.11.0"
    uuid "^8.1.0"

"@xhs/ozone-schema@^1.141.0", "@xhs/ozone-schema@^1.15.0", "@xhs/ozone-schema@^1.159.0", "@xhs/ozone-schema@^1.163.0":
  version "1.223.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/ozone-schema/-/@xhs/ozone-schema-1.223.0.tgz#9dbf08dcd5db20902245cfc00b4f57c8c704497a"
  integrity sha512-gsLNy/jK272q/bpdATjjaeduOWXemupGSyL/c6eN9B42Jf40cg/PdtOOJ7vZXIVKfzNOADliur2nh5SR0hxqUg==
  dependencies:
    "@types/uuid" "^8.3.0"
    "@xhs/data-transform" "^0.3.3"
    "@xhs/logger" "^2.6.2"
    "@xhs/ozone-detector" "^3.15.3"
    jsonschema "^1.2.6"
    prop-types "^15.8.1"
    url "^0.11.0"
    uuid "^8.1.0"

"@xhs/paint-timing@^0.3.0":
  version "0.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/paint-timing/-/@xhs/paint-timing-0.3.0.tgz#6704b742f3237941129741e73a33e611785e2013"
  integrity sha1-ZwS3QvMjeUESl0HnOjPmEXheIBM=

"@xhs/perf-metrics@1.10.3-23":
  version "1.10.3-23"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/perf-metrics/-/@xhs/perf-metrics-1.10.3-23.tgz#5b3276945991462599214f4167d0362349548bca"
  integrity sha1-WzJ2lFmRRiWZIU9BZ9A2I0lUi8o=
  dependencies:
    "@xhs/paint-timing" "^0.3.0"
    uuid "^8.3.1"

"@xhs/perf-metrics@^1.10.4-23", "@xhs/perf-metrics@^1.10.5":
  version "1.10.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/perf-metrics/-/@xhs/perf-metrics-1.10.6.tgz#175d295114aa6a4ecc1f3b2553932ed01a5f84c5"
  integrity sha512-wiirf2D8kLZ4CiNBbLu0CF6dVUv/0Y2LS0vFsATlho3s1zu1BvcaD+0cl7TDO7ERx78jCNN+lD1bl+YC4vFBTA==
  dependencies:
    "@xhs/paint-timing" "^0.3.0"
    uuid "^8.3.1"

"@xhs/performance-plugin-eaglet@1.0.1":
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/performance-plugin-eaglet/-/@xhs/performance-plugin-eaglet-1.0.1.tgz#8a8985c926f5ea79eba93e61d483b9c6b30da2ae"
  integrity sha1-iomFySb16nnrqT5h1IO5xrMNoq4=

"@xhs/untrace@^0.0.1":
  version "0.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/untrace/-/@xhs/untrace-0.0.1.tgz#4da50cf8c63e528b94ed52f8d8665cf03c6ff339"
  integrity sha1-TaUM+MY+UouU7VL42GZc8Dxv8zk=
  dependencies:
    long "^5.2.3"

"@xhs/uploader@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/uploader/-/@xhs/uploader-3.0.3.tgz#ee6750b38cfcfbab36b79985f26828accbffc6b2"
  integrity sha1-7mdQs4z8+6s2t5mF8mgorMv/xrI=
  dependencies:
    cos-js-sdk-v5 "^1.3.8"

"@xhs/uploader@^3.0.6", "@xhs/uploader@^3.0.7":
  version "3.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/uploader/-/@xhs/uploader-3.0.7.tgz#07568e5902d2bcf10330f7cd7ae081289a20ef38"
  integrity sha512-Ru9g6GvbopFVm2f2/C/S9faO3tVl7fHEUmf+ZH+AWpZruWtnMYi/wVWgw+1goE9seQYIp5tr0zuAe43rCJhgKw==
  dependencies:
    cos-js-sdk-v5 "^1.3.8"

"@xhs/vitepress-plugin-code-demo-component@^0.0.6":
  version "0.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/vitepress-plugin-code-demo-component/-/@xhs/vitepress-plugin-code-demo-component-0.0.6.tgz#48ddb37a89c7aff26ac78a307d689fdcda449a05"
  integrity sha512-/pffviPN1c8MHJXo/PwfiBdUzyTTVYnsfPKUZouTvroNAjicQ7UhTUNZPgi8cqz3Mfrwcp1S6OlzviH39D15Rg==
  dependencies:
    "@floating-ui/vue" "^1.0.2"

"@xhs/vitepress-plugin-code-demo@^0.0.9":
  version "0.0.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/vitepress-plugin-code-demo/-/@xhs/vitepress-plugin-code-demo-0.0.9.tgz#9241b765327797b7e2c4dfca48a264166326ae4d"
  integrity sha512-rAxPxHsIxsSY1p9tHPxMpfSwKy+Q1CDMp81YJ7RzL4b6zyaXW8X3okSbM0eW6pq7JlQkbbtks9UY9AlMTC2HfA==
  dependencies:
    hash-sum "^2.0.0"
    markdown-it "^13.0.2"
    markdown-it-container "^3.0.0"
    remark-frontmatter "^5.0.0"
    remark-parse "^11.0.0"
    remark-stringify "^11.0.0"
    unified "^11.0.4"

"@xhs/vue-di@^0.2.0":
  version "0.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/vue-di/-/@xhs/vue-di-0.2.1.tgz#a87fb53a3eb4bbd3c648018ec984d8b96970aa35"
  integrity sha512-jGBUu4UUPeuwaz00J8OYWrQGXn0bc7uwKNfaVfQ/Em/E/eBDxHoBvyx3X3Nwvbg3AAFfv+L2G82tPwSmpkLvCw==

"@xhs/xgplayer-mp4@2.1.30":
  version "2.1.30"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/xgplayer-mp4/-/@xhs/xgplayer-mp4-2.1.30.tgz#de4bb2c0c7abf11c2d0c6ac76609af07df153aa4"
  integrity sha1-3kuywMer8RwtDGrHZgmvB98VOqQ=
  dependencies:
    concat-typed-array "^1.0.2"
    deepmerge "^2.0.1"
    event-emitter "^0.3.5"

"@xhs/xgplayer@2.32.24":
  version "2.32.24"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xhs/xgplayer/-/@xhs/xgplayer-2.32.24.tgz#00a75919b611cccd3941312ac2efda131ce791cb"
  integrity sha1-AKdZGbYRzM05QTEqwu/aExznkcs=
  dependencies:
    chalk "^2.3.2"
    commander "^2.15.1"
    danmu.js "^0.5.9"
    downloadjs "1.4.7"
    draggabilly "^2.2.0"
    event-emitter "^0.3.5"
    fs-extra "^5.0.0"
    xgplayer-subtitles "^1.0.19"

"@xmldom/xmldom@^0.8.6":
  version "0.8.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@xmldom/xmldom/-/xmldom-0.8.10.tgz#a1337ca426aa61cef9fe15b5b28e340a72f6fa99"
  integrity sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==

"@zhead/schema-vue@^0.7.3":
  version "0.7.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@zhead/schema-vue/download/@zhead/schema-vue-0.7.4.tgz#dab303e372e4cd491eb292081c109f677d39039a"
  integrity sha1-2rMD43LkzUkespIIHBCfZ305A5o=
  dependencies:
    "@vueuse/shared" "^9.2.0"
    "@zhead/schema" "0.7.4"

"@zhead/schema@0.7.4":
  version "0.7.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/@zhead/schema/download/@zhead/schema-0.7.4.tgz#73e87110468dbe416b70df86e4cc95fdec4978ee"
  integrity sha1-c+hxEEaNvkFrcN+G5MyV/exJeO4=

JSV@^4.0.x:
  version "4.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/JSV/download/JSV-4.0.2.tgz#d077f6825571f82132f9dffaed587b4029feff57"
  integrity sha1-0Hf2glVx+CEy+d/67Vh7QCn+/1c=

abort-controller@^3.0.0, abort-controller@~3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/abort-controller/download/abort-controller-3.0.0.tgz#eaf54d53b62bae4138e809ca225c8439a6efb392"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

accepts@^1.3.5, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-globals@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/acorn-globals/download/acorn-globals-3.1.0.tgz#fd8270f71fbb4996b004fa880ee5d46573a731bf"
  integrity sha1-/YJw9x+7SZawBPqIDuXUZXOnMb8=
  dependencies:
    acorn "^4.0.4"

acorn-import-assertions@^1.9.0:
  version "1.9.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/acorn-import-assertions/download/acorn-import-assertions-1.9.0.tgz#507276249d684797c84e0734ef84860334cfb1ac"
  integrity sha1-UHJ2JJ1oR5fITgc074SGAzTPsaw=

acorn@^3.1.0:
  version "3.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/acorn/download/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^4.0.4, acorn@~4.0.2:
  version "4.0.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/acorn/download/acorn-4.0.13.tgz#105495ae5361d697bd195c825192e1ad7f253787"
  integrity sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c=

acorn@^8.11.3:
  version "8.12.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/acorn/-/acorn-8.12.1.tgz#71616bdccbe25e27a54439e0046e89ca76df2248"
  integrity sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==

address@^1.0.1:
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/address/download/address-1.2.2.tgz#2b5248dac5485a6390532c6a517fda2e3faac89e"
  integrity sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=

adm-zip@^0.5.5:
  version "0.5.14"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/adm-zip/-/adm-zip-0.5.14.tgz#2c557c0bf12af4311cf6d32970f4060cf8133b2a"
  integrity sha512-DnyqqifT4Jrcvb8USYjp6FHtBpEIz1mnXu6pTRHZ0RL69LbQYiO+0lDFg5+OKA7U29oWSs3a/i8fhn8ZcceIWg==

ajv-formats@^1.5.1:
  version "1.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ajv-formats/download/ajv-formats-1.6.1.tgz#35c7cdcd2a12d509171c37bac32f2e8eb010a536"
  integrity sha1-NcfNzSoS1QkXHDe6wy8ujrAQpTY=
  dependencies:
    ajv "^7.0.0"

ajv@^6.0.0, ajv@^6.12.3:
  version "6.12.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^7.0.0, ajv@^7.0.3:
  version "7.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ajv/download/ajv-7.2.4.tgz#8e239d4d56cf884bccca8cca362f508446dc160f"
  integrity sha1-jiOdTVbPiEvMyozKNi9QhEbcFg8=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ajv@^8.6.3:
  version "8.17.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ajv/-/ajv-8.17.1.tgz#37d9a5c776af6bc92d7f4f9510eba4c0a60d11a6"
  integrity sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ajv@~8.12.0:
  version "8.12.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ajv/download/ajv-8.12.0.tgz#d1a0527323e22f53562c567c00991577dfbe19d1"
  integrity sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

algoliasearch@^4.19.1:
  version "4.24.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/algoliasearch/-/algoliasearch-4.24.0.tgz#b953b3e2309ef8f25da9de311b95b994ac918275"
  integrity sha512-bf0QV/9jVejssFBmz2HQLxUadxk574t4iwjCKp5E7NBzwKkrDEhKPISIIjAU/p6K5qDx3qoeh4+26zWN1jmw3g==
  dependencies:
    "@algolia/cache-browser-local-storage" "4.24.0"
    "@algolia/cache-common" "4.24.0"
    "@algolia/cache-in-memory" "4.24.0"
    "@algolia/client-account" "4.24.0"
    "@algolia/client-analytics" "4.24.0"
    "@algolia/client-common" "4.24.0"
    "@algolia/client-personalization" "4.24.0"
    "@algolia/client-search" "4.24.0"
    "@algolia/logger-common" "4.24.0"
    "@algolia/logger-console" "4.24.0"
    "@algolia/recommend" "4.24.0"
    "@algolia/requester-browser-xhr" "4.24.0"
    "@algolia/requester-common" "4.24.0"
    "@algolia/requester-node-http" "4.24.0"
    "@algolia/transporter" "4.24.0"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/align-text/download/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"
  integrity sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-5.2.0.tgz#07449690ad45777d1924ac2abb2fc8895dba836b"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ansi-styles/download/ansi-styles-1.0.0.tgz#cb102df1c56f5123eab8b67cd7b98027a0279178"
  integrity sha1-yxAt8cVvUSPquLZ817mAJ6AnkXg=

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

asap@^2.0.0, asap@~2.0.3:
  version "2.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1.js@^4.10.1:
  version "4.10.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/asn1.js/download/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  integrity sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/assert/-/assert-2.1.0.tgz#6d92a238d05dc02e7427c881fb8be81c8448b2dd"
  integrity sha512-eLHpSK/Y4nhMJ07gDaAzoX/XAKS8PSaojml3M0DM4JpV1LAi5JOJ/p6H/XWrl8L+DzVEvVCW1z3vWAaB9oTsQw==
  dependencies:
    call-bind "^1.0.2"
    is-nan "^1.3.2"
    object-is "^1.1.5"
    object.assign "^4.1.4"
    util "^0.12.5"

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-task-mgr@>=1.1.0, async-task-mgr@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async-task-mgr/download/async-task-mgr-1.1.0.tgz#a82d11aef631ae567b5d76b1e83a999a4a34aaf6"
  integrity sha1-qC0RrvYxrlZ7XXax6DqZmko0qvY=

async-validator@^4.2.5:
  version "4.2.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async-validator/download/async-validator-4.2.5.tgz#c96ea3332a521699d0afaaceed510a54656c6339"
  integrity sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=

async@0.2.10:
  version "0.2.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async/download/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"
  integrity sha1-trvgsGdLnXGXCMo43owjfLUmw9E=

async@~0.9.0:
  version "0.9.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/async/download/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atomically@^1.7.0:
  version "1.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/atomically/download/atomically-1.7.0.tgz#c07a0458432ea6dbc9a3506fffa424b48bccaafe"
  integrity sha1-wHoEWEMuptvJo1Bv/6QktIvMqv4=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.13.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/aws4/-/aws4-1.13.0.tgz#d9b802e9bb9c248d7be5f7f5ef178dc3684e9dcc"
  integrity sha512-3AungXC4I8kKsS9PuS4JH2nc+0bVY/mjgrephHTIi8fpEeGsTHBUJeosp0Wc1myYMElmD0B3Oc4XL/HVJ4PV2g==

axios@0.x:
  version "0.28.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/axios/-/axios-0.28.1.tgz#2a7bcd34a3837b71ee1a5ca3762214b86b703e70"
  integrity sha512-iUcGA5a7p0mVb4Gm/sy+FSECNkPFT4y7wt6OM/CDpO/OnNCvSs3PoMG8ibrC9jRoGYU0gUK5pXVC4NPXq6lHRQ==
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@1.5.0:
  version "1.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/axios/-/axios-1.5.0.tgz#f02e4af823e2e46a9768cfc74691fdd0517ea267"
  integrity sha512-D4DdjDo5CY50Qms0qGQTTw6Q44jl7zRwY7bthds06pUGfChBCTcQs+N743eFWGEd6pRTMd6A+I87aWyFV5wiZQ==
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@^0.19.2:
  version "0.19.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axios@^0.26.1:
  version "0.26.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/axios/download/axios-0.26.1.tgz#1ede41c51fcf51bbbd6fd43669caaa4f0495aaa9"
  integrity sha1-Ht5BxR/PUbu9b9Q2acqqTwSVqqk=
  dependencies:
    follow-redirects "^1.14.8"

axios@^1.1.3:
  version "1.7.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/axios/-/axios-1.7.3.tgz#a1125f2faf702bc8e8f2104ec3a76fab40257d85"
  integrity sha512-Ar7ND9pU99eJ9GpoGQKhKf58GpUOgnzuaB7ueNQ5BMi0p+LZ5oaEnfF999fAArcTIBwXTCHAmGcHOZJaWPq9Nw==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-code-frame/download/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz#cce4517ada356f4220bcae8a02c2b346f9a56664"
  integrity sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz#ece6aacddc76e41c3461f88bfc575bd0daa2df8d"
  integrity sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz#a5f56dab41a25f97ecb498c7ebaca9819f95be5f"
  integrity sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz#f25b82cf7dc10433c55f70592d5746400ac22caa"
  integrity sha1-8luCz33BBDPFX3BZLVdGQArCLKo=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  integrity sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  integrity sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz#1ecb27689c9d25513eadbc9914a73f5408be7a76"
  integrity sha1-HssnaJydJVE+rbyZFKc/VAi+enY=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  integrity sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz#325c59f902f82f24b74faceed0363954f6495e72"
  integrity sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz#5ec581827ad723fecdd381f1c928390676e4551b"
  integrity sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  integrity sha1-v22/5Dk40XNpohPKiov3S2qQqxo=
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-jest@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-jest/-/babel-jest-29.7.0.tgz#f4369919225b684c56085998ac63dbd05be020d5"
  integrity sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==
  dependencies:
    "@jest/transform" "^29.7.0"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.6.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-messages/download/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  integrity sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz#fa88ec59232fd9b4e36dbbc540a8ec9a9b47da73"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^29.6.3:
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz#aadbe943464182a8922c3c927c3067ff40d24626"
  integrity sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"
  integrity sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU=

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"
  integrity sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4=

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"
  integrity sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM=

babel-plugin-transform-async-to-generator@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz#6536e378aff6cb1d5517ac0e40eb3e9fc8d08761"
  integrity sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz#452692cb711d5f79dc7f85e440ce41b9f244d221"
  integrity sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz#bbc51b49f964d70cb8d8e0b94e820246ce3a6141"
  integrity sha1-u8UbSflk1wy42OC5ToICRs46YUE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz#d70f5299c1308d05c12f463813b0a09e73b1895f"
  integrity sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.23.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  integrity sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz#6fe2a8d16895d5634f4cd999b6d3480a308159b3"
  integrity sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.23.0:
  version "6.23.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz#997bb1f1ab967f682d2b0876fe358d60e765c56d"
  integrity sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz#73eb3d310ca969e3ef9ec91c53741a6f1576423e"
  integrity sha1-c+s9MQypaePvnskcU3QabxV2Qj4=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.23.0:
  version "6.23.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz#f47c95b2b613df1d3ecc2fdb7573623c75248691"
  integrity sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz#834c89853bc36b1af0f3a4c5dbaa94fd8eacaa8b"
  integrity sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz#4f54a02d6cd66cf915280019a31d31925377ca2e"
  integrity sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz#3b3e54017239842d6d19c3011c4bd2f00a00d154"
  integrity sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1:
  version "6.26.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz#58a793863a9e7ca870bdc5a881117ffac27db6f3"
  integrity sha1-WKeThjqefKhwvcWogRF/+sJ9tvM=
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz#ff89a142b9119a906195f5f106ecf305d9407d23"
  integrity sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz#ac997e6285cd18ed6176adb607d602344ad38468"
  integrity sha1-rJl+YoXNGO1hdq22B9YCNErThGg=
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz#24cef69ae21cb83a7f8603dad021f572eb278f8d"
  integrity sha1-JM72muIcuDp/hgPa0CH1cusnj40=
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.23.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz#57ac351ab49caf14a97cd13b09f66fdf0a625f2b"
  integrity sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz#24f875d6721c87661bbd99a4622e51f14de38aa0"
  integrity sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  integrity sha1-1taKmfia7cRTbIGlQujdnxdG+NE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz#00c1cdb1aca71112cdf0cf6126c2ed6b457ccdbc"
  integrity sha1-AMHNsaynERLN8M9hJsLta0V8zbw=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz#a84b3450f7e9f8f1f6839d6d687da84bb1236d8d"
  integrity sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz#dec09f1cddff94b52ac73d505c84df59dcceb372"
  integrity sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz#d38b12f42ea7323f729387f18a7c5ae1faeb35e9"
  integrity sha1-04sS9C6nMj9yk4fxinxa4frrNek=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.22.0:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz#2ab0c9c7f3098fa48907772bb813fe41e8de3a0e"
  integrity sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@^6.22.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz#e0703696fbde27f0a3efcacf8b4dca2f7b3a8f2f"
  integrity sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz#d5faf7aa578a65bbe591cf5edae04a0c67020758"
  integrity sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz#b4399239b89b2a011f9ddbe3e4f401fc40cff73b"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-env@^1.7.0:
  version "1.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-preset-env/download/babel-preset-env-1.7.0.tgz#dea79fa4ebeb883cd35dab07e260c1c9c04df77a"
  integrity sha1-3qefpOvriDzTXasH4mDBycBN93o=
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^3.2.6"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-preset-jest@^29.6.3:
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz#fa05fa510e7d493896d7b0dd2033601c840f171c"
  integrity sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==
  dependencies:
    babel-plugin-jest-hoist "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"

babel-runtime@^6.18.0, babel-runtime@^6.22.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-template/download/babel-template-6.26.0.tgz#de03e2d16396b069f46dd9fff8521fb1a0e35e02"
  integrity sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-traverse/download/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.19.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babel-types/download/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.18.0:
  version "6.18.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/babylon/download/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

bail@^2.0.0:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bail/download/bail-2.0.2.tgz#d26f5cd8fe5d6f832a31517b9f7c356040ba6d5d"
  integrity sha1-0m9c2P5db4MqMVF7n3w1YEC6bV0=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/base64-arraybuffer/download/base64-arraybuffer-1.0.2.tgz#1c37589a7c4b0746e34bd1feb951da2df01c1bdc"
  integrity sha1-HDdYmnxLB0bjS9H+uVHaLfAcG9w=

base64-js@^1.1.2, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

big.js@^6.2.1:
  version "6.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/big.js/download/big.js-6.2.1.tgz#7205ce763efb17c2e41f26f121c420c6a7c2744f"
  integrity sha1-cgXOdj77F8LkHybxIcQgxqfCdE8=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/binary-extensions/-/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

binary-search-tree@0.2.5:
  version "0.2.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/binary-search-tree/download/binary-search-tree-0.2.5.tgz#7dbb3b210fdca082450dad2334c304af39bdc784"
  integrity sha1-fbs7IQ/coIJFDa0jNMMErzm9x4Q=
  dependencies:
    underscore "~1.4.4"

bintrees@1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bintrees/download/bintrees-1.0.2.tgz#49f896d6e858a4a499df85c38fb399b9aff840f8"
  integrity sha1-SfiW1uhYpKSZ34XDj7OZua/4QPg=

birpc@^0.2.17:
  version "0.2.17"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/birpc/-/birpc-0.2.17.tgz#d0bdb90d4d063061156637f03b7b0adea1779734"
  integrity sha512-+hkTxhot+dWsLpp3gia5AkVHIsKlZybNT5gIYiDlNzJrmYPcTM9k5/w2uaj3IPpd7LlEYpmCj4Jj1nC41VhDFg==

bluebird@^3.5.1:
  version "3.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bn.js/download/bn.js-4.12.0.tgz#775b3f278efbb9718eec7361f483fb36fbbfea88"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^5.0.0, bn.js@^5.2.1:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bn.js/download/bn.js-5.2.1.tgz#0bc527a6a0d18d0aa8d5b0538ce4a77dccfa7b70"
  integrity sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A=

body-parser@1.20.2, body-parser@^1.13.1:
  version "1.20.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/body-parser/download/body-parser-1.20.2.tgz#6feb0e21c4724d06de7ff38da36dad4f57a747fd"
  integrity sha1-b+sOIcRyTQbef/ONo22tT1enR/0=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/braces/-/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

brotli@^1.3.2:
  version "1.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/brotli/download/brotli-1.3.3.tgz#7365d8cc00f12cf765d2b2c898716bcf4b604d48"
  integrity sha1-c2XYzADxLPdl0rLImHFrz0tgTUg=
  dependencies:
    base64-js "^1.1.2"

browser-md5-file@^1.1.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browser-md5-file/download/browser-md5-file-1.1.1.tgz#247d63527f662d9667adecbe61808b4961b90dc6"
  integrity sha1-JH1jUn9mLZZnrey+YYCLSWG5DcY=
  dependencies:
    spark-md5 "^2.0.2"

browser-or-node@^1.2.1:
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browser-or-node/download/browser-or-node-1.3.0.tgz#f2a4e8568f60263050a6714b2cc236bb976647a7"
  integrity sha1-8qToVo9gJjBQpnFLLMI2u5dmR6c=

browser-resolve@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browser-resolve/download/browser-resolve-2.0.0.tgz#99b7304cb392f8d73dba741bb2d7da28c6d7842b"
  integrity sha1-mbcwTLOS+Nc9unQbstfaKMbXhCs=
  dependencies:
    resolve "^1.17.0"

browserify-aes@^1.0.4, browserify-aes@^1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserify-rsa/download/browserify-rsa-4.1.0.tgz#b2fd06b5b75ae297f7ce2dc651f918f5be158c8d"
  integrity sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserify-sign/-/browserify-sign-4.2.3.tgz#7afe4c01ec7ee59a89a558a4b75bd85ae62d4208"
  integrity sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==
  dependencies:
    bn.js "^5.2.1"
    browserify-rsa "^4.1.0"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.5"
    hash-base "~3.0"
    inherits "^2.0.4"
    parse-asn1 "^5.1.7"
    readable-stream "^2.3.8"
    safe-buffer "^5.2.1"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^3.2.6:
  version "3.2.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/download/browserslist-3.2.8.tgz#b0005361d6471f0f5952797a76fc985f1f978fc6"
  integrity sha1-sABTYdZHHw9ZUnl6dvyYXx+Xj8Y=
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

browserslist@^4.23.1:
  version "4.23.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/browserslist/-/browserslist-4.23.2.tgz#244fe803641f1c19c28c48c4b6ec9736eb3d32ed"
  integrity sha512-qkqSyistMYdxAcw+CzbZwlBy8AGmS/eEWs+sEV5TnLRGDOL+C5M2EnH6tlZyg0YoAxGJAFKh61En9BR941GnHA==
  dependencies:
    caniuse-lite "^1.0.30001640"
    electron-to-chromium "^1.4.820"
    node-releases "^2.0.14"
    update-browserslist-db "^1.1.0"

bser@2.1.1:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bser/download/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

btoa@^1.2.1:
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/btoa/download/btoa-1.2.1.tgz#01a9909f8b2c93f6bf680ba26131eb30f7fa3d73"
  integrity sha1-AamQn4ssk/a/aAuiYTHrMPf6PXM=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^5.7.1:
  version "5.7.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bundle-name@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bundle-name/-/bundle-name-4.1.0.tgz#f3b96b34160d6431a19d7688135af7cfb8797889"
  integrity sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q==
  dependencies:
    run-applescript "^7.0.0"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.2:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cache-content-type/download/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c"
  integrity sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/call-bind/-/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.1:
  version "4.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/camel-case/download/camel-case-4.1.2.tgz#9728072a954f805228225a6deea6b38461e1bd5a"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/camelcase/download/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.2.0, camelcase@^6.3.0:
  version "6.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/camelcase/download/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30000844:
  version "1.0.30001649"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001649.tgz#3ec700309ca0da2b0d3d5fb03c411b191761c992"
  integrity sha512-fJegqZZ0ZX8HOWr6rcafGr72+xcgJKI9oWfDW5DrD7ExUtgZC7a7R7ZYmZqplh7XDocFdGeIFn7roAxhOeYrPQ==

caniuse-lite@^1.0.30001640:
  version "1.0.30001643"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caniuse-lite/-/caniuse-lite-1.0.30001643.tgz#9c004caef315de9452ab970c3da71085f8241dbd"
  integrity sha512-ERgWGNleEilSrHM6iUz/zJNSQTP8Mr21wDWpdgvRwcTXGAq6jMtOUPP4dqFPTdKqZ2wKTdtB+uucZ3MRpAUSmg==

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/center-align/download/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.1.0, chalk@^2.3.2, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@~0.4.0:
  version "0.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chalk/download/chalk-0.4.0.tgz#5199a3ddcd0c1efe23bc08c1b027b06176e0c64f"
  integrity sha1-UZmj3c0MHv4jvAjBsCewYXbgxk8=
  dependencies:
    ansi-styles "~1.0.0"
    has-color "~0.1.0"
    strip-ansi "~0.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/char-regex/download/char-regex-1.0.2.tgz#d744358226217f981ed58f479b1d6bcc29545dcf"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

character-entities@^2.0.0:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/character-entities/download/character-entities-2.0.2.tgz#2d09c2e72cd9523076ccb21157dff66ad43fcc22"
  integrity sha1-LQnC5yzZUjB2zLIRV9/2atQ/zCI=

character-parser@^2.1.1:
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/character-parser/download/character-parser-2.2.0.tgz#c7ce28f36d4bcd9744e5ffc2c5fcde1c73261fc0"
  integrity sha1-x84o821LzZdE5f/CxfzeHHMmH8A=
  dependencies:
    is-regex "^1.0.3"

chardet@^0.4.0:
  version "0.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chardet/download/chardet-0.4.2.tgz#b5473b33dc97c424e5d98dc87d55d4d8a29c8bf2"
  integrity sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I=

charenc@0.0.2:
  version "0.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/charenc/download/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"
  integrity sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=

chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/chokidar/-/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ci-info/-/ci-info-3.9.0.tgz#4279a62028a7b1f262f3473fc9605f5e218c59b4"
  integrity sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

cjs-module-lexer@^1.0.0:
  version "1.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cjs-module-lexer/-/cjs-module-lexer-1.3.1.tgz#c485341ae8fd999ca4ee5af2d7a1c9ae01e0099c"
  integrity sha512-a3KdPAANPbNE4ZUv9h6LckSl9zLsYOP4MBmhIPkRaeyybt+r4UghLvq+xw/YwUcC1gqylCkL4rdVs3Lwupjm4Q==

classnames@^2.2.5:
  version "2.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/classnames/-/classnames-2.5.1.tgz#ba774c614be0f016da105c858e7159eae8e7687b"
  integrity sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==

clean-css@^4.1.11:
  version "4.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/clean-css/download/clean-css-4.2.4.tgz#733bf46eba4e607c6891ea57c24a989356831178"
  integrity sha1-czv0brpOYHxokepXwkqYk1aDEXg=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cli-width/download/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

clipboard-js@^0.3.3:
  version "0.3.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/clipboard-js/download/clipboard-js-0.3.6.tgz#6260add69b5318fde40b80f9d3c8c863efdaf339"
  integrity sha1-YmCt1ptTGP3kC4D508jIY+/a8zk=

clipboard@^2.0.11:
  version "2.0.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/clipboard/download/clipboard-2.0.11.tgz#62180360b97dd668b6b3a84ec226975762a70be5"
  integrity sha1-YhgDYLl91mi2s6hOwiaXV2KnC+U=
  dependencies:
    good-listener "^1.2.2"
    select "^1.1.2"
    tiny-emitter "^2.0.0"

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cliui/download/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cliui/download/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

cn-fontsource-lxgw-wen-kai-gb-screen-r@^1.0.6:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cn-fontsource-lxgw-wen-kai-gb-screen-r/-/cn-fontsource-lxgw-wen-kai-gb-screen-r-1.0.6.tgz#1eec28fc1a24bb62069e562edeedc9cdef69b591"
  integrity sha512-ipx3KfGJW51fKnqfUj1khIv9lqBqFcOoDodPjOTgQWKwpdckqhl+K5RJLd4/7fbub+4u6hzpP9+E2KoY1wSILQ==

co-body@^6.0.0:
  version "6.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/co-body/-/co-body-6.2.0.tgz#afd776d60e5659f4eee862df83499698eb1aea1b"
  integrity sha512-Kbpv2Yd1NdL1V/V4cwLVxraHDV6K8ayohr2rmH0J87Er8+zJjcTa6dAn9QMPC9CRgU8+aNajKbSf1TzDB1yKPA==
  dependencies:
    "@hapi/bourne" "^3.0.0"
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

collect-v8-coverage@^1.0.0:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz#c0b29bcd33bcd0779a1344c2136051e6afd3d9e9"
  integrity sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorful@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/colorful/download/colorful-2.1.0.tgz#6a8bdcbc2da42a50db3b4882fa25263aaa1f2d8e"
  integrity sha1-aovcvC2kKlDbO0iC+iUmOqofLY4=

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.15.1, commander@^2.2.0, commander@^2.9.0:
  version "2.20.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^3.0.2:
  version "3.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/commander/download/commander-3.0.2.tgz#6837c3fb677ad9933d1cfba42dd14d5117d6b39e"
  integrity sha1-aDfD+2d62ZM9HPukLdFNURfWs54=

commander@~2.11.0:
  version "2.11.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/commander/download/commander-2.11.0.tgz#157152fd1e7a6c8d98a5b715cf376df928004563"
  integrity sha1-FXFS/R56bI2YpbcVzzdt+SgARWM=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/component-emitter/-/component-emitter-1.3.1.tgz#ef1d5796f7d93f135ee6fb684340b26403c97d17"
  integrity sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.4.4:
  version "1.7.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/compression/download/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-typed-array@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/concat-typed-array/download/concat-typed-array-1.0.2.tgz#67880c3ce19ecff110c31598b41c7f4739d47ca0"
  integrity sha1-Z4gMPOGez/EQwxWYtBx/RznUfKA=

conf@^9.0.0:
  version "9.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/conf/download/conf-9.0.2.tgz#943589602b1ce274d9234265314336a698972bc5"
  integrity sha1-lDWJYCsc4nTZI0JlMUM2ppiXK8U=
  dependencies:
    ajv "^7.0.3"
    ajv-formats "^1.5.1"
    atomically "^1.7.0"
    debounce-fn "^4.0.0"
    dot-prop "^6.0.1"
    env-paths "^2.2.0"
    json-schema-typed "^7.0.3"
    make-dir "^3.1.0"
    onetime "^5.1.2"
    pkg-up "^3.1.0"
    semver "^7.3.4"

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/console-browserify/download/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

constantinople@^3.0.1, constantinople@^3.1.2:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/constantinople/download/constantinople-3.1.2.tgz#d45ed724f57d3d10500017a7d3a889c1381ae647"
  integrity sha1-1F7XJPV9PRBQABen06iJwTga5kc=
  dependencies:
    "@types/babel-types" "^7.0.0"
    "@types/babylon" "^6.16.2"
    babel-types "^6.26.0"
    babylon "^6.18.0"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.4, content-disposition@~0.5.2:
  version "0.5.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.4, content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/convert-source-map/download/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.6.0:
  version "0.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cookie/-/cookie-0.6.0.tgz#2798b04b071b0ecbff0dbb62a505a8efa4e19051"
  integrity sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==

cookies@~0.8.0:
  version "0.8.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cookies/download/cookies-0.8.0.tgz#1293ce4b391740a8406e3c9870e828c4b54f3f90"
  integrity sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

cookies@~0.9.0:
  version "0.9.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cookies/-/cookies-0.9.1.tgz#3ffed6f60bb4fb5f146feeedba50acc418af67e3"
  integrity sha512-TG2hpqe4ELx54QER/S3HQ9SRVnQnGBtKUz5bLQWtYAQ+o6GpgMs6sYUvaiJjVxb+UXwhRhAEP3m7LbsIZ77Hmw==
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

copy-anything@^3.0.2:
  version "3.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/copy-anything/download/copy-anything-3.0.5.tgz#2d92dce8c498f790fa7ad16b01a1ae5a45b020a0"
  integrity sha1-LZLc6MSY95D6etFrAaGuWkWwIKA=
  dependencies:
    is-what "^4.1.8"

copy-to@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/copy-to/download/copy-to-2.0.1.tgz#2680fbb8068a48d08656b6098092bdafc906f4a5"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cos-js-sdk-v5@^1.3.8:
  version "1.8.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cos-js-sdk-v5/-/cos-js-sdk-v5-1.8.3.tgz#76f9cd9f5d8c67ffd321e574485aaba90679c526"
  integrity sha512-Ne7VsOfuW6D5IuEWlR9VO9R2kFnotOSfAu6S0B5RyYETXmKlUWng3cl/JpVmwkn4VaFqcnsm2t45lCYnvozahA==
  dependencies:
    "@xmldom/xmldom" "^0.8.6"

cos-nodejs-sdk-v5@^2.9.13:
  version "2.14.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cos-nodejs-sdk-v5/-/cos-nodejs-sdk-v5-2.14.4.tgz#291db4255d4c5c9e78f81373008c4375af667bfc"
  integrity sha512-PNsk8wdHI2ij6ybBjU5ZKMieo0cqriMNDsKScoKeFEY8IxUaSd1oyFCs0uS1Rt7wPSQRSm/OONjNOJbd5OqZbg==
  dependencies:
    conf "^9.0.0"
    fast-xml-parser "4.2.5"
    mime-types "^2.1.24"
    request "^2.88.2"

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/create-ecdh/download/create-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

create-jest@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/create-jest/-/create-jest-29.7.0.tgz#a355c5b3cb1e1af02ba177fe7afd7feee49a5320"
  integrity sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    prompts "^2.0.1"

create-require@^1.1.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/create-require/download/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cropperjs@^1.5.12, cropperjs@^1.6.1:
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cropperjs/-/cropperjs-1.6.2.tgz#d1a5d627d880581cca41b7901f06923500e4201b"
  integrity sha512-nhymn9GdnV3CqiEHJVai54TULFAE3VshJTXSqSJKa8yXAKyBKDWdhHarnlIPrshJ0WMFTGuFvG02YjLXfPiuOA==

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/crypt/download/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"
  integrity sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/css-line-break/download/css-line-break-2.1.0.tgz#bfef660dfa6f5397ea54116bb3cb4873edbc4fa0"
  integrity sha1-v+9mDfpvU5fqVBFrs8tIc+28T6A=
  dependencies:
    utrie "^1.0.2"

csstype@^3.1.3:
  version "3.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

d@1, d@^1.0.1, d@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/d/-/d-1.0.2.tgz#2aefd554b81981e7dccf72d6842ae725cb17e5de"
  integrity sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==
  dependencies:
    es5-ext "^0.10.64"
    type "^2.7.2"

danmu.js@^0.5.9:
  version "0.5.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/danmu.js/download/danmu.js-0.5.10.tgz#616f308f353eefc1f7b9c68a855130b1c3888cbf"
  integrity sha1-YW8wjzU+78H3ucaKhVEwscOIjL8=
  dependencies:
    event-emitter "^0.3.5"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

dayjs@^1.10.6, dayjs@^1.10.7:
  version "1.11.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dayjs/-/dayjs-1.11.12.tgz#5245226cc7f40a15bf52e0b99fd2a04669ccac1d"
  integrity sha512-Rt2g+nTbLlDWZTwwrIXjy9MeiZmSDI375FvZs72ngxx8PDC6YXOeR3q5LAuPzjZQxhiWdRKac7RKV+YyQYfYIg==

debounce-fn@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debounce-fn/download/debounce-fn-4.0.0.tgz#ed76d206d8a50e60de0dd66d494d82835ffe61c7"
  integrity sha1-7XbSBtilDmDeDdZtSU2Cg1/+Ycc=
  dependencies:
    mimic-fn "^3.0.0"

debug@2.6.9, debug@^2.6.8:
  version "2.6.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@4:
  version "4.3.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/-/debug-4.3.6.tgz#2ab2c38fbaffebf8aa95fdfe6d88438c7a13c52b"
  integrity sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==
  dependencies:
    ms "2.1.2"

debug@=3.1.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^3.1.0:
  version "3.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.0.0, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.5:
  version "4.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/debug/-/debug-4.3.5.tgz#e83444eceb9fedd4a1da56d671ae2446a01a6e1e"
  integrity sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==
  dependencies:
    ms "2.1.2"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-named-character-reference@^1.0.0:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/decode-named-character-reference/download/decode-named-character-reference-1.0.2.tgz#daabac9690874c394c81e4162a0304b35d824f0e"
  integrity sha1-2quslpCHTDlMgeQWKgMEs12CTw4=
  dependencies:
    character-entities "^2.0.0"

decode-uri-component@^0.4.1:
  version "0.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/decode-uri-component/download/decode-uri-component-0.4.1.tgz#2ac4859663c704be22bf7db760a1494a49ab2cc5"
  integrity sha1-KsSFlmPHBL4iv323YKFJSkmrLMU=

dedent@^1.0.0:
  version "1.5.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dedent/-/dedent-1.5.3.tgz#99aee19eb9bae55a67327717b6e848d0bf777e5a"
  integrity sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deepmerge@^2.0.1:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/deepmerge/download/deepmerge-2.2.1.tgz#5d3ff22a01c00f645405a2fbc17d0778a1801170"
  integrity sha1-XT/yKgHAD2RUBaL7wX0HeKGAEXA=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/deepmerge/download/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-browser-id@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/default-browser-id/-/default-browser-id-5.0.0.tgz#a1d98bf960c15082d8a3fa69e83150ccccc3af26"
  integrity sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA==

default-browser@^5.2.1:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/default-browser/-/default-browser-5.2.1.tgz#7b7ba61204ff3e425b556869ae6d3e9d9f1712cf"
  integrity sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg==
  dependencies:
    bundle-name "^4.1.0"
    default-browser-id "^5.0.0"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/define-lazy-prop/download/define-lazy-prop-3.0.0.tgz#dbb19adfb746d7fc6d734a06b72f4a00d021255f"
  integrity sha1-27Ga37dG1/xtc0oGty9KANAhJV8=

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/define-properties/-/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegate@^3.1.2:
  version "3.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/delegate/download/delegate-3.2.0.tgz#b66b71c3158522e8ab5744f720d8ca0c2af59166"
  integrity sha1-tmtxwxWFIuirV0T3INjKDCr1kWY=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

dequal@^2.0.0:
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dequal/download/dequal-2.0.3.tgz#2644214f1997d39ed0ee0ece72335490a7ac67be"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

des.js@^1.0.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/des.js/download/des.js-1.1.0.tgz#1d37f5766f3bbff4ee9638e871a8768c173b81da"
  integrity sha1-HTf1dm87v/Tuljjocah2jBc7gdo=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0, destroy@^1.0.4:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/detect-newline/download/detect-newline-3.1.0.tgz#576f5dfc63ae1a192ff192d8ad3af6308991b651"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

detect-port@^1.5.1:
  version "1.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/detect-port/-/detect-port-1.6.1.tgz#45e4073997c5f292b957cb678fb0bb8ed4250a67"
  integrity sha512-CmnVc+Hek2egPx1PeTFVta2W78xy2K/9Rkf6cC4T59S50tVnzKj+tnx5mmx5lwvCkujZ4uRrpRSuV+IVs3f90Q==
  dependencies:
    address "^1.0.1"
    debug "4"

devlop@^1.0.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/devlop/download/devlop-1.1.0.tgz#4db7c2ca4dc6e0e834c30be70c94bbc976dc7018"
  integrity sha1-TbfCyk3G4Og0wwvnDJS7yXbccBg=
  dependencies:
    dequal "^2.0.0"

dezalgo@^1.0.4:
  version "1.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dezalgo/download/dezalgo-1.0.4.tgz#751235260469084c132157dfa857f386d4c33d81"
  integrity sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE=
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff-sequences@^28.1.1:
  version "28.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/diff-sequences/download/diff-sequences-28.1.1.tgz#9989dc731266dc2903457a70e996f3a041913ac6"
  integrity sha1-mYnccxJm3CkDRXpw6ZbzoEGROsY=

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/diff-sequences/-/diff-sequences-29.6.3.tgz#4deaf894d11407c51efc8418012f9e70b84ea921"
  integrity sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

doctypes@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/doctypes/download/doctypes-1.1.0.tgz#ea80b106a87538774e8a3a4a5afe293de489e0a9"
  integrity sha1-6oCxBqh1OHdOijpKWv4pPeSJ4Kk=

domain-browser@^4.22.0:
  version "4.23.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/domain-browser/-/domain-browser-4.23.0.tgz#427ebb91efcb070f05cffdfb8a4e9a6c25f8c94b"
  integrity sha512-ArzcM/II1wCCujdCNyQjXrAFwS4mrLh4C7DZWlaI8mdh7h3BfKdNd3bKXITfl2PT9FtfQqaGvhi1vPRQPimjGA==

dompurify@^2.0.6:
  version "2.5.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dompurify/-/dompurify-2.5.6.tgz#8402b501611eaa7fb3786072297fcbe2787f8592"
  integrity sha512-zUTaUBO8pY4+iJMPE1B9XlO2tXVYIcEA4SNGtvDELzTSCQO7RzH+j7S180BmhmJId78lqGU2z19vgVx2Sxs/PQ==

dompurify@^3.0.5:
  version "3.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dompurify/-/dompurify-3.1.6.tgz#43c714a94c6a7b8801850f82e756685300a027e2"
  integrity sha512-cTOAhc36AalkjtBpfG6O8JimdTMWNXjiePT2xQH/ppBGi/4uIpmj8eKyIkMJErXWARyINV/sB38yf8JCLF5pbQ==

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dot-case/download/dot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dot-prop@^6.0.1:
  version "6.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/dot-prop/download/dot-prop-6.0.1.tgz#fc26b3cf142b9e59b74dbd39ed66ce620c681083"
  integrity sha1-/CazzxQrnlm3Tb057WbOYgxoEIM=
  dependencies:
    is-obj "^2.0.0"

downloadjs@1.4.7:
  version "1.4.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/downloadjs/download/downloadjs-1.4.7.tgz#f69f96f940e0d0553dac291139865a3cd0101e3c"
  integrity sha1-9p+W+UDg0FU9rCkROYZaPNAQHjw=

draggabilly@^2.2.0:
  version "2.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/draggabilly/download/draggabilly-2.4.1.tgz#df02b9e2c3a837d42591af807a0d250b3816887f"
  integrity sha1-3wK54sOoN9Qlka+Aeg0lCzgWiH8=
  dependencies:
    get-size "^2.0.2"
    unidragger "^2.4.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts@5.2.1:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/echarts/download/echarts-5.2.1.tgz#bd58ec011cd82def4a714e4038ef4b73b8417bc3"
  integrity sha1-vVjsARzYLe9KcU5AOO9Lc7hBe8M=
  dependencies:
    tslib "2.3.0"
    zrender "5.2.1"

echarts@^5.2.2, echarts@^5.4.3:
  version "5.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/echarts/-/echarts-5.5.1.tgz#8dc9c68d0c548934bedcb5f633db07ed1dd2101c"
  integrity sha512-Fce8upazaAXUVUVsjgV6mBnGuqgO+JNDlcgF79Dksy4+wgGpQB2lmYoO4TSweFg/mZITdpGHomw/cNBJZj1icA==
  dependencies:
    tslib "2.3.0"
    zrender "5.6.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.3.47:
  version "1.5.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.5.tgz#03bfdf422bdd2c05ee2657efedde21264a1a566b"
  integrity sha512-QR7/A7ZkMS8tZuoftC/jfqNkZLQO779SSW3YuZHP4eXpj3EffGLFcB/Xu9AAZQzLccTiCV+EmUo3ha4mQ9wnlA==

electron-to-chromium@^1.4.820:
  version "1.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/electron-to-chromium/-/electron-to-chromium-1.5.1.tgz#24640bd4dcfaccb6d82bb4c3f4c7311503241581"
  integrity sha512-FKbOCOQ5QRB3VlIbl1LZQefWIYwszlBloaXcY2rbfpu9ioJnNh3TK03YtIDKDo3WKBi8u+YV4+Fn2CkEozgf4w==

elliptic@^6.5.3, elliptic@^6.5.5:
  version "6.5.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/elliptic/-/elliptic-6.5.6.tgz#ee5f7c3a00b98a2144ac84d67d01f04d438fa53e"
  integrity sha512-mpzdtpeCLuS3BmE3pO3Cpp5bbjlOPY2Q0PgoF+Od1XZrHLYI28Xe3ossCmYCQt11FQKEYd9+PF8jymTvtWJSHQ==
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/emittery/download/emittery-0.13.1.tgz#c04b8c3457490e0847ae51fced3af52d338e3dad"
  integrity sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@^1.0.2, encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

entities@^4.5.0:
  version "4.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/entities/download/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

entities@~3.0.1:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/entities/download/entities-3.0.1.tgz#2b887ca62585e96db3903482d336c1006c3001d4"
  integrity sha1-K4h8piWF6W2zkDSC0zbBAGwwAdQ=

env-paths@^2.2.0:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/env-paths/download/env-paths-2.2.1.tgz#420399d416ce1fbe9bc0a07c62fa68d67fd0f8f2"
  integrity sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser-es@^0.1.4:
  version "0.1.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/error-stack-parser-es/-/error-stack-parser-es-0.1.5.tgz#15b50b67bea4b6ed6596976ee07c7867ae25bb1c"
  integrity sha512-xHku1X40RO+fO8yJ8Wh2f2rZWVjqyhb1zgq1yZ8aZRQkv6OOKhKWRUaht3eSCUbAOBaKIgM+ykwFLE+QUxgGeg==

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-define-property/-/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es5-ext@^0.10.35, es5-ext@^0.10.62, es5-ext@^0.10.64, es5-ext@~0.10.14:
  version "0.10.64"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es5-ext/-/es5-ext-0.10.64.tgz#12e4ffb48f1ba2ea777f1fcdd1918ef73ea21714"
  integrity sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    esniff "^2.0.1"
    next-tick "^1.1.0"

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-promise@^3.3.1:
  version "3.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es6-promise/download/es6-promise-3.3.1.tgz#a08cdde84ccdbf34d027a1451bc91d4bcd28a613"
  integrity sha1-oIzd6EzNvzTQJ6FFG8kdS80ophM=

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/es6-symbol/-/es6-symbol-3.1.4.tgz#f4e7d28013770b4208ecbf3e0bf14d3bcb557b8c"
  integrity sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==
  dependencies:
    d "^1.0.2"
    ext "^1.7.0"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esbuild/-/esbuild-0.21.5.tgz#9ca301b120922959b766360d8ac830da0d02997d"
  integrity sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.1.1, escalade@^3.1.2:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escalade/-/escalade-3.1.2.tgz#54076e9ab29ea5bf3d8f1ed62acffbb88272df27"
  integrity sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==

escape-html@^1.0.3, escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz#a30304e99daa32e23b2fd20f51babd07cffca344"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/escape-string-regexp/download/escape-string-regexp-5.0.0.tgz#4683126b500b61762f2dbebace1806e8be31b1c8"
  integrity sha1-RoMSa1ALYXYvLb66zhgG6L4xscg=

esniff@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esniff/-/esniff-2.0.1.tgz#a4d4b43a5c71c7ec51c51098c1d8a29081f9b308"
  integrity sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.62"
    event-emitter "^0.3.5"
    type "^2.7.2"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/estree-walker/download/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

estree-walker@^3.0.3:
  version "3.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/estree-walker/download/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

ev-emitter@^1.0.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ev-emitter/download/ev-emitter-1.1.1.tgz#8f18b0ce5c76a5d18017f71c0a795c65b9138f2a"
  integrity sha1-jxiwzlx2pdGAF/ccCnlcZbkTjyo=

event-emitter@^0.3.5:
  version "0.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/event-target-shim/download/event-target-shim-5.0.1.tgz#5d4d3ebdf9583d63a5333ce2deb7480ab2b05789"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter3@^4.0.7:
  version "4.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exif-js@^2.3.0:
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/exif-js/download/exif-js-2.3.0.tgz#9d10819bf571f873813e7640241255ab9ce1a814"
  integrity sha1-nRCBm/Vx+HOBPnZAJBJVq5zhqBQ=

exit@^0.1.2:
  version "0.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expect@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/expect/-/expect-29.7.0.tgz#578874590dcb3214514084c08115d8aee61e11bc"
  integrity sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==
  dependencies:
    "@jest/expect-utils" "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"

express@^4.8.5:
  version "4.19.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/express/-/express-4.19.2.tgz#e25437827a3aa7f2a827bc8171bbbb664a356465"
  integrity sha512-5T6nhjsT+EOMzuck8JjBHARTHfMht0POzlA60WV2pMD3gyXw2LZnZ+ueGdNxG+0calOJcWKbpFcuzLZ91YWq9Q==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.2"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.6.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

ext@^1.7.0:
  version "1.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ext/download/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
  integrity sha1-DqQ4PAED1g5wvpnpp/EQJ6M8T18=
  dependencies:
    type "^2.7.2"

extend@^3.0.0, extend@~3.0.2:
  version "3.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^2.1.0:
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/external-editor/download/external-editor-2.2.0.tgz#045511cfd8d133f3846673d1047c154e214ad3d5"
  integrity sha1-BFURz9jRM/OEZnPRBHwVTiFK09U=
  dependencies:
    chardet "^0.4.0"
    iconv-lite "^0.4.17"
    tmp "^0.0.33"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-decode-uri-component@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-decode-uri-component/download/fast-decode-uri-component-1.0.1.tgz#46f8b6c22b30ff7a81357d4f59abfae938202543"
  integrity sha1-Rvi2wisw/3qBNX1PWav66TggJUM=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0, fast-json-stable-stringify@~2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-json-stringify@^0.17.0:
  version "0.17.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-json-stringify/download/fast-json-stringify-0.17.0.tgz#7c3e98ccde5e32802f7930bc360307e4d449c69f"
  integrity sha1-fD6YzN5eMoAveTC8NgMH5NRJxp8=
  dependencies:
    ajv "^6.0.0"
    fast-safe-stringify "^1.2.1"

fast-querystring@^1.0.0:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-querystring/download/fast-querystring-1.1.2.tgz#a6d24937b4fc6f791b4ee31dcb6f53aeafb89f53"
  integrity sha1-ptJJN7T8b3kbTuMdy29Trq+4n1M=
  dependencies:
    fast-decode-uri-component "^1.0.1"

fast-safe-stringify@^1.2.1:
  version "1.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-safe-stringify/download/fast-safe-stringify-1.2.3.tgz#9fe22c37fb2f7f86f06b8f004377dbf8f1ee7bc1"
  integrity sha1-n+IsN/svf4bwa48AQ3fb+PHue8E=

fast-uri@^3.0.1:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-uri/-/fast-uri-3.0.1.tgz#cddd2eecfc83a71c1be2cc2ef2061331be8a7134"
  integrity sha512-MWipKbbYiYI0UC7cl8m/i/IWTqfC8YXsqjzybjddLsFjStroQzsHXkc73JutMvBiXmOvapk+axIl79ig5t55Bw==

fast-xml-parser@4.2.5:
  version "4.2.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fast-xml-parser/download/fast-xml-parser-4.2.5.tgz#a6747a09296a6cb34f2ae634019bf1738f3b421f"
  integrity sha1-pnR6CSlqbLNPKuY0AZvxc487Qh8=
  dependencies:
    strnum "^1.0.5"

fault@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fault/download/fault-2.0.1.tgz#d47ca9f37ca26e4bd38374a7c500b5a384755b6c"
  integrity sha1-1Hyp83yibkvTg3SnxQC1o4R1W2w=
  dependencies:
    format "^0.2.0"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fb-watchman/download/fb-watchman-2.0.2.tgz#e9524ee6b5c77e9e5001af0f85f3adbb8623255c"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-saver@^2.0.5:
  version "2.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/file-saver/download/file-saver-2.0.5.tgz#d61cfe2ce059f414d899e9dd6d4107ee25670c38"
  integrity sha1-1hz+LOBZ9BTYmendbUEH7iVnDDg=

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fill-range/-/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^5.1.0:
  version "5.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/filter-obj/download/filter-obj-5.1.0.tgz#5bd89676000a713d7db2e197f660274428e524ed"
  integrity sha1-W9iWdgAKcT19suGX9mAnRCjlJO0=

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/finalhandler/download/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
  integrity sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-my-way@^4.3.3:
  version "4.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-my-way/download/find-my-way-4.5.1.tgz#758e959194b90aea0270db18fff75e2fceb2239f"
  integrity sha1-dY6VkZS5CuoCcNsY//deL86yI58=
  dependencies:
    fast-decode-uri-component "^1.0.1"
    fast-deep-equal "^3.1.3"
    safe-regex2 "^2.0.0"
    semver-store "^0.3.0"

find-my-way@~8.1.0:
  version "8.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-my-way/-/find-my-way-8.1.0.tgz#cc05e8e4b145322299d0de0a839b5be528c2083e"
  integrity sha512-41QwjCGcVTODUmLLqTMeoHeiozbMXYMAE1CKFiDyi9zVZ2Vjh0yz3MF0WQZoIb+cmzP/XlbFjlF2NtJmvZHznA==
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-querystring "^1.0.0"
    safe-regex2 "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

fluent-json-schema@^3.0.1:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fluent-json-schema/-/fluent-json-schema-3.1.0.tgz#b83579429187ad68a76c756d05d9c93cffa38086"
  integrity sha512-Ft7WGihiwpcZkZsJuXVCcEHLDOJ1vGS4wFK8V08xWffFthadgjwpSExfBWz/72VxHZ0xe7/N3T38hOxskwxd3A==
  dependencies:
    deepmerge "^4.2.2"

fluent-json-schema@~4.2.1:
  version "4.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fluent-json-schema/-/fluent-json-schema-4.2.1.tgz#5684fc153f00c2a5f8d680c7a2918d9de64d9377"
  integrity sha512-vSvURY8BBRqxOFquy/wwjdnT4z07j2NZ+Cm3Nj881NHXKPSdiE4ZNyRImDh+SIk2yFZKzj7Clt+ENb5ha4uYJA==
  dependencies:
    "@fastify/deepmerge" "^1.1.0"

focus-trap@^7.5.4:
  version "7.5.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/focus-trap/-/focus-trap-7.5.4.tgz#6c4e342fe1dae6add9c2aa332a6e7a0bbd495ba2"
  integrity sha512-N7kHdlgsO/v+iD/dMoJKtsSqs5Dz/dXZVebRgJw23LDk+jMi/974zyiOYDziY2JPp8xivq9BmUGwIJMiuSBi7w==
  dependencies:
    tabbable "^6.2.0"

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.14.8, follow-redirects@^1.15.0, follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/follow-redirects/-/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/for-each/download/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/form-data/download/form-data-3.0.1.tgz#ebd53791b78356a99af9a300d4282c4d5eb9755f"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

format@^0.2.0:
  version "0.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/format/download/format-0.2.2.tgz#d6170107e9efdc4ed30c9dc39016df942b5cb58b"
  integrity sha1-1hcBB+nv3E7TDJ3DkBbflCtctYs=

formidable@~3.5.1:
  version "3.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/formidable/-/formidable-3.5.1.tgz#9360a23a656f261207868b1484624c4c8d06ee1a"
  integrity sha512-WJWKelbRHN41m5dumb0/k8TeAx7Id/y3a+Z7QfhxP/htI9Js5zYaEDtG8uMgG0vM0lOlqnmjE99/kfpOYi/0Og==
  dependencies:
    dezalgo "^1.0.4"
    hexoid "^1.0.0"
    once "^1.4.0"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@0.5.2, fresh@~0.5.2:
  version "0.5.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fs-extra/download/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^11.2.0:
  version "11.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fs-extra/-/fs-extra-11.2.0.tgz#e70e17dfad64232287d01929399e0ea7c86b0e5b"
  integrity sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fs-extra/download/fs-extra-5.0.0.tgz#414d0110cdd06705734d055652c5411260c31abd"
  integrity sha1-QU0BEM3QZwVzTQVWUsVBEmDDGr0=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.2, fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-intrinsic/-/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-package-type/download/get-package-type-0.1.0.tgz#8de2d803cff44df3bc6c456e6668b36c3926e11a"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-size@^2.0.2:
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-size/download/get-size-2.0.3.tgz#54a1d0256b20ea7ac646516756202769941ad2ef"
  integrity sha1-VKHQJWsg6nrGRlFnViAnaZQa0u8=

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^8.0.3:
  version "8.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/glob/download/glob-8.1.0.tgz#d388f656593ef708ee3e34640fdfb99a9fd1c33e"
  integrity sha1-04j2Vlk+9wjuPjRkD9+5mp/Rwz4=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^5.0.1"
    once "^1.3.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^9.18.0:
  version "9.18.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/globals/download/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

good-listener@^1.2.2:
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/good-listener/download/good-listener-1.2.2.tgz#d53b30cdf9313dffb7dc9a0d477096aa6d145c50"
  integrity sha1-1TswzfkxPf+33JoNR3CWqm0UXFA=
  dependencies:
    delegate "^3.1.2"

google-protobuf@^3.17.3:
  version "3.21.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/google-protobuf/-/google-protobuf-3.21.4.tgz#2f933e8b6e5e9f8edde66b7be0024b68f77da6c9"
  integrity sha512-MnG7N936zcKTco4Jd2PX2U96Kf9PxygAPKBug+74LHzmHXmceN16MmRcdgZv+DGef/S9YvQAfRsNCn4cjf9yyQ==

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-color@~0.1.0:
  version "0.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-color/download/has-color-0.1.7.tgz#67144a5260c34fc3cca677d041daf52fe7b78b2f"
  integrity sha1-ZxRKUmDDT8PMpnfQQdr1L+e3iy8=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-proto/-/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hash-base/download/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-base@~3.0:
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hash-base/download/hash-base-3.0.4.tgz#5fc8686847ecd73499403319a6b0a3f3f6ae4918"
  integrity sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hash-sum/download/hash-sum-2.0.0.tgz#81d01bb5de8ea4a214ad5d6ead1b523460b0b45a"
  integrity sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hash.js/download/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

headers-polyfill@^3.1.2:
  version "3.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/headers-polyfill/-/headers-polyfill-3.3.0.tgz#67c6ef7b72d4c8cac832ad5936f5b3a56e7b705a"
  integrity sha512-5e57etwBpNcDc0b6KCVWEh/Ro063OxPvzVimUdM0/tsYM/T7Hfy3kknIGj78SFTOhNd8AZY41U8mOHoO4LzmIQ==

hexoid@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hexoid/download/hexoid-1.0.0.tgz#ad10c6573fb907de23d9ec63a711267d9dc9bc18"
  integrity sha1-rRDGVz+5B94j2exjpxEmfZ3JvBg=

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hookable@^5.5.3:
  version "5.5.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/hookable/download/hookable-5.5.3.tgz#6cfc358984a1ef991e2518cb9ed4a778bbd3215d"
  integrity sha1-bPw1iYSh75keJRjLntSneLvTIV0=

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/html-escaper/download/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-***************************=

html-tags@^3.3.1:
  version "3.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/html-tags/download/html-tags-3.3.1.tgz#a04026a18c882e4bba8a01a3d39cfe465d40b5ce"
  integrity sha1-oEAmoYyILku6igGj05z+Rl1Atc4=

html-to-image@1.11.11:
  version "1.11.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/html-to-image/-/html-to-image-1.11.11.tgz#c0f8a34dc9e4b97b93ff7ea286eb8562642ebbea"
  integrity sha512-9gux8QhvjRO/erSnDPv28noDZcPZmYE7e1vFsBLKLlRlKDSqNJYebj6Qz1TGd5lsRV+X+xYyjCKjuZdABinWjA==

html2canvas@^1.4.1:
  version "1.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/html2canvas/download/html2canvas-1.4.1.tgz#7cef1888311b5011d507794a066041b14669a543"
  integrity sha1-fO8YiDEbUBHVB3lKBmBBsUZppUM=
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

http-assert@^1.3.0:
  version "1.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-assert/download/http-assert-1.5.0.tgz#c389ccd87ac16ed2dfa6246fd73b926aa00e6b8f"
  integrity sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.8.0"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@^1.6.3, http-errors@^1.7.3, http-errors@~1.8.0:
  version "1.8.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-errors/download/http-errors-1.8.1.tgz#7c3f28577cbc8a207388455dbd62295ed07bd68c"
  integrity sha1-fD8oV3y8iiBziEVdvWIpXtB71ow=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

iconv-lite@0.4.24, iconv-lite@^0.4.17, iconv-lite@^0.4.6:
  version "0.4.24"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/immediate/download/immediate-3.0.6.tgz#9db1dbd0faf8de6fbe0f5dd5e56bb606280de69b"
  integrity sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=

import-html-entry@^1.9.0:
  version "1.17.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/import-html-entry/-/import-html-entry-1.17.0.tgz#65211779b4ebf5a25200308bf0cbc6f831d5e4e1"
  integrity sha512-2SDsRlGlE8bqdnGqsOyiDPEWlzJR0jNW4LWopnZl5QE1Yd0nJ7fykWo2GaKUF7Jq7pR0g3dElhuJHyamTt1gPQ==
  dependencies:
    "@babel/runtime" "^7.7.2"

import-local@^3.0.2:
  version "3.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/import-local/-/import-local-3.2.0.tgz#c3d5c745798c02a6f8b897726aba5100186ee260"
  integrity sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflation@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inflation/-/inflation-2.1.0.tgz#9214db11a47e6f756d111c4f9df96971c60f886c"
  integrity sha512-t54PPJHG1Pp7VQvxyVCJ9mBbjG3Hqryges9bXoOO6GExCPa+//i/d5GSuFtpx3ALLd7lgIAur6zrIlBQyJuMlQ==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@~2.0.4:
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^5.2.0:
  version "5.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/inquirer/download/inquirer-5.2.0.tgz#db350c2b73daca77ff1243962e9f22f099685726"
  integrity sha1-2zUMK3Paynf/EkOWLp8i8JloVyY=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.1.0"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^5.5.2"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

invariant@^2.2.2:
  version "2.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

ip@^0.3.2:
  version "0.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ip/download/ip-0.3.3.tgz#8ee8309e92f0b040d287f72efaca1a21702d3fb4"
  integrity sha1-jugwnpLwsEDSh/cu+soaIXAtP7Q=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-arguments/download/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5, is-buffer@~1.1.6:
  version "1.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.3:
  version "1.2.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.13.0:
  version "2.15.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-core-module/-/is-core-module-2.15.0.tgz#71c72ec5442ace7e76b306e9d48db361f22699ea"
  integrity sha512-Dd+Lb2/zvk9SKy1TGCt1wFJFo/MWBPMX5x7KcvLajWTGuomczdQX61PvY5yK6SVACwpoexWo81IfFyoKY2QnTA==
  dependencies:
    hasown "^2.0.2"

is-docker@^2.0.0:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-docker/download/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-docker@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-docker/download/is-docker-3.0.0.tgz#90093aa3106277d8a77a5910dbae71747e15a200"
  integrity sha1-kAk6oxBid9inelkQ265xdH4VogA=

is-expression@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-expression/download/is-expression-3.0.0.tgz#39acaa6be7fd1f3471dc42c7416e61c24317ac9f"
  integrity sha1-Oayqa+f9HzRx3ELHQW5hwkMXrJ8=
  dependencies:
    acorn "~4.0.2"
    object-assign "^4.0.1"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-generator-fn/download/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-generator-function/download/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-inside-container@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-inside-container/download/is-inside-container-1.0.0.tgz#e81fba699662eb31dbdaf26766a61d4814717ea4"
  integrity sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=
  dependencies:
    is-docker "^3.0.0"

is-nan@^1.3.2:
  version "1.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-nan/download/is-nan-1.3.2.tgz#043a54adea31748b55b6cd4e09aadafa69bd9e1d"
  integrity sha1-BDpUreoxdItVts1OCara+mm9nh0=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-obj/download/is-obj-2.0.0.tgz#473fb05d973705e3fd9620545018ca8e22ef4982"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-plain-obj@^4.0.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-plain-obj/download/is-plain-obj-4.1.0.tgz#d65025edec3657ce032fd7db63c97883eaed71f0"
  integrity sha1-1lAl7ew2V84DL9fbY8l4g+rtcfA=

is-promise@^2.0.0:
  version "2.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-promise/download/is-promise-2.2.2.tgz#39ab959ccbf9a774cf079f7b40c7a26f763135f1"
  integrity sha1-OauVnMv5p3TPB597QMeib3YxNfE=

is-reference@1.2.1:
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-reference/download/is-reference-1.2.1.tgz#8b2dac0b371f4bc994fdeaba9eb542d03002d0b7"
  integrity sha1-iy2sCzcfS8mU/eq6nrVC0DAC0Lc=
  dependencies:
    "@types/estree" "*"

is-regex@^1.0.3:
  version "1.1.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-typed-array@^1.1.3:
  version "1.1.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-typed-array/-/is-typed-array-1.1.13.tgz#d6c5ca56df62334959322d7d7dd1cca50debe229"
  integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
  dependencies:
    which-typed-array "^1.1.14"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-what@^4.1.8:
  version "4.1.16"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-what/-/is-what-4.1.16.tgz#1ad860a19da8b4895ad5495da3182ce2acdd7a6f"
  integrity sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A==

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

is-wsl@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/is-wsl/-/is-wsl-3.1.0.tgz#e1c657e39c10090afcbedec61720f6b924c3cbd2"
  integrity sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==
  dependencies:
    is-inside-container "^1.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isomorphic-timers-promises@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isomorphic-timers-promises/-/isomorphic-timers-promises-1.0.1.tgz#e4137c24dbc54892de8abae3a4b5c1ffff381598"
  integrity sha512-u4sej9B1LPSxTGKB/HiuzvEQnXH0ECYkSVQU39koSwmFAxhlEAFl9RdTvLv4TOTQUgBS5O3O5fwUxk6byBZ+IQ==

isomorphic-ws@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isomorphic-ws/download/isomorphic-ws-4.0.1.tgz#55fd4cd6c5e6491e76dc125938dd863f5cd4f2dc"
  integrity sha1-Vf1M1sXmSR523BJZON2GP1zU8tw=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz#2d166c4b0644d43a39f04bf6c2edd1e585f31756"
  integrity sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz#d10c8885c2125574e1c231cacadf955675e1ce3d"
  integrity sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-instrument@^6.0.0:
  version "6.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz#fa15401df6c15874bcb2105f773325d78c666765"
  integrity sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz#908305bac9a5bd175ac6a74489eafd0fc2445a7d"
  integrity sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz#895f3a709fcfba34c6de5a42939022f3e4358551"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/istanbul-reports/-/istanbul-reports-3.1.7.tgz#daed12b9e1dca518e15c056e1e537e741280fa0b"
  integrity sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jest-changed-files@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-changed-files/-/jest-changed-files-29.7.0.tgz#1c06d07e77c78e1585d020424dedc10d6e17ac3a"
  integrity sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==
  dependencies:
    execa "^5.0.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"

jest-circus@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-circus/-/jest-circus-29.7.0.tgz#b6817a45fcc835d8b16d5962d0c026473ee3668a"
  integrity sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^1.0.0"
    is-generator-fn "^2.0.0"
    jest-each "^29.7.0"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"
    pretty-format "^29.7.0"
    pure-rand "^6.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-cli/-/jest-cli-29.7.0.tgz#5592c940798e0cae677eec169264f2d839a37995"
  integrity sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    create-jest "^29.7.0"
    exit "^0.1.2"
    import-local "^3.0.2"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    yargs "^17.3.1"

jest-config@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-config/-/jest-config-29.7.0.tgz#bcbda8806dbcc01b1e316a46bb74085a84b0245f"
  integrity sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-jest "^29.7.0"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-get-type "^29.6.3"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-runner "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^28.0.2:
  version "28.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-diff/download/jest-diff-28.1.3.tgz#948a192d86f4e7a64c5264ad4da4877133d8792f"
  integrity sha1-lIoZLYb056ZMUmStTaSHcTPYeS8=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^28.1.1"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-diff@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-diff/-/jest-diff-29.7.0.tgz#017934a66ebb7ecf6f205e84699be10afd70458a"
  integrity sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.6.3"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-docblock@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-docblock/-/jest-docblock-29.7.0.tgz#8fddb6adc3cdc955c93e2a87f61cfd350d5d119a"
  integrity sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==
  dependencies:
    detect-newline "^3.0.0"

jest-each@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-each/-/jest-each-29.7.0.tgz#162a9b3f2328bdd991beaabffbb74745e56577d1"
  integrity sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    jest-util "^29.7.0"
    pretty-format "^29.7.0"

jest-environment-node@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-environment-node/-/jest-environment-node-29.7.0.tgz#0b93e111dda8ec120bc8300e6d1fb9576e164376"
  integrity sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

jest-get-type@^28.0.2:
  version "28.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-get-type/download/jest-get-type-28.0.2.tgz#34622e628e4fdcd793d46db8a242227901fcf203"
  integrity sha1-NGIuYo5P3NeT1G24okIieQH88gM=

jest-get-type@^29.6.3:
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-get-type/-/jest-get-type-29.6.3.tgz#36f499fdcea197c1045a127319c0481723908fd1"
  integrity sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==

jest-haste-map@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-haste-map/-/jest-haste-map-29.7.0.tgz#3c2396524482f5a0506376e6c858c3bbcc17b104"
  integrity sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz#5b7ec0dadfdfec0ca383dc9aa016d36b5ea4c728"
  integrity sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==
  dependencies:
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-matcher-utils@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz#ae8fec79ff249fd592ce80e3ee474e83a6c44f12"
  integrity sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-message-util@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-message-util/-/jest-message-util-29.7.0.tgz#8bc392e204e95dfe7564abbe72a404e28e51f7f3"
  integrity sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^29.6.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-mock/-/jest-mock-29.7.0.tgz#4e836cf60e99c6fcfabe9f99d017f3fdd50a6347"
  integrity sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-util "^29.7.0"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-pnp-resolver/download/jest-pnp-resolver-1.2.3.tgz#930b1546164d4ad5937d5540e711d4d38d4cad2e"
  integrity sha1-kwsVRhZNStWTfVVA5xHU041MrS4=

jest-regex-util@^29.6.3:
  version "29.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-regex-util/-/jest-regex-util-29.6.3.tgz#4a556d9c776af68e1c5f48194f4d0327d24e8a52"
  integrity sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==

jest-resolve-dependencies@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz#1b04f2c095f37fc776ff40803dc92921b1e88428"
  integrity sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==
  dependencies:
    jest-regex-util "^29.6.3"
    jest-snapshot "^29.7.0"

jest-resolve@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-resolve/-/jest-resolve-29.7.0.tgz#64d6a8992dd26f635ab0c01e5eef4399c6bcbc30"
  integrity sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-pnp-resolver "^1.2.2"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    resolve "^1.20.0"
    resolve.exports "^2.0.0"
    slash "^3.0.0"

jest-runner@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-runner/-/jest-runner-29.7.0.tgz#809af072d408a53dcfd2e849a4c976d3132f718e"
  integrity sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/environment" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.13.1"
    graceful-fs "^4.2.9"
    jest-docblock "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-leak-detector "^29.7.0"
    jest-message-util "^29.7.0"
    jest-resolve "^29.7.0"
    jest-runtime "^29.7.0"
    jest-util "^29.7.0"
    jest-watcher "^29.7.0"
    jest-worker "^29.7.0"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-runtime/-/jest-runtime-29.7.0.tgz#efecb3141cf7d3767a3a0cc8f7c9990587d3d817"
  integrity sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/globals" "^29.7.0"
    "@jest/source-map" "^29.6.3"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-snapshot/-/jest-snapshot-29.7.0.tgz#c2c574c3f51865da1bb329036778a69bf88a6be5"
  integrity sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-jsx" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^29.7.0"
    graceful-fs "^4.2.9"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    natural-compare "^1.4.0"
    pretty-format "^29.7.0"
    semver "^7.5.3"

jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-util/-/jest-util-29.7.0.tgz#23c2b62bfb22be82b44de98055802ff3710fc0bc"
  integrity sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-validate/-/jest-validate-29.7.0.tgz#7bf705511c64da591d46b15fce41400d52147d9c"
  integrity sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==
  dependencies:
    "@jest/types" "^29.6.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    leven "^3.1.0"
    pretty-format "^29.7.0"

jest-watcher@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-watcher/-/jest-watcher-29.7.0.tgz#7810d30d619c3a62093223ce6bb359ca1b28a2f2"
  integrity sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==
  dependencies:
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.13.1"
    jest-util "^29.7.0"
    string-length "^4.0.1"

jest-worker@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest-worker/-/jest-worker-29.7.0.tgz#acad073acbbaeb7262bd5389e1bcf43e10058d4a"
  integrity sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^29.5.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jest/-/jest-29.7.0.tgz#994676fc24177f088f1c5e3737f5697204ff2613"
  integrity sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/types" "^29.6.3"
    import-local "^3.0.2"
    jest-cli "^29.7.0"

js-cookie@2.2.1:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-cookie/download/js-cookie-2.2.1.tgz#69e106dc5d5806894562902aa5baec3744e9b2b8"
  integrity sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg=

js-cookie@3.0.1:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-cookie/download/js-cookie-3.0.1.tgz#9e39b4c6c2f56563708d7d31f6f5f21873a92414"
  integrity sha1-njm0xsL1ZWNwjX0x9vXyGHOpJBQ=

js-cookie@^3.0.1:
  version "3.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-cookie/download/js-cookie-3.0.5.tgz#0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc"
  integrity sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=

js-stringify@^1.0.1:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-stringify/download/js-stringify-1.0.2.tgz#1736fddfd9724f28a3682adc6230ae7e4e9679db"
  integrity sha1-Fzb939lyTyijaCrcYjCufk6Weds=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-tokens/download/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

jsmind@^0.8.5:
  version "0.8.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsmind/-/jsmind-0.8.5.tgz#5075a07d2451b831d8371df9dc9370f76012f57a"
  integrity sha512-fz7SZ4X9GFktM8Kr9qyqt9fw2fcSe3n94HmdDTsibTKrvgFFeXneO3z7xSk1zE2NK9Zzex2X3TCKxHczdpxmfg==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-schema-typed@^7.0.3:
  version "7.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-schema-typed/download/json-schema-typed-7.0.3.tgz#23ff481b8b4eebcd2ca123b4fa0409e66469a2d9"
  integrity sha1-I/9IG4tO680soSO0+gQJ5mRpotk=

json-schema@0.4.0:
  version "0.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^2.2.3:
  version "2.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsonfile/download/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonlint@^1.6.3:
  version "1.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsonlint/download/jsonlint-1.6.3.tgz#cb5e31efc0b78291d0d862fbef05900adf212988"
  integrity sha1-y14x78C3gpHQ2GL77wWQCt8hKYg=
  dependencies:
    JSV "^4.0.x"
    nomnom "^1.5.x"

jsonschema@^1.2.6:
  version "1.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsonschema/download/jsonschema-1.4.1.tgz#cc4c3f0077fb4542982973d8a083b6b34f482dab"
  integrity sha1-zEw/AHf7RUKYKXPYoIO2s09ILas=

jsprim@^1.2.2:
  version "1.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

jstransformer@1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jstransformer/download/jstransformer-1.0.0.tgz#ed8bf0921e2f3f1ed4d5c1a44f68709ed24722c3"
  integrity sha1-7Yvwkh4vPx7U1cGkT2hwntJHIsM=
  dependencies:
    is-promise "^2.0.0"
    promise "^7.0.1"

jszip@^3.10.1:
  version "3.10.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/jszip/download/jszip-3.10.1.tgz#34aee70eb18ea1faec2f589208a157d1feb091c2"
  integrity sha1-NK7nDrGOofrsL1iSCKFX0f6wkcI=
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

juicer@^0.6.6-stable:
  version "0.6.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/juicer/download/juicer-0.6.15.tgz#110a9430e865288ea533da7013a47f8f99c5f876"
  integrity sha1-EQqUMOhlKI6lM9pwE6R/j5nF+HY=
  dependencies:
    optimist "~0.3"
    uglify-js "~1.2"

keygrip@~1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/keygrip/download/keygrip-1.1.0.tgz#871b1681d5e159c62a445b0c74b615e0917e7226"
  integrity sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=
  dependencies:
    tsscmp "1.0.6"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

klaw-sync@^6.0.0, klaw-sync@~6.0.0:
  version "6.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/klaw-sync/download/klaw-sync-6.0.0.tgz#1fd2cfd56ebb6250181114f0a581167099c2b28c"
  integrity sha1-H9LP1W67YlAYERTwpYEWcJnCsow=
  dependencies:
    graceful-fs "^4.1.11"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/kleur/download/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

koa-bodyparser@^4.3.0, koa-bodyparser@~4.4.0:
  version "4.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa-bodyparser/download/koa-bodyparser-4.4.1.tgz#a908d848e142cc57d9eece478e932bf00dce3029"
  integrity sha1-qQjYSOFCzFfZ7s5HjpMr8A3OMCk=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"
    type-is "^1.6.18"

koa-compose@^4.1.0, koa-compose@~4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa-compose/download/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-convert@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa-convert/download/koa-convert-2.0.0.tgz#86a0c44d81d40551bae22fee6709904573eea4f5"
  integrity sha1-hqDETYHUBVG64i/uZwmQRXPupPU=
  dependencies:
    co "^4.6.0"
    koa-compose "^4.1.0"

koa-send@^5.0.0:
  version "5.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa-send/download/koa-send-5.0.1.tgz#39dceebfafb395d0d60beaffba3a70b4f543fe79"
  integrity sha1-Odzuv6+zldDWC+r/ujpwtPVD/nk=
  dependencies:
    debug "^4.1.1"
    http-errors "^1.7.3"
    resolve-path "^1.4.0"

koa-static@^5.0.0, koa-static@~5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa-static/download/koa-static-5.0.0.tgz#5e92fc96b537ad5219f425319c95b64772776943"
  integrity sha1-XpL8lrU3rVIZ9CUxnJW2R3J3aUM=
  dependencies:
    debug "^3.1.0"
    koa-send "^5.0.0"

koa@^2.13.3:
  version "2.15.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa/-/koa-2.15.3.tgz#062809266ee75ce0c75f6510a005b0e38f8c519a"
  integrity sha512-j/8tY9j5t+GVMLeioLaxweJiKUayFhlGqNTzf2ZGwL0ZCQijd2RLHK0SLW5Tsko8YyyqCZC2cojIb0/s62qTAg==
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.9.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

koa@~2.14.1:
  version "2.14.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/koa/download/koa-2.14.2.tgz#a57f925c03931c2b4d94b19d2ebf76d3244863fc"
  integrity sha1-pX+SXAOTHCtNlLGdLr920yRIY/w=
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.8.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lazy-cache/download/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/leven/download/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

lie@3.1.1:
  version "3.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lie/download/lie-3.1.1.tgz#9a436b2cc7746ca59de7a41fa469b3efb76bd87e"
  integrity sha1-mkNrLMd0bKWd56QfpGmz77dr2H4=
  dependencies:
    immediate "~3.0.5"

lie@~3.3.0:
  version "3.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lie/download/lie-3.3.0.tgz#dcf82dee545f46074daf200c7c1c5a08e0f40f6a"
  integrity sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o=
  dependencies:
    immediate "~3.0.5"

limiter@^1.0.5:
  version "1.1.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/limiter/download/limiter-1.1.5.tgz#8f92a25b3b16c6131293a0cc834b4a838a2aa7c2"
  integrity sha1-j5KiWzsWxhMSk6DMg0tKg4oqp8I=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

linkify-it@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/linkify-it/download/linkify-it-4.0.1.tgz#01f1d5e508190d06669982ba31a7d9f56a5751ec"
  integrity sha1-AfHV5QgZDQZmmYK6MafZ9WpXUew=
  dependencies:
    uc.micro "^1.0.1"

loadsh@0.0.4:
  version "0.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/loadsh/download/loadsh-0.0.4.tgz#5314babd12bb13315dde024a4ca70758c5489d2d"
  integrity sha1-UxS6vRK7EzFd3gJKTKcHWMVInS0=

localforage@^1.3.0:
  version "1.10.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/localforage/download/localforage-1.10.0.tgz#5c465dc5f62b2807c3a84c0c6a1b1b3212781dd4"
  integrity sha1-XEZdxfYrKAfDqEwMahsbMhJ4HdQ=
  dependencies:
    lie "3.1.1"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash-es/download/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash-unified@^1.0.2:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash-unified/download/lodash-unified-1.0.3.tgz#80b1eac10ed2eb02ed189f08614a29c27d07c894"
  integrity sha1-gLHqwQ7S6wLtGJ8IYUopwn0HyJQ=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.get/download/lodash.get-4.4.2.tgz#2d177f652fa31e939b4438d5341499dfa3825e99"
  integrity sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=

lodash.merge@^4.6.2, lodash.merge@~4.6.2:
  version "4.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.mergewith@^4.6.2:
  version "4.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.mergewith/download/lodash.mergewith-4.6.2.tgz#617121f89ac55f59047c7aec1ccd6654c6590f55"
  integrity sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=

lodash.set@^4.3.2:
  version "4.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.set/download/lodash.set-4.3.2.tgz#d8757b1da807dde24816b0d6a84bea1a76230b23"
  integrity sha1-2HV7HagH3eJIFrDWqEvqGnYjCyM=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.throttle/download/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash.uniqueid@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash.uniqueid/download/lodash.uniqueid-4.0.1.tgz#3268f26a7c88e4f4b1758d679271814e31fa5b26"
  integrity sha1-MmjyanyI5PSxdY1nknGBTjH6WyY=

lodash@4.17.20:
  version "4.17.20"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash/download/lodash-4.17.20.tgz#b44a9b6297bcb698f1c51a3545a2b3b368d59c52"
  integrity sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=

lodash@^4.17.11, lodash@^4.17.21, lodash@^4.17.4, lodash@^4.3.0:
  version "4.17.21"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

long@^5.2.3:
  version "5.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/long/download/long-5.2.3.tgz#a3ba97f3877cf1d778eccbcb048525ebb77499e1"
  integrity sha1-o7qX84d88dd47MvLBIUl67d0meE=

longest-streak@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/longest-streak/download/longest-streak-3.1.0.tgz#62fa67cd958742a1574af9f39866364102d90cd4"
  integrity sha1-YvpnzZWHQqFXSvnzmGY2QQLZDNQ=

longest@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/longest/download/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.1, lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lower-case/download/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

magic-string@^0.30.10, magic-string@^0.30.3:
  version "0.30.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/magic-string/-/magic-string-0.30.10.tgz#123d9c41a0cb5640c892b041d4cfb3bd0aa4b39e"
  integrity sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/make-dir/download/make-dir-4.0.0.tgz#c3c2307a771277cd9638305f915c29ae741b614e"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/makeerror/download/makeerror-1.0.12.tgz#3e5dd2079a82e812e983cc6610c4a2cb0eaa801a"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

mark.js@8.11.1:
  version "8.11.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mark.js/download/mark.js-8.11.1.tgz#180f1f9ebef8b0e638e4166ad52db879beb2ffc5"
  integrity sha1-GA8fnr74sOY45BZq1S24eb6y/8U=

markdown-it-container@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/markdown-it-container/download/markdown-it-container-3.0.0.tgz#1d19b06040a020f9a827577bb7dbf67aa5de9a5b"
  integrity sha1-HRmwYECgIPmoJ1d7t9v2eqXemls=

markdown-it@^13.0.2:
  version "13.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/markdown-it/-/markdown-it-13.0.2.tgz#1bc22e23379a6952e5d56217fbed881e0c94d536"
  integrity sha512-FtwnEuuK+2yVU7goGn/MJ0WBZMM9ZPgU9spqlFs7/A/pDIUNSOQZhUgOqYCficIuR2QaFnrt8LHqBWsbTAoI5w==
  dependencies:
    argparse "^2.0.1"
    entities "~3.0.1"
    linkify-it "^4.0.1"
    mdurl "^1.0.1"
    uc.micro "^1.0.5"

marked@0.4.0:
  version "0.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/marked/download/marked-0.4.0.tgz#9ad2c2a7a1791f10a852e0112f77b571dce10c66"
  integrity sha1-mtLCp6F5HxCoUuARL3e1cdzhDGY=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/md5.js/download/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

md5@^2.2.1:
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/md5/download/md5-2.3.0.tgz#c3da9a6aae3a30b46b7b0c349b87b110dc3bda4f"
  integrity sha1-w9qaaq46MLRreww0m4exENw72k8=
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

mdast-util-from-markdown@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.1.tgz#32a6e8f512b416e1f51eb817fc64bd867ebcd9cc"
  integrity sha512-aJEUyzZ6TzlsX2s5B4Of7lN7EQtAxvtradMMglCQDyaTFgse6CmtmdJ15ElnVRlCg1vpNyVtbem0PWzlNieZsA==
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    mdast-util-to-string "^4.0.0"
    micromark "^4.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"
    unist-util-stringify-position "^4.0.0"

mdast-util-frontmatter@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdast-util-frontmatter/-/mdast-util-frontmatter-2.0.1.tgz#f5f929eb1eb36c8a7737475c7eb438261f964ee8"
  integrity sha512-LRqI9+wdgC25P0URIJY9vwocIzCcksduHQ9OF2joxQoyTNVduwLAFUzjoopuRJbJAReaKrNQKAZKL3uCMugWJA==
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    escape-string-regexp "^5.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    micromark-extension-frontmatter "^2.0.0"

mdast-util-phrasing@^4.0.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz#7cc0a8dec30eaf04b7b1a9661a92adb3382aa6e3"
  integrity sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==
  dependencies:
    "@types/mdast" "^4.0.0"
    unist-util-is "^6.0.0"

mdast-util-to-markdown@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.0.tgz#9813f1d6e0cdaac7c244ec8c6dabfdb2102ea2b4"
  integrity sha512-SR2VnIEdVNCJbP6y7kVTJgPLifdr8WEU440fQec7qHoHOUz/oJ2jmNRqdDQ3rbiStOXb2mCDGTuwsK5OPUgYlQ==
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    longest-streak "^3.0.0"
    mdast-util-phrasing "^4.0.0"
    mdast-util-to-string "^4.0.0"
    micromark-util-decode-string "^2.0.0"
    unist-util-visit "^5.0.0"
    zwitch "^2.0.0"

mdast-util-to-string@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdast-util-to-string/download/mdast-util-to-string-4.0.0.tgz#7a5121475556a04e7eddeb67b264aae79d312814"
  integrity sha1-elEhR1VWoE5+3etnsmSq550xKBQ=
  dependencies:
    "@types/mdast" "^4.0.0"

mdurl@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mdurl/download/mdurl-1.0.1.tgz#fe85b2ec75a59037f2adfec100fd6c601761152e"
  integrity sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromark-core-commonmark@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-core-commonmark/-/micromark-core-commonmark-2.0.1.tgz#9a45510557d068605c6e9a80f282b2bb8581e43d"
  integrity sha512-CUQyKr1e///ZODyD1U3xit6zXwy1a8q2a1S1HKtIlmgvurrEpaw/Y9y6KSIbF8P59cn/NjzHyO+Q2fAyYLQrAA==
  dependencies:
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-factory-destination "^2.0.0"
    micromark-factory-label "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-factory-title "^2.0.0"
    micromark-factory-whitespace "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-html-tag-name "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-frontmatter@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-extension-frontmatter/-/micromark-extension-frontmatter-2.0.0.tgz#651c52ffa5d7a8eeed687c513cd869885882d67a"
  integrity sha512-C4AkuM3dA58cgZha7zVnuVxBhDsbttIMiytjgsM2XbHAB2faRVaHRle40558FBN+DJcrLNCoqG5mlrpdU4cRtg==
  dependencies:
    fault "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-destination@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-factory-destination/download/micromark-factory-destination-2.0.0.tgz#857c94debd2c873cba34e0445ab26b74f6a6ec07"
  integrity sha1-hXyU3r0shzy6NOBEWrJrdPam7Ac=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-label@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-factory-label/download/micromark-factory-label-2.0.0.tgz#17c5c2e66ce39ad6f4fc4cbf40d972f9096f726a"
  integrity sha1-F8XC5mzjmtb0/Ey/QNly+Qlvcmo=
  dependencies:
    devlop "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-space@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-factory-space/download/micromark-factory-space-2.0.0.tgz#5e7afd5929c23b96566d0e1ae018ae4fcf81d030"
  integrity sha1-Xnr9WSnCO5ZWbQ4a4BiuT8+B0DA=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-title@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-factory-title/download/micromark-factory-title-2.0.0.tgz#726140fc77892af524705d689e1cf06c8a83ea95"
  integrity sha1-cmFA/HeJKvUkcF1onhzwbIqD6pU=
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-whitespace@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-factory-whitespace/download/micromark-factory-whitespace-2.0.0.tgz#9e92eb0f5468083381f923d9653632b3cfb5f763"
  integrity sha1-npLrD1RoCDOB+SPZZTYys8+192M=
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-character@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-character/-/micromark-util-character-2.1.0.tgz#31320ace16b4644316f6bf057531689c71e2aee1"
  integrity sha512-KvOVV+X1yLBfs9dCBSopq/+G1PcgT3lAK07mC4BzXi5E7ahzMAF8oIupDDJ6mievI6F+lAATkbQQlQixJfT3aQ==
  dependencies:
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-chunked@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-chunked/download/micromark-util-chunked-2.0.0.tgz#e51f4db85fb203a79dbfef23fd41b2f03dc2ef89"
  integrity sha1-5R9NuF+yA6edv+8j/UGy8D3C74k=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-classify-character@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-classify-character/download/micromark-util-classify-character-2.0.0.tgz#8c7537c20d0750b12df31f86e976d1d951165f34"
  integrity sha1-jHU3wg0HULEt8x+G6XbR2VEWXzQ=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-combine-extensions@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-combine-extensions/download/micromark-util-combine-extensions-2.0.0.tgz#75d6ab65c58b7403616db8d6b31315013bfb7ee5"
  integrity sha1-ddarZcWLdANhbbjWsxMVATv7fuU=
  dependencies:
    micromark-util-chunked "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-decode-numeric-character-reference@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.1.tgz#2698bbb38f2a9ba6310e359f99fcb2b35a0d2bd5"
  integrity sha512-bmkNc7z8Wn6kgjZmVHOX3SowGmVdhYS7yBpMnuMnPzDq/6xwVA604DuOXMZTO1lvq01g+Adfa0pE2UKGlxL1XQ==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-decode-string@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-decode-string/download/micromark-util-decode-string-2.0.0.tgz#7dfa3a63c45aecaa17824e656bcdb01f9737154a"
  integrity sha1-ffo6Y8Ra7KoXgk5la82wH5c3FUo=
  dependencies:
    decode-named-character-reference "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-encode@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-encode/download/micromark-util-encode-2.0.0.tgz#0921ac7953dc3f1fd281e3d1932decfdb9382ab1"
  integrity sha1-CSGseVPcPx/SgePRky3s/bk4KrE=

micromark-util-html-tag-name@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-html-tag-name/download/micromark-util-html-tag-name-2.0.0.tgz#ae34b01cbe063363847670284c6255bb12138ec4"
  integrity sha1-rjSwHL4GM2OEdnAoTGJVuxITjsQ=

micromark-util-normalize-identifier@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-normalize-identifier/download/micromark-util-normalize-identifier-2.0.0.tgz#91f9a4e65fe66cc80c53b35b0254ad67aa431d8b"
  integrity sha1-kfmk5l/mbMgMU7NbAlStZ6pDHYs=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-resolve-all@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-resolve-all/download/micromark-util-resolve-all-2.0.0.tgz#189656e7e1a53d0c86a38a652b284a252389f364"
  integrity sha1-GJZW5+GlPQyGo4plKyhKJSOJ82Q=
  dependencies:
    micromark-util-types "^2.0.0"

micromark-util-sanitize-uri@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-sanitize-uri/download/micromark-util-sanitize-uri-2.0.0.tgz#ec8fbf0258e9e6d8f13d9e4770f9be64342673de"
  integrity sha1-7I+/Aljp5tjxPZ5HcPm+ZDQmc94=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-subtokenize@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-subtokenize/-/micromark-util-subtokenize-2.0.1.tgz#76129c49ac65da6e479c09d0ec4b5f29ec6eace5"
  integrity sha512-jZNtiFl/1aY73yS3UGQkutD0UbhTt68qnRpw2Pifmz5wV9h8gOVsN70v+Lq/f1rKaU/W8pxRe8y8Q9FX1AOe1Q==
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-symbol@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-symbol/download/micromark-util-symbol-2.0.0.tgz#12225c8f95edf8b17254e47080ce0862d5db8044"
  integrity sha1-EiJcj5Xt+LFyVORwgM4IYtXbgEQ=

micromark-util-types@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark-util-types/download/micromark-util-types-2.0.0.tgz#63b4b7ffeb35d3ecf50d1ca20e68fc7caa36d95e"
  integrity sha1-Y7S3/+s10+z1DRyiDmj8fKo22V4=

micromark@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromark/download/micromark-4.0.0.tgz#84746a249ebd904d9658cfabc1e8e5f32cbc6249"
  integrity sha1-hHRqJJ69kE2WWM+rwejl8yy8Ykk=
  dependencies:
    "@types/debug" "^4.0.0"
    debug "^4.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromatch@^4.0.4:
  version "4.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/micromatch/-/micromatch-4.0.7.tgz#33e8190d9fe474a9895525f5618eee136d46c2e5"
  integrity sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

"mime-db@>= 1.43.0 < 2":
  version "1.53.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mime-db/-/mime-db-1.53.0.tgz#3cb63cd820fc29896d9d4e8c32ab4fcd74ccb447"
  integrity sha512-oHlN/w+3MQ3rba9rqFr6V/ypF10LSkdwUysQL7GkXoTgIWeV+tcXGA852TBxH+gsh8UWoyhR1hKcoMJTuWflpg==

mime-db@~1.23.0:
  version "1.23.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mime-db/download/mime-db-1.23.0.tgz#a31b4070adaea27d732ea333740a64d0ec9a6659"
  integrity sha1-oxtAcK2uon1zLqMzdApk0OyaZlk=

mime-types@2.1.11:
  version "2.1.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mime-types/download/mime-types-2.1.11.tgz#c259c471bda808a85d6cd193b430a5fae4473b3c"
  integrity sha1-wlnEcb2oCKhdbNGTtDCl+uRHOzw=
  dependencies:
    mime-db "~1.23.0"

mime-types@^2.1.12, mime-types@^2.1.18, mime-types@^2.1.24, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-fn@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mimic-fn/download/mimic-fn-3.1.0.tgz#65755145bbf3e36954b949c16450427451d5ca74"
  integrity sha1-ZXVRRbvz42lUuUnBZFBCdFHVynQ=

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimatch/download/minimatch-5.1.6.tgz#1cfcb8cf5522ea69952cd2af95ae09477f122a96"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimatch/-/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.6:
  version "1.2.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

minisearch@^7.0.0:
  version "7.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/minisearch/-/minisearch-7.1.0.tgz#f5830e9109b5919ee7b291c29a304f381aa68770"
  integrity sha512-tv7c/uefWdEhcu6hvrfTihflgeEi2tN6VV7HJnCjK6VxM75QQJh4t9FwJCsA2EsRS8LCnu3W87CuGPWMocOLCA==

mitt@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mitt/download/mitt-2.1.0.tgz#f740577c23176c6205b121b2973514eade1b2230"
  integrity sha1-90BXfCMXbGIFsSGylzUU6t4bIjA=

mitt@^3.0.0, mitt@^3.0.1:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mitt/download/mitt-3.0.1.tgz#ea36cf0cc30403601ae074c8f77b7092cdab36d1"
  integrity sha1-6jbPDMMEA2Aa4HTI93twks2rNtE=

mkdirp@~0.5.1:
  version "0.5.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

moment@^2.15.1:
  version "2.30.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

mrmime@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mrmime/-/mrmime-2.0.0.tgz#151082a6e06e59a9a39b46b3e14d5cfe92b3abb4"
  integrity sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==

ms@2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3, ms@^2.1.1:
  version "2.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

nanoid@3.3.4:
  version "3.3.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/nanoid/download/nanoid-3.3.4.tgz#730b67e3cd09e2deacf03c027c81c9d9dbc5e8ab"
  integrity sha1-cwtn480J4t6s8DwCfIHJ2dvF6Ks=

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/nanoid/download/nanoid-3.3.7.tgz#d0c301a691bc8d54efa0a2226ccf3fe2fd656bd8"
  integrity sha1-0MMBppG8jVTvoKIibM8/4v1la9g=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

nedb@^1.8.0:
  version "1.8.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/nedb/download/nedb-1.8.0.tgz#0e3502cd82c004d5355a43c9e55577bd7bd91d88"
  integrity sha1-DjUCzYLABNU1WkPJ5VV3vXvZHYg=
  dependencies:
    async "0.2.10"
    binary-search-tree "0.2.5"
    localforage "^1.3.0"
    mkdirp "~0.5.1"
    underscore "~1.4.4"

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

next-tick@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/next-tick/download/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"
  integrity sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/no-case/download/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-easy-cert@^1.0.0:
  version "1.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-easy-cert/download/node-easy-cert-1.3.3.tgz#2f0279b5d88fd104303456c8667e3d284f17f3ca"
  integrity sha1-LwJ5tdiP0QQwNFbIZn49KE8X88o=
  dependencies:
    async-task-mgr "^1.1.0"
    colorful "^2.1.0"
    commander "^2.9.0"
    node-forge "^0.6.42"
    node-powershell "^3.3.1"

node-fetch@^2.6.1:
  version "2.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-fetch/-/node-fetch-2.7.0.tgz#d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@~2.6.9:
  version "2.6.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-fetch/-/node-fetch-2.6.13.tgz#a20acbbec73c2e09f9007de5cda17104122e0010"
  integrity sha512-StxNAxh15zr77QvvkmveSQ8uCQ4+v5FkvNTj0OESmiHu+VRi/gXArXtkWMElOsOUNLtUEvI4yS+rdtOHZTwlQA==
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^0.6.42:
  version "0.6.49"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-forge/download/node-forge-0.6.49.tgz#f1ee95d5d74623938fe19d698aa5a26d54d2f60f"
  integrity sha1-8e6V1ddGI5OP4Z1piqWibVTS9g8=

node-int64@^0.4.0, node-int64@~0.4.0:
  version "0.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-powershell@^3.3.1:
  version "3.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-powershell/download/node-powershell-3.3.1.tgz#bbf76b29f091ed83eae16ad9e7a180a13f36d113"
  integrity sha1-u/drKfCR7YPq4WrZ56GAoT820RM=
  dependencies:
    bluebird "^3.5.1"
    chalk "^2.1.0"

node-releases@^2.0.14:
  version "2.0.18"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-releases/-/node-releases-2.0.18.tgz#f010e8d35e2fe8d6b2944f03f70213ecedc4ca3f"
  integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==

node-stdlib-browser@^1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/node-stdlib-browser/-/node-stdlib-browser-1.2.0.tgz#5ddcfdf4063b88fb282979a1aa6ddab9728d5e4c"
  integrity sha512-VSjFxUhRhkyed8AtLwSCkMrJRfQ3e2lGtG3sP6FEgaLKBBbxM/dLfjRe1+iLhjvyLFW3tBQ8+c0pcOtXGbAZJg==
  dependencies:
    assert "^2.0.0"
    browser-resolve "^2.0.0"
    browserify-zlib "^0.2.0"
    buffer "^5.7.1"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    create-require "^1.1.1"
    crypto-browserify "^3.11.0"
    domain-browser "^4.22.0"
    events "^3.0.0"
    https-browserify "^1.0.0"
    isomorphic-timers-promises "^1.0.1"
    os-browserify "^0.3.0"
    path-browserify "^1.0.1"
    pkg-dir "^5.0.0"
    process "^0.11.10"
    punycode "^1.4.1"
    querystring-es3 "^0.2.1"
    readable-stream "^3.6.0"
    stream-browserify "^3.0.0"
    stream-http "^3.2.0"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.1"
    url "^0.11.0"
    util "^0.12.4"
    vm-browserify "^1.0.1"

nomnom@^1.5.x:
  version "1.8.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/nomnom/download/nomnom-1.8.1.tgz#2151f722472ba79e50a76fc125bb8c8f2e4dc2a7"
  integrity sha1-IVH3Ikcrp55Qp2/BJbuMjy5Nwqc=
  dependencies:
    chalk "~0.4.0"
    underscore "~1.6.0"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

numerify@^1.2.9:
  version "1.2.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/numerify/download/numerify-1.2.9.tgz#af4696bb1d57f8d3970a615d8b0cd53d932bd559"
  integrity sha1-r0aWux1X+NOXCmFdiwzVPZMr1Vk=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.1:
  version "1.13.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-inspect/-/object-inspect-1.13.2.tgz#dea0088467fb991e67af4058147a24824a3043ff"
  integrity sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==

object-is@^1.1.5:
  version "1.1.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-is/-/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.4:
  version "4.1.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/object.assign/-/object.assign-4.1.5.tgz#3a833f9ab7fdb80fc9e8d2300c803d216d8fdbb0"
  integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

on-finished@2.4.1, on-finished@^2.3.0:
  version "2.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

only@~0.0.2:
  version "0.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/only/download/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

open@^10.1.0:
  version "10.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/open/-/open-10.1.0.tgz#a7795e6e5d519abe4286d9937bb24b51122598e1"
  integrity sha512-mnkeQ1qP5Ue2wd+aivTD3NHd/lZ96Lu0jgf0pwktLPtx6cTZiH7tyeGRRHs0zX0rbrahXPnXlUnbeXyaBBuIaw==
  dependencies:
    default-browser "^5.2.1"
    define-lazy-prop "^3.0.0"
    is-inside-container "^1.0.0"
    is-wsl "^3.1.0"

open@^7.0.4:
  version "7.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/open/download/open-7.4.2.tgz#b8147e26dcf3e426316c730089fd71edd29c2321"
  integrity sha1-uBR+Jtzz5CYxbHMAif1x7dKcIyE=
  dependencies:
    is-docker "^2.0.0"
    is-wsl "^2.1.1"

optimist@~0.3:
  version "0.3.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/optimist/download/optimist-0.3.7.tgz#c90941ad59e4273328923074d2cf2e7cbc6ec0d9"
  integrity sha1-yQlBrVnkJzMokjB00s8ufLxuwNk=
  dependencies:
    wordwrap "~0.0.2"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.2, pako@~1.0.5:
  version "1.0.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pako/download/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/param-case/download/param-case-3.0.4.tgz#7d17fe4aa12bde34d4a77d91acfb6219caad01c5"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parse-asn1@^5.0.0, parse-asn1@^5.1.7:
  version "5.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/parse-asn1/-/parse-asn1-5.1.7.tgz#73cdaaa822125f9647165625eb45f8a051d2df06"
  integrity sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==
  dependencies:
    asn1.js "^4.10.1"
    browserify-aes "^1.2.0"
    evp_bytestokey "^1.0.3"
    hash-base "~3.0"
    pbkdf2 "^3.1.2"
    safe-buffer "^5.2.1"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@^1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.1, pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pascal-case/download/pascal-case-3.1.2.tgz#b48e0ef2b98e205e7c1dae747d0b1508237660eb"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-browserify/download/path-browserify-1.0.1.tgz#d98454a9c3753d5790860f16f68867b9e46be1fd"
  integrity sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^6.2.1:
  version "6.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/path-to-regexp/download/path-to-regexp-6.2.2.tgz#324377a83e5049cbecadc5554d6a63a9a4866b36"
  integrity sha1-MkN3qD5QScvsrcVVTWpjqaSGazY=

pbkdf2@^3.0.3, pbkdf2@^3.1.2:
  version "3.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pbkdf2/download/pbkdf2-3.1.2.tgz#dd822aa0887580e52f1a039dc3eda108efae3075"
  integrity sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

perfect-debounce@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/perfect-debounce/download/perfect-debounce-1.0.0.tgz#9c2e8bc30b169cc984a58b7d5b28049839591d2a"
  integrity sha1-nC6LwwsWnMmEpYt9WygEmDlZHSo=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.0.0, picocolors@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picocolors/-/picocolors-1.0.1.tgz#a8ad579b571952f0e5d25892de5445bcfe25aaa1"
  integrity sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pirates@^4.0.4:
  version "4.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pirates/-/pirates-4.0.6.tgz#3018ae32ecfcff6c29ba2267cbf21166ac1f36b9"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-dir@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pkg-dir/download/pkg-dir-5.0.0.tgz#a02d6aebe6ba133a928f74aec20bafdfe6b8e760"
  integrity sha1-oC1q6+a6EzqSj3Suwguv3+a452A=
  dependencies:
    find-up "^5.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pkg-up/download/pkg-up-3.1.0.tgz#100ec235cc150e4fd42519412596a28512a0def5"
  integrity sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU=
  dependencies:
    find-up "^3.0.0"

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz#89bb63c6fada2c3e90adc4a647beeeb39cc7bf8f"
  integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==

postcss@^8.4.38, postcss@^8.4.39:
  version "8.4.40"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss/-/postcss-8.4.40.tgz#eb81f2a4dd7668ed869a6db25999e02e9ad909d8"
  integrity sha512-YF2kKIUzAofPMpfH6hOi2cGnv/HrUlfucspc7pDyvv7kGdqXrfj8SCl/t8owkEgKEuu8ZcRjSOxFxVLqwChZ2Q==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.1"
    source-map-js "^1.2.0"

postcss@^8.4.40:
  version "8.4.41"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/postcss/-/postcss-8.4.41.tgz#d6104d3ba272d882fe18fc07d15dc2da62fa2681"
  integrity sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.1"
    source-map-js "^1.2.0"

preact@^10.0.0:
  version "10.23.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/preact/-/preact-10.23.1.tgz#d400107289bc979881c5212cb5f5cd22cd1dc38c"
  integrity sha512-O5UdRsNh4vdZaTieWe3XOgSpdMAmkIYBCT3VhQDlKrzyCm8lUYsk0fmVEvoQQifoOjFRTaHZO69ylrzTW2BH+A==

pretty-format@^28.1.3:
  version "28.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pretty-format/download/pretty-format-28.1.3.tgz#c9fba8cedf99ce50963a11b27d982a9ae90970d5"
  integrity sha1-yfuozt+ZzlCWOhGyfZgqmukJcNU=
  dependencies:
    "@jest/schemas" "^28.1.3"
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

pretty-format@^29.7.0:
  version "29.7.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pretty-format/-/pretty-format-29.7.0.tgz#ca42c758310f365bfa71a0bda0a807160b776812"
  integrity sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

private@^0.1.6:
  version "0.1.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/private/download/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"
  integrity sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

prom-client@^14.0.0:
  version "14.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prom-client/-/prom-client-14.2.0.tgz#ca94504e64156f6506574c25fb1c34df7812cf11"
  integrity sha512-sF308EhTenb/pDRPakm+WgiN+VdM/T1RaHj1x+MvAuT8UiQP8JmOEbxVqtkbfR4LrvOg5n7ic01kRBDGXjYikA==
  dependencies:
    tdigest "^0.1.1"

prom-client@~14.1.1:
  version "14.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prom-client/download/prom-client-14.1.1.tgz#e9bebef0e2269bfde22a322f4ca803cb52b4a0c0"
  integrity sha1-6b6+8OImm/3iKjIvTKgDy1K0oMA=
  dependencies:
    tdigest "^0.1.1"

promise@^7.0.1:
  version "7.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/promise/download/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prompts/download/prompts-2.4.2.tgz#7b57e73b3a48029ad10ebd44f74b01722a4cb069"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

psl@^1.1.28:
  version "1.9.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/psl/download/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
  integrity sha1-0N8qE38AeUVl/K87LADNCfjVpac=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/public-encrypt/download/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pug-attrs@^2.0.4:
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-attrs/download/pug-attrs-2.0.4.tgz#b2f44c439e4eb4ad5d4ef25cac20d18ad28cc336"
  integrity sha1-svRMQ55OtK1dTvJcrCDRitKMwzY=
  dependencies:
    constantinople "^3.0.1"
    js-stringify "^1.0.1"
    pug-runtime "^2.0.5"

pug-code-gen@^2.0.2:
  version "2.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-code-gen/download/pug-code-gen-2.0.3.tgz#122eb9ada9b5bf601705fe15aaa0a7d26bc134ab"
  integrity sha1-Ei65ram1v2AXBf4VqqCn0mvBNKs=
  dependencies:
    constantinople "^3.1.2"
    doctypes "^1.1.0"
    js-stringify "^1.0.1"
    pug-attrs "^2.0.4"
    pug-error "^1.3.3"
    pug-runtime "^2.0.5"
    void-elements "^2.0.1"
    with "^5.0.0"

pug-error@^1.3.3:
  version "1.3.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-error/download/pug-error-1.3.3.tgz#f342fb008752d58034c185de03602dd9ffe15fa6"
  integrity sha1-80L7AIdS1YA0wYXeA2At2f/hX6Y=

pug-filters@^3.1.1:
  version "3.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-filters/download/pug-filters-3.1.1.tgz#ab2cc82db9eeccf578bda89130e252a0db026aa7"
  integrity sha1-qyzILbnuzPV4vaiRMOJSoNsCaqc=
  dependencies:
    clean-css "^4.1.11"
    constantinople "^3.0.1"
    jstransformer "1.0.0"
    pug-error "^1.3.3"
    pug-walk "^1.1.8"
    resolve "^1.1.6"
    uglify-js "^2.6.1"

pug-lexer@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-lexer/download/pug-lexer-4.1.0.tgz#531cde48c7c0b1fcbbc2b85485c8665e31489cfd"
  integrity sha1-UxzeSMfAsfy7wrhUhchmXjFInP0=
  dependencies:
    character-parser "^2.1.1"
    is-expression "^3.0.0"
    pug-error "^1.3.3"

pug-linker@^3.0.6:
  version "3.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-linker/download/pug-linker-3.0.6.tgz#f5bf218b0efd65ce6670f7afc51658d0f82989fb"
  integrity sha1-9b8hiw79Zc5mcPevxRZY0Pgpifs=
  dependencies:
    pug-error "^1.3.3"
    pug-walk "^1.1.8"

pug-load@^2.0.12:
  version "2.0.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-load/download/pug-load-2.0.12.tgz#d38c85eb85f6e2f704dea14dcca94144d35d3e7b"
  integrity sha1-04yF64X24vcE3qFNzKlBRNNdPns=
  dependencies:
    object-assign "^4.1.0"
    pug-walk "^1.1.8"

pug-parser@^5.0.1:
  version "5.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-parser/download/pug-parser-5.0.1.tgz#03e7ada48b6840bd3822f867d7d90f842d0ffdc9"
  integrity sha1-A+etpItoQL04Ivhn19kPhC0P/ck=
  dependencies:
    pug-error "^1.3.3"
    token-stream "0.0.1"

pug-runtime@^2.0.5:
  version "2.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-runtime/download/pug-runtime-2.0.5.tgz#6da7976c36bf22f68e733c359240d8ae7a32953a"
  integrity sha1-baeXbDa/IvaOczw1kkDYrnoylTo=

pug-strip-comments@^1.0.4:
  version "1.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-strip-comments/download/pug-strip-comments-1.0.4.tgz#cc1b6de1f6e8f5931cf02ec66cdffd3f50eaf8a8"
  integrity sha1-zBtt4fbo9ZMc8C7GbN/9P1Dq+Kg=
  dependencies:
    pug-error "^1.3.3"

pug-walk@^1.1.8:
  version "1.1.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug-walk/download/pug-walk-1.1.8.tgz#b408f67f27912f8c21da2f45b7230c4bd2a5ea7a"
  integrity sha1-tAj2fyeRL4wh2i9FtyMMS9Kl6no=

pug@^2.0.0-beta6:
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pug/download/pug-2.0.4.tgz#ee7682ec0a60494b38d48a88f05f3b0ac931377d"
  integrity sha1-7naC7ApgSUs41IqI8F87CskxN30=
  dependencies:
    pug-code-gen "^2.0.2"
    pug-filters "^3.1.1"
    pug-lexer "^4.1.0"
    pug-linker "^3.0.6"
    pug-load "^2.0.12"
    pug-parser "^5.0.1"
    pug-runtime "^2.0.5"
    pug-strip-comments "^1.0.4"

punycode@^1.4.1:
  version "1.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

pure-rand@^6.0.0:
  version "6.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/pure-rand/-/pure-rand-6.1.0.tgz#d173cf23258231976ccbdb05247c9787957604f2"
  integrity sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==

q@^1.5.0:
  version "1.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qiankun@2.4.0:
  version "2.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qiankun/download/qiankun-2.4.0.tgz#e0c847fcee1e79c167899bfcb7441a23590c5430"
  integrity sha1-4MhH/O4eecFniZv8t0QaI1kMVDA=
  dependencies:
    "@babel/runtime" "^7.10.5"
    import-html-entry "^1.9.0"
    lodash "^4.17.11"
    single-spa "5.8.1"
    tslib "^1.10.0"

qr-code-styling@^1.6.0-rc.1:
  version "1.6.0-rc.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qr-code-styling/download/qr-code-styling-1.6.0-rc.1.tgz#6c89e185fa50cc9135101085c12ae95b06f1b290"
  integrity sha1-bInhhfpQzJE1EBCFwSrpWwbxspA=
  dependencies:
    qrcode-generator "^1.4.3"

qrcode-generator@^1.4.3:
  version "1.4.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qrcode-generator/download/qrcode-generator-1.4.4.tgz#63f771224854759329a99048806a53ed278740e7"
  integrity sha1-Y/dxIkhUdZMpqZBIgGpT7SeHQOc=

qrcode-npm@0.0.3:
  version "0.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qrcode-npm/download/qrcode-npm-0.0.3.tgz#77ee6fbefa9c0f29fa09d4d1520807c6a6042b9a"
  integrity sha1-d+5vvvqcDyn6CdTRUggHxqYEK5o=

qs@6.11.0:
  version "6.11.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qs/download/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=
  dependencies:
    side-channel "^1.0.4"

qs@^6.11.2, qs@^6.5.2:
  version "6.12.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qs/-/qs-6.12.3.tgz#e43ce03c8521b9c7fd7f1f13e514e5ca37727754"
  integrity sha512-AWJm14H1vVaO/iNZ4/hO+HyaTehuy9nRqVdkTqlJt0HWvBiBIEXFmb4C0DGeYo3Xes9rrEW+TxHsaigCbN5ICQ==
  dependencies:
    side-channel "^1.0.6"

qs@~6.5.2:
  version "6.5.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

query-string@^9.1.0:
  version "9.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/query-string/-/query-string-9.1.0.tgz#5f12a4653a4ba56021e113b5cf58e56581823e7a"
  integrity sha512-t6dqMECpCkqfyv2FfwVS1xcB6lgXW/0XZSaKdsCNGYkqMO76AFiJEg4vINzoDKcZa6MS7JX+OHIjwh06K5vczw==
  dependencies:
    decode-uri-component "^0.4.1"
    filter-obj "^5.1.0"
    split-on-first "^3.0.0"

querystring-es3@^0.2.1:
  version "0.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.2, raw-body@^2.3.3:
  version "2.5.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/raw-body/download/raw-body-2.5.2.tgz#99febd83b90e08975087e8f1f9419a149366b68a"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rd@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/rd/download/rd-2.0.1.tgz#e18a8af5b2f7440c0db1523ca04c6e0f9660003f"
  integrity sha1-4YqK9bL3RAwNsVI8oExuD5ZgAD8=
  dependencies:
    "@types/node" "^10.3.6"

react-is@^16.13.1:
  version "16.13.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^18.0.0:
  version "18.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/react-is/-/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

readable-stream@^2.3.8, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.5.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

reflect-metadata@^0.1.13, reflect-metadata@~0.1.13:
  version "0.1.14"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/reflect-metadata/download/reflect-metadata-0.1.14.tgz#24cf721fe60677146bb77eeb0e1f9dece3d65859"
  integrity sha1-JM9yH+YGdxRrt37rDh+d7OPWWFk=

reflect-metadata@^0.2.1:
  version "0.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/reflect-metadata/-/reflect-metadata-0.2.2.tgz#400c845b6cba87a21f2c65c4aeb158f4fa4d9c5b"
  integrity sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q==

regenerate@^1.2.1:
  version "1.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz#356ade10263f685dda125100cd862c1db895327f"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regenerator-transform/download/regenerator-transform-0.10.1.tgz#1e4996837231da8b7f3cf4114d71b5691a0680dd"
  integrity sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0=
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regexpu-core/download/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  integrity sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regjsgen/download/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"
  integrity sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/regjsparser/download/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  integrity sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=
  dependencies:
    jsesc "~0.5.0"

remark-frontmatter@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/remark-frontmatter/-/remark-frontmatter-5.0.0.tgz#b68d61552a421ec412c76f4f66c344627dc187a2"
  integrity sha512-XTFYvNASMe5iPN0719nPrdItC9aU0ssC4v14mH1BCi1u0n1gAocqcujWUrByftZTbLhRtiKRyjYTSIOcr69UVQ==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-frontmatter "^2.0.0"
    micromark-extension-frontmatter "^2.0.0"
    unified "^11.0.0"

remark-parse@^11.0.0:
  version "11.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/remark-parse/-/remark-parse-11.0.0.tgz#aa60743fcb37ebf6b069204eb4da304e40db45a1"
  integrity sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    micromark-util-types "^2.0.0"
    unified "^11.0.0"

remark-stringify@^11.0.0:
  version "11.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/remark-stringify/-/remark-stringify-11.0.0.tgz#4c5b01dd711c269df1aaae11743eb7e2e7636fd3"
  integrity sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-to-markdown "^2.0.0"
    unified "^11.0.0"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request@^2.74.0, request@^2.88.2:
  version "2.88.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve-cwd/download/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-path@^1.4.0:
  version "1.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve-path/download/resolve-path-1.4.0.tgz#c4bda9f5efb2fce65247873ab36bb4d834fe16f7"
  integrity sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=
  dependencies:
    http-errors "~1.6.2"
    path-is-absolute "1.0.1"

resolve.exports@^2.0.0:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve.exports/download/resolve.exports-2.0.2.tgz#f8c934b8e6a13f539e38b7098e2e36134f01e800"
  integrity sha1-+Mk0uOahP1OeOLcJji42E08B6AA=

resolve@^1.1.6, resolve@^1.17.0, resolve@^1.20.0:
  version "1.22.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/resolve/-/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.2.0:
  version "0.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ret/download/ret-0.2.2.tgz#b6861782a1f4762dce43402a71eb7a283f44573c"
  integrity sha1-toYXgqH0di3OQ0Aqcet6KD9EVzw=

rfdc@^1.4.1:
  version "1.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/rfdc/-/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/right-align/download/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rollup@^4.13.0:
  version "4.19.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/rollup/-/rollup-4.19.0.tgz#83b08cc0b2bc38c26c194cb7f2cdabd84a2a8c02"
  integrity sha512-5r7EYSQIowHsK4eTZ0Y81qpZuJz+MUuYeqmmYmRMl1nwhdmbiYqt5jwzf6u7wyOzJgYqtCRMtVRKOtHANBz7rA==
  dependencies:
    "@types/estree" "1.0.5"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.19.0"
    "@rollup/rollup-android-arm64" "4.19.0"
    "@rollup/rollup-darwin-arm64" "4.19.0"
    "@rollup/rollup-darwin-x64" "4.19.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.19.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.19.0"
    "@rollup/rollup-linux-arm64-gnu" "4.19.0"
    "@rollup/rollup-linux-arm64-musl" "4.19.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.19.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.19.0"
    "@rollup/rollup-linux-s390x-gnu" "4.19.0"
    "@rollup/rollup-linux-x64-gnu" "4.19.0"
    "@rollup/rollup-linux-x64-musl" "4.19.0"
    "@rollup/rollup-win32-arm64-msvc" "4.19.0"
    "@rollup/rollup-win32-ia32-msvc" "4.19.0"
    "@rollup/rollup-win32-x64-msvc" "4.19.0"
    fsevents "~2.3.2"

run-applescript@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/run-applescript/-/run-applescript-7.0.0.tgz#e5a553c2bffd620e169d276c1cd8f1b64778fbeb"
  integrity sha512-9by4Ij99JUr/MCFBUkDKLWK3G9HVXmabKz9U5MlIAIuvuzkiOicRYs8XJLxX+xahD+mLiiCYDqF9dKAgtzKP1A==

run-async@^2.2.0:
  version "2.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

rxjs@^5.5.2:
  version "5.5.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/rxjs/download/rxjs-5.5.12.tgz#6fa61b8a77c3d793dbaf270bee2f43f652d741cc"
  integrity sha1-b6YbinfD15PbrycL7i9D9lLXQcw=
  dependencies:
    symbol-observable "1.0.1"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex2@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safe-regex2/download/safe-regex2-2.0.0.tgz#b287524c397c7a2994470367e0185e1916b1f5b9"
  integrity sha1-sodSTDl8eimURwNn4BheGRax9bk=
  dependencies:
    ret "~0.2.0"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@~1.3.0:
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sax/-/sax-1.3.0.tgz#a5dbe77db3be05c9d1ee7785dbd3ea9de51593d0"
  integrity sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==

select@^1.1.2:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/select/download/select-1.1.2.tgz#0e7350acdec80b1108528786ec1d4418d11b396d"
  integrity sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0=

semver-store@^0.3.0:
  version "0.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver-store/download/semver-store-0.3.0.tgz#ce602ff07df37080ec9f4fb40b29576547befbe9"
  integrity sha1-zmAv8H3zcIDsn0+0CylXZUe+++k=

semver@^5.3.0:
  version "5.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.0.0, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.4, semver@^7.5.3, semver@^7.5.4:
  version "7.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/semver/-/semver-7.6.3.tgz#980f7b5550bc175fb4dc09403085627f9eb33143"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

send@0.18.0:
  version "0.18.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/send/download/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  integrity sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.0, serialize-javascript@~6.0.1:
  version "6.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/serialize-javascript/-/serialize-javascript-6.0.2.tgz#defa1e055c83bf6d59ea805d8da862254eb6a6c2"
  integrity sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==
  dependencies:
    randombytes "^2.1.0"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/serve-static/download/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  integrity sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setimmediate@^1.0.4, setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shared@^1.0.0, shared@^1.8.5:
  version "1.9.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shared/download/shared-1.9.1.tgz#3a926afe42f193186ccb9b1214c46441f2c87c26"
  integrity sha1-OpJq/kLxkxhsy5sSFMRkQfLIfCY=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shiki@1.11.1, shiki@^1.10.3:
  version "1.11.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/shiki/-/shiki-1.11.1.tgz#6c06c5fcf55f1dac2db2596af935fef6a41a209d"
  integrity sha512-VHD3Q0EBXaaa245jqayBe5zQyMQUdXBFjmGr9MpDaDpAKRMYn7Ff00DM5MLk26UyKjnml3yQ0O2HNX7PtYVNFQ==
  dependencies:
    "@shikijs/core" "1.11.1"
    "@types/hast" "^3.0.4"

side-channel@^1.0.4, side-channel@^1.0.6:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/side-channel/-/side-channel-1.0.6.tgz#abd25fb7cd24baf45466406b1096b7831c9215f2"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

single-spa@5.8.1:
  version "5.8.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/single-spa/download/single-spa-5.8.1.tgz#86c2575e297e31d8f06945944ec97e31851a59ae"
  integrity sha1-hsJXXil+MdjwaUWUTsl+MYUaWa4=

sirv@^2.0.4:
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sirv/-/sirv-2.0.4.tgz#5dd9a725c578e34e449f332703eb2a74e46a29b0"
  integrity sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sisteransi/download/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

sortablejs@1.10.2:
  version "1.10.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sortablejs/download/sortablejs-1.10.2.tgz#6e40364d913f98b85a14f6678f92b5c1221f5290"
  integrity sha1-bkA2TZE/mLhaFPZnj5K1wSIfUpA=

sortablejs@1.14.0:
  version "1.14.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sortablejs/download/sortablejs-1.14.0.tgz#6d2e17ccbdb25f464734df621d4f35d4ab35b3d8"
  integrity sha1-bS4XzL2yX0ZHNN9iHU811Ks1s9g=

sortablejs@^1.15.0:
  version "1.15.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sortablejs/-/sortablejs-1.15.2.tgz#4e9f7bda4718bd1838add9f1866ec77169149809"
  integrity sha512-FJF5jgdfvoKn1MAKSdGs33bIqLi3LmsgVTliuX6iITj834F+JRQZN90Z93yql8h0K2t0RwDPBmxwlbZfDcxNZA==

source-map-js@^1.2.0:
  version "1.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map-js/-/source-map-js-1.2.0.tgz#16b809c162517b5b8c3e7dcd315a2a5c2612b2af"
  integrity sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map-support/download/source-map-support-0.5.13.tgz#31b24a9c2e73c2de85066c0feb7d44767ed52932"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0:
  version "0.6.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map/download/source-map-0.7.4.tgz#a9bbe705c9d8846f4e08ff6765acf0f1b0898656"
  integrity sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=

source-map@~0.5.1:
  version "0.5.7"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

spark-md5@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/spark-md5/download/spark-md5-2.0.2.tgz#37b763847763ae7e7acef2ca5233d01e649a78b7"
  integrity sha1-N7djhHdjrn56zvLKUjPQHmSaeLc=

speakingurl@^14.0.1:
  version "14.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/speakingurl/download/speakingurl-14.0.1.tgz#f37ec8ddc4ab98e9600c1c9ec324a8c48d772a53"
  integrity sha1-837I3cSrmOlgDByewySoxI13KlM=

split-on-first@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/split-on-first/download/split-on-first-3.0.0.tgz#f04959c9ea8101b9b0bbf35a61b9ebea784a23e7"
  integrity sha1-8ElZyeqBAbmwu/NaYbnr6nhKI+c=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.18.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/sshpk/-/sshpk-1.18.0.tgz#1663e55cddf4d688b86a46b77f0d5fe363aba028"
  integrity sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stack-utils/download/stack-utils-2.0.6.tgz#aaf0748169c02fc33c8232abccf933f54a1cc34f"
  integrity sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=
  dependencies:
    escape-string-regexp "^2.0.0"

statuses@2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.5.0:
  version "1.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stream-browserify@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stream-browserify/download/stream-browserify-3.0.0.tgz#22b0a2850cdf6503e73085da1fc7b7d0c2122f2f"
  integrity sha1-IrCihQzfZQPnMIXaH8e30MISLy8=
  dependencies:
    inherits "~2.0.4"
    readable-stream "^3.5.0"

stream-http@^3.2.0:
  version "3.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stream-http/download/stream-http-3.2.0.tgz#1872dfcf24cb15752677e40e5c3f9cc1926028b5"
  integrity sha1-GHLfzyTLFXUmd+QOXD+cwZJgKLU=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    xtend "^4.0.2"

stream-throttle@^0.1.3:
  version "0.1.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stream-throttle/download/stream-throttle-0.1.3.tgz#add57c8d7cc73a81630d31cd55d3961cfafba9c3"
  integrity sha1-rdV8jXzHOoFjDTHNVdOWHPr7qcM=
  dependencies:
    commander "^2.2.0"
    limiter "^1.0.5"

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string-length/download/string-length-4.0.2.tgz#a8a8dc7bd5c1a82b9b3c8b87e125f66871b6e57a"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^2.1.0:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@~0.1.0:
  version "0.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-ansi/download/strip-ansi-0.1.1.tgz#39e8a98d044d150660abe4a6808acf70bb7bc991"
  integrity sha1-OeipjQRNFQZgq+SmgIrPcLt7yZE=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-bom/download/strip-bom-4.0.0.tgz#9c3505c1db45bcedca3d9cf7a16f5c5aa3901878"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/strnum/download/strnum-1.0.5.tgz#5c4e829fe15ad4ff0d20c3db5ac97b73c9b072db"
  integrity sha1-XE6Cn+Fa1P8NIMPbWsl7c8mwcts=

stylus@^0.63.0:
  version "0.63.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/stylus/-/stylus-0.63.0.tgz#511e8d56f2005b09010fbc1f62561c7b6f72a490"
  integrity sha512-OMlgrTCPzE/ibtRMoeLVhOY0RcNuNWh0rhAVqeKnk/QwcuUKQbnqhZ1kg2vzD8VU/6h3FoPTq4RJPHgLBvX6Bw==
  dependencies:
    "@adobe/css-tools" "~4.3.3"
    debug "^4.3.2"
    glob "^7.1.6"
    sax "~1.3.0"
    source-map "^0.7.3"

superjson@^2.2.1:
  version "2.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/superjson/-/superjson-2.2.1.tgz#9377a7fa80fedb10c851c9dbffd942d4bcf79733"
  integrity sha512-8iGv75BYOa0xRJHK5vRLEjE2H/i4lulTjzpUXic3Eg8akftYjkmQDa8JARQ42rlczXyFR3IeRoeFCc7RxHsYZA==
  dependencies:
    copy-anything "^3.0.2"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-color/download/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-inline-react@^1.0.2:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/svg-inline-react/download/svg-inline-react-1.0.3.tgz#68d15bf88f99f64daa52821ed5441612d8ad9041"
  integrity sha1-aNFb+I+Z9k2qUoIe1UQWEtitkEE=

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

swiper@^11.0.5:
  version "11.1.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/swiper/-/swiper-11.1.9.tgz#55505c7cf4723b678df8220fc06152b793585dbc"
  integrity sha512-rflu8zvfGa3x1v/aeSufk4zRJffhOQowyvtJlp46sUBnOqAuk1Rdv5Ldj0AWWBV595iZ+ZMk7VB35ZRtRUomtA==

symbol-observable@1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/symbol-observable/download/symbol-observable-1.0.1.tgz#8340fc4702c3122df5d22288f88283f513d3fdd4"
  integrity sha1-g0D8RwLDEi310iKI+IKD9RPT/dQ=

tabbable@^6.2.0:
  version "6.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tabbable/download/tabbable-6.2.0.tgz#732fb62bc0175cfcec257330be187dcfba1f3b97"
  integrity sha1-cy+2K8AXXPzsJXMwvhh9z7ofO5c=

tdigest@^0.1.1:
  version "0.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tdigest/download/tdigest-0.1.2.tgz#96c64bac4ff10746b910b0e23b515794e12faced"
  integrity sha1-lsZLrE/xB0a5ELDiO1FXlOEvrO0=
  dependencies:
    bintrees "1.0.2"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/test-exclude/download/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/text-segmentation/download/text-segmentation-1.0.3.tgz#52a388159efffe746b24a63ba311b6ac9f2d7943"
  integrity sha1-UqOIFZ7//nRrJKY7oxG2rJ8teUM=
  dependencies:
    utrie "^1.0.2"

three@^0.164.1:
  version "0.164.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/three/-/three-0.164.1.tgz#b742f76bd8dfd3736ba0d86a12dfddb73c5cdcc0"
  integrity sha512-iC/hUBbl1vzFny7f5GtqzVXYjMJKaTPxiCxXfrvVdBi1Sf+jhd1CAkitiFwC7mIBFCo3MrDLJG97yisoaWig0w==

thrift@~0.18.1:
  version "0.18.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/thrift/download/thrift-0.18.1.tgz#b17946f79fd220c7b1e839d956ef63071ec6982b"
  integrity sha1-sXlG95/SIMex6DnZVu9jBx7GmCs=
  dependencies:
    browser-or-node "^1.2.1"
    isomorphic-ws "^4.0.1"
    node-int64 "^0.4.0"
    q "^1.5.0"
    ws "^5.2.3"

through@^2.3.6:
  version "2.3.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunkify@^2.1.2:
  version "2.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/thunkify/download/thunkify-2.1.2.tgz#faa0e9d230c51acc95ca13a361ac05ca7e04553d"
  integrity sha1-+qDp0jDFGsyVyhOjYawFyn4EVT0=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/timers-browserify/download/timers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

tiny-emitter@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tiny-emitter/download/tiny-emitter-2.1.0.tgz#1d1a56edfc51c43e863cbb5382a72330e3555423"
  integrity sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tmpl/download/tmpl-1.0.5.tgz#8683e0b902bb9c20c4f726e3c0b69f36518c07cc"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/to-fast-properties/download/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

token-stream@0.0.1:
  version "0.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/token-stream/download/token-stream-0.0.1.tgz#ceeefc717a76c4316f126d0b9dbaa55d7e7df01a"
  integrity sha1-zu78cXp2xDFvEm0LnbqlXX598Bo=

totalist@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/totalist/download/totalist-3.0.1.tgz#ba3a3d600c915b1a97872348f79c127475f6acf8"
  integrity sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

trough@^2.0.0:
  version "2.2.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/trough/-/trough-2.2.0.tgz#94a60bd6bd375c152c1df911a4b11d5b0256f50f"
  integrity sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==

tslib@2.3.0:
  version "2.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-2.3.0.tgz#803b8cdab3e12ba581a4ca41c8839bbb0dacb09e"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tslib@^1.10.0:
  version "1.14.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.3:
  version "2.6.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tslib/-/tslib-2.6.3.tgz#0438f810ad7a9edcde7a241c3d80db693c8cbfe0"
  integrity sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==

tsscmp@1.0.6:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tsscmp/download/tsscmp-1.0.6.tgz#85b99583ac3589ec4bfef825b5000aa911d605eb"
  integrity sha1-hbmVg6w1iexL/vgltQAKqRHWBes=

tty-browserify@0.0.1:
  version "0.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tty-browserify/download/tty-browserify-0.0.1.tgz#3f05251ee17904dfd0677546670db9651682b811"
  integrity sha1-PwUlHuF5BN/QZ3VGZw25ZRaCuBE=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/type-detect/download/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-is@^1.6.16, type-is@^1.6.18, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^2.7.2:
  version "2.7.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/type/-/type-2.7.3.tgz#436981652129285cc3ba94f392886c2637ea0486"
  integrity sha512-8j+1QmAbPvLZow5Qpi6NCaN8FB60p/6x8/vfNqOk/hC+HuvFZhL4+WfekuhQLiqFZXOgQdrs3B+XxEmCc6b3FQ==

typescript@4.5.2:
  version "4.5.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/typescript/download/typescript-4.5.2.tgz#8ac1fba9f52256fdb06fb89e4122fa6a346c2998"
  integrity sha1-isH7qfUiVv2wb7ieQSL6ajRsKZg=

ua-parser-js@^0.7.23:
  version "0.7.38"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ua-parser-js/-/ua-parser-js-0.7.38.tgz#f497d8a4dc1fec6e854e5caa4b2f9913422ef054"
  integrity sha512-fYmIy7fKTSFAhG3fuPlubeGaMoAd6r0rSnfEsO5nEY55i26KSLt9EH7PLQiiqPUhNqYIJvSkTy1oArIcXAbPbA==

ua-parser-js@^1.0.35:
  version "1.0.38"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ua-parser-js/-/ua-parser-js-1.0.38.tgz#66bb0c4c0e322fe48edfe6d446df6042e62f25e2"
  integrity sha512-Aq5ppTOfvrCMgAPneW1HfWj66Xi7XL+/mIy996R1/CLS/rcyJQm6QZdsKrUeivDFQ+Oc9Wyuwor8Ze8peEoUoQ==

uc.micro@^1.0.1, uc.micro@^1.0.5:
  version "1.0.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uc.micro/download/uc.micro-1.0.6.tgz#9c411a802a409a91fc6cf74081baba34b24499ac"
  integrity sha1-nEEagCpAmpH8bPdAgbq6NLJEmaw=

uglify-js@^2.6.1:
  version "2.8.29"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uglify-js/download/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  integrity sha1-KcVzMUgFe7Th913zW3qcty5qWd0=
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-js@~1.2:
  version "1.2.6"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uglify-js/download/uglify-js-1.2.6.tgz#d354b2d3c1cf10ebc18fa78c11a28bdd9ce1580d"
  integrity sha1-01Sy08HPEOvBj6eMEaKL3ZzhWA0=

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

underscore@~1.4.4:
  version "1.4.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/underscore/download/underscore-1.4.4.tgz#61a6a32010622afa07963bf325203cf12239d604"
  integrity sha1-YaajIBBiKvoHljvzJSA88SI51gQ=

underscore@~1.6.0:
  version "1.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/underscore/download/underscore-1.6.0.tgz#8b38b10cacdef63337b8b24e4ff86d45aea529a8"
  integrity sha1-izixDKze9jM3uLJOT/htRa6lKag=

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/undici-types/-/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unhead@1.6.2:
  version "1.6.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unhead/-/unhead-1.6.2.tgz#901903f9ad85d723224654e1ce8aec7bbf14f82d"
  integrity sha512-vAkjsjs6SPq/LHzuxOgyNvR4LsGau3CRKke9A6ErQEFm8mNKf50WIyd//ZNEBx8Zg+Iyn7FHyYZdgzpdPwC+Mw==
  dependencies:
    "@unhead/dom" "1.6.2"
    "@unhead/schema" "1.6.2"
    "@unhead/shared" "1.6.2"
    hookable "^5.5.3"

unidragger@^2.4.0:
  version "2.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unidragger/download/unidragger-2.4.0.tgz#4cd7e564317af0ef42632d5984a82d4ae6314d8d"
  integrity sha1-TNflZDF68O9CYy1ZhKgtSuYxTY0=
  dependencies:
    unipointer "^2.4.0"

unified@^11.0.0, unified@^11.0.4:
  version "11.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unified/-/unified-11.0.5.tgz#f66677610a5c0a9ee90cab2b8d4d66037026d9e1"
  integrity sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==
  dependencies:
    "@types/unist" "^3.0.0"
    bail "^2.0.0"
    devlop "^1.0.0"
    extend "^3.0.0"
    is-plain-obj "^4.0.0"
    trough "^2.0.0"
    vfile "^6.0.0"

unipointer@^2.4.0:
  version "2.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unipointer/download/unipointer-2.4.0.tgz#ac7316aff6170ff87a4b008e55e842fb4bf13181"
  integrity sha1-rHMWr/YXD/h6SwCOVehC+0vxMYE=
  dependencies:
    ev-emitter "^1.0.1"

unist-util-is@^6.0.0:
  version "6.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unist-util-is/download/unist-util-is-6.0.0.tgz#b775956486aff107a9ded971d996c173374be424"
  integrity sha1-t3WVZIav8Qep3tlx2ZbBczdL5CQ=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-stringify-position@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unist-util-stringify-position/download/unist-util-stringify-position-4.0.0.tgz#449c6e21a880e0855bf5aabadeb3a740314abac2"
  integrity sha1-RJxuIaiA4IVb9aq63rOnQDFKusI=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-visit-parents@^6.0.0:
  version "6.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unist-util-visit-parents/download/unist-util-visit-parents-6.0.1.tgz#4d5f85755c3b8f0dc69e21eca5d6d82d22162815"
  integrity sha1-TV+FdVw7jw3GniHspdbYLSIWKBU=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"

unist-util-visit@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unist-util-visit/download/unist-util-visit-5.0.0.tgz#a7de1f31f72ffd3519ea71814cccf5fd6a9217d6"
  integrity sha1-p94fMfcv/TUZ6nGBTMz1/WqSF9Y=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

update-browserslist-db@^1.1.0:
  version "1.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/update-browserslist-db/-/update-browserslist-db-1.1.0.tgz#7ca61c0d8650766090728046e416a8cde682859e"
  integrity sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==
  dependencies:
    escalade "^3.1.2"
    picocolors "^1.0.1"

upper-case@^2.0.1:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/upper-case/download/upper-case-2.0.2.tgz#d89810823faab1df1549b7d97a76f8662bae6f7a"
  integrity sha1-2JgQgj+qsd8VSbfZenb4Ziuub3o=
  dependencies:
    tslib "^2.0.3"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

url-parse@1.5.10, url-parse@^1.5.10:
  version "1.5.10"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/url/-/url-0.11.3.tgz#6f495f4b935de40ce4a0a52faee8954244f3d3ad"
  integrity sha512-6hxOLGfZASQK/cijlZnZJTq8OXAkt/3YGfQX45vvMYXpZoo8NdWZcY73K108Jf759lS1Bv/8wXnHDTSz17dSRw==
  dependencies:
    punycode "^1.4.1"
    qs "^6.11.2"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.12.4, util@^0.12.5:
  version "0.12.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/util/download/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha1-XxemBZtz22GodWaHgaHCsTa9b7w=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

utrie@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/utrie/download/utrie-1.0.2.tgz#d42fe44de9bc0119c25de7f564a6ed1b2c87a645"
  integrity sha1-1C/kTem8ARnCXef1ZKbtGyyHpkU=
  dependencies:
    base64-arraybuffer "^1.0.2"

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^7.0.2:
  version "7.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-7.0.3.tgz#c5c9f2c8cf25dc0a372c4df1441c41f5bd0c680b"
  integrity sha1-xcnyyM8l3Ao3LE3xRBxB9b0MaAs=

uuid@^8.1.0, uuid@^8.3.1, uuid@^8.3.2:
  version "8.3.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz#b9572abfa62bd556c16d75fdebc1a411d5ff3175"
  integrity sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

vary@^1.1.2, vary@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vfile-message@^4.0.0:
  version "4.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vfile-message/-/vfile-message-4.0.2.tgz#c883c9f677c72c166362fd635f21fc165a7d1181"
  integrity sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-stringify-position "^4.0.0"

vfile@^6.0.0:
  version "6.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vfile/-/vfile-6.0.2.tgz#ef49548ea3d270097a67011921411130ceae7deb"
  integrity sha512-zND7NlS8rJYb/sPqkb13ZvbbUoExdbi4w3SfRrMq6R3FvnLQmmfpajJNITuuYm6AZ5uao9vy4BAos3EXBPf2rg==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-stringify-position "^4.0.0"
    vfile-message "^4.0.0"

vite-plugin-cjs-interop@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vite-plugin-cjs-interop/-/vite-plugin-cjs-interop-2.1.1.tgz#871fe5534dbecc5ce1e35d8b56e5917a1b00926b"
  integrity sha512-n02vo+/xRkIG9IlwHPcwI4BgJ+Gbt05Cy7ofSioMaXRon6dnNdJHH1CC7WofPa+PmHfLi1SvJUOZfFmYSHdBLA==
  dependencies:
    acorn "^8.11.3"
    acorn-import-assertions "^1.9.0"
    estree-walker "^3.0.3"
    magic-string "^0.30.10"
    minimatch "^9.0.4"

vite-plugin-inspect@^0.8.4:
  version "0.8.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vite-plugin-inspect/-/vite-plugin-inspect-0.8.5.tgz#9063a03c6868c3c78bdddd3a64aa57f7fa9cd3b7"
  integrity sha512-JvTUqsP1JNDw0lMZ5Z/r5cSj81VK2B7884LO1DC3GMBhdcjcsAnJjdWq7bzQL01Xbh+v60d3lju3g+z7eAtNew==
  dependencies:
    "@antfu/utils" "^0.7.10"
    "@rollup/pluginutils" "^5.1.0"
    debug "^4.3.5"
    error-stack-parser-es "^0.1.4"
    fs-extra "^11.2.0"
    open "^10.1.0"
    perfect-debounce "^1.0.0"
    picocolors "^1.0.1"
    sirv "^2.0.4"

vite-plugin-node-polyfills@^0.21.0:
  version "0.21.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vite-plugin-node-polyfills/-/vite-plugin-node-polyfills-0.21.0.tgz#5566b3a725d4bbc75f4f794b0467e4047c14a58c"
  integrity sha512-Sk4DiKnmxN8E0vhgEhzLudfJQfaT8k4/gJ25xvUPG54KjLJ6HAmDKbr4rzDD/QWEY+Lwg80KE85fGYBQihEPQA==
  dependencies:
    "@rollup/plugin-inject" "^5.0.5"
    node-stdlib-browser "^1.2.0"

vite@^5.3.3:
  version "5.3.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vite/-/vite-5.3.4.tgz#b36ebd47c8a5e3a8727046375d5f10bf9fdf8715"
  integrity sha512-Cw+7zL3ZG9/NZBB8C+8QbQZmR54GwqIz+WMI4b3JgdYJvX+ny9AjJXqkGQlDXSXRP9rP0B4tbciRMOVEKulVOA==
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.39"
    rollup "^4.13.0"
  optionalDependencies:
    fsevents "~2.3.3"

vitepress@^1.1.3:
  version "1.3.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vitepress/-/vitepress-1.3.1.tgz#e0b674d5a9975d10e4af8dc3da19d95976b8f077"
  integrity sha512-soZDpg2rRVJNIM/IYMNDPPr+zTHDA5RbLDHAxacRu+Q9iZ2GwSR0QSUlLs+aEZTkG0SOX1dc8RmUYwyuxK8dfQ==
  dependencies:
    "@docsearch/css" "^3.6.0"
    "@docsearch/js" "^3.6.0"
    "@shikijs/core" "^1.10.3"
    "@shikijs/transformers" "^1.10.3"
    "@types/markdown-it" "^14.1.1"
    "@vitejs/plugin-vue" "^5.0.5"
    "@vue/devtools-api" "^7.3.5"
    "@vue/shared" "^3.4.31"
    "@vueuse/core" "^10.11.0"
    "@vueuse/integrations" "^10.11.0"
    focus-trap "^7.5.4"
    mark.js "8.11.1"
    minisearch "^7.0.0"
    shiki "^1.10.3"
    vite "^5.3.3"
    vue "^3.4.31"

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vm-browserify/download/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

void-elements@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/void-elements/download/void-elements-2.0.1.tgz#c066afb582bb1cb4128d60ea92392e94d5e9dbec"
  integrity sha1-wGavtYK7HLQSjWDqkjkulNXp2+w=

vue-demi@*, vue-demi@>=0.13.0, vue-demi@>=0.14.8:
  version "0.14.9"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-demi/-/vue-demi-0.14.9.tgz#db2be43705e2bc8501f01ca6163e34ada2f2eb21"
  integrity sha512-dC1TJMODGM8lxhP6wLToncaDPPNB3biVxxRDuNCYpuXwi70ou7NsGd97KVTJ2omepGId429JZt8oaZKeXbqxwg==

vue-demi@^0.13.6:
  version "0.13.11"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-demi/download/vue-demi-0.13.11.tgz#7d90369bdae8974d87b1973564ad390182410d99"
  integrity sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=

vue-frag@^1.1.4:
  version "1.4.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-frag/-/vue-frag-1.4.3.tgz#ae207e2f9ca05fb40786582d30705c8f840cdfb6"
  integrity sha512-pQZj03f/j9LRhzz9vKaXTCXUHVYHuAXicshFv76VFqwz4MG3bcb+sPZMAbd0wmw7THjkrTPuoM0EG9TbG8CgMQ==

vue-observe-visibility@^2.0.0-alpha.1:
  version "2.0.0-alpha.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-observe-visibility/download/vue-observe-visibility-2.0.0-alpha.1.tgz#1e4eda7b12562161d58984b7e0dea676d83bdb13"
  integrity sha1-Hk7aexJWIWHViYS34N6mdtg72xM=

vue-resize@^2.0.0-alpha.1:
  version "2.0.0-alpha.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-resize/download/vue-resize-2.0.0-alpha.1.tgz#43eeb79e74febe932b9b20c5c57e0ebc14e2df3a"
  integrity sha1-Q+63nnT+vpMrmyDFxX4OvBTi3zo=

vue-router@4, vue-router@4.x:
  version "4.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-router/-/vue-router-4.4.0.tgz#128e3fc0c84421035a9bd26027245e6bd68f69ab"
  integrity sha512-HB+t2p611aIZraV2aPSRNXf0Z/oLZFrlygJm+sZbdJaW6lcFqEDQwnzUBXn+DApw+/QzDU/I9TeWx9izEjTmsA==
  dependencies:
    "@vue/devtools-api" "^6.5.1"

vue-router@^4.0.14:
  version "4.4.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-router/-/vue-router-4.4.2.tgz#bc7bf27f108fc15e5cc2a30b314a662275e2b2bb"
  integrity sha512-1qNybkn2L7QsLzaXs8nvlQmRKp8XF8DCxZys/Jr1JpQcHsKUxTKzTxCVA1G7NfBfwRIBgCJPoujOG5lHCCNUxw==
  dependencies:
    "@vue/devtools-api" "^6.6.3"

vue-slicksort@^2.0.0-alpha.5:
  version "2.0.5"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-slicksort/-/vue-slicksort-2.0.5.tgz#d080829de9d04b16e8d194ed8b4e219a501091a2"
  integrity sha512-fXz1YrNjhUbJK7o0tMk27mIr4pMAZYLSYvtmLazCtfpvz+zafPCn34ILDL8B7hT7WLVZKreYs6JVe5VWymqmzA==

vue-virtual-scroller@^2.0.0-beta.3:
  version "2.0.0-beta.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue-virtual-scroller/download/vue-virtual-scroller-2.0.0-beta.8.tgz#eeceda57e4faa5ba1763994c873923e2a956898b"
  integrity sha1-7s7aV+T6pboXY5lMhzkj4qlWiYs=
  dependencies:
    mitt "^2.1.0"
    vue-observe-visibility "^2.0.0-alpha.1"
    vue-resize "^2.0.0-alpha.1"

vue@3.x, vue@^3.4.27, vue@^3.4.31:
  version "3.4.34"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue/-/vue-3.4.34.tgz#19d9a82854d54c4506d1e2854c9c038ee430484a"
  integrity sha512-VZze05HWlA3ItreQ/ka7Sx7PoD0/3St8FEiSlSTVgb6l4hL+RjtP2/8g5WQBzZgyf8WG2f+g1bXzC7zggLhAJA==
  dependencies:
    "@vue/compiler-dom" "3.4.34"
    "@vue/compiler-sfc" "3.4.34"
    "@vue/runtime-dom" "3.4.34"
    "@vue/server-renderer" "3.4.34"
    "@vue/shared" "3.4.34"

vue@^3.2.22:
  version "3.4.35"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vue/-/vue-3.4.35.tgz#9ad23525919eece40153fdf8675d07ddd879eb33"
  integrity sha512-+fl/GLmI4GPileHftVlCdB7fUL4aziPcqTudpTGXCT8s+iZWuOCeNEB5haX6Uz2IpRrbEXOgIFbe+XciCuGbNQ==
  dependencies:
    "@vue/compiler-dom" "3.4.35"
    "@vue/compiler-sfc" "3.4.35"
    "@vue/runtime-dom" "3.4.35"
    "@vue/server-renderer" "3.4.35"
    "@vue/shared" "3.4.35"

vuedraggable@4.0.1:
  version "4.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vuedraggable/download/vuedraggable-4.0.1.tgz#3bcaab0808b7944030b7d9a29f9a63d59dfa12c5"
  integrity sha1-O8qrCAi3lEAwt9min5pj1Z36EsU=
  dependencies:
    sortablejs "1.10.2"

vuedraggable@4.1.0, vuedraggable@^4.0.3:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vuedraggable/download/vuedraggable-4.1.0.tgz#edece68adb8a4d9e06accff9dfc9040e66852270"
  integrity sha1-7ezmituKTZ4GrM/538kEDmaFInA=
  dependencies:
    sortablejs "1.14.0"

vuex@^4.0.0, vuex@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/vuex/download/vuex-4.1.0.tgz#aa1b3ea5c7385812b074c86faeeec2217872e36c"
  integrity sha1-qhs+pcc4WBKwdMhvru7CIXhy42w=
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

walker@^1.0.8:
  version "1.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/walker/download/walker-1.0.8.tgz#bd498db477afe573dc04185f011d3ab8a8d7653f"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

"web-events@npm:events@^3.3.0":
  version "3.3.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

web-vitals@^3.1.1:
  version "3.5.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/web-vitals/-/web-vitals-3.5.2.tgz#5bb58461bbc173c3f00c2ddff8bfe6e680999ca9"
  integrity sha512-c0rhqNcHXRkY/ogGDJQxZ9Im9D19hDihbzSQJrsioex+KnFgmMzBiy57Z1EjkhX/+OjyBpclDCzz2ITtjokFmg==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

whatwg-fetch@^1.0.0:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/whatwg-fetch/download/whatwg-fetch-1.1.1.tgz#ac3c9d39f320c6dce5339969d054ef43dd333319"
  integrity sha1-rDydOfMgxtzlM5lp0FTvQ90zMxk=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-typed-array@^1.1.14, which-typed-array@^1.1.2:
  version "1.1.15"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which-typed-array/-/which-typed-array-1.1.15.tgz#264859e9b11a649b388bfaaf4f767df1f779b38d"
  integrity sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

window-size@0.1.0:
  version "0.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/window-size/download/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

with@^5.0.0:
  version "5.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/with/download/with-5.1.1.tgz#fa4daa92daf32c4ea94ed453c81f04686b575dfe"
  integrity sha1-+k2qktrzLE6pTtRTyB8EaGtXXf4=
  dependencies:
    acorn "^3.1.0"
    acorn-globals "^3.0.0"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wordwrap/download/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wordwrap/download/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
  integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/write-file-atomic/download/write-file-atomic-4.0.2.tgz#a9df01ae5b77858a027fd2e80768ee433555fcfd"
  integrity sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@7.4.0:
  version "7.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ws/download/ws-7.4.0.tgz#a5dd76a24197940d4a8bb9e0e152bb4503764da7"
  integrity sha1-pd12okGXlA1Ki7ng4VK7RQN2Tac=

ws@^5.1.0, ws@^5.2.3:
  version "5.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ws/download/ws-5.2.4.tgz#c7bea9f1cfb5f410de50e70e82662e562113f9a7"
  integrity sha1-x76p8c+19BDeUOcOgmYuViET+ac=
  dependencies:
    async-limiter "~1.0.0"

xgplayer-subtitles@^1.0.19:
  version "1.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/xgplayer-subtitles/-/xgplayer-subtitles-1.1.1.tgz#060814c654d82f90aaec2aa98f84ed73c8dde8da"
  integrity sha512-GYzrK/e4ydAATP3Xg06sXYliiSCcyNIqqQSwnWbs7pw+cc5NwyrYXuLfa3Bp9skIxT6pT+A7qTicUps58N3eEQ==
  dependencies:
    eventemitter3 "^4.0.7"

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yargs-parser/download/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^17.3.1:
  version "17.7.2"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yargs/download/yargs-17.7.2.tgz#991df39aca675a192b816e1e0363f9d75d2aa269"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yargs/download/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

ylru@^1.2.0:
  version "1.4.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/ylru/-/ylru-1.4.0.tgz#0cf0aa57e9c24f8a2cbde0cc1ca2c9592ac4e0f6"
  integrity sha512-2OQsPNEmBCvXuFlIni/a+Rn+R2pHW9INm0BxXJ4hVDA8TirqMj+J/Rp9ItLatT/5pZqWwefVrTQcHpixsxnVlA==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zhead@^2.1.1:
  version "2.2.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/zhead/-/zhead-2.2.4.tgz#87cd1e2c3d2f465fa9f43b8db23f9716dfe6bed7"
  integrity sha512-8F0OI5dpWIA5IGG5NHUg9staDwz/ZPxZtvGVf01j7vHqSyZ0raHY+78atOVxRqb73AotX22uV1pXt3gYSstGag==

zrender@5.2.1:
  version "5.2.1"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/zrender/download/zrender-5.2.1.tgz#5f4bbda915ba6d412b0b19dc2431beaad05417bb"
  integrity sha1-X0u9qRW6bUErCxncJDG+qtBUF7s=
  dependencies:
    tslib "2.3.0"

zrender@5.6.0:
  version "5.6.0"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/zrender/-/zrender-5.6.0.tgz#01325b0bb38332dd5e87a8dbee7336cafc0f4a5b"
  integrity sha512-uzgraf4njmmHAbEUxMJ8Oxg+P3fT04O+9p7gY+wJRVxo8Ge+KmYv0WJev945EH4wFuc4OY2NLXz46FZrWS9xJg==
  dependencies:
    tslib "2.3.0"

zwitch@^2.0.0:
  version "2.0.4"
  resolved "https://artifactory.devops.xiaohongshu.com/artifactory/api/npm/npm-public/zwitch/download/zwitch-2.0.4.tgz#c827d4b0acb76fc3e685a4c6ec2902d51070e9d7"
  integrity sha1-yCfUsKy3b8PmhaTG7CkC1RBw6dc=
