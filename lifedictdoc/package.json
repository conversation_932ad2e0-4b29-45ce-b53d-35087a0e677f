{"name": "lifedictdoc", "version": "1.0.2", "scripts": {"preinstall": "node scripts/preinstall.js", "dev": "vitepress dev", "build": "vitepress build", "preview": "vitepress preview", "pub-doc": "dockwalloper publish -d public -k smyhexkh4yYw0CYKEE26elwW6L8JVrfIE30NQOwxxuQ2QXOK"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.8", "@types/node": "^20.12.11", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/babel-plugin-jsx": "^1.2.2", "@xhs/dockwalloper": "^0.4.2", "@xhs/vitepress-plugin-code-demo": "^0.0.9", "@xhs/vitepress-plugin-code-demo-component": "^0.0.6", "postcss": "^8.4.38", "stylus": "^0.63.0", "vite-plugin-node-polyfills": "^0.21.0", "vitepress": "^1.1.3", "vue": "^3.4.27"}, "peerDependencies": {"vue": "^3.4.27"}, "private": true, "dependencies": {"@babel/plugin-proposal-decorators": "^7.24.6", "@vant/lazyload": "^1.2.0", "@xhs/apm-insight": "^1.1.8", "@xhs/ark-datacenter": "0.0.9", "@xhs/biz-dict-kit-core": "0.0.3-beta.0", "@xhs/delight": "1.2.15", "@xhs/delight-ark-indicator-card": "^0.3.0", "@xhs/delight-ark-qrcode": "^1.0.1", "@xhs/delight-formily": "0.6.6", "@xhs/delight-material-ads-note-detail": "^0.1.6", "@xhs/delight-material-ark-goods-select": "0.2.15", "@xhs/delight-material-ark-qrcode": "^0.1.8", "@xhs/delight-material-item-calendar": "^0.0.7", "@xhs/delight-material-life-identification-upload": "0.1.8", "@xhs/delight-material-life-image": "^0.1.2", "@xhs/delight-material-life-qrcode": "^0.1.5", "@xhs/delight-material-life-region-cascader": "0.1.1", "@xhs/delight-material-life-show-qrcode": "^0.1.7", "@xhs/delight-material-promotion-pc-feedback": "^0.1.5", "@xhs/delight-material-ultra-delight-form": "^0.1.8", "@xhs/delight-material-ultra-enhance-upload": "0.2.1", "@xhs/delight-material-ultra-image-conversion": "^0.1.13", "@xhs/delight-material-ultra-outline-filter": "^0.1.15", "@xhs/delight-material-ultra-page-header": "^0.2.10", "@xhs/delight-material-ultra-table-cell": "^0.1.30", "@xhs/delight-material-ultra-toolbar": "^0.1.28", "@xhs/delight-material-ultra-tour-guide": "^1.0.4", "@xhs/delight-official-chart": "^1.1.3", "@xhs/di": "^0.1.1", "@xhs/launcher": "^3.24.0", "@xhs/launcher-ark": "1.2.16", "@xhs/launcher-plugin-anti-spam": "^3.6.8", "@xhs/modular-startup": "4.2.0", "@xhs/module-reactive": "^0.1.0", "@xhs/ozone-bridge": "3.18.0", "@xhs/uploader": "3.0.3", "@xhs/vue-di": "^0.2.0", "axios": "1.5.0", "big.js": "^6.2.1", "cn-fontsource-lxgw-wen-kai-gb-screen-r": "^1.0.6", "dayjs": "^1.10.7", "dompurify": "^3.0.5", "echarts": "^5.4.3", "html-to-image": "1.11.11", "js-cookie": "2.2.1", "jsmind": "^0.8.5", "lodash": "4.17.20", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.mergewith": "^4.6.2", "lodash.set": "^4.3.2", "lodash.uniq": "^4.5.0", "qiankun": "2.4.0", "query-string": "^9.1.0", "shared": "^1.0.0", "three": "^0.164.1", "vite-plugin-cjs-interop": "^2.1.1", "vite-plugin-inspect": "^0.8.4", "vue-router": "4", "vuedraggable": "4.0.1", "vuex": "^4.1.0", "@xhs/delight-material-life-enhance-input-number": "^0.1.3", "@xhs/delight-material-life-category-selector": "^0.1.3", "@xhs/delight-material-life-validity-period": "^0.1.2", "@formily/core": "2.2.7", "@formily/reactive-vue": "2.2.7", "@formily/vue": "2.2.7", "@formily/reactive": "2.2.7", "@formily/shared": "2.2.7", "@xhs/formula-cli": "^3.21.6", "vue-cropperjs": "^5.0.0"}}