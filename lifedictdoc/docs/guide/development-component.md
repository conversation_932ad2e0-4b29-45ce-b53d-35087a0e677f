# 字典开发指南

### 目录结构

```
- shared
    - service // 通用API
    - dictMaterials
        - MerchantManage
            - goodsModel
                - GoodsName // 字典文件名
                    - components // UI组件-区分双端
                        - H5
                        - PC
                    - formCore // 表单场景核心模型
                        - index.ts // core出口文件
                        - validator.ts // 校验
                        - effects.ts // 副作用
                    - detailCore // 常规场景核心模型
                        - GoodsNameDomain.ts // 命名以 Domain 结尾
                    - data // 数据层
                        - GoodsNameData.ts // 命名以 Data 结尾
                    - type.ts // 类型
                    - constant.ts // 枚举
                    - index.ts // PC模块汇总导出 + 各种类型
                    - index.h5.ts // h5模块汇总导出 + 各种类型
                    - demo.md
```

### 快速创建模型字典

1. 安装 vscode 插件： xhs-life-plugin
2. 在对应文件夹下，右键创建模型字典
   - ![alt text](image.png)

### 如何使用

#### 表单

- 目前 DelightFrom 和 Formily 组件不能混用，后续提供磨平方案

##### DelightFrom

:::demo

```vue
<template>
  <Form class="form" :model="model">
    <ActualAmountInputField
      v-model="model.actual_amount"
      :style="{ width: '280px' }"
      name="actual_amount"
      label="基准售价"
      placeholder="基准售价"
    />
  </Form>
</template>
<script setup>
  import { reactive, watch } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import { ActualAmountInputField } from "shared/dictMaterials/MerchantManage/GoodsModel/Price/components/PC/DelightFormItem";
  const model = reactive({
    actual_amount: 10,
  });
</script>
<style lang="stylus">
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

##### Formily

:::demo

```vue
<template>
  <FormProvider :form="form">
    <PrincipalType name="principalType" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { PrincipalType } from "shared/dictMaterials/settled/PrincipalType";

  const form = createForm({
    values: {
      authorizationName: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

#### 详情

:::demo

```vue
<template>
  <div>入参为元：<PriceText price="2" unit="yuan" /></div>
  <div>入参为分：<PriceText price="2" /></div>
</template>
<script setup>
  import PriceText from "shared/dictMaterials/MerchantManage/GoodsModel/Price/components/PC/PriceText.vue";
</script>
```

:::
