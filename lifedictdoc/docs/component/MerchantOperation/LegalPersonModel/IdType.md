# 证件类型 <Tag text="IdType" />

证件类型

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <IDType name="formIdType" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { IDType } from "shared/dictMaterials/settled/IDType";

  const form = createForm();
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性           | 说明                 | 类型                       | 默认值                                                                          |
| -------------- | -------------------- | -------------------------- | ------------------------------------------------------------------------------- |
| name           | formily name         | string                     | certificateType                                                                 |
| title          | formily title        | string                     | 证件类型                                                                        |
| value          | formily value        | string                     |                                                                                 |
| initialValue   | formily initialValue | string                     |                                                                                 |
| required       | formily required     | boolean                    | true                                                                            |
| validator      | formily validator    | FieldValidator 、 Function |
| disabled       | formily disabled     | boolean                    | false                                                                           |
| dataSource     | formily dataSource   |                            |                                                                                 |
| componentProps | 内部组件入参         | Object                     | { placeholder: '请选择身份证件类型',clearable: true,options: IDTypeOptionsAll } |
