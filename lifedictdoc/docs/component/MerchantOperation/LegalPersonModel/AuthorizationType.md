# 认证方式 <Tag text="AuthorizationType" />

认证方式

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <FormLayout :label-col="5" label-align="right" class="userInfoBlock">
      <Authentication
        :componentProps="{
          link: 'testLink',
          status: 'DOING',
          getLink: getAuthenticationData,
          getPublicPaymentResult,
          onChangeError: changeError,
          onChangePublicPaymentStep: changePublicPaymentStep,
          onChangeSuccess: changeSuccess,
        }"
      />
    </FormLayout>
  </FormProvider>
</template>
<script setup lang="ts">
  import { Layout } from "@xhs/delight-formily";
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { Authentication } from "shared/dictMaterials/settled/AuthorizationType";

  const FormLayout = Layout.FormLayout;

  const form = createForm();
  // 不放在validator中，因为validator会在提交时触发，导致多次调用
  const getAuthenticationData = (showToast?: boolean) => {};

  // 获取支付结果
  const getPublicPaymentResult = () => {};
  // 打款验证 表单提交 error
  const changeError = (e?: {
    step: number;
    errMsg?: string;
    type: MerchantAuthorizationTypeEnum;
  }) => {};
  // 打款验证步骤改变
  const changePublicPaymentStep = (step) => {};
  // 打款验证 表单提交正常
  const changeSuccess = (e) => {};
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性           | 说明                 | 类型                       | 默认值                                                                 |
| -------------- | -------------------- | -------------------------- | ---------------------------------------------------------------------- |
| name           | formily name         | string                     | authorizationType                                                      |
| title          | formily title        | string                     | 认证方式                                                               |
| value          | formily value        | string                     |                                                                        |
| initialValue   | formily initialValue | string                     |                                                                        |
| required       | formily required     | boolean                    | true                                                                   |
| validator      | formily validator    | FieldValidator 、 Function |
| disabled       | formily disabled     | boolean                    | false                                                                  |
| dataSource     | formily dataSource   |                            |                                                                        |
| componentProps | 内部组件入参         | Object                     | { status: AUTH_STATUS.DOING, options: [optionsFace, optionsPayment], } |
