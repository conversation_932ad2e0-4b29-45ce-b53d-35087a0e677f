# 用户邮箱 <Tag text="UserEmail" />

用户邮箱

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <EMail name="formUserEmail" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { EMail } from "shared/dictMaterials/settled/EMail";

  const form = createForm({
    values: {
      formUserEmail: "<EMAIL>",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |
