# 用户手机号 <Tag text="UserPhone" />

用户手机号

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <PhoneNumber name="formUserPhone" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { PhoneNumber } from "shared/dictMaterials/settled/PhoneNumber";

  const form = createForm({
    values: {
      formUserPhone: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |
