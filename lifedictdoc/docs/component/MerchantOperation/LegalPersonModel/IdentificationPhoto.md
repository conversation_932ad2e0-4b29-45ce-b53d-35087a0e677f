# 证件照 <Tag text="IdentificationPhoto" />

证件照

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <IdentificationPhoto
      name="formIdentificationPhoto"
      :componentProps="{
        type: 'ID_CARD',
      }"
    />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { IdentificationPhoto } from "shared/dictMaterials/settled/IdentificationPhoto";

  const form = createForm({
    values: {
      formIdentificationPhoto: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性           | 说明                 | 类型                       | 默认值                                |
| -------------- | -------------------- | -------------------------- | ------------------------------------- |
| name           | formily name         | string                     |                                       |
| title          | formily title        | string                     |                                       |
| value          | formily value        | string                     |                                       |
| initialValue   | formily initialValue | string                     |                                       |
| required       | formily required     | boolean                    | true                                  |
| validator      | formily validator    | FieldValidator 、 Function |
| disabled       | formily disabled     | boolean                    | false                                 |
| dataSource     | formily dataSource   |                            |                                       |
| componentProps | 内部组件入参         | Object                     | componentProps="{ type: 'ID_CARD', }" |

### IdentificationUpload

| 属性 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ------ |
