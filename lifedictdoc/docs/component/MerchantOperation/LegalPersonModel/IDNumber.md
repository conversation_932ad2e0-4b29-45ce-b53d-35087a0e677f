# 证件号码 <Tag text="IDNumber" />

证件号码

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <IDNumber name="formIDNumber" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { IDNumber } from "shared/dictMaterials/settled/IDNumber";

  const form = createForm({
    values: {
      formIDNumber: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |

### componentProps

- 参考 InputNumber https://delight.devops.xiaohongshu.com/delight/cmp/input-number

| 属性        | 说明 | 类型    | 默认值         |
| ----------- | ---- | ------- | -------------- |
| placeholder |      | string  | 请输入证件号码 |
| clearable   |      | boolean | true           |
| max-length  |      | number  | 18             |
