# 用户名称 <Tag text="UserName" />

用户名称

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <UserName title="法人姓名" name="authorizationName" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { UserName } from "shared/dictMaterials/settled/UserName";

  const form = createForm({
    values: {
      authorizationName: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性         | 说明                 | 类型                       | 默认值            |
| ------------ | -------------------- | -------------------------- | ----------------- |
| name         | formily name         | string                     | authorizationName |
| title        | formily title        | string                     | 名称              |
| value        | formily value        | string                     |                   |
| initialValue | formily initialValue | string                     |                   |
| required     | formily required     | boolean                    | true              |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false             |
| dataSource   | formily dataSource   |                            |                   |
