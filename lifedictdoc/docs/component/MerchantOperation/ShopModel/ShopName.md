# 门店名称 <Tag text="ShopName" />

门店名称

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <ShopName name="formShopName" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { ShopName } from "shared/dictMaterials/settled/ShopName";

  const form = createForm({
    values: {
      formShopName: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型         | 默认值 |
| ---------- | -------- | ------------ | ------ |
| modelValue | 绑定数据 | ShopNameType |        |
