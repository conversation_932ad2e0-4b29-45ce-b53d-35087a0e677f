# 图片上传 <Tag text="SettleUpload" />

图片上传

- 品牌 logo
- 商标注册证上传
- 商标使用授权书
- 其他补充信息
- 行业资质
- 门店 Logo

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <SettleUpload name="formSettleUpload" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { SettleUpload } from "shared/dictMaterials/settled/SettleUpload";

  const form = createForm();
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |

### componentProps
