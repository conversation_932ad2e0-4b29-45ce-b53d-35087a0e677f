# 品牌名称 <Tag text="BrandName" />

BrandName 字典

## 品牌名称

:::demo

```vue
<template>
  <FormProvider :form="form">
    <BrandName name="formBrandName" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { BrandName } from "shared/dictMaterials/settled/BrandName";

  const form = createForm({
    values: {
      formBrandName: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## delight-form 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <!-- <BrandName v-model="model.BrandName" /> -->
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import BrandName from 'shared/dictMaterials/MerchantManage/GoodsModel/BrandName/index.vue'

  const model = reactive({
    BrandName: "10",
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |

### componentProps

- 参考 InputNumber https://delight.devops.xiaohongshu.com/delight/cmp/input-number
