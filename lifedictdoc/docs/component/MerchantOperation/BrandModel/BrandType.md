# 品牌类型 <Tag text="BrandType" />

品牌类型

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <BrandType name="formBrandType" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { BrandType } from "shared/dictMaterials/settled/BrandType";

  const form = createForm({
    values: {
      formBrandType: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |

### componentProps

- 参考 SegmentControl https://delight.devops.xiaohongshu.com/delight/cmp/segment-control
