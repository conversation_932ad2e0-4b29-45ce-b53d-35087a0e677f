# 地址 <Tag text="Address" />

Address 字典

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <Address name="address" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { Address } from "shared/dictMaterials/settled/Address";

  const form = createForm({
    values: {
      address: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性         | 说明                 | 类型                       | 默认值                   |
| ------------ | -------------------- | -------------------------- | ------------------------ |
| name         | formily name         | string                     | companyRegisteredAddress |
| title        | formily title        | string                     | 经营地址                 |
| value        | formily value        | string                     |                          |
| initialValue | formily initialValue | string                     |                          |
| required     | formily required     | boolean                    | true                     |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false                    |
| dataSource   | formily dataSource   |                            |                          |
