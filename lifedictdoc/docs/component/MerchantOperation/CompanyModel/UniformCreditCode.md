# 社会统一信用代码 <Tag text="UniformCreditCode" />

社会统一信用代码

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <UniformCreditCode name="formUniformCreditCode" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { UniformCreditCode } from "shared/dictMaterials/settled/UniformCreditCode";

  const form = createForm({
    values: {
      formUniformCreditCode: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## delight-form 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <!-- <UniformCreditCode v-model="model.UniformCreditCode" /> -->
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import UniformCreditCode from 'shared/dictMaterials/MerchantManage/GoodsModel/UniformCreditCode/index.vue'

  const model = reactive({
    UniformCreditCode: "10",
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性         | 说明                 | 类型                       | 默认值            |
| ------------ | -------------------- | -------------------------- | ----------------- |
| name         | formily name         | string                     | uniformCreditCode |
| title        | formily title        | string                     | 统一社会信用代码  |
| value        | formily value        | string                     |                   |
| initialValue | formily initialValue | string                     |                   |
| required     | formily required     | boolean                    | true              |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false             |
| dataSource   | formily dataSource   |                            |                   |
