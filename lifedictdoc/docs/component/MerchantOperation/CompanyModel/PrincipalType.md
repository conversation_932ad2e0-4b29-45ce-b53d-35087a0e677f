# 主体类型 <Tag text="PrincipalType" />

主体类型

## 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <PrincipalType name="principalType" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { PrincipalType } from "shared/dictMaterials/settled/PrincipalType";

  const form = createForm({
    values: {
      authorizationName: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型              | 默认值 |
| ---------- | -------- | ----------------- | ------ |
| modelValue | 绑定数据 | PrincipalTypeType |        |
