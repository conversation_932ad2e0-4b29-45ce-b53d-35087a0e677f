# 有效期 <Tag text="ValidityPeriod" />

有效期

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <ValidityPeriod name="formValidityPeriod" />
  </FormProvider>
</template>
<script setup>
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { ValidityPeriod } from "shared/dictMaterials/settled/ValidityPeriod";

  const form = createForm({
    values: {
      formValidityPeriod: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

###

| 属性 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ------ |
