# 资质 code <Tag text="QualificationCode" />

资质 code

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <QualificationCode
      name="formQualificationCode"
      :component-props="componentProps"
    />
  </FormProvider>
</template>
<script setup>
  import { ref } from "vue";
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { QualificationCode } from "shared/dictMaterials/settled/QualificationCode";

  const componentProps = ref({
    disabled: false,
    readonly: false,
    validate: () => true,
    validateTiming: "immediate",
    options: [
      {
        label: "string1",
        value: "string1",
      },
      {
        label: "string2",
        value: "string2",
      },
    ],
  });
  const form = createForm({
    values: {
      formQualificationCode: "xxx",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### FormilyItem

| 属性         | 说明                 | 类型                       | 默认值 |
| ------------ | -------------------- | -------------------------- | ------ |
| name         | formily name         | string                     |        |
| title        | formily title        | string                     |        |
| value        | formily value        | string                     |        |
| initialValue | formily initialValue | string                     |        |
| required     | formily required     | boolean                    | true   |
| validator    | formily validator    | FieldValidator 、 Function |
| disabled     | formily disabled     | boolean                    | false  |
| dataSource   | formily dataSource   |                            |        |

### componentProps

| 属性 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ------ |
