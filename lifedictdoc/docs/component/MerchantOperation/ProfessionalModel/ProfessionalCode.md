# 专业号身份 <Tag text="ProfessionalCode" />

专业号身份

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <ProfessionalCode
      name="formProfessionalCode"
      :component-props="componentProps"
    />
  </FormProvider>
</template>
<script setup>
  import { ref } from "vue";
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { ProfessionalCode } from "shared/dictMaterials/settled/ProfessionalCode";

  const form = createForm({
    values: {
      formProfessionalCode: "xxx",
    },
  });

  const componentProps = {
    options: [
      {
        /** 专业号身份code */
        professionalCode: "index1",
        /** 专业号身份name */
        professionalName: "选项1",
      },
      {
        /** 专业号身份code */
        professionalCode: "index2",
        /** 专业号身份name */
        professionalName: "选项2",
      },
    ],
  };
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### componentProps

| 属性    | 说明 | 类型                      | 默认值 |
| ------- | ---- | ------------------------- | ------ |
| options | 选项 | IProfessionalConfigList[] | []     |
