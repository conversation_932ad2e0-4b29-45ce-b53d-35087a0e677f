# 经营范围 <Tag text="ProIdentityScope" />

经营范围

## formily 用法

:::demo

```vue
<template>
  <FormProvider :form="form">
    <ProIdentityScope
      name="formProIdentityScope"
      :component-props="componentProps"
    />
    111
  </FormProvider>
</template>
<script setup>
  import { ref } from "vue";
  import { createForm } from "@formily/core";
  import { FormProvider } from "@formily/vue";
  import { ProIdentityScope } from "shared/dictMaterials/settled/ProIdentityScope";

  const form = createForm({
    values: {
      formProIdentityScope: "xxx",
    },
  });
  const componentProps = ref({
    options: [
      {
        label: "其他",
        value: "string1",
      },
      {
        label: "xxxxx",
        value: "string2",
      },
    ],
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型                 | 默认值 |
| ---------- | -------- | -------------------- | ------ |
| modelValue | 绑定数据 | ProIdentityScopeType |        |

- RadioGroup 参考:
