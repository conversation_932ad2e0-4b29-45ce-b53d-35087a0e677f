# 实付金额 <Tag text="PayAmount" />

实付金额

## 用法

:::demo

```vue
<template>
  <div>
    <!-- <PayAmount /> -->
  </div>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import PayAmount from 'shared/dictMaterials/MerchantManage/GoodsModel/PayAmount/index.vue'
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型          | 默认值 |
| ---------- | -------- | ------------- | ------ |
| modelValue | 绑定数据 | PayAmountType |        |
