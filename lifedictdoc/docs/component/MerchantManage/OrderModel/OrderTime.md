# 订单时间 <Tag text="OrderTime" />

订单时间

## 用法

:::demo

```vue
<template>
  <div>
    <!-- <OrderTime /> -->
  </div>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import OrderTime from 'shared/dictMaterials/MerchantManage/GoodsModel/OrderTime/index.vue'
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型          | 默认值 |
| ---------- | -------- | ------------- | ------ |
| modelValue | 绑定数据 | OrderTimeType |        |
