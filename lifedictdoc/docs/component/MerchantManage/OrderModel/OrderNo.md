# 订单号 <Tag text="OrderNo" />

订单号

## 用法

:::demo

```vue
<template>
  <div>
    <!-- <OrderNo /> -->
  </div>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import OrderNo from "shared/dictMaterials/MerchantManage/GoodsModel/OrderNo/index.vue";
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型        | 默认值 |
| ---------- | -------- | ----------- | ------ |
| modelValue | 绑定数据 | OrderNoType |        |
