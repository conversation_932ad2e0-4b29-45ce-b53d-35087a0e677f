# 优惠券数量 <Tag text="CountNumber" />

优惠券数量

## 用法

:::demo

```vue
<template>
  <div>
    <!-- <CountNumber /> -->
  </div>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import CountNumber from 'shared/dictMaterials/MerchantManage/GoodsModel/CountNumber/index.vue'
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型            | 默认值 |
| ---------- | -------- | --------------- | ------ |
| modelValue | 绑定数据 | CountNumberType |        |
