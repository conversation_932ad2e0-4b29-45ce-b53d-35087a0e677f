# 订单类型 <Tag text="OrderType" />

订单类型

## 用法

:::demo

```vue
<template>
  <div>
    <OrderType />
  </div>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import OrderType from 'shared/dictMaterials/MerchantManage/GoodsModel/OrderType/index.vue'
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型          | 默认值 |
| ---------- | -------- | ------------- | ------ |
| modelValue | 绑定数据 | OrderTypeType |        |
