# 商品类目 <Tag text="Category" />

商品类目

## 用法

:::demo

```vue
<template>
  <Form class="form" :model="model">
    <Category />
  </Form>
</template>
<script setup lang="ts">
  import { reactive, watch } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import Category from "shared/dictMaterials/MerchantManage/GoodsModel/Category/index.vue";
</script>
<style lang="stylus">
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ------ |
