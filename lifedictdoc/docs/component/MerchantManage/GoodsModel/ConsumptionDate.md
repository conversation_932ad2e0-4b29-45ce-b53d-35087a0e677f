# 可消费日期 <Tag text="ConsumptionDate" />

可消费日期

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    ref="formRef"
    :model="model"
  >
    <ConsumptionDate
      :modelValue="model.product.attr_key_value_map.use_date"
      @update:modelValue="change"
    />
  </Form>
</template>
<script setup>
  import { reactive, computed, ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import { get } from "lodash";
  import ConsumptionDate from "lifeitem/src/dictMaterials/UseDateRule/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        use_date: {
          use_date_type: 4,
          use_start_date: "",
          use_end_date: "",
          day_duration: 180,
        },
      },
    },
  });
  const change = (v) => {
    console.log(v, 111);
    model.value = {
      product: {
        attr_key_value_map: {
          use_date: v,
        },
      },
    };
  };
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型                | 默认值 |
| ---------- | -------- | ------------------- | ------ |
| modelValue | 绑定数据 | ConsumptionDateType |        |
