# 商品图片 <Tag text="ItemImage" />

- 商品图：ImageListUpload
- 菜品图：DetailImageListUpload
- 环境图：DetailImageListUpload

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    商品图-待抽象整合
    <!-- <ImageUpload /> -->
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import ImageUpload from "shared/seller/components/ImageUpload/index.vue";
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型          | 默认值 |
| ---------- | -------- | ------------- | ------ |
| modelValue | 绑定数据 | ItemImageType |        |
