# 每日消费时段 <Tag text="UseTimeRuleV2" />

UseTimeRuleV2 字典

## delight-form 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <UseTimeRuleV2 v-model="model.useTime" />
    <div>
      <div>数据：</div>
      {{ model.useTime }}
    </div>
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import UseTimeRuleV2 from "lifeitem/src/dictMaterials/UseTimeRuleV2/index.vue";
  import { multiStepUseTimeRuleBusinessToLocal } from "lifeitem/src/dictMaterials/UseTimeRuleV2/transform";

  const details = {
    product: {
      attr_key_value_map: {
        use_time:
          '[{"end_time":"23:59:59","start_time":"00:00:00","time_duration_types":["WORK_DAY"]},{"end_time":"23:59:59","start_time":"00:00:00","time_duration_types":["WEEK_END","HOLIDAY"]}]',
      },
    },
  };
  // 初始化 useTime
  const useTime = multiStepUseTimeRuleBusinessToLocal(details);
  console.log(useTime);
  const model = ref({
    useTime,
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型              | 默认值 |
| ---------- | -------- | ----------------- | ------ |
| modelValue | 绑定数据 | UseTimeRuleV2Type |        |
