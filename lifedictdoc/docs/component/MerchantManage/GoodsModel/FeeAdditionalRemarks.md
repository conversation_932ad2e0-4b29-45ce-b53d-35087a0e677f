# 费用补充说明 <Tag text="FeeAdditionalRemarks" />

费用补充说明

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <FeeAdditionalRemarks v-model="model" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import FeeAdditionalRemarks from "lifeitem/src/dictMaterials/FeeAdditionalRemarks/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        feeAdditionalRemarks: {
          includes: [{ name: undefined, content: undefined }],
          excludes: [{ name: undefined, content: undefined }],
        },
      },
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型                     | 默认值 |
| ---------- | -------- | ------------------------ | ------ |
| modelValue | 绑定数据 | FeeAdditionalRemarksType |        |
