# 最大团体人数 <Tag text="MaxTeamPerson" />

最大团体人数（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <MaxTeamPersonInput
      v-model="model.product.attr_key_value_map.maxTeamPersonNum"
    />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import MaxTeamPersonInput from "lifeitem/src/dictMaterials/MaxTeamPersonInput/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        maxTeamPersonNum: 3,
      },
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型              | 默认值 |
| ---------- | -------- | ----------------- | ------ |
| modelValue | 绑定数据 | MaxTeamPersonType |        |
