# 服务商抽佣比例 <Tag text="CommissionInfo" />

服务商抽佣比例

## 用法

:::demo

```vue
<template>
  <Form class="form" :model="model">
    <CommissionInfo :visible="true" :modelValue="modelValue" />
  </Form>
</template>
<script setup lang="ts">
  import { reactive, watch, ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import CommissionInfo from "shared/dictMaterials/MerchantManage/GoodsModel/CommissionInfo/index.vue";

  const modelValue = ref({
    fee_status: 1,
  });
</script>
<style lang="stylus">
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明      | 类型                                      | 默认值 |
| ---------- | --------- | ----------------------------------------- | ------ |
| modelValue | 绑定数据  | { rate?: number; fee_status?: 0\1\2\3\4 } |        |
| disabled   | 是否禁用  | boolean                                   | false  |
| visible    | 是否展示  | boolean                                   |        |
| name       | 表单 name | string                                    |        |
| rules      | 校验规则  | any                                       |        |
