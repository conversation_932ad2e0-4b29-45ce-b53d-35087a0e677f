# 商品名称 <Tag text="Name" />

- 商品名称
- 商品组名

## 用法

:::demo

```vue
<template>
  <Form class="form" :model="model">
    <Name :modelValue="modelValue" :componentProps="componentProps" />
  </Form>
</template>
<script setup lang="ts">
  import { reactive, watch, ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import Name from "shared/dictMaterials/MerchantManage/GoodsModel/Name/index.vue";

  const model = ref({});
  const modelValue = ref("");
  const componentProps = ref({
    disabled: false,
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性           | 说明     | 类型                  | 默认值 |
| -------------- | -------- | --------------------- | ------ |
| modelValue     | 绑定数据 | string                |        |
| componentProps | 组件参数 | { disabled: boolean } | false  |
