# 库存 <Tag text="Stock" />

库存（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    {{ model.product_info.seller_need_provider_min_stock }}
    <Stock v-model="model.product_info.seller_need_provider_min_stock" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import Stock from "shared/dictMaterials/camp-admin/activityLife/StockInput/index.vue";

  const model = ref({
    product_info: {
      seller_need_provider_min_stock: 10,
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型      | 默认值 |
| ---------- | -------- | --------- | ------ |
| modelValue | 绑定数据 | StockType |        |
