# 券类型 <Tag text="CouponType" />

券类型

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <CouponCodeTypeSelect v-model="model.extCouponExtraInfo" />
  </Form>
</template>
<script setup lang="ts">
  import { ref } from "vue";
  import { cloneDeep } from "lodash";
  import { Form2 as Form } from "@xhs/delight";
  import CouponCodeTypeSelect from "lifeitem/src/dictMaterials/CouponCodeTypeSelect/index.vue";
  import { CouponCodeType } from "lifeitem/src/dictMaterials/CouponCodeTypeSelect/model.ts";
  import { thirdExtInfo } from "lifeitem/src/dictMaterials/ExtCouponExtraInfo/constant";

  const model = ref({
    extCouponExtraInfo: {
      couponCodeType: CouponCodeType.PLATFORM,
      thirdExtInfo: cloneDeep(thirdExtInfo),
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### model

| 属性           | 说明         | 类型                                                                                                      | 默认值 | 是否必传 |
| -------------- | ------------ | --------------------------------------------------------------------------------------------------------- | ------ | -------- |
| couponCodeType | 券类型       | CouponCodeType                                                                                            | '1'    | 是       |
| thirdExtInfo   | 第三方券数据 | { appId: '', outId: '', verifyWays: [], // 1 二维码核销，2 小程序核销 miniProgramVerifyDetailImages: [] } |        | 否       |
