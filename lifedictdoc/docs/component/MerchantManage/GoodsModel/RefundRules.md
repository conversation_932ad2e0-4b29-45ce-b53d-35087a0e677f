# 退款规则 <Tag text="RefundRules" />

退款规则（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <RefundRules :ruleText="model.ruleText" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import RefundRules from "lifeitem/src/dictMaterials/RefundRules/index.vue";

  const model = ref({
    ruleText: "退款规则文案",
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型            | 默认值 |
| ---------- | -------- | --------------- | ------ |
| modelValue | 绑定数据 | RefundRulesType |        |
