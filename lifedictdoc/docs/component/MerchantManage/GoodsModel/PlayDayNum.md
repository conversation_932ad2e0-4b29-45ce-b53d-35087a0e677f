# 游玩天数 <Tag text="PlayDayNum" />

游玩天数（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <PlayDayNum v-model="model.product.attr_key_value_map.playDayNum" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import PlayDayNum from "lifeitem/src/dictMaterials/PlayDayInput/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        playDayNum: 10,
      },
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型   | 默认值 |
| ---------- | -------- | ------ | ------ |
| modelValue | 绑定数据 | number |        |
