# 商品数量 <Tag text="ItemNumber" />

商品数量

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <ItemNumber v-model="model.item_number" name="item_number" required />
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import { ItemNumber } from "shared/dictMaterials/MerchantManage/GoodsModel/ItemNumber";

  const model = reactive({
    item_number: 10,
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型           | 默认值 |
| ---------- | -------- | -------------- | ------ |
| modelValue | 绑定数据 | ItemNumberType |        |
