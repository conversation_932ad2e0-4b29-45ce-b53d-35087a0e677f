# 商品价格字典 <Tag text="ItemPrice" />

商品价格字典

## delight-form-item 用法

delight 表单中的 input 输入框

:::demo

```vue
<template>
  <Form class="form" :model="model">
    <ActualAmountInputField
      v-model="model.actual_amount"
      :style="{ width: '280px' }"
      name="actual_amount"
      label="基准售价"
      placeholder="基准售价"
    />
  </Form>
</template>
<script setup>
  import { reactive, watch } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import { ActualAmountInputField } from "shared/dictMaterials/MerchantManage/GoodsModel/Price/components/PC/DelightFormItem";
  const model = reactive({
    actual_amount: 10,
  });
</script>
<style lang="stylus">
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## 常规价格展示

常规价格展示 ¥2

:::demo

```vue
<template>
  <div>入参为元：<PriceText price="2" unit="yuan" /></div>
  <div>入参为分：<PriceText price="2" /></div>
</template>
<script setup>
  import PriceText from "shared/dictMaterials/MerchantManage/GoodsModel/Price/components/PC/PriceText.vue";
</script>
```

:::

## API

- 参考 FormItem https://delight.devops.xiaohongshu.com/delight/cmp/form
- 参考 InputNumber https://delight.devops.xiaohongshu.com/delight/cmp/input-number

### 扩展属性

| 属性 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ------ |
