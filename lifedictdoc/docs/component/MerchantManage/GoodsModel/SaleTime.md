# 售卖时间 <Tag text="SaleTime" />

售卖时间

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    字典待沉淀
    <!-- <SaleTime /> -->
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import SaleTime from "shared/dictMaterials/MerchantManage/GoodsModel/SaleTime/index.vue";
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型         | 默认值 |
| ---------- | -------- | ------------ | ------ |
| modelValue | 绑定数据 | SaleTimeType |        |
