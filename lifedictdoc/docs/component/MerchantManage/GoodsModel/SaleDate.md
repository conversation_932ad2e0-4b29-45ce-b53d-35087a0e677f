# 售卖日期 <Tag text="SaleDate" />

售卖日期（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <SaleDate v-model="model.product" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import SaleDate from "lifeitem/src/dictMaterials/SoldRangeSelect/index.vue";

  const model = ref({
    product: {
      sold_start_time: "2024-08-25 00:00:00",
      sold_end_time: "2024-08-26 23:59:59",
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型         | 默认值 |
| ---------- | -------- | ------------ | ------ |
| modelValue | 绑定数据 | SaleDateType |        |
