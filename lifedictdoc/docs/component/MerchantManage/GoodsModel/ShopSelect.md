# 门店选择 <Tag text="ShopSelect" />

收款方式字典

## 用法

备注：内部依赖了 useUser ，无法展示 demo

:::demo

```vue
<template>
  <Form
    label-width="200px"
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <ShopSelect />
  </Form>
</template>

<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import ShopSelect from "shared/dictMaterials/MerchantManage/GoodsModel/ShopSelect/index.vue";
</script>
<style lang="stylus">
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性           | 说明                            | 类型                  | 默认值 |
| -------------- | ------------------------------- | --------------------- | ------ |
| modelValue     | 绑定数据                        | ISettleType           |        |
| componentProps | radio 组件入参，仅支持 disabled | { disabled: boolean } | false  |
