# 使用人数 <Tag text="UsePerson" />

使用人数-(字典不通用，待沉淀,和【游玩天数】功能重叠）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <UsePerson v-model="model.product.attr_key_value_map.usePersonNum" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import UsePerson from "lifeitem/src/dictMaterials/UsePersonNumInput/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        usePersonNum: 7,
      },
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型          | 默认值 |
| ---------- | -------- | ------------- | ------ |
| modelValue | 绑定数据 | UsePersonType |        |
