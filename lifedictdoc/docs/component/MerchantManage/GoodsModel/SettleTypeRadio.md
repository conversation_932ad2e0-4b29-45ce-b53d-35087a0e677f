# 收款方式字典 <Tag text="SettleTypeRadio" />

收款方式字典

## 用法

备注：Http 请求有兼容问题，导致无法查看 demo

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    <SettleTypeRadio />
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import SettleTypeRadio from "shared/dictMaterials/MerchantManage/GoodsModel/SettleTypeRadio/index.vue";
</script>
<style lang="stylus">
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性           | 说明                            | 类型                  | 默认值 |
| -------------- | ------------------------------- | --------------------- | ------ |
| modelValue     | 绑定数据                        | ISettleType           |        |
| componentProps | radio 组件入参，仅支持 disabled | { disabled: boolean } | false  |
