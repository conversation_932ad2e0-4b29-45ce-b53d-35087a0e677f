# 预约规则 <Tag text="NeedBook" />

预约规则（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <AppointmentRule v-model="model.product.attr_key_value_map.appointment" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import AppointmentRule from "lifeitem/src/dictMaterials/AppointmentRule/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        appointment: {
          need_appointment: true,
        },
      },
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型         | 默认值 |
| ---------- | -------- | ------------ | ------ |
| modelValue | 绑定数据 | NeedBookType |        |
