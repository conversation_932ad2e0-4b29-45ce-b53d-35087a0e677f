# 购买限制 <Tag text="LimitRule" />

购买限制

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    <LimitRuleFiled
      name="limit_rule"
      v-model="model.limit_rule"
      label="每人限制购买数量"
      required
    />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import { LimitRuleFiled } from "lifeitem/src/dictMaterials/LimitRule";

  const model = ref({
    limit_rule: { is_limit: false },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性     | 说明            | 类型          | 默认值                            |
| -------- | --------------- | ------------- | --------------------------------- |
| name     | form item name  | string        | sku.attr_key_value_map.limit_rule |
| label    | form item label | string        | 购买限制                          |
| required | 是否必填        | boolean       | true                              |
| model    | 绑定数据        | LimitRuleType |                                   |
