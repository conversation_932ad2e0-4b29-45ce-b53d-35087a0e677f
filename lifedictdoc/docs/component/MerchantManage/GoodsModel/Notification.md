# 其他说明信息 <Tag text="Notification" />

其他说明信息（字典不通用，待沉淀）

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
    :model="model"
  >
    {{ model.product.attr_key_value_map.notification }}
    <Notification v-model="model.product.attr_key_value_map.notification" />
  </Form>
</template>
<script setup>
  import { ref } from "vue";
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  import Notification from "lifeitem/src/dictMaterials/NotificationInput/index.vue";

  const model = ref({
    product: {
      attr_key_value_map: {
        notification: [
          { content: "这是一端描述" },
          { content: "这是一端描述" },
        ],
      },
    },
  });
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型             | 默认值 |
| ---------- | -------- | ---------------- | ------ |
| modelValue | 绑定数据 | NotificationType |        |
