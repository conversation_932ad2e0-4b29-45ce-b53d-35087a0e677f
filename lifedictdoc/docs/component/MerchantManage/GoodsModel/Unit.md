# 商品数量单位 <Tag text="Unit" />

商品数量单位

## 用法

:::demo

```vue
<template>
  <Form
    label-position="Left"
    :hideRequiredMark="true"
    :hideOptionalText="false"
  >
    字典待沉淀
    <!-- <Unit /> -->
  </Form>
</template>
<script setup>
  import { reactive } from "vue";
  import { Form2 as Form } from "@xhs/delight";
  // import Unit from "shared/dictMaterials/MerchantManage/GoodsModel/Unit/index.vue";
</script>
<style>
  .form {
    padding-bottom: 20px;
  }
</style>
```

:::

## API

### 扩展属性

| 属性       | 说明     | 类型     | 默认值 |
| ---------- | -------- | -------- | ------ |
| modelValue | 绑定数据 | UnitType |        |
