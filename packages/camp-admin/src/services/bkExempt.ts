import http from '@/utils/http'
import { ClaimResult } from '@/services/poiClaim'

export interface CancelPunishmentItem {
  cooperateNo: string
  kolUserId: string
  cancelReason: string
  cancelBy: string
  createdBy: string
  errorMsg: string | null
  createTime: number
}

// 获取列表
export const getCancelPunishmentList = (params: {
  cooperateNo?: string
  pageNum: number
  pageSize: number
}) => http.get<{
  cancelPunishmentList: CancelPunishmentItem[]
  total: number
}>('/api/hera/localLife/bk/query_page_punishment_cancel', {
  params,
  transform: false
})

// 导入表格
export const importCancelPunishment = (data: FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/bk/import_cancel_punishment',
  data
)
