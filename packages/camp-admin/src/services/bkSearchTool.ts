import http from '@/utils/http'

const { get } = http

interface NoteInfoItem {
  title:string
  noteId:string
  kolUserName:string
  kolUserId:string
  createDate:string
  contentType:string
  contentId:string
  contentName:string
  cooperateNo:string
  chainTime:string
  level:string
}

export interface INoteInfoParams {
  noteIds?: string
}

export enum ContentType {
  goodsSellerOutdoor = 'GOODS_SELLER_OUTDOOR', // 商品
  shopPoi = 'SHOP_POI'// 门店
}

interface CooperateInfoItem {
  cooperateNo:string
  cooperateId:string
  sellerName:string
  sellerId:string
  campaignName:string
  campaignId:string
  campaignType:string
  businessType:string
  kolUserName:string
  kolUserId:string
  fulfillmentStartTime:string
  publishStartTime:string
  publishEndTime:string
  chainStartTime:string
  chainEndTime:string
  fulfillmentStatus:string
}

export interface ICooperateInfoParams {
  cooperateNos?: string
}

// 分页查询笔记详情
export function searchBkNoteDetail(params: INoteInfoParams): Promise<NoteInfoItem[]> {
  return get('/api/hera/localLife/bk/query_bk_note_detail', { params, transform: false })
}

// 分页查询合作信息
export function searchCooperateDetail(params: ICooperateInfoParams): Promise<CooperateInfoItem[]> {
  return get('/api/hera/localLife/bk/query_bk_cooperate_detail', { params, transform: false })
}
