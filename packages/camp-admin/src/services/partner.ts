import http from '@/utils/http'

export interface PartnerParams {
  pageNo: number
  pageSize: number
  partnerAppId?: string
  partnerName?: string
}
export type PartnerRoleType = 1 | 2
export interface Partner {
  appId: string
  partnerName: string
  partnerCompanyName: string
  partnerRoleType?: PartnerRoleType // 1=纯技术 2=代运营
  partnerSellerId?: string // 代运营服务商sellerId
}
// 获取代运营服务商列表
export const fetchPartner = (params:PartnerParams) => http.get<{
  total: number
  partnerList: Partner[]
}>('/api/hera/localLife/partner/query_partner_list', { params })

export interface PartnerConfig {
  partnerAppId: string
  partnerSellerId: string
  partnerRoleType: PartnerRoleType
}
// 配置待运营服务商
export const configPartner = (data:PartnerConfig) => http.post(
  '/api/hera/localLife/partner/update_partner_config',
  data
)
