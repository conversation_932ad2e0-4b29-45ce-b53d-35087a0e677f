/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33992
  * @name: 本地入驻-用户信息
  * @identifier: api.redlife.merchant.user.info.get
  * @version: undefined
  * @path: /api/redlife/merchant/user/info
  * @method: get
  * @description: 独立入驻查询用户基本信息
  * 
  * @XHS_API_KIT-INFO
*/

import http from '@/utils/http'

export interface IProUserInfo {
	/** 专业号身份code */
	professionalCode: string
	/** 专业号身份名称 */
	professionalName: string
	/** 专业号状态 */
	status: string
	/** 专业号身份类型 */
	subjectType?: string
	/** 名称可否开店校验结果 */
	nameCheckResult?: string
}

export interface IData {
	/** 用户ID */
	userId: string
	/** 用户名称 */
	userName: string
	/** 用户头像 */
	userAvator: string
	/** 小红书号 */
	redAccountNo?: string
	/** 邮箱 */
	email?: string
	/** 专业号信息 */
	proUserInfo?: IProUserInfo
	/** b端userId */
	porchUserId?: string
	/** 店铺ID */
	sellerId?: string
	/** 店铺名称 */
	sellerName?: string
	/** 是否门店白名单用户 */
	isPoiWhiteUser?: boolean
}

export interface IGetUserInfoResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getUserInfo(options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/user/info', { transform: false, ...options })
}
