import http from '@/utils/http'

/**
 * 根据名称模糊搜索商户名称
 * @param sellerName 商户名称-搜索词
 */
export async function list(sellerName: string, sellerId: string = '') {
  const res = await http.get<Record<string, string>>('/api/hera/outdoor/activity/getSellerKV', { params: { sellerName, sellerId } })
  // console.log(res)
  const data = Object.keys(res).map(key => ({
    name: res[key],
    value: key
  }))
  return data
}
