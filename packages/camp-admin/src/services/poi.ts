import {
  PoiForm, POI, TodoPoi, PoiListParams, TodoPoiListParams, PoiGoods, SuiteImageInfo
} from '@/types/poi'
import http from '@/utils/http'

const { get, post } = http

// 获取POI列表
export function list(params: PoiListParams): Promise<{ total: number; list: POI[] }> {
  return get('/api/hera/outdoor/point/point_list', { params })
}

// 待处理POI
export function todoList(params: TodoPoiListParams) {
  return http.get<{
    count: number
    poinRelations: TodoPoi[]
  }>(
    '/api/v2/outdoor/point/relation_todo_list',
    { params }
  )
}

// 转化extend
function extendPayload(poi: PoiForm) {
  const extend = poi.type === 'CAMP' ? [
    {
      code: 'basicFacilities',
      name: '基础设施',
      value: poi.extend1
    },
    {
      code: 'campFacilities',
      name: '营地设施',
      value: poi.extend2
    },
    {
      code: 'naturalScene',
      name: '自然景观',
      value: poi.extend3
    }
  ] : [
    {
      code: 'basicFacilities',
      name: '基础设施',
      value: poi.extend1
    },
    {
      code: 'skiFacilities',
      name: '雪场设施',
      value: poi.extend2
    },
    {
      code: 'trailType',
      name: '雪道类型',
      value: poi.extend3
    }
  ]
  return {
    ...poi,
    extend,
    extend1: undefined,
    extend2: undefined,
    extend3: undefined,
  }
}

// POI详情
export async function detail(poiId: string): Promise<POI> {
  const res: any = await get('/api/hera/outdoor/point/point_detail', { params: { poiId } })
  return {
    ...res,
    // @ts-ignore
    extend1: res?.extend?.find(item => item.code === 'basicFacilities')?.value,
    // @ts-ignore
    extend2: res?.extend?.find(item => item.code === 'campFacilities' || item.code === 'skiFacilities')?.value,
    // @ts-ignore
    extend3: res?.extend?.find(item => item.code === 'naturalScene' || item.code === 'trailType')?.value,
  }
}

// 创建Poi
export function create(data: PoiForm) {
  return post('/api/hera/outdoor/point/create_point', extendPayload(data))
}

// 更新POI
export function update(data: PoiForm) {
  return post('/api/hera/outdoor/point/update_point', extendPayload(data))
}

interface PoiSearchPayload {
  provinceId: string
  cityId: string
  districtId: string
  poiKeyword: string
}

export function getPoiInternal(params: PoiSearchPayload) {
  return http.get('/api/hera/businesscenter/poi/queryPoiInternal', { params })
}

export function getPoiExternal(params: PoiSearchPayload) {
  return http.get('/api/hera/businesscenter/poi/queryPoiExternal', { params })
}

// 更换POI
export function modifyPOIRelation(
  data: {
    id: number
    poiId: string
    suiteId: string
    status?: string
  }
) {
  return http.post('/api/v2/outdoor/point/modify_poi_relation', data, undefined, { shouldLoading: true })
}

// 删除待处理POI
export function removePOIRelation(
  data: {
    id: number
    poiId: string
    suiteId: string
    status?: string
  }
) {
  return http.post('/api/v2/outdoor/point/delete_poi_relation', data, undefined, { shouldLoading: true })
}

// 获取商品
export function goods(params: {poiId:string}) {
  return http.get<{result: PoiGoods[]}>('/api/v2/outdoor/point/poi_product_list', { params })
}

// 更新POI商品
export function updateGoodsStatus(
  data: {
    poiId: string
    productId: string
    status: 'ACTIVE' | 'DISABLE'
  }
) {
  return http.post('/api/v2/outdoor/point/product_status', data)
}

// POI关联商户图片
export async function getPoiSuiteImages(poiId:string) {
  const res = await http.get<{poiSuiteImageVos: SuiteImageInfo[]}>('/api/v2/outdoor/point/poi_images', { params: { poiId } })
  const { poiSuiteImageVos } = res
  return poiSuiteImageVos
}
// 审核状态可能影响接口返回，所以不进行缓存
// const imagesCache:Record<string, SuiteImageInfo[]> = {}
// export async function getPoiSuiteImages(poiId:string) {
//   if (imagesCache[poiId]) return imagesCache[poiId]
//   const res = await http.get<{poiSuiteImageVos: SuiteImageInfo[]}>('/api/v2/outdoor/point/poi_images', { params: { poiId } })
//   const { poiSuiteImageVos } = res
//   imagesCache[poiId] = poiSuiteImageVos
//   return poiSuiteImageVos
// }

// 审核关联关系
export function auditRelation(
  data: {
    id: number
    poiId: string
    suiteId: string
    status: 'PASS' | 'PROCESSING' | 'REJECT'
  }
) {
  return http.post('/api/v2/outdoor/point/relation_audit', data)
}

// 添加关联关系
export function addRelation(
  data: {
    // id: number
    poiId: string
    poiName: string
    suiteId: string
    // status: 'PASS' | 'PROCESSING' | 'REJECT'
    // type
  }
) {
  return http.post('/api/v2/outdoor/point/create_relation', data)
}
