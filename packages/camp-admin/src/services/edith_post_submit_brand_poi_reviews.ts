/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 52650
  * @name: 提交门店装修APP
  * @identifier: api.edith.app.poi.submit_brand_poi_reviews.post
  * @version: undefined
  * @path: /api/edith/app/poi/submit_brand_poi_reviews
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from '@/utils/http'

export interface IHeaderImage {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IVideo {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IImage {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IRecommendationList {
	/** 图片 */
	image?: IImage
	/** 名称 */
	name?: string
	/** 价格 */
	price?: number
}

export interface IShopFitUpDetailInfo {
	/** 头图 */
	headerImage?: IHeaderImage[]
	/** 视频 */
	video?: IVideo[]
	/** 营业时间 */
	openTime?: string
	/** 营业时间(语义化) */
	openTimeStr?: string
	/** 均价 */
	avgPrice?: number
	/** 推荐菜 */
	recommendationList?: IRecommendationList[]
	/** 电话 */
	phoneNumber?: string[]
	/** 营业状态 */
	businessType?: string
	/** 审核信息 */
	auditRemark?: string
}

export interface IPostSubmitBrandPoiReviewsPayload {
	/** / */
	shopIdList?: string[]
	/** 门店装修详细信息 */
	shopFitUpDetailInfo?: IShopFitUpDetailInfo
	/** / */
	sourceFrom?: string
	/** / */
	sellerId?: string
}

export interface IData {
	/** / */
	shopIdList?: string[]
}

export interface IPostSubmitBrandPoiReviewsResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postSubmitBrandPoiReviews(payload: IPostSubmitBrandPoiReviewsPayload, options = {}): Promise<IData> {
  return http.post('/api/edith/app/poi/submit_brand_poi_reviews', payload, { transform: false, ...options })
}
