import http from '@/utils/http'
import { ClaimResult } from '@/services/poiClaim'

export interface NoteItem {
  'noteId': string
  'contentId': string
  'content_type': string
  'businessType': string
  'reason': string
  'createdBy': string
  'createTime': number
  'eventType': string
}

export type NoteList = NoteItem[]

export interface HistoryItem {
  reason: string
  eventType: string
  createdBy: string
  createTime: number
  contentId: string
}

// 导入刷数笔记数据
export const importNoteInfo = (data: FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/note/import_note_detail_info',
  data
)

// 导入批量删除表格
export const notesUnbind = (data: FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/note/import_unbrand_note_detail_info',
  data
)

// 分页查询笔记列表
export const getNoteList = (params: {
  noteId?: string
  pageSize: number
  pageNum: number
}) => http.get<{
  total: number
  brushNotePageList: NoteList
}>(
  '/api/hera/localLife/note/brush_note_page',
  {
    params,
    transform: false
  }
)

// 笔记解绑
export const noteUnbind = (data: {noteId: string}) => http.post(
  '/api/hera/localLife/note/unbind_brush_note',
  data,
  {
    transform: false
  }
)

// 导出表格
export const exportNote = (data: any = undefined) => http.post<{
  url: string
}>(
  '/api/hera/localLife/note/download_brush_note',
  data
)

// 查看操作记录
export const lookHistory = (params: {noteId: string}) => http.get<HistoryItem[]>(
  '/api/hera/localLife/note/query_brush_note_history',
  {
    params,
    transform: false
  }
)
