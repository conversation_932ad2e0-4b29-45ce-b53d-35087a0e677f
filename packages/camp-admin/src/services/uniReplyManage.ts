import http from 'shared/http'
import dayjs from 'dayjs'

export interface QAItem {
  question_description: string
  question_answer: string
  id?: number
}

export enum QuestionTypeEnum {
  TEXT = 'text',
  IMG = 'img'
}

export interface QuestionItem {
  user_id: string
  latest_time: number
  latest_answer?: string
  questions: {
    type: QuestionTypeEnum
    content: string
  }[]
}

// 自动回复配置列表
export const getQAList = async () => {
  const res = await http.get<{ question_configs: QAItem[] }>('/api/hera/organize/question_config/list')
  return {
    listSource: res?.question_configs?.map((item, index) => ({ ...item, key: index + 1 }))
  }
}

// 新增/编辑自动回复配置
export const upsertQA = (data: QAItem) => http.post('/api/hera/organize/question_config/upsert', data)

// 删除自动回复配置
export const deleteQA = (id: number) => http.post('/api/hera/organize/question_config/delete', { id })

// 获取问题列表
export const getQuestionList = async (params: { page_num: number; page_size: number }) => {
  const res = await http.get<{
    count: number
    questions: QuestionItem[]
  }>('/api/hera/organize/question/list', { params })
  return {
    listSource: res.questions?.map(item => ({
      ...item,
      latest_time: dayjs(item.latest_time).format('YYYY-MM-DD HH:mm:ss'),
    })),
    total: res.count
  }
}

// 回复问题
export const replyQuestion = (data: { user_id: string; answer: string }) => http.post('/api/hera/organize/question/answer', data)
