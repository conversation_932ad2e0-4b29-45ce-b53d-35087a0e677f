import http from '@/utils/http'

import { ClaimResult } from '@/services/poiClaim'

export interface KolParams {
  kolUserId?: string
  pageNo: number
  pageSize: number
}
export interface KolItem {
  kolUserId: string
  sellerId: string
}
// 获取kol绑定数据
export const fetchKol = (params: KolParams) => http.get<{
  kolSellerVOS: KolItem[]
  total: number
}>(
  '/api/hera/localLife/kol/get_kol_seller_list',
  {
    params
  }
)

// 导入kol绑定数据
// 批量认领 file=@"/Users/<USER>/final_poi_bind_seller.xlsx"'
export const importKolRelation = (data:FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/kol/import_kol_seller_commerce_relation',
  data
)

// 博主绑定删除按钮
export const deleteBinding = (data: {
  kolUserId: string
  sellerId: string
}) => http.post('/api/hera/localLife/kol/deleted_kol_seller', data, { transform: false })
