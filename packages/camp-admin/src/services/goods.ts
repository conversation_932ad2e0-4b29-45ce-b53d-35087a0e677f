import { IGoodsInfo } from '@/types/goods'
import { GoodsParams } from '@/types/activity'
import http from '@/utils/http'

// 获取商品详情
export function detail(productId: string):Promise<IGoodsInfo> {
  return http.get('/api/hera/outdoor/activity/product/get', {
    params: {
      productId
    }
  })
}

export function getGoodsType(goods?: IGoodsInfo): ('CAMP' | 'SKI') {
  const productType = goods?.baseInfo?.productType || ''
  if (productType === 'Tent') {
    return 'CAMP'
  }
  if (['SkiingLessonSpu', 'SkiingLessonSku', 'SkiingHireSpu', 'SkiingHireSku', 'SkiingComboSpu', 'SkiingComboSku'].includes(productType)) {
    return 'SKI'
  }
  return 'CAMP'
}

// 商品列表
interface GoodsResponse {
  pageNum: number
  pageSize: number
  total: number
  totalPage: number
  list: IGoodsInfo[]
}
export async function list(params: GoodsParams):Promise<GoodsResponse> {
  const res = await http.get<GoodsResponse>('/api/hera/outdoor/activity/product/list', { params })
  res.list.forEach(item => {
    item.type = getGoodsType(item)
  })
  return res
}
