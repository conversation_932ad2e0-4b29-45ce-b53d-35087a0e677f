import http from '@/utils/http'

// 创建关联关系
export interface AccountData {
  mainUserId: string
  burnerUserId: string
}
export const createAccountRelation = (
  data: AccountData
) => http.post('/api/hera/localLife/account/relationship', data, { transform: false })

// 取消关联关系
export const delAccountRelation = (id: string) => http.del(`/api/hera/localLife/account/relationship/${id}`, { transform: false }, { shouldLoading: true })

// 获取关联关系列表
export interface RelationItem {
  id: string
  mainAccountName: string
  mainAccountUserId: string
  mainAccountSellerId: string
  burnerAccountName: string
  burnerAccountUserId: string
  burnerAccountSellerId: string
  relationshipStatus: string
}
export const fetchAccountRelation = (
  params: {
    mainUserId: string
    pageNo: number
    pageSize: number
  }
) => http.get<{
  accountRelationshipList: RelationItem[]
  total: number
}>('/api/hera/localLife/account/relationship', { params, transform: false })

// 获取日志信息
export interface RelationLogItem {
  operator: string
  operateType: string
  operateTime: string
}
export const fetchAccountRelationLog = (
  id: string
) => http.get<{
  accountRelationshipLogList: RelationLogItem[]
}>('/api/hera/localLife/account/relationship/log', { params: { id }, transform: false }, { shouldLoading: true })

// 搜索账号
export interface UserItem {
  userId: number
  imageS: string
  imageB: string
  userNickname: string
}
export const searchAccount = (userId:string) => http.get<UserItem[]>(
  '/api/hera/localLife/account',
  {
    params: {
      userId
    },
    transform: false
  },
)
