import http from '@/utils/http'

import { ClaimResult } from './poiClaim'

// 查询区域里边
export interface QueryAreaParams {
  sellerId: string
  sellerName: string
  poiGroupId: string
  poiGroupName: string
  pageNo: number
  pageSize: number
}
export interface AreaItem {
  'sellerId': string
  'sellerName': string
  'poiGroupId': string
  'poiGroupName': string
  'poiNum': string
  'status'?: 'QUAL_COMMITED' | 'INIT'
  'statusText': string
  'poiGroupType': string
  'poiGroupTypeText': string
  'updateTime': string
  'createTime': string
}
export const queryArea = (
  params: QueryAreaParams
) => http.get<{
  total: number
  poiGroupList: AreaItem[]
}>('/api/hera/localLife/poiGroup/pageQueryPoiGroupList', {
  params,
  transform: false
})

// 查询门店列表
export interface QueryShopParams {
  poiGroupId: string
  sellerId: string
  pageNo: number
  pageSize: number
}
export interface PoiItem {
  shopId: string
  shopName: string
}
export const queryShop = (
  params: QueryShopParams
) => http.get<{
  total: number
  poiList: PoiItem[]
}>('/api/hera/localLife/poiGroup/pageQueryPoiGroupBindPoiList', {
  params,
  transform: false
})

// 查看资质信息
export interface AreaQual {
  'userId': string
  'poiGroupId': string
  'companySubjectType': string // 普通企业店/个体工商户
  'poiGroupName': string // 区域企业名称
  'companyLicenseNo': string // 区域统一社会信用代码
  'companyLicenseUrl': string // 区域营业执照URL
  'companyLicenseIsPermanent': string // 0 非长期有效; 1 长期有效
  'companyLicenseStartTime': string // 区域营业执照有效期开始时间
  'companyLicenseEndTime': string // 区域营业执照有效期结束时间
  'idCardType': string // 身份证/护照/港澳通行证/台胞证
  'idCardFortUrl': string // 法人身份证件正面
  'idCardBackUrl': string // 法人身份证件背面
  'idCardStartTime': string // 法人身份证件有效时间-开始时间
  'idCardEndTime': string // 法人身份证件有效时间-结束时间
  'maintainerName': string // 法人姓名
  'maintainerIdentifyNo': string // 法人证件号码
  'maintainerPhone': string // 区域运营者联系手机号码

}
export const queryAreaQual = (poiGroupId: string, sellerId: string) => http.get<AreaQual>(
  '/api/hera/localLife/poiGroup/getPoiGroupQualification',
  {
    params: { poiGroupId, sellerId },
    transform: false
  }
)

// 上传门店POI资质 'file=@"/Users/<USER>/import_person_qual_template.xlsx"'
export const importAreaQualication = (data: FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/poiGroup/importPoiGroupQualification',
  data
)
