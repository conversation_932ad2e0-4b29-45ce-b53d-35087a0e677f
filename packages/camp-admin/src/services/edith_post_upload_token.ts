/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 52706
  * @name: 获取专业号文件上传 token-APP
  * @identifier: api.edith.app.userqms.prof.upload.token.post
  * @version: undefined
  * @path: /api/edith/app/userqms/prof/upload/token
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from '@/utils/http'

export interface IPostUploadTokenPayload {
	/** / */
	bizName?: string
	/** / */
	scene?: string
	/** / */
	fileCount?: string
}

export interface IUploadTempPermit {
	/** / */
	secretId?: string
	/** / */
	secretKey?: string
	/** / */
	token?: string
	/** / */
	uploadAddr?: string
	/** / */
	expireTime?: number
	/** / */
	qos?: number
	/** / */
	cloudType?: number
	/** / */
	bucket?: string
	/** / */
	region?: string
	/** / */
	fileIds?: string[]
	/** / */
	masterCloudId?: number
	/** / */
	uploadId?: number
	/** / */
	cdnDomain?: string
}

export interface IUploadLimitPolicy {
	/** / */
	fileTypes?: string[]
	/** / */
	maxSize?: number
}

export interface IData {
	/** 上传通道临时凭证列表 */
	uploadTempPermits?: IUploadTempPermit[]
	/** 上传限制策略 */
	uploadLimitPolicy?: IUploadLimitPolicy
}

export interface IPostUploadTokenResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postUploadToken(payload: IPostUploadTokenPayload, options = {}): Promise<IData> {
  return http.post('/api/edith/app/userqms/prof/upload/token', payload, { transform: false, ...options })
}
