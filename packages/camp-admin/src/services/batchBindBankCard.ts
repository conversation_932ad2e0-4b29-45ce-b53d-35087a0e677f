import http from '@/utils/http'
import { BizTypeEnum } from 'shared/components/TableCellDesensitize'

export const enum CardConfirmStatus {
  NOT_CONFIRM = 'NOT_CONFIRM',
  CONFIRM = 'CONFIRM',
  REFUSE = 'REFUSE',
  INVALID= 'INVALID'
}

export const enum CardBindStatus {
  UN_BIND = 'UN_BIND',
  BIND = 'BIND',
  WAIT_BIND = 'WAIT_BIND'
}

export interface GetBankCardOptionalParams {
  shopId?: string
  sellerId?: string
  cardConfirmStatus?: CardConfirmStatus // 绑卡信息确认状态：NOT_CONFIRM:未确认；CONFIRM:已确认；REFUSE:已拒绝；
  cardBindStatus?: CardBindStatus // 绑卡状态，UN_BIND：未绑定； BIND：已绑定
}

export interface GetBankCardParams extends GetBankCardOptionalParams {
  pageSize?: number
  pageNo?: number
  page_size?: number
  page_num?: number
}

export interface PoiBankCardDataItem {
  sellerId: string // 商家id
  sellerName: string // 商家名称
  shopId: string // 门店id
  shopName: string // 门店名称
  cardUserName: string // 绑卡人名称
  cardUserCertNo: string // 身份证号
  cardNo: string // 银行卡号
  bankName: string // 开户行名称
  mobile: string // 手机号
  bankSubBranchName: string // 支行信息
  cardInfoOperator: string // 绑卡信息导入人
  bankConfirmUrl: string // 绑卡确认链接
  cardConfirmStatus: CardConfirmStatus // 绑卡信息确认状态：NOT_CONFIRM:未确认；CONFIRM:已确认；REFUSE:已拒绝；
  confirmAccountName: string // 绑卡确认账号名称
  cardBindOperator: string // 绑卡操作人
  cardBindStatus?: CardBindStatus // 绑卡状态，UN_BIND：未绑定； BIND：已绑定
  cardBindFailedReason: string // 绑卡失败原因
  id: number | string
  key?: number | string
  secureFields: string[]
}

export type PoiBankCardDataList = PoiBankCardDataItem[]

export interface GetBankCardRes {
  dataList: PoiBankCardDataList
  total: number
  secureFields: string[]
}

export interface GetLongTaskRes {
  id: string
  finishedCount?: number
  totalCount?: number
  status: number
  result: {
    extraJson?: string
    fileUrl: string
    msg: string
    success: boolean
  }
}

export const getPoiBankCardList = async (data: GetBankCardParams) => {
  const params: GetBankCardParams = {
    ...data,
    pageSize: data.page_size,
    pageNo: data.page_num
  }
  delete params.page_size
  delete params.page_num
  const res = await http.post<GetBankCardRes>('/api/hera/localLife/poi/get_poi_bank_card', params, { transform: false })
  return {
    listSource: res.dataList?.map(item => {
      if (!item.key) {
        item.key = item.id
      }
      item.secureFields = res?.secureFields
      return item
    }) || [],
    total: res.total || 0,
  }
}

export const getPoiBankCardBindSwitch = () => http.get<boolean>('/api/hera/localLife/poi/getPoiBankCardCanBindSwitch')

// 脱敏数据
export const getSecureUserData = rowData => async () => {
  const payload = {
    bizId: rowData?.id,
    bizType: BizTypeEnum.POI_BANK_CARD,
    secureFields: ['dataList.cardUserName', 'dataList.cardNo', 'dataList.mobile', 'dataList.cardUserCertNo'],
    customInfo: {}
  }
  const res = await http.post('/api/hera/localLife/poi/secure/data', payload, { transform: false })
  return res
}
