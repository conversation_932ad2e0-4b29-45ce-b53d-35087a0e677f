/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 52708
  * @name: 置换永久 fileId-APP
  * @identifier: api.edith.app.userqms.prof.file.replace.post
  * @version: undefined
  * @path: /api/edith/app/userqms/prof/file/replace
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from '@/utils/http'

export interface IUploaderInfoModelList {
	/** / */
	url?: string
	/** / */
	bizName?: string
	/** / */
	scene?: string
	/** / */
	fileId?: string
	/** / */
	isSecret?: boolean
	/** / */
	cloudType?: number
}

export interface IPostFileReplacePayload {
	/** / */
	uploaderInfoModelList?: IUploaderInfoModelList[]
}

export interface IUploaderInfoModelList1 {
	/** / */
	url?: string
	/** / */
	bizName?: string
	/** / */
	scene?: string
	/** / */
	fileId?: string
	/** / */
	isSecret?: boolean
	/** / */
	cloudType?: number
}

export interface IData {
	/** / */
	uploaderInfoModelList?: IUploaderInfoModelList1[]
}

export interface IPostFileReplaceResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postFileReplace(payload: IPostFileReplacePayload, options = {}): Promise<IData> {
  return http.post('/api/edith/app/userqms/prof/file/replace', payload, { transform: false, ...options })
}
