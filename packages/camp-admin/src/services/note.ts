import { http } from '@xhs/launcher'

import { Note, NoteListParams, NoteQueryParams } from '@/types/note'

const { get, post } = http

// 获取笔记列表
export function list(params: NoteListParams): Promise<{ total: number; list: Note[] }> {
  return get('/api/hera/outdoor/point/note_list', { params })
}

// 置顶笔记
export function setTop(data: { noteId: string; poiId: string }): Promise<{ total: number; list: Note[] }> {
  return post('/api/hera/outdoor/point/set_top', data)
}

// 取消置顶
export function deleteTop(data: { noteId: string; poiId: string }): Promise<{ total: number; list: Note[] }> {
  return post('/api/hera/outdoor/point/delete_note', data)
}

// 创建笔记
export function create(data: { noteId: string; poiId: string }): Promise<{ total: number; list: Note[] }> {
  return post('/api/hera/outdoor/point/create_note', data)
}

// 笔记查询
export function query(data: NoteQueryParams): Promise<Note[]> {
  return post('/api/hera/outdoor/point/query_note', data)
}
