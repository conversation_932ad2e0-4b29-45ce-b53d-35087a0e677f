/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 52668
  * @name: 提交审核（提交认领）APP
  * @identifier: api.edith.app.businesscenter.poi.submitapply.post
  * @version: undefined
  * @path: /api/edith/app/businesscenter/poi/submitapply
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from '@/utils/http'

export interface IUploaderInfoModel {
	/** 文件直链地址 */
	url?: string
	/** 业务名称（例如：poi绑定） */
	bizName?: string
	/** 上传场景（例如：营业执照、资质证明等） */
	scene?: string
	/** 文件ID */
	fileId?: string
	/** 是否为涉密文件 */
	isSecret?: boolean
	/** 存储类型（例如：阿里云、腾讯云等） */
	cloudType?: number
}

export interface IFileAttachment {
	/** 附件文件名（不能为空） */
	filename?: string
	/** 文件访问地址 */
	url?: string
	/** 上传人信息 */
	uploaderInfoModel?: IUploaderInfoModel
	/** 文件类型（如：image/jpeg） */
	fileType?: string
}

export interface ICertificateQual {
	/** 资质ID */
	id?: number
	/** 资质编码（不能为空） */
	qualificationTypeCode?: string
	/** 资质名称 */
	qualificationTypeName?: string
	/** 有效期开始时间（格式：yyyy-MM-dd） */
	startTime?: string
	/** 有效期结束时间（格式：yyyy-MM-dd） */
	endTime?: string
	/** 是否永久有效（1=永久，0=有起止时间） */
	isPermanent?: number
	/** 资质附件列表（不能为空） */
	fileAttachments?: IFileAttachment[]
}

export interface IUploaderInfoModel1 {
	/** 文件直链地址 */
	url?: string
	/** 业务名称（例如：poi绑定） */
	bizName?: string
	/** 上传场景（例如：营业执照、资质证明等） */
	scene?: string
	/** 文件ID */
	fileId?: string
	/** 是否为涉密文件 */
	isSecret?: boolean
	/** 存储类型（例如：阿里云、腾讯云等） */
	cloudType?: number
}

export interface IFileAttachment1 {
	/** 附件文件名（不能为空） */
	filename?: string
	/** 文件访问地址 */
	url?: string
	/** 上传人信息 */
	uploaderInfoModel?: IUploaderInfoModel1
	/** 文件类型（如：image/jpeg） */
	fileType?: string
}

export interface ILicenseChgQual {
	/** 资质ID */
	id?: number
	/** 资质编码（不能为空） */
	qualificationTypeCode?: string
	/** 资质名称 */
	qualificationTypeName?: string
	/** 有效期开始时间（格式：yyyy-MM-dd） */
	startTime?: string
	/** 有效期结束时间（格式：yyyy-MM-dd） */
	endTime?: string
	/** 是否永久有效（1=永久，0=有起止时间） */
	isPermanent?: number
	/** 资质附件列表（不能为空） */
	fileAttachments?: IFileAttachment1[]
}

export interface IPostPoiSubmitapplyPayload {
	/** 记录ID */
	id?: number
	/** 门店ID（不能为空） */
	poiId?: string
	/** 门店名称 */
	poiName?: string
	/** 门店地址 */
	poiAddress?: string
	/** 门店联系电话列表 */
	poiTelephones?: string[]
	/** 主体性质（不能为空） */
	refType?: string
	/** 主体类型 */
	refSubType?: string
	/** 审核状态（通过枚举转换后的状态） */
	auditStatus?: string
	/** 其它证明性材料 */
	certificateQual?: ICertificateQual
	/** 营业执照变更证明 */
	licenseChgQual?: ILicenseChgQual
	/** 是否提交 */
	isSubmit?: number
	/** 审核拒绝原因 */
	auditRemark?: string
	/** 营业执照图片列表 */
	companyLicense?: string[]
	/** 公司名称 */
	companyName?: string
}

export interface IData {
	/** / */
	applyId?: number
}

export interface IPostPoiSubmitapplyResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postPoiSubmitapply(payload: IPostPoiSubmitapplyPayload, options = {}): Promise<IData> {
  return http.post('/api/edith/app/businesscenter/poi/submitapply', payload, { transform: false, ...options })
}
