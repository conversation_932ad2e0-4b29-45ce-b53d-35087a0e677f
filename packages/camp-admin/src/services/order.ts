// 6168fb13fa6da30001a05a6b 测试sellerId

import http from '@/utils/http'

import { OrderListParams, OrderItem, OrderSnapshot } from '@/types/order'

/**
 * 查询订单商品快照
 * @param thirdOrderId 订单Id  Pcp657796521739483431
 */
export async function orderPackageSnapshot(thirdOrderId:string) {
  return http.get<{orderSnapshot: OrderSnapshot}>('/api/v2/outdoor/order/order_package_snapshot', { params: { thirdOrderId } })
}

// 订单列表
export async function orderList(params:OrderListParams) {
  return http.get<{
    items: OrderItem[]
    pagination: {
      pageNo: number
      perPage: number
      total: number
    }
  }>('/api/hera/outdoor/order_list', {
    params: {
      ...params,
      pageNo: params.pageNum,
      perPage: params.pageSize,
      pageNum: undefined,
      pageSize: undefined
    }
  })
}
