import { Activity, ActivityListParams, ActivityCreatePayload } from '@/types/activity'
import http from '@/utils/http'
import { list as fetchGoods, getGoodsType } from './goods'

const { get, post } = http

interface ListResponse {
  pageNum: number
  pageSize: number
  total: number
  totalPage: number
  list: Activity[]
}
export function list(params: ActivityListParams): Promise<ListResponse> {
  return get('/api/hera/outdoor/activity/list', { params })
}

export function create(data: ActivityCreatePayload) {
  return post('/api/hera/outdoor/activity/create', data)
}

export function update(data: ActivityCreatePayload) {
  return post('/api/hera/outdoor/activity/update', data)
}

interface StatusPayload {
  activityParentId: string | number
  status: number
  reason: string
}
export function changeStatus(data: StatusPayload) {
  return post('/api/hera/outdoor/activity/change/status', data)
}

// 活动详情
export async function detail(activityParentId: string) {
  const res = await get<Activity>('/api/hera/outdoor/activity/detail', { params: { activityParentId } })
  res.type = getGoodsType(res.items[0]?.productInfo)
  return res
}

// 商品列表
export const goods = fetchGoods
