import http from '@/utils/http'

import { ClaimResult } from './poiClaim'

// 上传门店装修 'file=@"/Users/<USER>/门店装修模板.xlsx"'
export const importShop = (data:FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/poi/import_poi_detail_info',
  data
)

// 获取店铺详情
export interface PoiDetail {
  addressDetail: string
  cityName: string
  districtName: string
  latitude: number
  longitude: number
  poiId: string
  poiName: string
  provinceName: string
  sellerId: string
  storeBasedFeature: {
    auditStatus: string
    avgPrice: string
    images: {url: string}[]
    mobiles: string[]
    openTime: string
    recommendCaters: {name:string;url:string;price:number}[]
    videos: null
  }
}
export const fetchShopDetail = (poiId:string) => http.get<PoiDetail>(
  '/api/hera/localLife/poi/point_detail',
  { params: { poiId } }
)
