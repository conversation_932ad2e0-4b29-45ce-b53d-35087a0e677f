import http from '@/utils/http'

// 创建接口
export const createInterface = (params?: CreateInterfaceRequest) => http.post('/api/hera/admin/opencore/interfaces/createInterface', params, {
  transform: false
})

// 编辑接口
export const updateInterface = (params?: EditInterfaceResponse) => http.post('/api/hera/admin/opencore/interfaces/updateInterface', params, {
  transform: false
})

// 详情接口
export const queryInterfaceByInterfaceId = (interfaceId?: string): Promise<{interfaces: EditInterfaceResponse[]}> => http.post('/api/hera/admin/opencore/interfaces/queryInterfaceByInterfaceId', { interfaceId }, {
  transform: false
})

// 导入接口
export const parseEdithParams = (paramsStr?: string): Promise<{requestParams: InterfaceParamDTO[] ; responseParams: InterfaceParamDTO[]}> => http.post('/api/hera/admin/opencore/interfaces/parseEdithParams', { paramsStr }, {
  transform: false
})

// 接口列表
export const pageQueryInterface = (params?: PageQueryInterfaceRequest): Promise<PageQueryInterfaceResponse> => http.post('/api/hera/admin/opencore/interfaces/pageQueryInterface', params, {
  transform: false
})

// 接口启用禁用
export const batchUpdateInterfaceStatus = (params?: BatchUpdateInterfaceStatusRequest): Promise<void> => http.post('/api/hera/admin/opencore/interfaces/batchUpdateInterfaceStatus', params, {
  transform: false
})
// 接口包列表
export const pageQueryPackage = (params?: PageQueryPackageRequest): Promise<PageQueryPackageResponse> => http.post('/api/hera/admin/opencore/packages/pageQueryPackage', params, {
  transform: false
})

// 接口包列表
export const createPackage = (params?: CreatePackageRequest): Promise<void> => http.post('/api/hera/admin/opencore/packages/createPackage', params, {
  transform: false
})

export const updatePackage = (params?: UpdatePackageRequest): Promise<void> => http.post('/api/hera/admin/opencore/packages/updatePackage', params, {
  transform: false
})

export const queryPackageByPackageId = (packageId?: string): Promise<{openPackages: OpenPackageDTO[]}> => http.post('/api/hera/admin/opencore/packages/queryPackageByPackageId', { packageId }, {
  transform: false
})

// 接口包列表
export const batchUpdatePackageStatus = (params?: BatchUpdatePackageStatusRequest): Promise<void> => http.post('/api/hera/admin/opencore/packages/batchUpdatePackageStatus', params, {
  transform: false
})

export enum InterfaceTypeEnum {
  // Define your enum values here
  /* rpc接口 */
  CALL_RPC = 'CALL_RPC',
  /* http接口 */
  CALL_HTTP = 'CALL_HTTP',
  /* callback事件 */
  CALLBACK_EVENT = 'CALLBACK_EVENT',
}

export interface CreateInterfaceRequest {
  /** 系统code */
  sysCode: string

  /** 接口名称 */
  interfaceName: string

  /** 接口描述 */
  interfaceDesc?: string

  /** 接口类型 */
  interfaceType: InterfaceTypeEnum

  /** 服务名 */
  serviceName: string

  /** 类全路径 */
  clazzFullPath?: string

  /** 方法名 */
  methodName: string

  timeoutMilliSeconds?: number

  /** API请求路径 */
  apiRequestPath: string

  apiRequestPathAlias: string

  /** API请求类型 */
  apiRequestMethod: ApiRequestMethodEnum

  /** 接口请求参数 */
  requestParams: InterfaceParamDTO[]

  /** 接口返回参数 */
  responseParams: InterfaceParamDTO[]

  /* 是否支持三方 */
  supportThird: boolean
}

export enum ApiRequestMethodEnum {
  GET = 'GET',
  POST = 'POST'
}

export enum InterfaceFieldTypeEnum {
  /* 布尔类型 */
  BOOL ='BOOL',
  /* byte类型 */
  BYTE ='BYTE',
  /* short类型 */
  INT16 ='INT16',
  /* intger类型 */
  INT32 ='INT32',
  /* long类型 */
  INT64 ='INT64',
  /* 浮点类型 */
  FLOAT64 ='FLOAT64',
  /* 字符类型 */
  STRING ='STRING',
  /* 结构体 */
  STRUCT ='STRUCT',
  /* 结构体 */
  LOOPSTRUCT ='LOOPSTRUCT',
  /* Map类型 */
  MAP ='MAP',

  /* list类型 */
  LIST_BOOL ='LIST_BOOL',
  LIST_BYTE ='LIST_BYTE',
  LIST_INT16 ='LIST_INT16',
  LIST_INT32 ='LIST_INT32',
  LIST_INT64 ='LIST_INT64',
  LIST_FLOAT64 ='LIST_FLOAT64',
  LIST_STRING ='LIST_STRING',
  LIST_STRUCT ='LIST_STRUCT',
  LIST_LOOPSTRUCT ='LIST_LOOPSTRUCT',
  LIST_MAP ='LIST_MAP',

  /* 枚举类型 */
  ENUM_BOOL ='ENUM_BOOL',
  ENUM_BYTE ='ENUM_BYTE',
  ENUM_INT16 ='ENUM_INT16',
  ENUM_INT32 ='ENUM_INT32',
  ENUM_INT64 ='ENUM_INT64',
  ENUM_FLOAT64 ='ENUM_FLOAT64',
  ENUM_STRING ='ENUM_STRING',
  ENUM_STRUCT ='ENUM_STRUCT',
  ENUM_LOOPSTRUCT ='ENUM_LOOPSTRUCT',
  ENUM_MAP ='ENUM_MAP',
  JSON='JSON' // 3.11 新增
}
export enum ApiParamSourceFrom {
  // Define your enum values here
  // Path参数
  QUERY='QUERY',
  // Header
  HEADER ='HEADER',
  // Body请求体
  BODY ='BODY',
}

export interface InterfaceParamDTO {
  /** 参数字段 */
  fieldName: string

  /** 参数名称 */
  fieldComment?: string

  /** 参数类型 */
  fieldType: InterfaceFieldTypeEnum

  /** 是否必填 */
  fieldRequired: boolean

  /** 参数对应网关字段 */
  fieldApiName: string

  /** 参数对应网关字段名称 */
  fieldApiComment?: string

  /** 参数下的嵌套参数 */
  fieldChildren?: InterfaceParamDTO[]

  /** 请求来源 */
  sourceFrom?: ApiParamSourceFrom

  /** 是否是rpc中通用Result对象 */
  fieldCommonResult?: boolean
}

interface PageParam {
  pageNo: number
  pageSize: number
  // Define fields for pagination parameters
}

interface PageQueryInterfaceRequest {
  /** 分页参数 */
  pageParam: PageParam

  /** 接口ID */
  interfaceId?: string

  /** 接口名称 */
  interfaceName?: string

  status?: CommonStatusEnum
}

interface InterfaceDTO {
  /** 接口ID */
  interfaceId: string

  /** 系统code */
  sysCode: string

  /** 接口名称 */
  interfaceName: string

  /** 接口描述 */
  interfaceDesc?: string

  /** 接口版本 */
  interfaceVersion?: string

  /** 接口类型 */
  interfaceType: InterfaceTypeEnum

  /** 服务名 */
  serviceName: string

  /** 类全路径 */
  clazzFullPath?: string

  /** 方法名 */
  methodName: string

  /** API请求路径 */
  apiRequestPath: string

  apiRequestPathAlias: string

  /** API请求类型 */
  apiRequestMethod: ApiRequestMethodEnum

  /** 接口请求参数 */
  requestParams?: InterfaceParamDTO[]

  /** 接口返回参数 */
  responseParams?: InterfaceParamDTO[]

  /** 接口状态 */
  status?: CommonStatusEnum
}

interface PageQueryInterfaceResponse {

  /** 接口列表 */
  interfaces?: InterfaceDTO[]

  /** 总数 */
  total?: number
}

interface EditInterfaceResponse extends CreateInterfaceRequest {
  interfaceId: string
}

// // 获取笔记列表
// export function create(params: CreateInterfaceRequest): Promise<{ total: number; list: Note[] }> {
//   return post('/api/hera/admin/opencore/interfaces/createInterface', { params })
// }

export enum CommonStatusEnum {
  // Define your enum values here
  /** 有效的 * */
  VALID = 'VALID',
  /** 无效的 * */
  INVALID = 'INVALID',
}

interface BatchUpdateInterfaceStatusRequest {
  /** 接口包IDS */
  interfaceIds: string[]

  /** 状态 */
  status: CommonStatusEnum
}

interface PageParam {
  pageNo: number
  pageSize: number
}

interface PageQueryPackageRequest {
  /** 分页参数 */
  pageParam: PageParam

  /** 接口包ID */
  packageId?: string

  /** 接口包名称 */
  packageName?: string
}

interface PageQueryPackageResponse {

  /** 开放接口包列表 */
  openPackages?: OpenPackageDTO[]

  /** 总数 */
  total?: number
}

export interface CreatePackageRequest {
  /** 接口包名称 */
  packageName: string

  /** 接口包描述 */
  packageDesc: string

  /** 接口ID列表 */
  interfaceIds: string[]
}

interface UpdatePackageRequest {
  /** 接口包ID */
  packageId: string

  /** 接口包名称 */
  packageName: string

  /** 接口包描述 */
  packageDesc?: string

  /** 新增接口ID列表 */
  addInterfaceIds?: string[]

  /** 移除接口ID列表 */
  removeInterfaceIds?: string[]
}

interface BatchUpdatePackageStatusRequest {
  /** 接口包IDS */
  packageIds: string[]

  /** 状态 */
  status: CommonStatusEnum
}

interface OpenPackageDTO {
  /** 接口包ID */
  packageId: string

  /** 接口包名称 */
  packageName: string

  /** 接口包描述 */
  packageDesc: string

  /** 状态 */
  status?: CommonStatusEnum

  interfaceIds: string[]
}
