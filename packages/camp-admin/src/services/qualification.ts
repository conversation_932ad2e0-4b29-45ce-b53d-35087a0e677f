import http from '@/utils/http'

import { ClaimResult, PoiItem } from './poiClaim'

// 上传POI法人资质 'file=@"/Users/<USER>/import_person_qual_template.xlsx"'
export const importPoiPersionQualication = (data:FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/qual/import_poi_legal_person_qual_info',
  data
)

// 上传门店POI资质 'file=@"/Users/<USER>/import_person_qual_template.xlsx"'
export const importPoiQualication = (data:FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/qual/import_poi_base_qual_info',
  data
)

export interface LicenseQulDetail {
  'type': 'BUSINESS_LICENSE' | 'FOOD_BUSINESS_LICENSE' | 'LEGAL_PERSON_ID_CARD_FRONT'
  'qualTitle': string
  'userDefinedId': string
  'qualificationUrl': string
  'qualDataList': {
    'nameDesc': string
    'name': string
    'valueDesc': string
    'value': string
  }[]
  'permanent': boolean
  'startTime': string
  'endTime': string
}

export interface QualificationItem {
  qualDetailList: LicenseQulDetail[]
  poiInfo: PoiItem
}

// 资质列表
export const fetchQualification = (
  params: {
    userId?: string
    poiId?: string
  }
) => http.get<{
  poiQualInfoList?: QualificationItem[]
}>(
  '/api/hera/localLife/qual/batch_get_poi_qual_info',
  {
    params
  }
)
