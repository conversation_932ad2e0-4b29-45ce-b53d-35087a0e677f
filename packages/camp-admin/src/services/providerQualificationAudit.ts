import http from 'shared/http'
import { env } from '@/config/http.config'

// 联调时请求没走logan，原因未知
const baseURLMap = {
  development: 'http://hera-providermarket.sl.sit.xiaohongshu.com',
  test: 'http://hera-providermarket.sl.sit.xiaohongshu.com',
  prerelease: '',
  production: '',
}

export enum ApplyStatusEnum {
  APPROVED = 3,
  PROCESSING = 1,
  REJECT = 2,
}

export const ApplyStatusTextMap = {
  [ApplyStatusEnum.APPROVED]: '已通过',
  [ApplyStatusEnum.PROCESSING]: '待审核',
  [ApplyStatusEnum.REJECT]: '已驳回',
}

export const ApplyStatusColorMap = {
  [ApplyStatusEnum.APPROVED]: 'primary',
  [ApplyStatusEnum.PROCESSING]: 'success',
  [ApplyStatusEnum.REJECT]: 'danger',
}

export interface ISettlement {
  /** 结算方式{"SALES_COMMISSION":2,"SERVICE_FEE":1,"SERVICE_FEE_AND_SALES_COMMISSION":3} */
  providerSettlement?: number
  /** 描述 */
  desc?: string
}
export interface IProviderBaseInfo {
  providerName?: string
  /** 服务商ID */
  providerId?: string
  /** 服务商头像 */
  providerAvatar?: string
  /** 人员规模 */
  staffSize?: string
  /** 服务地域 */
  region?: string[]
  /** 主营行业 */
  mainIndustry?: string[]
  /** 联系方式 */
  contact?: string[]
  /** 联系方式-手机号 */
  contactPhone?: string
  /** 成功案例 */
  cases?: string
  /** 案例图片 */
  casePhoto?: string[]
  /** 结算方式 */
  settlement?: ISettlement[]
  /** 申请状态{"APPROVED":3,"PROCESSING":1,"REJECT":2} */
  applyStatus?: number
  /** applyId */
  applyId?: string
  /** 驳回原因 */
  reason?: string
  /** 历史服务过的商家数 */
  historySellerCount?: number
  /** 入驻时间 */
  createTime?: number
  /** 脱敏数据 */
  desensitizeData?: Record<string, string>
  /** 脱敏状态 */
  secureStatus?: string
  /** 脱敏字段 */
  secureFields?: string[]
}

export interface IProviderQualificationAuditItem {
  applyId: string
  providerName: string
  providerId: string
  applyStatus: ApplyStatusEnum
  providerInfo: IProviderBaseInfo
  reason: string
}

// http://hera-providermarket.sl.sit.xiaohongshu.com
export const getProviderQualificationAuditList = async (params: any) => {
  const res = await http.get<{
    data: IProviderQualificationAuditItem[]
    total: number
    secureFields: string[]
  }>(`${baseURLMap[env]}/api/hera/life_service/serviceprovider/provider/audit/query`, {
    params: {
      ...params,
      pageNo: params.pageNum
    },
  })
  return {
    listSource: res?.data?.map(item => ({
      ...item,
      secureFields: res?.secureFields,
    })) || [],
    total: res?.total || 0,
  }
}

export const fetchProviderInfo = (params: {
  providerId: string
  applyId: string
}) => http.get<{
  providerAuditDetail: IProviderBaseInfo
  secureFields: string[]
}>(`${baseURLMap[env]}/api/hera/life_service/serviceprovider/provider/audit/detail`, {
  params,
})

export const providerAudit = (params: {
  applyId: string
  providerId: string
  applyStatus: ApplyStatusEnum
  reason?: string
}) => http.post(`${baseURLMap[env]}/api/hera/life_service/serviceprovider/provider/audit/manage`, params)

export const getSecureUserData = async (item: IProviderBaseInfo) => {
  const res = await http.post(`${baseURLMap[env]}/api/hera/life_service/serviceprovider/provider/secure/data`, {
    bizId: item.providerId,
    secureFields: item?.secureFields,
    bizType: 'PROVIDER_BASE_INFO'
  })
  return res
}
