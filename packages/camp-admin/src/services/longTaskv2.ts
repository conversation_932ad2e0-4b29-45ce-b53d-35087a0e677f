import http from '@/utils/http'

export interface PostLongTaskParams<T = any> {
  input: {
    file_url?: string
    file_info?: {
      file_id: string // ark-common/iJotkiYBWKq4op9JZzXS8_7TizkEeYmTt-F9BndBUdA6Be4
      cloud_type:number
      biz_name: string // ark
      scene: string // common_file
    }
    extra: T // extra 需要以 map 的形式进行传递
  }
  task_name: string // batch_upload // 与consumer上的taskName相同    moduleName:promotion, //模块名称，如促销、薯券等    subsystem_alias:ark //系统名
}

export interface GetLongTaskRes {
  id: string
  finishedCount: number
  totalCount: number
  progress: number
  result: {
    fileUrl?: string
    msg: string
    extraJson: string
    business_status: null
    success: boolean
    code: number
    fileNameAlias?: string
  }
  status: number
}

// const baseUrl = window.location.origin.replace(/local|hera/, 'castle')
const subsystemAlias = 'hera'

export const postLongTask = <T>(data: PostLongTaskParams<T>) => http.post<{
  taskId: string
}>(
  '/api/hera/common/long_task/task/submit',
  {
    ...data,
    subsystemAlias,
    moduleName: ''
  },
  {
    transform: true,
  }
)

export const getLongTask = <T = GetLongTaskRes>(taskId: string) => http.get<T>(
  '/api/hera/common/long_task/task/detail',
  {
    params: {
      taskId
    },
    transform: true
  },
  {
    shouldAlert: false
  }
)
