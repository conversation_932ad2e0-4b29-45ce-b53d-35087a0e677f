import http from '@/utils/http'

// 获取代运营商接口
export interface ProviderParams {
  pageNo: number
  pageSize: number
  providerId?: string
  providerName?: string
}
export const enum AuditStatus {
  noConfirmed = 1,
  hasConfirmed = 2
}
export const AUDITSTATUS_TEXT_MAP = {
  [AuditStatus.noConfirmed]: '未入驻',
  [AuditStatus.hasConfirmed]: '已入驻',
}
export interface Provider {
  providerId: string // 服务商id
  providerName: string // 服务商名称
  providerCompanyName: string // 服务商公司名称
  providerUid: string // 服务主账号uid（小红书uid）
  sellerNum: number // 合作的商家数量
  auditStatus: AuditStatus
  officialHidden?: boolean // 是否官方隐藏
  banNewSeller?: boolean // 是否禁止新增商家
}
export const fetchProvider = (params: ProviderParams) => http.get<{
  providerList: Provider[]
  total: number
}>(
  '/api/hera/life_service/serviceprovider/page_query_service_provider',
  {
    params,
    transform: false
  }
)

// 代运营服务商新增和编辑接口
export interface ProviderPayload {
  providerId?: string // 服务商id（修改时必填）
  providerName: string // 服务商名称
  spCompanyName: string // 服务商公司名称
  providerUid: string // 服务主账号uid（小红书uid）
}
export const updateProvider = (data:ProviderPayload) => http.post(
  '/api/hera/life_service/serviceprovider/save_service_provider',
  data,
  {
    transform: false
  }
)

// 小红书账号查询接口
export interface BaseUser {
  uid: string // 小红书uid
  nickname: string // 小红书昵称
  companyName: string // 专业号公司主体名称。
}
export const fetchUser = (uid:string) => http.get<BaseUser | undefined>(
  '/api/hera/life_service/serviceprovider/get_user_base_info',
  {
    params: { uid },
  }
)

export interface PunishmentPayload {
  providerId: string
  officialHidden: boolean
  banNewSeller: boolean
}
// 处罚服务商
export const punishment = (data: PunishmentPayload) => http.post('/api/hera/life_service/serviceprovider/punishment', data, { transform: false })
