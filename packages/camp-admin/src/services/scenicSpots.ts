import http from '@/utils/http'

export interface ClaimResult {
  successNum: number
  failNum: number
  failReasonPath: string
}

// 新增白名单导入
export const ImportAddWhiteList = (data:FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/poi/import_add_mark_white_list',
  data
)
// 删除白名单导入
export const ImportDelWhiteList = (data:FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/poi/import_delete_mark_white_list',
  data
)

export interface poiMessageVOLItem {
  poiId: string
  poiName: string
  poiType:string
  distance: string
  submitBy: string
  createBy:string
  createTime:number
}

// 景点信息列表查询
export const fetchScenicSpotsList = (
  params: {
    poiId?: string
    pageSize: number
    pageNum: number
  }
) => http.get<{
  queryPoiMarkWhileVOList?: poiMessageVOLItem[]
  total: number
}>(
  '/api/hera/localLife/poi/query_page_mark_white_list',
  {
    params,
    transform: false
  },
)
