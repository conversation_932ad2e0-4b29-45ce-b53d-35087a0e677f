import http from '@/utils/http'

export const poiClaimStatusMap = {
  NOT_CLAIM: '未认领门店',
  CLAIMED: '已认领门店'
}

export const productOnShelfStatusMap = {
  OFF_SHELF: '没有在架商品',
  ON_SHELF: '有在架商品'
}

export interface GetClaimManageListParams {
  sellerId: string
  shopName: string
  operateManagerId: string // 运营负责人id
  poiClaimStatus: 'NOT_CLAIM' | 'CLAIMED' | '' // 门店认领状态：NOT_CLAIM：未认领门店，CLAIMED：已认领门店
  productOnShelfStatus: 'OFF_SHELF' | 'ON_SHELF' | '' // 商品上架状态：OFF_SHELF：没有在架商品，ON_SHELF：有在架商品
  settlementTimeFrom: any // 商家入驻日期起，格式：yyyy-MM-dd
  settlementTimeTo: string // 商家入驻日期止，格式：yyyy-MM-dd
  pageSize: number
  pageNo: number
}

export interface ClaimManageListItem {
  sellerId: string
  shopName: string
  settlementTime: string // 商家入驻日期
  categoryName: string // 商家入驻类目
  poiClaimStatus: 'NOT_CLAIM' | 'CLAIMED' // 门店认领状态：NOT_CLAIM：未认领门店，CLAIMED：已认领门店
  totalClaimPoiCnt: number
  productOnShelfStatus: 'OFF_SHELF' | 'ON_SHELF' // 商品上架状态：OFF_SHELF：没有在架商品，ON_SHELF：有在架商品
  dgtv7d: number
  dgtv30d: number
  providerId: string // 代运营服务商id
  providerName: string // 代运营服务商名称
  coverCity: string // '商家门店覆盖城市范围$$枚举：跨城市、上海市、贵阳市......
  operateManagerId: string // 运营负责人id
  operateManagerName: string // 运营负责人薯名(姓名)
  operateManagerRegionName: string// 运营负责人负责区域
}

export interface Employee {
  userId: string
  userName: string
  email: string
}

// 查询运营认领管理列表
export const getClaimManageList = (params: GetClaimManageListParams) => http.get<{
  sellerList: ClaimManageListItem[]
  total: number
}>('/api/hera/localLife/operate/claim_manage_list', {
  params,
  transform: false
})

// 分配店铺运营负责人
export const submitSellerOperateManager = (params: {
  sellerId: string
  operateManagerId: string // 运营负责人id
  operateManagerName: string // 运营负责人薯名(姓名)
  operateManagerRegionName: string // 运营负责人负责区域
}) => http.post('/api/hera/localLife/operate/submit_seller_operate_manager', params, {
  transform: false
})

// 搜索员工信息
export const searchEmployee = (params: {
  key: string
  includeLeaveEmployee: boolean // 是否包含已离职员工 true/false
}) => http.get<{
  employeeInfos: Employee[]
  total: number
}>('/api/hera/localLife/operate/search_employee', {
  params,
  transform: false
})

// 查询运营配置
export const getOperateConfig = () => http.get<{
  operateManagerRegionList: string[] // 运营负责人区域列表
}>('/api/hera/localLife/operate/get_config')
