import {
  postApiHawkLongTaskTaskSubmit,
  getApiHawkLongTaskTaskDetail,
} from '@xhs/logan-services-barley-iron'

// 提交长任务
export function postLongTask(props: {
  taskName: string
  moduleName: string
  input: any
}) {
  return postApiHawkLongTaskTaskSubmit(props, {
    transform: true,
  })
}

// 长任务详情
export function getLongTask(props: { taskId: string }) {
  return getApiHawkLongTaskTaskDetail({
    params: props,
    transform: true,
  })
}
