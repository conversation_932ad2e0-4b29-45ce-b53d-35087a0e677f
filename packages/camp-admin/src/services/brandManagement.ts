import { SearchSellerBrandParams, ISellerBrandRelationItem } from '@/types/brandManagement'
import http from '@/utils/http'

const { get, post, del } = http

interface ListResponse {
  total: number
  sellerBrandRelation: ISellerBrandRelationItem[]
}

interface CreateSellerBrandParams {
  sellerId: string
  sellerName: string
  brandId: string
  brandName: string
}

interface SellerOptionsResponse {
  sellerId: string
  publicName: string
}

interface BrandOptionsResponse {
  brandName: string
  brandId: number
  brandCategory: string
  outerId: string
}

// 分页查询商家品牌关系接口
export function searchSellerBrandList(params: SearchSellerBrandParams): Promise<ListResponse> {
  return get('/api/hera/localLife/poi/permission/sellerBrand/page', { params, transform: false })
}

// 创建商家品牌关系接口
export function createSellerBrand(params: CreateSellerBrandParams): Promise<void> {
  return post('/api/hera/localLife/poi/permission/sellerBrand', params, { transform: false })
}

// 删除商家品牌关系接口
export function deleteSellerBrandItem(id: string): Promise<void> {
  return del(`/api/hera/localLife/poi/permission/sellerBrand/${id}`)
}

// 根据商家id或名称搜索商家信息接口
export function getSellerOptions(sellerInfo: string): Promise<SellerOptionsResponse[]> {
  return get('/api/hera/localLife/seller', { params: { sellerInfo }, transform: false })
}

// 根据品牌名搜索品牌接口
export function getBrandOptions(brandName: string): Promise<BrandOptionsResponse[]> {
  return get('/api/hera/localLife/brand', { params: { brandName }, transform: false })
}

// poi黑名单解除接口
export function excludePoi(poiId: string): Promise<void> {
  return post('/api/hera/localLife/poi/permission/excludePoi', { poiId }, { transform: false })
}

// poi黑名单查询接口
export function getPoiBlackList(poiInfo: string): Promise<{ poiId: string; poiName: string }[]> {
  return get('/api/hera/localLife/poi/permission/poiBlackList', { params: { poiInfo }, transform: false })
}
