import http from '@/utils/http'

export const enum POIRules {
  'MSG' = '留言板',
  'CINEMA' = '电影院',
  'DIG' = '挖一挖'
}

export const POIRulesMap = {
  MSG: '留言板',
  CINEMA: '电影院',
  DIG: '挖一挖'
}

interface PaganationParams {
  pageNum: number
  pageSize: number
}

export interface GetPoiMessageListParams extends PaganationParams{
  poiIds?: string
  poiName?: string
  poiRule?: POIRules
  provinceName?: string
  cityName?: string
  districtName?: string
}

export interface GetPoiMessageListParamsTemp extends GetPoiMessageListParams{
  cascader?: any
}

export interface poiMessageListItem {
  id: string
  poiId: string
  poiName: string
  addressName: string
  addressDetail: string
  poiType: string
  poiRule: POIRules
  poiRiskLevel: string
}

export const transformParams = (payload: GetPoiMessageListParamsTemp): GetPoiMessageListParams => {
  const filterParams = { ...payload }
  delete filterParams.cascader
  return filterParams
}

export const getPoiMessageList = (params: GetPoiMessageListParams) => http.get<{
  total: number
  queryPoiMessageVOList: poiMessageListItem[]
}>('/api/hera/localLife/poi/poi_message_page', {
  params,
  transform: false
})

export const deletePoiMessageItem = (data: { id: string }) => http.post('/api/hera/localLife/poi/delete_poi_message', data)

export const downloadPoiMessageList = (data: GetPoiMessageListParams) => http.post<{ url: string }>('/api/hera/localLife/poi/download_poi_message', data, {
  transform: false
})
// 批量圈选
export const uploadPoiChoose = (data: FormData) => http.post<{
  total: number
  successNum: number
}>('/api/hera/localLife/poi/import_poi_message', data, { transform: false })
