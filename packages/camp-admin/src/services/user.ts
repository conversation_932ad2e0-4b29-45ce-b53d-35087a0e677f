import { http } from '@xhs/launcher'

import { getPorchLogoutUrl } from '@/config/porch.config'
import nonConcurrent from '@/utils/nonConcurrent'

const { get, post } = http

export async function loginSSO(service: any, ticket: any) {
  return post('LOGIN_SSO', {
    service,
    ticket,
  })
}

export interface UserInfo {
  userId: string
  accountNo: string
  name: string
  permissions: string[]
  email: string
}
export const getUserInfo = nonConcurrent(() => get<UserInfo>('USER_INFO'))

let userInfo:UserInfo
export async function getUserInfoWithCache() {
  if (userInfo) return userInfo
  userInfo = await getUserInfo()
  return userInfo
}

export function logout() {
  return get('LOGOUT').then(() => {
    window.location.href = getPorchLogoutUrl()
  })
}

interface AuthParams {
  checkAuth: (promissionName: string) => Promise<{ permissions: string[] }>
}

// oudoor权限
export const outdoorAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const whiteList = ['outdoor', 'camp_manage']
  const hasCamp = permissions.some(p => whiteList.includes(p))
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// 订单权限
export const orderAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const hasCamp = permissions.some(p => p === 'outdoor_find')
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// 笔记权限
export const lifeNoteAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const whiteList = ['life_note']
  const hasCamp = permissions.some(p => whiteList.includes(p))
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// bk豁免
export const bkExemptAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const whiteList = ['life_bk_exempt']
  const hasCamp = permissions.some(p => whiteList.includes(p))
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// bk查询工具权限
export const lifeToolAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const hasCamp = permissions.some(p => p === 'life_bk_query' || p === 'outdoor')
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// 景点打卡白名单工具权限
export const scenicSpotsAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const hasCamp = permissions.some(p => p === 'poi_check_in_distance')
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// 开放服务查看权限
export const openServiceManagerAuth = nonConcurrent(async (authParams: AuthParams) => {
  const { checkAuth } = authParams
  const { permissions } = await checkAuth('isLogin')
  const hasCamp = permissions.some(p => p === 'opencore_package_interface_view_permission' || p === 'opencore_package_interface_admin_permission')
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// 开放服务管理发布权限
export const openServiceAdminManagerAuth = nonConcurrent(async () => {
  const userInfo = await getUserInfoWithCache()
  const hasCamp = userInfo.permissions.some(p => p === 'opencore_package_interface_admin_permission')
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})

// 批量绑卡
export const lifeBatchBindBankCardAuth = nonConcurrent(async () => {
  const userInfo = await getUserInfoWithCache()
  const hasCamp = userInfo.permissions.some(p => p === 'life_batch_bind_bank_card')
  return hasCamp ? Promise.resolve(true) : Promise.reject('请检查权限')
})
