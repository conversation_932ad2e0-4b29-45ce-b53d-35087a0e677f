import http from '@/utils/http'
import axios, { Canceler } from 'axios'

import { SellerItem } from 'shared/dictMaterials/camp-admin/activityLife/type'

// 商家列表
let canceler: Canceler | undefined
export const fetchSellerList = (
  params: {
    pageNo: number
    pageSize: number
    sellerId?: string
    sellerName?: string
    [key: string]: any
  },
  shouldCancel = false
) => {
  if (!shouldCancel) {
    canceler = undefined
  } else {
    canceler?.()
  }
  const source = axios.CancelToken.source()
  canceler = source.cancel
  return http.get<{
    sellerInfoList: SellerItem[]
    total: number
  }>(
    '/api/hera/localLife/seller/batch_get_seller_info',
    {
      params,
      // @ts-ignore
      cancelToken: shouldCancel
        ? source.token
        : undefined
    }
  )
}

// 商家已认领列表
export interface PoiItem {
  poiId: string
  poiName: string
  cityName: string
  available: 0 | 1
  longitude: number
  latitude: number
  address: string
  accountType: string
  createTime: string
  updateTime: string
  phone: string
  provinceName: string
  districtName: string
}
export const fetchSellerClaimedPoi = (params: {
  pageNo: number
  pageSize: number
  sellerId: string
  [key: string]: any
}) => http.get<{
  poiList: PoiItem[]
  total: number
}>(
  '/api/hera/localLife/poi/get_bind_poi_info',
  {
    params
  }
)

// 批量认领 file=@"/Users/<USER>/final_poi_bind_seller.xlsx"'
export interface ClaimResult {
  claimInitNum?: number // 待补充资质
  successNum: number
  failNum: number
  failReasonPath: string
}
export const claimPoi = (data: FormData) => http.post<ClaimResult>(
  '/api/hera/localLife/poi/import_seller_bind_poi',
  data,
  undefined,
  {
    shouldAlert: false,
  }
)

export {
  SellerItem
}
