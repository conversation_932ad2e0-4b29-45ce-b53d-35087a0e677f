import http from '@/utils/http'

import { SuiteParams, SimpleSuite } from '@/types/suite'

export async function list(
  params: SuiteParams
) {
  const res = await http.get<{
    campInfos: {
      suiteId: string
      suiteName: string
    }[]
  }>('/api/v2/outdoor/suite/camp_list', {
    params: {
      pageNum: 1,
      pageSize: 20,
      ...params
    }
  })
  return res.campInfos.map(item => ({ name: item.suiteName, value: item.suiteId }))
}

// 获取新增关联关系列表
export async function simpleList(
  params:{
    suiteIds?: string
    sellerId?: string
  }
) {
  return http.get<{
    suiteSimples: SimpleSuite[]
  }>('/api/v2/outdoor/point/suite_simple_list', { params })
}
