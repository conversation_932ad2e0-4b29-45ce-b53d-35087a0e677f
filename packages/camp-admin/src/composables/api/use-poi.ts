import { ref } from 'vue'

import {
  PoiListParams, PoiType, POI, TodoPoi
} from '@/types/poi'

import * as Api from '@/services/poi'

import useSaveParams from '../use-save-params'
import { useCrossRefresh } from '../use-cross-refresh'

export default function usePoi(autoFetch = true) {
  const { query, saveParams } = useSaveParams()

  const total = ref(0)
  const data = ref<POI[]>([])
  const loading = ref(true)

  const todoTotal = ref(0)
  const todoData = ref<TodoPoi[]>([])

  const payload = ref<PoiListParams>({
    type: `${query.type as PoiType || ''}`,
    poiName: `${query.poiName || ''}`,
    poiId: `${query.poiId || ''}`,
    isUpgrade: `${query.isUpgrade || ''}`,
    pageNum: Number(query.pageNum || 1),
    pageSize: Number(query.pageSize || 20),
  })

  // 获取待处理POI列表之外的POI
  const fetchCommoonPoi = async () => {
    loading.value = true
    try {
      const res = await Api.list(payload.value)
      total.value = res.total
      data.value = res.list
      loading.value = false
    } catch (e: any) {
      loading.value = false
      data.value = []
    }
  }

  // 获取待处理POI列表数据
  const fetchTodoPoi = async () => {
    loading.value = true
    try {
      const res = await Api.todoList({
        ...payload.value,
        isUpgrade: undefined,
        type: undefined,
      })
      todoTotal.value = res.count
      todoData.value = res.poinRelations
      loading.value = false
    } catch (e: any) {
      loading.value = false
      todoData.value = []
    }
  }

  // 获取POI数据
  const fetchData = async () => {
    if (payload.value.isUpgrade === 'TODO') {
      fetchTodoPoi()
    } else {
      fetchCommoonPoi()
    }
  }
  if (autoFetch) {
    fetchCommoonPoi()
    fetchTodoPoi()
  }
  // 监听刷新
  useCrossRefresh(fetchData)

  const saveAndFetch = () => {
    saveParams(payload.value)
    fetchData()
  }

  const search = (f?: PoiListParams) => {
    payload.value = { ...payload.value, ...f, pageNum: 1 }
    saveAndFetch()
  }

  return {
    payload,
    data,
    total,

    todoData,
    todoTotal,

    loading,
    fetchData,
    saveAndFetch,
    search
  }
}
