import { ref } from 'vue'

import { PoiGoods } from '@/types/poi'
import * as Api from '@/services/poi'

import useSaveParams from '../use-save-params'

export default function useGoods() {
  const { query, saveParams } = useSaveParams()

  const payload = ref({
    poiId: query.poiId as string,
    pageNum: Number(query.pageNum || 1),
    pageSize: Number(query.pageSize || 20),
  })

  const loading = ref(true)

  const data = ref<PoiGoods[]>([])

  const fetchData = async () => {
    loading.value = true
    try {
      const res = await Api.goods(payload.value)
      data.value = res.result
      loading.value = false
    } catch (e: any) {
      loading.value = false
      data.value = []
    }
  }
  fetchData()

  const saveAndFetch = () => {
    saveParams(payload.value)
    fetchData()
  }

  return {
    payload,
    loading,
    data,
    saveAndFetch,
  }
}
