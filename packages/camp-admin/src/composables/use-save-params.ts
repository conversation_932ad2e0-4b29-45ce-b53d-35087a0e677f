import { useRouter, useRoute } from 'vue-router'

export default function useSaveParams() {
  const router = useRouter()
  const route = useRoute()
  const saveParams = (params: Record<string, string | number | undefined> = {}) => {
    const { name, query } = route
    router.push({
      name: name!,
      query: {
        ...query,
        ...params
      }
    })
  }
  return {
    query: route.query,
    saveParams
  }
}
