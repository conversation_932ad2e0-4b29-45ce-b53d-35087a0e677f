import { computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import { ThContent } from '@xhs/delight/components/Table/interface'
import TableCell from '@xhs/delight-material-ultra-table-cell'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const pageHeaderProps: PageHeaderProps = { // 页头的配置
  type: 'combination',
  items: [],
}

export const useFilter = (scheme, fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: scheme.value?.filterScheme || [],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = (scheme, funcs) => { // 工具栏的配置
  const toolBarConfig = computed(() => ({
    actions: scheme.value?.toolbarActions?.map((action, index) => {
      action.onClick = funcs?.[index]
      return action
    }) || []
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = (
  scheme,
  funcs?,
  columnClickMap?
) => {
  const tableColumns = computed<ThContent[]>(() => {
    const list: any[] = []
    scheme.value?.tableColumnScheme.forEach(item => {
      const field = item.field
      const render = item.render
      if (columnClickMap?.[field]) {
        item.render = ({ rowData }) => render({ rowData, onClick: columnClickMap[field].bind(null, rowData) })
      }
      list.push(item)
    })

    if (scheme.value?.tableActionScheme?.length) {
      list.push({
        title: '操作',
        fixed: 'right',
        minWidth: 116,
        render: ({ rowData }) => rowData && scheme.value?.tableActionScheme?.filter(t => t?.visible?.(rowData) !== false)?.length > 0 && (
          <TableCell
            class={'camp-table-action'}
            type="action"
            action-props={{
              direction: 'vertical',
              actionOptions: scheme.value?.tableActionScheme?.map((action, index) => ({
                text: action?.text,
                disabled: action?.disabled?.(rowData),
                tooltip: action?.tooltip?.(rowData),
                visible: action?.visible?.(rowData),
                popConfirmProps: action?.popConfirmProps?.(rowData),
                onClick: funcs?.[index]?.bind(null, rowData)
              })),
            }} />
        )
      })
    }

    return list
  })

  return {
    tableColumns,
  }
}
