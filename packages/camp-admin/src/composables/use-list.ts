import { ref } from 'vue'

export const useList = (scheme, getList, isHump:boolean = false) => {
  const listSource = ref<any[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const total = ref(0) // 列表数据总数

  // 基础参数
  const baseParams = ref({
    page_num: 1,
    page_size: 10,
  })

  // 驼峰基础参数
  const baseHumpParams = ref({
    pageNum: 1,
    pageSize: 10,
  })

  // 搜索参数
  const filterParams = ref({})
  if (scheme?.value?.filterScheme?.length) {
    scheme?.value.filterScheme.forEach(item => {
      const field = item.name
      filterParams.value[field] = item.defaultValue ?? ''
    })
  }

  // 获取列表
  const fetchList = async (isResetPageNum: boolean = true, extraParams = {}) => {
    try {
      loading.value = true
      if (isResetPageNum) { // 是否重置
        if (isHump) {
          baseHumpParams.value.pageNum = 1
        } else {
          baseParams.value.page_num = 1
        }
      }
      const finalBaseParam = isHump ? baseHumpParams.value : baseParams.value
      const res = await getList({
        ...finalBaseParam,
        ...filterParams.value,
        ...extraParams
      })

      total.value = res?.total || 0
      listSource.value = [...res?.listSource] || []
    } catch (e: any) {
      console.log(e)
    } finally {
      loading.value = false
    }
  }

  return {
    listSource,
    total,
    loading,
    baseParams,
    baseHumpParams,
    filterParams,
    fetchList,
  }
}
