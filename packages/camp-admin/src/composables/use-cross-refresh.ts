import { onMounted, onUnmounted } from 'vue'

const refreshKey = 'camp_admin_cross_refresh'

// 注册刷新函数
export function useCrossRefresh(fetchData: () => any) {
  const onStorage = (e:StorageEvent) => {
    if (e.key === refreshKey && e.newValue !== e.oldValue) {
      fetchData()
    }
  }
  onMounted(() => {
    window.addEventListener('storage', onStorage)
  })
  onUnmounted(() => {
    window.removeEventListener('storage', onStorage)
  })
}

// 主动出发刷新
export function refresh() {
  localStorage.setItem(refreshKey, `${Date.now()}`)
}
