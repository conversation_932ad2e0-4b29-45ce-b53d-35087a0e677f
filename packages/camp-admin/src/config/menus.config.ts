export default [
  {
    text: '资质管理',
    icon: 'label-shu-f',
    path: '/camp/qualification',
    auth: ['outdoor'],
  },
  {
    text: '运营认领管理',
    icon: 'fans-persona',
    path: '/camp/operateClaim/list',
    auth: ['outdoor'],
  },
  {
    text: '代运营管理',
    icon: 'earth',
    path: '/camp/merchant/provider',
    auth: ['outdoor'],
  },
  {
    text: '交易活动管理',
    icon: 'activity',
    path: '/camp/activityLife/list',
    auth: ['outdoor']
  },
  {
    text: '批量绑卡',
    icon: 'task-reimbursement-b',
    path: '/camp/batchBindBankCard/list',
    auth: ['bankCard']
  },
  {
    text: '异名绑卡管理',
    icon: 'activity',
    path: '/camp/bindCard/list',
    auth: ['outdoor']
  },
  {
    text: '地瓜组局回复管理',
    icon: 'sound-on-b',
    path: '/camp/uniReplyManage/list',
    auth: ['outdoor']
  },
  {
    text: '区域管理',
    icon: 'section-b',
    path: '/camp/area/manage',
    auth: ['outdoor'],
  },
  {
    text: 'POI认领',
    icon: 'guidance',
    path: '/camp/poi/claim',
    auth: ['outdoor'],
  },
  {
    text: '门店装修',
    icon: 'warehouse-delivery',
    path: '/camp/shop/list',
    auth: ['outdoor'],
  },
  {
    text: 'SKA/CKA商家品牌管理',
    icon: 'setting',
    path: '/camp/brandManagement/list',
    auth: ['outdoor'],
  },
  {
    text: '账号关联',
    icon: 'link-b',
    path: '/camp/accountRelation/list',
    auth: ['outdoor'],
  },
  {
    text: '笔记刷数',
    icon: 'note-b',
    path: '/camp/noteTool',
    auth: ['life_note'],
  },
  {
    text: '博主带货',
    icon: 'goods',
    path: '/camp/kol/list',
    auth: ['outdoor'],
  },
  {
    text: 'POI圈选',
    icon: 'ar',
    path: '/camp/poiMessage/list',
    auth: ['outdoor'],
  },
  {
    text: 'POI黑名单解除',
    icon: 'warning-b',
    path: '/camp/removePoiBlackList/list',
    auth: ['outdoor'],
  },
  {
    text: '技术管理',
    icon: 'express-distribution',
    path: '/camp/merchant/partner',
    auth: ['outdoor'],
  },
  {
    text: '订单查询',
    icon: 'order',
    path: '/camp/order/list',
    auth: ['outdoor_find'],
  },
  {
    text: 'BK撮合惩罚豁免自助工具',
    icon: 'amenties-business',
    path: '/camp/bkExempt/list',
    auth: ['life_bk_exempt'],
  },
  {
    text: 'BK查询工具',
    icon: 'note-b',
    path: '/camp/bkSearchTool/list',
    auth: ['life_bk_query', 'outdoor'],
  },
  {
    text: '景点打卡管理',
    icon: 'offline-entities',
    path: '/camp/scenicSpotsTool/list',
    auth: ['poi_check_in_distance'],
  },
  {
    text: '开放平台接口配置',
    icon: 'note-b',
    path: '/camp/openServiceManager/list',
    auth: ['opencore_package_interface_view_permission', 'opencore_package_interface_admin_permission'],
  },
  {
    text: '开放平台接口包配置',
    icon: 'note-b',
    path: '/camp/openServicePkgManager/list',
    auth: ['opencore_package_interface_view_permission', 'opencore_package_interface_admin_permission'],
  },
  {
    text: '审核管理',
    icon: 'complaint-b',
    path: '/camp/audit/provider-qualification-list',
    auth: ['outdoor'],
  },
]
