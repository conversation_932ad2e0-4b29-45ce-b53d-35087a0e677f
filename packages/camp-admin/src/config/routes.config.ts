import { LauncherOptions } from '@xhs/launcher'
import { RouterView } from 'vue-router'

const Login = (): any => import(/* webpackChunkName: "common" */ '../pages/login/index.vue')
const Index = (): any => import(/* webpackChunkName: "common" */ '../components/beco-page-layout.vue')
const Page404 = (): any => import(/* webpackChunkName: "common" */ '../components/exception/404.vue')
const Page403 = (): any => import(/* webpackChunkName: "common" */ '../components/exception/403.vue')
const Page500 = (): any => import(/* webpackChunkName: "common" */ '../components/exception/500.vue')

const routesChildren: LauncherOptions['routes'] = [
  // 首页
  {
    name: 'campHome',
    path: '/camp/home',
    meta: {
      auth: {
        check: ['isLogin'],
        fallback: 'Login',
      },
    },
    component: () => import('@/pages/home/<USER>'),
  },
  // POI管理
  {
    name: 'poi',
    path: '/camp/poi',
    component: RouterView,
    redirect: { path: '/poi/list' },
    meta: {
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'poiList',
        path: '/camp/poi/list',
        meta: {
          instanceId: 'poiList',
          title: 'POI信息',
        },
        component: () => import('@/pages/poi/list/index.vue'),
      },
      {
        name: 'poiNote',
        path: '/camp/poi/note',
        meta: {
          instanceId: 'poiList',
          title: '关联笔记',
        },
        component: () => import('@/pages/poi/note/index.vue')
      },
      {
        name: 'poiForm',
        path: '/camp/poi/form',
        meta: {
          instanceId: 'poiForm',
          title: '编辑POI'
        },
        component: () => import('@/pages/poi/form/index.vue')
      },
      {
        name: 'poiGoods',
        path: '/camp/poi/goods',
        component: () => import('@/pages/poi/goods/index.vue')
      },
      {
        name: 'poiClaim',
        path: '/camp/poi/claim',
        meta: {
          pageKey: 'camp_admin_poi',
          instanceId: 'poiClaim',
          title: 'POI认领'
        },
        component: () => import('@/pages/poiClaim/index.vue')
      },
      {
        name: 'sellerClaimedPoi',
        path: '/camp/poi/claimedPoi',
        meta: {
          instanceId: 'sellerClaimedPoi',
          title: 'POI认领'
        },
        component: () => import('@/pages/sellerClaimedPoi/index.vue')
      }
    ],
  },
  // 活动发布
  {
    name: 'activity',
    path: '/camp/activity/list',
    component: RouterView,
    redirect: { path: '/camp/activity/list' },
    meta: {
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'activityList',
        path: '/camp/activity/list',
        component: () => import('@/pages/activity/list/index.vue'),
        meta: {
          instanceId: 'activityList',
          title: '活动信息',
        },
      },
      {
        name: 'activityGoods',
        path: '/camp/activity/goods',
        component: () => import('@/pages/activity/goods/index.vue'),
        meta: {
          instanceId: 'activityGoods',
          title: '商品创建活动',
        },
      },
      {
        name: 'activityForm',
        path: '/camp/activity/form',
        component: () => import('@/pages/activity/form/index.vue'),
        meta: {
          instanceId: 'activityForm',
          title: '编辑活动',
        },
      },
    ]
  },
  {
    name: 'activityList',
    path: '/camp/activityLife/list',
    component: RouterView,
    redirect: { path: '/camp/activityLife/list' },
    meta: {
      pageKey: 'camp_admin_life_activity',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'activityList',
        path: '/camp/activityLife/list',
        component: () => import('@/pages/activityLife/list/index.vue'),
        meta: {
          instanceId: 'activityList',
          title: '活动列表',
        },
      },
      {
        name: 'activityDetail',
        path: '/camp/activityLife/detail/:id?',
        component: () => import('@/pages/activityLife/form/index.vue'),
        meta: {
          instanceId: 'activityDetail',
          title: '活动详情',
        },
      },
      {
        name: 'activityGeneralDetail',
        path: '/camp/activityLife/detailGeneral/:id?',
        component: () => import('@/pages/activityLife/formGeneral/index.vue'),
        meta: {
          instanceId: 'activityGeneralDetail',
          title: '活动详情',
        },
      },
      {
        name: 'previewList',
        path: '/camp/activityLife/previewList/:id/:creatorType',
        component: () => import('@/pages/activityLife/previewList/index.vue'),
        meta: {
          instanceId: 'previewList',
          title: '商家提报商品详情',
        },
      },
    ]
  },
  // 异名绑卡管理
  {
    name: 'bindCard',
    path: '/camp/bindCard/list',
    component: RouterView,
    redirect: { path: '/camp/bindCard/list' },
    meta: {
      pageKey: 'camp_admin_bind_card',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'bindCard',
        path: '/camp/bindCard/list',
        component: () => import('@/pages/bindCard/list/index.vue'),
        meta: {
          instanceId: 'bindCard',
          title: '异名绑卡',
        },
      },
    ]
  },
  // 订单查询
  {
    name: 'order',
    path: '/camp/order',
    component: RouterView,
    redirect: { path: '/camp/order/list' },
    meta: {
      pageKey: 'camp_admin_order',
      auth: {
        check: ['order'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'orderList',
        path: '/camp/order/list',
        component: () => import('@/pages/order/index.vue'),
        meta: {
          instanceId: 'orderList',
          title: '订单查询',
        },
      },
    ]
  },
  // 资质管理
  {
    name: 'qualification',
    path: '/camp/qualification',
    component: RouterView,
    redirect: { path: '/camp/qualification/manage' },
    meta: {
      pageKey: 'qualification_manage',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'qualificationManage',
        path: '/camp/qualification/manage',
        component: () => import('@/pages/qualification/index.vue'),
        meta: {
          instanceId: 'qualificationManage',
          title: '资质管理',
        },
      },
    ]
  },
  // 资质管理
  {
    name: 'area',
    path: '/camp/area',
    component: RouterView,
    redirect: { path: '/camp/area/manage' },
    meta: {
      pageKey: 'camp_admin_area_manage',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'areaManage',
        path: '/camp/area/manage',
        component: () => import('@/pages/area/index.vue'),
        meta: {
          instanceId: 'areaManage',
          title: '资质管理',
        },
      },
    ]
  },
  // 门店
  {
    name: 'shop',
    path: '/camp/shop',
    component: RouterView,
    redirect: { path: '/camp/shop/list' },
    meta: {
      pageKey: 'camp_admin_shop',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'shopList',
        path: '/camp/shop/list',
        component: () => import('@/pages/shop/index.vue'),
        meta: {
          instanceId: 'shopList',
          title: '店铺装修',
        },
      },
    ]
  },
  // 服务商管理
  {
    name: 'merchant',
    path: '/camp/merchant',
    component: RouterView,
    redirect: { path: '/camp/merchant/partner' },
    meta: {
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'merchant',
        path: '/camp/merchant/partner',
        component: () => import('@/pages/merchant/partner/index.vue'),
        meta: {
          pageKey: 'camp_admin_partner',
          instanceId: 'merchantPartner',
          title: '技术服务商管理',
        },
      },
      {
        name: 'provider',
        path: '/camp/merchant/provider',
        component: () => import('@/pages/merchant/provider/index.vue'),
        meta: {
          pageKey: 'camp_admin_provider',
          instanceId: 'merchantProvider',
          title: '代运营服务商管理',
        },
      },
    ]
  },
  // 博主
  {
    name: 'kol',
    path: '/camp/kol',
    component: RouterView,
    redirect: { path: '/camp/kol/list' },
    meta: {
      pageKey: 'camp_admin_kol_list',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'kolList',
        path: '/camp/kol/list',
        component: () => import('@/pages/kol/index.vue'),
        meta: {
          instanceId: 'kolList',
          title: '店铺装修',
        },
      },
    ]
  },
  // 主账号绑定本地生活店铺 accountRelation
  {
    name: 'accountRelation',
    path: '/camp/accountRelation',
    component: RouterView,
    redirect: { path: '/camp/accountRelation/list' },
    meta: {
      pageKey: 'camp_admin_account_relation',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'accountRelation',
        path: '/camp/accountRelation/list',
        component: () => import('@/pages/accountRelation/index.vue'),
        meta: {
          instanceId: 'accountRelation',
          title: '账号关联',
        },
      },
    ]
  },
  // SKA/CKA商家品牌管理
  {
    name: 'brandManagement',
    path: '/camp/brandManagement',
    component: RouterView,
    redirect: { path: '/camp/brandManagement/list' },
    meta: {
      pageKey: 'camp_admin_ska_brand',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'brandManagementList',
        path: '/camp/brandManagement/list',
        meta: {
          instanceId: 'brandManagement',
          title: 'SKA/CKA商家品牌管理',
        },
        component: () => import('@/pages/brandManagement/list/index.vue'),
      }
    ],
  },
  // 解除POI黑名单
  {
    name: 'removePoiBlackList',
    path: '/camp/removePoiBlackList',
    component: RouterView,
    redirect: { path: '/camp/removePoiBlackList/list' },
    meta: {
      pageKey: 'camp_admin_black_poi',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'removePoiBlackListList',
        path: '/camp/removePoiBlackList/list',
        meta: {
          instanceId: 'removePoiBlackList',
          title: 'POI黑名单解除',
        },
        component: () => import('@/pages/removePoiBlackList/index.vue'),
      }
    ],
  },
  // 运营笔记刷数工具
  {
    name: 'noteTool',
    path: '/camp/noteTool',
    component: RouterView,
    redirect: { path: '/camp/noteTool/list' },
    meta: {
      pageKey: 'camp_admin_note_tool',
      auth: {
        check: ['note'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'noteToolList',
        path: '/camp/noteTool/list',
        meta: {
          instanceId: 'noteToolList',
          title: '笔记刷数',
        },
        component: () => import('@/pages/noteTool/index.vue'),
      }
    ],
  },
  // 运营认领
  {
    name: 'operateClaim',
    path: '/camp/operateClaim',
    component: RouterView,
    redirect: { path: '/camp/operateClaim/list' },
    meta: {
      pageKey: 'camp_admin_operation_claim',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'operateClaimList',
        path: '/camp/operateClaim/list',
        meta: {
          instanceId: 'operateClaimList',
          title: '运营认领',
        },
        component: () => import('@/pages/operateClaim/index.vue'),
      }
    ],
  },
  // poi留言圈选
  {
    name: 'poiMessage',
    path: '/camp/poiMessage',
    component: RouterView,
    redirect: { path: '/camp/poiMessage/list' },
    meta: {
      pageKey: 'camp_admin_poi_choose',
      auth: {
        check: ['camp'],
      }
    },
    children: [
      {
        name: 'poiMessageList',
        path: '/camp/poiMessage/list',
        meta: {
          instanceId: 'poiMessageList',
          title: 'POI圈选',
        },
        component: () => import('@/pages/poiMessage/index.vue'),
      }
    ]
  },
  // 招募合作惩罚自助豁免工具
  {
    name: 'bkExempt',
    path: '/camp/bkExempt',
    component: RouterView,
    redirect: { path: '/camp/bkExempt/list' },
    meta: {
      auth: {
        check: ['bkExempt'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'bkExemptList',
        path: '/camp/bkExempt/list',
        meta: {
          instanceId: 'bkExemptList',
          title: '本地招募合作惩罚豁免刷数',
        },
        component: () => import('@/pages/bkExempt/index.vue'),
      }
    ],
  },
  // bk查询工具
  {
    name: 'bkSearchTool',
    path: '/camp/bkSearchTool',
    component: RouterView,
    redirect: { path: '/camp/bkSearchTool/list' },
    meta: {
      auth: {
        check: ['lifeTool'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'bkSearchTool',
        path: '/camp/bkSearchTool/list',
        meta: {
          instanceId: 'bkSearchTool',
          title: '查询工具',
        },
        component: () => import('@/pages/bkSearchTool/index.vue'),
      }
    ],
  },
  // 景点打卡工具
  {
    name: 'scenicSpotsTool',
    path: '/camp/scenicSpotsTool',
    component: RouterView,
    redirect: { path: '/camp/scenicSpotsTool/list' },
    meta: {
      auth: {
        check: ['scenicSpots'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'scenicSpotsTool',
        path: '/camp/scenicSpotsTool/list',
        meta: {
          instanceId: 'bkSearchTool',
          title: '景点打卡工具',
        },
        component: () => import('@/pages/scenicSpotsTool/index.vue'),
      }
    ],
  },
  // 开放平台接口管理
  {
    name: 'openServiceManager',
    path: '/camp/openServiceManager',
    component: RouterView,
    redirect: { path: '/camp/openServiceManager/list' },
    meta: {
      auth: {
        check: ['openServiceManager'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'openServiceManagerList',
        path: '/camp/openServiceManager/list',
        meta: {
          instanceId: 'openServiceManagerList',
          title: '开放平台接口管理',
        },
        component: () => import('@/pages/openServiceManager/list/index.vue'),
      },
      {
        name: 'openServiceManagerEdit',
        path: '/camp/openServiceManager/create/:id',
        meta: {
          instanceId: 'openServiceManagerEdit',
          title: '开放平台接口管理',
        },
        component: () => import('@/pages/openServiceManager/create/index.vue'),
      },
      {
        name: 'openServiceManagerCreate',
        path: '/camp/openServiceManager/create',
        meta: {
          instanceId: 'openServiceManagerCreate',
          title: '开放平台接口管理',
        },
        component: () => import('@/pages/openServiceManager/create/index.vue'),
      }
    ],
  },
  // 开放平台接口包管理
  {
    name: 'openServicePkgManager',
    path: '/camp/openServicePkgManager',
    component: RouterView,
    redirect: { path: '/camp/openServicePkgManager/list' },
    meta: {
      auth: {
        check: ['openServiceManager'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'openServicePkgManagerList',
        path: '/camp/openServicePkgManager/list',
        meta: {
          instanceId: 'openServicePkgManagerList',
          title: '开放平台接口管理',
        },
        component: () => import('@/pages/openServiceManager/pkgList/index.vue'),
      },
      {
        name: 'openServicePkgManagerEdit',
        path: '/camp/openServicePkgManager/create/:id',
        meta: {
          instanceId: 'openServicePkgManagerEdit',
          title: '开放平台接口管理',
        },
        component: () => import('@/pages/openServiceManager/pkgCreate/index.vue'),
      },
      {
        name: 'openServicePkgManagerCreate',
        path: '/camp/openServicePkgManager/create',
        meta: {
          instanceId: 'openServicePkgManagerCreate',
          title: '开放平台接口管理',
        },
        component: () => import('@/pages/openServiceManager/pkgCreate/index.vue'),
      }
    ],
  },
  // 支持SKA批量绑门店银行卡
  {
    name: 'batchBindBankCard',
    path: '/camp/batchBindBankCard',
    component: RouterView,
    redirect: { path: '/camp/batchBindBankCard/list' },
    meta: {
      auth: {
        check: ['bankCard'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'batchBindBankCard',
        path: '/camp/batchBindBankCard/list',
        meta: {
          instanceId: 'batchBindBankCard',
          title: '批量绑卡',
        },
        component: () => import('@/pages/batchBindBankCard/index.vue'),
      }
    ],
  },
  // 地瓜组局回复管理
  {
    name: 'uniReplyManage',
    path: '/camp/uniReplyManage',
    component: RouterView,
    redirect: { path: '/camp/uniReplyManage/list' },
    meta: {
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'uniReplyManage',
        path: '/camp/uniReplyManage/list',
        meta: {
          title: '地瓜组局回复管理',
        },
        component: () => import('@/pages/uniReplyManage/index.vue'),
      }
    ],
  },
  // 审核管理
  {
    name: 'audit',
    path: '/camp/audit',
    component: RouterView,
    redirect: { path: '/camp/audit/provider-qualification-list' },
    meta: {
      pageKey: 'camp_admin_audit',
      auth: {
        check: ['camp'],
        fallback: 'Login',
      },
    },
    children: [
      {
        name: 'providerQualificationList',
        path: '/camp/audit/provider-qualification-list',
        meta: {
          instanceId: 'providerQualificationList',
          title: '代运营服务商主页资质审核',
        },
        component: () => import('@/pages/audit/providerQualificationAudit/index.vue'),
      }
    ],
  },
]

const routes: LauncherOptions['routes'] = [
  {
    path: '',
    redirect: {
      name: 'Index',
    },
  },
  {
    path: '/camp/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/camp/403',
    name: 'Page403',
    component: Page403,
  },
  {
    path: '/camp/404',
    name: 'Page404',
    component: Page404,
  },
  {
    path: '/camp/500',
    name: 'Page500',
    component: Page500,
  },
  {
    name: 'Index',
    path: '/',
    component: Index,
    redirect: {
      path: '/camp/home',
    },
    meta: {
      pageKey: 'camp_admin_root',
      auth: {
        fallback: 'Login',
      },
    },
    children: routesChildren
  },
  {
    path: '/:catchAll(.*)',
    redirect: {
      path: '/camp/home',
    },
  },
]

if (process.env.NODE_ENV === 'development') {
  routes.splice(1, 0, {
    path: '/test',
    name: 'test',
    alias: '/camp/test',
    component: () => import('@/pages/test/index.vue')
  })
}

export default routes
