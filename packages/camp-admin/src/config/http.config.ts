import { HttpConfig } from '@xhs/launcher'
import { getLoganUrlByQuery } from '@/utils'

const PROD_HOSTNAME = /^hera\.xiaohongshu\.com$/
const BETA_HOSTNAME = /beta\.xiaohongshu\.com$/
const SIT_HOSTNAME = /sit\.xiaohongshu\.com$/

export const getEnv = () => {
  // eslint-disable-next-line
  const { hostname } = location
  if (PROD_HOSTNAME.test(hostname)) {
    return 'production'
  } if (BETA_HOSTNAME.test(hostname)) {
    return 'prerelease'
  } if (SIT_HOSTNAME.test(hostname)) {
    return 'test'
  }
  return 'development'
}
// 开发环境
export const env = getEnv()

const baseURLMap = {
  development: getLoganUrlByQuery(),
  // development: getLoganUrlByQuery('jinyuan-prod'),
  test: '',
  prerelease: '',
  production: '',
}

const edithBaseURLMap = {
  development: getLoganUrlByQuery(),
  // development: getLoganUrlByQuery('jinyuan-prod'),
  test: '//www.sit.xiaohongshu.com',
  prerelease: '//www.beta.xiaohongshu.com',
  production: '//www.xiaohongshu.com',
}

export const baseURL = baseURLMap[env]
export const edithBaseURL = edithBaseURLMap[env]

const httpConfig: HttpConfig = {
  BASE_CONFIG: {
    defaults: {
      transform: true,
      timeout: 30000,
    },
    development: {
      withCredentials: true,
    },
    test: {
      withCredentials: true,
    },
  },
  API_LIST: {
    // 0. SSO登录接口三兄弟
    LOGIN_SSO: `${baseURL}/api/hera/ssoLogin`,
    LOGOUT: `${baseURL}/api/hera/logout`,
    USER_INFO: `${baseURL}/api/hera/user/info`,
  },
}

export default httpConfig
