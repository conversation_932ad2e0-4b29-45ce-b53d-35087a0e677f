import { AuthConfig } from '@xhs/launcher-plugin-auth'
import {
  loginSSO,
  getUserInfo,
  logout,
  outdoorAuth,
  orderAuth,
  lifeNoteAuth,
  bkExemptAuth,
  lifeToolAuth,
  scenicSpotsAuth,
  openServiceManagerAuth,
  openServiceAdminManagerAuth,
  lifeBatchBindBankCardAuth,
} from '../services/user'

// provide user releated services, so that it can handle "isLogin" permission
const services = {
  login: loginSSO,
  logout,
  getUserInfo,
}

const authConfig: AuthConfig = {
  services,
  // @ts-ignore 类型声明错误
  permission: {
    // @ts-ignore
    camp: outdoorAuth, // 检查是否是露营管理平台
    // @ts-ignore
    order: orderAuth, // 检查是否是露营管理平台
    note: lifeNoteAuth, // 笔记刷数工具
    bkExempt: bkExemptAuth, // BK撮合惩罚豁免自助工具
    lifeTool: lifeToolAuth, // BK撮合查询工具
    scenicSpots: scenicSpotsAuth, // 景点打卡工具
    openServiceManager: openServiceManagerAuth, // 开放服务管理
    openServiceManagerAdmin: openServiceAdminManagerAuth, // 开放服务管理
    bankCard: lifeBatchBindBankCardAuth, // 批量绑卡
  }
}

export default authConfig
