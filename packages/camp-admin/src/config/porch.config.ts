import { h } from 'vue'
import qs from 'qs'
import { Modal, Text } from '@xhs/delight'
import { env } from './http.config'

export const SSO_HOSTS = {
  development: 'http://login2.sit.xiaohongshu.com',
  // development: 'http://login2.xiaohongshu.com',
  test: 'http://login2.sit.xiaohongshu.com',
  prerelease: 'https://login2.xiaohongshu.com',
  production: 'https://login2.xiaohongshu.com',
}

// @ts-ignore
const host = SSO_HOSTS[env]

export function getCurrentUrl() {
  // 设置登录后的跳转链接为当前链接
  const queryObj = qs.parse(window.location.search.replace('?', ''))
  delete queryObj['ticket'] // eslint-disable-line
  let queryStr = qs.stringify(queryObj)
  queryStr = queryStr ? `?${queryStr}` : ''
  const redirectUrl = `${window.location.origin}/camp/login${queryStr}`
  return redirectUrl
}

export function getPorchLoginUrl(): string {
  return `${host}?service=${getCurrentUrl()}`
}

export function getPorchLogoutUrl(): string {
  return `${host}/logout?service=${getCurrentUrl()}`
}

export const last401TimeKey = 'CAMP_ADMIN_LAST_TIME_401'
export const toPorch = () => {
  const now = Date.now()
  const last401Time = Number(sessionStorage.getItem(last401TimeKey) || 0)

  const doToPorch = async () => {
    sessionStorage.setItem(last401TimeKey, `${now}`)
    window.location.href = getPorchLoginUrl()
  }

  if (now - last401Time > 3000) {
    doToPorch()
  } else {
    Modal.warning({
      title: '警告',
      confirmText: '重新登录',
      content: h(Text, {}, {
        default: () => '登录过期，请重新登录'
      }),
      onCancel(close:any) {
        close?.()
        doToPorch()
      },
      onConfirm(close:any) {
        close?.()
        doToPorch()
      },
    })
  }
  return true
}
