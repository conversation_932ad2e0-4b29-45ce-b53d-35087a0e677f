import { ref, computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const pageHeaderProps:PageHeaderProps = { // 页头的配置
  type: 'combination',
  items: [
    { title: 'SKA/CKA商家品牌管理' },
    { title: '品牌信息列表' },
  ],
}

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'sellerInfo',
        label: '商家名称/ID',
        component: {
          props: {
            placeholder: '请输入商家名称/ID',
          },
        },
      },
      {
        name: 'brandName',
        label: '品牌名称',
        component: {
          props: {
            placeholder: '请输入品牌名称',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const createVisible = ref(false)
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新增',
        onClick: () => {
          createVisible.value = true
        },
      },
      // {
      //   text: '次要操作',
      //   onClick: () => {
      //     toast.warning('次要操作')
      //   },
      // },
    ],
  }))
  return {
    toolBarConfig,
    createVisible,
  }
}

export const useTableColumns = ({
  handleDelete,
}: {
  handleDelete: (item:any) => void
  handleModal: (item:any) => void
}) => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '店铺ID',
      dataIndex: 'sellerId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.sellerId}</Text>),
    },
    {
      title: '商家名称',
      dataIndex: 'sellerName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.sellerName}</Text>),
    },
    {
      title: '品牌名称',
      dataIndex: 'brandName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.brandName}</Text>),
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.createdBy}</Text>),
    },
    {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [{
            text: '删除',
            popConfirmProps: {
              outsideCloseable: true,
              placement: 'top-end',
              title: '确认删除',
              description: `您确认是否要删除${rowData?.sellerName}+${rowData?.brandName}？`,
              width: 250,
              onConfirm: () => handleDelete(rowData),
            },
          }],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
