import { ref } from 'vue'
import { searchSellerBrandList, deleteSellerBrandItem } from '@/services/brandManagement'
import { toast2 as toast } from '@xhs/delight'

export const useList = () => {
  const listSource = ref<any[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const editItem = ref() // 编辑的Item
  const total = ref(0)
  const filterParam = ref({ // 搜索参数
    pageSize: 10,
    pageNum: 1,
    sellerInfo: '',
    brandName: '',
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNum = 1
    }
    try {
      const {
        sellerInfo, brandName, pageSize, pageNum
      } = filterParam.value
      const res = await searchSellerBrandList({
        sellerInfo, brandName, pageSize, pageNo: pageNum
      })
      total.value = res.total || 0
      listSource.value = res.sellerBrandRelation
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const handleDelete = async (deleteItem:any) => { // 删除方法
    try {
      await deleteSellerBrandItem(deleteItem.id)
      toast.success({ // 规范Toast交互，要求带背景色
        content: `${deleteItem?.sellerName}删除成功`,
        strong: true,
      })
    } catch (error) {
      console.error(error)
    } finally {
      fetchList(false)
    }
  }
  const handleModal = (item:any) => { // 处理编辑弹窗
    editVisible.value = true
    editItem.value = item
  }
  const handleEdit = () => { // 处理编辑
    editVisible.value = false
  }

  return {
    listSource,
    total,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleDelete,
    handleEdit,
    handleModal,
  }
}
