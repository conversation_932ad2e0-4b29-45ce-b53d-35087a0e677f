<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
        filter-text="搜索"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Toolbar :config="toolBarConfig" />
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
        </Table>
        <Pagination
          v-model="filterParam.pageNum"
          v-model:pageSize="filterParam.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
  </Space>
  <Modal
    v-model:visible="createVisible"
    title="新增品牌"
    @confirm="handleCreate"
    @cancel="handleCancel"
  >
    <Form ref="formRef" :model="model" :rules="rules" label-width="100px">
      <FormItem label="选择商家" name="shop">
        <Select
          v-model="model.shop"
          :options="shopSelectOptions"
          filterable
          :filter="shopFilter"
          :loading="shopSelectLoading"
          placeholder="请输入商家名称/ID"
          remote
        >
          <template #empty>
            <div style="padding: var(--size-space-large) 0;">
              <Result title="请输入筛选项进行搜索" />
            </div>
          </template>
        </Select>
      </FormItem>
      <FormItem label="品牌名称" name="brand">
        <Select
          v-model="model.brand"
          :options="brandSelectOptions"
          filterable
          :filter="brandFilter"
          :loading="brandSelectLoading"
          placeholder="请输入品牌名称"
          remote
        >
          <template #empty>
            <div style="padding: var(--size-space-large) 0;">
              <Result title="请输入筛选项进行搜索" />
            </div>
          </template>
        </Select>
      </FormItem>
    </Form>
  </Modal>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import {
    toast2 as toast,
    Space, Table, Pagination, Modal, Text,
    Form2 as Form, FormItem2 as FormItem, Result, Select, useDebounce
  } from '@xhs/delight'

  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import { createSellerBrand, getBrandOptions, getSellerOptions } from '@/services/brandManagement'
  import {
    spaceProps, pageHeaderProps, useToolBar, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  const formRef = ref()
  const model = reactive({
    shop: '',
    brand: ''
  })

  const rules = {
    shop: { required: true, message: '请选择商家' },
    brand: { required: true, message: '请选择品牌名称' },
  }

  const shopSelectLoading = ref(false)
  const shopSelectOptions = ref<{label:string; value: string}[]>([])
  const brandSelectLoading = ref(false)
  const brandSelectOptions = ref<{label:string; value: string}[]>([])

  const {
    listSource, // 列表数据
    total, // 数据总数
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
    handleDelete, // 删除方法
    handleModal, // 处理弹窗
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { toolBarConfig, createVisible } = useToolBar()
  const { tableColumns } = useTableColumns({
    handleDelete,
    handleModal,
  })

  const handleCreate = async () => {
    formRef.value.validate()
      .then(async () => {
        try {
          const brand = brandSelectOptions.value.find(item => item.value === model.brand) || { value: '', label: '' }
          const shop = shopSelectOptions.value.find(item => item.value === model.shop) || { value: '', label: '' }
          await createSellerBrand({
            sellerId: shop?.value, brandId: brand?.value, sellerName: shop?.label, brandName: brand?.label
          })
          toast.success({ // 规范Toast交互，要求带背景色
            content: '新增品牌成功',
            strong: true,
          })
          fetchList()
          createVisible.value = false
        } catch (error) {
          console.log(error, 'error')
        }
      })
      .catch((e: any) => {
        console.log(e, 'error')
      })
  }
  const handleCancel = () => {
    createVisible.value = false
    formRef.value.resetFields()
  }

  const shopFilter = useDebounce(
    async filterValue => {
      if (filterValue) {
        shopSelectLoading.value = true
        try {
          const res = await getSellerOptions(filterValue)
          shopSelectOptions.value = res.map(item => ({ ...item, label: item.publicName, value: item.sellerId }))
        } catch (error) {
          console.log(error, 'error')
        } finally {
          shopSelectLoading.value = false
        }
      }
    },
    { delay: 300 },
  )

  const brandFilter = useDebounce(
    async filterValue => {
      if (filterValue) {
        brandSelectLoading.value = true
        try {
          const res = await getBrandOptions(filterValue)
          brandSelectOptions.value = res.map(item => ({ ...item, label: item.brandName, value: String(item.brandId) }))
        } catch (error) {
          console.log(error, 'error')
        } finally {
          brandSelectLoading.value = false
        }
      }
    },
    { delay: 300 },
  )

  fetchList()
</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
</style>
