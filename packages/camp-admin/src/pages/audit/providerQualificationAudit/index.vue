<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader title="代运营服务商主页资质审核" />
      <OutlineFilter
        v-model="filterParams"
        :config="filterConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        v-loading="actionLoading"
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        >
          <template #companyInfo="{ rowData }">
            <span style="color: #2372FB; cursor: pointer; font-size: 14px; font-weight: 500" @click="() => openProviderInfo(rowData)">查看详情</span>
          </template>
        </Table>
        <Pagination
          v-model="baseParams.pageNum"
          v-model:pageSize="baseParams.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
    <Modal
      v-model:visible="auditModalVisible"
      title="驳回原因"
      confirm-text="提交"
      :z-index="100"
      :loading="actionLoading"
      @confirm="() => handleAudit(ApplyStatusEnum.REJECT)"
      @cancel="auditModalVisible = false"
    >
      <TextArea
        v-model="rejectReason"
        :max-length="50"
        placeholder="请输入驳回原因"
        style="width: 100%"
      />
    </Modal>
    <Drawer
      v-model:visible="companyInfoVisible"
      title="公司主页信息"
      :size="750"
      mask
      close
      mask-closeable
    >
      <ProviderDetail :provider-id="currentRowData.providerId" :apply-id="currentRowData.applyId" />
      <template #footer>
        <Space v-if="currentRowData.applyStatus === ApplyStatusEnum.PROCESSING" style="width: 100%" :size="12" justify="end">
          <Button @click="openAuditModal(currentRowData)">
            不通过
          </Button>
          <Button :loading="actionLoading" type="primary" @click="() => handleAudit(ApplyStatusEnum.APPROVED)">
            通过
          </Button>
        </Space>
      </template>
    </Drawer>
  </Space>

</template>
<script setup lang="ts">
  import { computed, ref, watch } from 'vue'
  import {
    Space, Table, Pagination, Modal, TextArea, Drawer, Button, toast2 as toast,
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import { useList } from 'shared/composables/use-list'
  import { useTableColumns, useFilter, spaceProps } from 'shared/composables/use-props'
  import { tryCatch } from 'shared/utils'
  import {
    getProviderQualificationAuditList, providerAudit, ApplyStatusEnum,
  } from '@/services/providerQualificationAudit'
  import { scheme as schemeNormal } from './scheme'
  import ProviderDetail from './detail.vue'

  const scheme = computed(() => schemeNormal)

  const actionLoading = ref(false)

  const auditModalVisible = ref(false)
  const rejectReason = ref('')
  watch(() => auditModalVisible.value, () => {
    rejectReason.value = ''
  })

  const currentRowData = ref<any>({})
  const companyInfoVisible = ref(false)
  const {
    listSource,
    total,
    loading,
    baseParams,
    filterParams,
    fetchList
  } = useList(scheme, getProviderQualificationAuditList)

  const openAuditModal = (rowData: any) => {
    currentRowData.value = rowData
    auditModalVisible.value = true
  }

  const openProviderInfo = (rowData: any) => {
    currentRowData.value = rowData
    companyInfoVisible.value = true
  }

  const handleAudit = async (auditStatus: ApplyStatusEnum) => {
    if (auditStatus === ApplyStatusEnum.REJECT && !rejectReason.value) {
      toast.danger('请输入驳回原因')
      return
    }
    actionLoading.value = true
    const [, err] = await tryCatch(() => providerAudit({
      applyId: currentRowData.value.applyId,
      providerId: currentRowData.value.providerId,
      applyStatus: auditStatus,
      reason: rejectReason.value
    }))
    actionLoading.value = false
    if (!err) {
      auditModalVisible.value = false
      companyInfoVisible.value = false
      toast.success('审核成功')
      fetchList(false)
    }
  }

  const { tableColumns } = useTableColumns(scheme)
  const { filterConfig } = useFilter(scheme, fetchList)

  fetchList()

</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
  .ultra-list-body
    width 100%
  .ultra-list-content
    width 100%
    background #FFFFFF
    border-radius 8px
    padding 24px
  :deep(.ultra-material-toolbar-wrap)
    padding 0
  :deep(.d-pagination)
    width 100%
</style>
