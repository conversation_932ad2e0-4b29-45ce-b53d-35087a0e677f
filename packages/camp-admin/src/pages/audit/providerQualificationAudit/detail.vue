<template>
  <Space
    :size="20"
    direction="vertical"
    align="start"
    class="camp-admin-provider-detail-container"
  >
    <Space
      v-for="item in detailScheme"
      :key="item.name"
      :size="24"
      align="start"
    >
      <Text class="label" color="text-title">{{ item.label }}</Text>
      <component :is="item.render(providerInfo)" v-if="item.render" />
      <Text
        v-else
        style="color: #333; width: 500px"
      >{{ providerInfo?.[item.name] }}</Text>
    </Space>
  </Space>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { Space, Text } from '@xhs/delight'
  import { tryCatch } from 'shared/utils'
  import { fetchProviderInfo } from '@/services/providerQualificationAudit'
  import { getRegionMap } from 'shared/utils/providerMarket/regionMap'
  import { detailScheme, IProviderInfoDetail } from './scheme'

  const props = defineProps<{
    providerId: string
    applyId: string
  }>()

  const providerInfo = ref<IProviderInfoDetail>({})

  const getProviderInfo = async () => {
    const [res, err] = await tryCatch(() => fetchProviderInfo({
      applyId: props.applyId,
      providerId: props.providerId
    }))
    if (res && !err) {
      providerInfo.value = {
        ...res.providerAuditDetail,
        regionMap: await getRegionMap(res?.providerAuditDetail?.region?.map(item => JSON.parse(item)) ?? [], true) ?? {},
        secureFields: res?.secureFields
      }
    }
  }

  watch(() => [props.providerId, props.applyId], () => {
    if (props.providerId && props.applyId) {
      getProviderInfo()
    }
  }, {
    immediate: true
  })

</script>

<style lang="stylus" scoped>
.camp-admin-provider-detail-container
  .label
    width 96px
  img
    border-radius: --size-radius-default;
    width 100px
    height 100px
</style>
