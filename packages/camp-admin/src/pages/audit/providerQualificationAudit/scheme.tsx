import { Text, Badge, Space } from '@xhs/delight'
import TableColumn from 'shared/components/TableColumn/index.vue'
import {
  ApplyStatusEnum, ApplyStatusColorMap, ApplyStatusTextMap, IProviderBaseInfo, getSecureUserData,
} from '@/services/providerQualificationAudit'
import { CONTACT_TYPE_MAP, PROVIDER_SETTLEMENT_MAP } from 'shared/constants/provider/providerMarket'
import { ProviderSettlementEnum } from 'shared/types/providerMarket'
import {
  changeDesensitize,
  TableCellDesensitizeUI as TableCellDesensitize,
} from 'shared/components/TableCellDesensitize'
import dayjs from 'dayjs'

export interface FilterPayload {
  condition_type: number
  data: string
}

export interface IProviderInfoDetail extends IProviderBaseInfo {
  regionMap?: Record<string, string>
}

export const scheme = {
  filterScheme: [
    {
      name: 'applyId',
      label: '审核ID',
      component: {
        props: {
          placeholder: '请输入审核ID',
          clearable: true
        },
      },
    },
    {
      name: 'providerName',
      label: '服务商名称',
      component: {
        props: {
          placeholder: '请输入服务商名称',
          clearable: true
        },
      },
    },
    {
      name: 'providerId',
      label: '服务商ID',
      component: {
        props: {
          placeholder: '请输入服务商ID',
          clearable: true
        },
      },
    },
    {
      name: 'applyStatus',
      label: '申请状态',
      component: {
        is: 'Select',
        props: {
          options: [
            { label: '待审核', value: ApplyStatusEnum.PROCESSING },
            { label: '已通过', value: ApplyStatusEnum.APPROVED },
            { label: '已驳回', value: ApplyStatusEnum.REJECT },
          ],
        },
      },
    },
  ],
  tableColumnScheme: [
    {
      field: 'applyId',
      title: '审核ID',
      dataIndex: 'applyId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="applyId" />
    },
    {
      field: 'providerName',
      title: '服务商名称',
      dataIndex: 'providerName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="providerName" />
    },
    {
      field: 'providerId',
      title: '服务商ID',
      dataIndex: 'providerId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="providerId" />
    },
    {
      field: 'createTime',
      title: '入驻时间',
      dataIndex: 'createTime',
      minWidth: 88,
      render: ({ rowData }) => <Text class="value">{dayjs(rowData.createTime).format('YYYY-MM-DD')}</Text>
    },
    {
      field: 'companyInfo',
      title: '公司主页信息',
      dataIndex: 'companyInfo',
      minWidth: 88,
    },
    {
      field: 'applyStatus',
      title: '申请状态',
      dataIndex: 'applyStatus',
      minWidth: 88,
      render: ({ rowData }) => <Space size={8}>
        <Badge type={ApplyStatusColorMap[rowData.applyStatus]} dot={true} />
        <Text>{ApplyStatusTextMap[rowData.applyStatus]}</Text>
      </Space>
    },
    {
      field: 'reason',
      title: '驳回原因',
      dataIndex: 'reason',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="reason" />
    },
  ],
}

export const detailScheme = [
  {
    label: '审核ID',
    name: 'applyId',
  },
  {
    label: '服务商名称',
    name: 'providerName',
  },
  {
    label: '服务商ID',
    name: 'providerId',
  },
  {
    label: '入驻时间',
    name: 'createTime',
    render: (value: IProviderInfoDetail) => <Text class="value">{dayjs(value.createTime).format('YYYY-MM-DD')}</Text>
  },
  {
    label: '公司头像',
    name: 'providerAvatar',
    render: (providerInfo: IProviderInfoDetail) => <img src={providerInfo?.providerAvatar ?? ''} />
  },
  {
    label: '公司简介',
    name: 'desc',
  },
  {
    label: '服务地域',
    name: 'region',
    render: (value: IProviderInfoDetail) => {
      const region = value?.region?.map(item => JSON.parse(item))
      const result = region?.map(item => `${value.regionMap?.[item[0]]}${value.regionMap?.[item[1]] ? `/${value.regionMap?.[item[1]]}` : ''}`)
      return <Text class="value">{result?.join('、')}</Text>
    },
  },
  {
    label: '主营行业',
    name: 'mainIndustry',
    render: (value: IProviderInfoDetail) => <Text class="value">{value?.mainIndustry?.join('、')}</Text>
  },
  {
    label: '人员规模',
    name: 'staffSize',
  },
  {
    label: '联系方式',
    name: 'contact',
    render: (value: IProviderInfoDetail) => {
      if (value?.contact?.length) {
        return <Space size={0} direction="vertical" align="start">
          { String(value?.contact?.[0] ?? '') ? <Text class="value">{CONTACT_TYPE_MAP[value?.contact?.[0]]}</Text> : null}
          { String(value?.contact?.[1] ?? '') ? <Space class="value">
            <Text>{CONTACT_TYPE_MAP[value?.contact?.[1]]}</Text>
            <TableCellDesensitize
              content={value?.contactPhone ?? ''}
              fullContent={value?.desensitizeData?.['providerBaseInfo.contactPhone']}
              status={value?.secureStatus as any}
              secureFields={value?.secureFields}
              // @ts-ignore
              onClick={() => changeDesensitize(value, () => getSecureUserData(value))}
            />
          </Space> : null}
        </Space>
      }
      return <Text class="value">-</Text>
    }
  },
  {
    label: '案例简介',
    name: 'cases',
  },
  {
    label: '案例图片',
    name: 'casePhoto',
    render: (value: IProviderInfoDetail) => <img src={value?.casePhoto?.[0]} />
  },
  {
    label: '结算方式',
    name: 'settlement',
    render: (value: IProviderInfoDetail) => <Space direction="vertical" size={0} align="start">
      { value?.settlement?.map(item => <Text class="value">{PROVIDER_SETTLEMENT_MAP[item.providerSettlement as ProviderSettlementEnum]} {item.desc ? `(${item.desc})` : ''}</Text>) }
    </Space>
  },
]
