<template>
  <BeerTable border-style="full" :columns="columns" :data="data" />
</template>

<script lang="tsx" setup>
  import { defineProps } from 'vue'
  import { RouterLink } from 'vue-router'
  import { BeerTable, BeerCheckbox } from '@xhs/yam-beer'

  import { typeMap } from '@/constants/POI'
  import * as Api from '@/services/poi'
  import confirm from '@/utils/confirm'

  import PoiInfo from '@/components/poi-info/index.vue'

  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['refresh'])

  const columns = [
    {
      title: 'POI',
      key: 'poiName',
      td: {
        is: (data:any) => {
          const row = data.props?.rowData
          return <PoiInfo poi={row} />
        }
      }
    },
    {
      title: '省市区',
      key: 'province',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          return [row.province, row.city, row.district].filter(Boolean).join(' ')
        }
      }
    },
    {
      title: 'POI地址',
      key: 'address',
    },
    {
      title: 'POI类型',
      key: 'type',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          return typeMap[row.type as ('CAMP' | 'SKI')]
        }
      }
    },
    {
      title: '升级状态',
      key: 'isUgrade',
      formatter: (status: boolean, i: number) => {
        const row: any = props.data[i]
        const scope: any = {
          'onUpdate:modelValue': async () => {
            const isUpgrade = row.isUpgrade === 'N' ? 'Y' : 'N'
            await confirm(`请确认是否${isUpgrade === 'Y' ? '开启' : '关闭'}`)
            try {
              await Api.update({ ...row, isUpgrade })
              row.isUpgrade = isUpgrade
            } catch (err) {
            // @ts-ignore
            }
          }
        }
        return <BeerCheckbox modelValue={row.isUpgrade === 'Y'} {...scope} />
      },
    },
    {
      title: '地图展示',
      key: 'map',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const scope: any = {
            'onUpdate:modelValue': async (e: boolean) => {
              const isShow = e ? 'Y' : 'N'
              await confirm(`请确认是否${isShow ? '开启' : '关闭'}`)
              await Api.update({ ...row, isShow })
              row.isShow = isShow
            }
          }
          return <BeerCheckbox disabled={true} modelValue={row.isShow === 'Y'} {...scope} />
        },
      }
    },
    {
      title: '商品数量',
      key: 'count',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const linkProps: any = {
            class: 'primary',
            target: '_blank',
            to: {
              name: 'poiGoods', query: { poiId: row.poiId, poiName: row.poiName },
            }
          }
          return <RouterLink {...linkProps}>{row.productCount || 0}</RouterLink>
        },
      }
    },
    {
      title: '精选笔记',
      key: 'count',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const linkProps: any = {
            class: 'primary',
            target: '_blank',
            to: {
              name: 'poiNote', query: { poiId: row.poiId, poiName: row.poiName },
            }
          }
          return <RouterLink {...linkProps}>{row.noteNum || 0}</RouterLink>
        },
      }
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const linkProps: any = {
            class: 'primary',
            target: '_blank',
            to: {
              name: 'poiForm', query: { poiId: row.poiId },
            }
          }
          const remove = async () => {
            await confirm('请确定是否删除?')
            try {
              await Api.update({
                ...row,
                deleted: 1
              })
              emit('refresh')
            } catch (err) {
            // @ts-ignore
            }
          }
          return (
          <div class="r-actions">
            <RouterLink {...linkProps}>编辑</RouterLink>
            <span class="primary" onClick={remove}>删除</span>
          </div>
        )
        },
      }
    },
  ]

</script>

<style>
</style>
