<template>
  <BeerTable border-style="full" :columns="columns" :data="data" />
</template>

<script lang="tsx" setup>
  import { defineProps } from 'vue'
  import { RouterLink } from 'vue-router'
  import { BeerTable } from '@xhs/yam-beer'

  import { POI } from '@/types/poi'

  import * as Api from '@/services/poi'
  import confirm from '@/utils/confirm'
  import getPoi from '@/components/poi-modal/getPoi'

  import PoiInfo from '@/components/poi-info/index.vue'

  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['refresh'])

  const columns = [
    {
      title: 'POI',
      key: 'poiName',
      td: {
        is: (data:any) => {
          const row = data.props?.rowData
          return <PoiInfo poi={row} />
        }
      }
    },
    {
      title: '省市区',
      key: 'province',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          return [row.province, row.city, row.district].filter(Boolean).join(' ')
        }
      }
    },
    {
      title: 'POI地址',
      key: 'address',
    },
    {
      title: '关联信息',
      key: 'connect',
      formatter: (status: boolean, i: number) => {
        const row: any = props.data[i]
        return <div>
        <div>{ row.suiteName || '-' }</div>
        <div class="plain-text">suite_id: { row.suiteId || '-' }</div>
      </div>
      },
    },
    {
      title: '商家信息',
      key: 'seller',
      formatter: (status: boolean, i: number) => {
        const row: any = props.data[i]
        return <div>
        <div>{ row.sellerName || '-' }</div>
        <div class="plain-text">seller_id: { row.sellerId || '-' }</div>
      </div>
      },
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const linkProps: any = {
            class: 'primary',
            target: '_blank',
            to: {
              name: 'poiForm', query: { poiId: row.poiId, target: !row.isNew ? 'part3-title' : '' },
            }
          }
          const remove = async () => {
            await confirm('请确定是否删除?')
            await Api.removePOIRelation({
              id: row.id,
              poiId: row.poiId,
              suiteId: row.suiteId,
              status: row.status
            })
            emit('refresh')
          }
          const changePOI = () => {
            const gotPoi = async (poi:POI) => {
              await confirm('请确定是否更换POI')
              await Api.modifyPOIRelation({
                id: row.id,
                poiId: poi.poiId,
                suiteId: row.suiteId,
                status: row.status
              })
              emit('refresh')
            }
            getPoi(gotPoi)
          }
          return (
          <div class="r-actions">
            <span class="primary" onClick={changePOI}>更换POI</span>
            <RouterLink {...linkProps}>编辑</RouterLink>
            <span class="primary" onClick={remove}>删除</span>
          </div>
        )
        },
      }
    },
  ]

</script>

<style>
</style>
