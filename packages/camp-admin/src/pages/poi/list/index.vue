<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card>
      <!-- 搜索 -->
      <Search :payload="payload" @search="search" />

      <!-- 添加 -->
      <div class="between-flex" style="margin-bottom: 20px">
        <BaseButtonGroup v-model="payload.isUpgrade" :options="buttonOptions" @change="search()" />
        <BeerButton icon-before="add-m" @click="toAdd">添加POI</BeerButton>
      </div>

      <!-- 待处理POI -->
      <TodoPoiTable v-if="payload.isUpgrade === 'TODO'" :data="todoData" @refresh="fetchData()" />
      <!--非待处理POI -->
      <PoiTable v-else :data="data" @refresh="fetchData()" />

      <BasePagination v-model="payload" :total="payload.isUpgrade === 'TODO' ? todoTotal : total" @change="saveAndFetch" />
    </Card>
  </PageLayout>
</template>

<script setup lang="tsx">
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { BeerButton } from '@xhs/yam-beer'

  import { POI } from '@/types/poi'

  import { statusOptions } from '@/constants/POI'
  import confirm from '@/utils/confirm'
  import usePoi from '@/composables/api/use-poi'
  import loadingUtil from '@/utils/loading'
  import * as Api from '@/services/poi'

  import PageLayout from '@/components/page-layout/index.vue'
  import Card from '@/components/base-card/index.vue'
  import BaseButtonGroup from '@/components/base-button-group/index.vue'
  import BasePagination from '@/components/base-pagination/index.vue'
  import getPoi from '@/components/poi-modal/getPoi'

  import Search from './poi-search.vue'
  import PoiTable from './poi-table.vue'
  import TodoPoiTable from './todo-poi-table.vue'

  const router = useRouter()

  const {
    payload,
    data,
    total,

    todoData,
    todoTotal,

    loading,
    fetchData,
    saveAndFetch,
    search
  } = usePoi()

  // 面包屑 breadCrumb
  const navList = [
    {
      label: 'POI管理',
    },
    {
      label: 'POI信息',
    },
  ]

  const buttonOptions = computed(() => [...statusOptions, { name: `待处理 · ${todoTotal.value}`, value: 'TODO' }])

  const toAdd = () => {
    const receivePoi = async (poi:POI) => {
      loadingUtil.show()
      try {
        const res = await Api.detail(poi.poiId)
        loadingUtil.close()
        if (res?.isUpgrade === 'Y') {
          await confirm('POI已添加，是否进入编辑页')
          router.push({
            name: 'poiForm',
            query: { poiId: res.poiId }
          })
        } else {
          router.push({
            name: 'poiForm',
            query: {
              poiId: res.poiId,
              poiName: res.poiName,
              province: res.province,
              city: res.city,
              district: res.district,
              address: res.address,
            }
          })
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log('err', err)
        loadingUtil.close()
        return Promise.reject(err)
      }
    }
    getPoi(receivePoi)
  }

</script>
