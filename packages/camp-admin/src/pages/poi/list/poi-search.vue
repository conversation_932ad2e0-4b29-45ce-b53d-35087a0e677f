<template>
  <BaseForm ok-text="查询" layout="inline" :payload="payload" :fields="fields" @submit="search" />
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import BaseForm from '@/components/base-form/index.vue'
  import BaseSelect from '@/components/base-select/index.vue'
  import { POITypes } from '@/constants/POI'
  import { FormFields } from '@/types/index'
  import { PoiListParams as Payload } from '@/types/poi'

  const props = defineProps({
    payload: {
      type: Object as PropType<Payload>,
      default: () => ({}),
    },
  })

  // 基础表单
  const fields = computed<FormFields>(() => [
    {
      label: 'POI类型',
      name: 'type',
      value: props.payload.isUpgrade === 'TODO' ? '' : props.payload.type,
      is: BaseSelect,
      props: {
        options: [
          { name: '全部', value: '' },
          ...POITypes,
        ],
        disabled: props.payload.isUpgrade === 'TODO'
      },
    },
    {
      label: 'POI名称',
      name: 'poiName',
      value: props.payload.poiName,
    },
    {
      label: 'POIID',
      name: 'poiId',
      value: props.payload.poiId,
    },
  ])

  const emit = defineEmits(['search'])

  // 搜索
  const search = (payload: Payload) => {
    emit('search', payload)
  }

</script>

<style>
</style>
