<template>
  <BeerTable border-style="full" :columns="columns" :data="data" />
  <GoodsModal v-model="visible" :goods="goods" :tags="tags" />
</template>

<script lang="tsx" setup>
  import { defineProps, ref } from 'vue'
  import { BeerTable, BeerCheckbox } from '@xhs/yam-beer'

  import { IGoodsInfo } from '@/types/goods'
  import { PoiGoods } from '@/types/poi'
  import confirm from '@/utils/confirm'
  import loading from '@/utils/loading'
  import * as GoodsApi from '@/services/goods'
  import { updateGoodsStatus } from '@/services/poi'

  import GoodsInfo from '@/components/goods-info/index.vue'
  import GoodsModal from '@/components/goods-modal/index.vue'

  defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  const emit = defineEmits(['loading'])

  const visible = ref(false)
  const goods = ref<IGoodsInfo>()
  const tags = ref<{text:string;type:string}[]>([
  // { text: 'POI未升级样式' },
  // { text: 'POI非升级样式' },
  // { text: '商品已展示' },
  // { text: '商品未展示' },
  ])

  const showGoods = async (row: PoiGoods) => {
    loading.show()
    try {
      tags.value = [
        {
          text: row.isTerminalShow ? 'POI为升级样式' : 'POI非升级样式',
          type: row.isTerminalShow ? 'success' : 'warning'
        },
        {
          text: row.isProductShow ? '商品已展示' : '商品未展示',
          type: row.isProductShow ? 'success' : 'warning'
        },
      ]
      const res = await GoodsApi.detail(row.productId)
      visible.value = true
      goods.value = res
    } catch (err) {
    // @ts-ignore
    }
    loading.close()
  }

  const columns = [
    {
      title: '商品',
      key: 'goods',
      td: {
        is: (data:any) => {
          const row = data.props?.rowData
          return <GoodsInfo data={row} />
        }
      }
    },
    {
      title: '关联POI',
      key: 'poi',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiGoods
          return (
          <div>
            <div>{ row.poiName || '-' }</div>
            <div class="plain-text">poi_id: { row.poiId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '营地信息',
      key: 'suite',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiGoods
          return (
          <div>
            <div>{ row.suiteName || '-' }</div>
            <div class="plain-text">suite_id: { row.suiteId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '商家信息',
      key: 'seller',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiGoods
          return (
          <div>
            <div>{ row.sellerName || '-' }</div>
            <div class="plain-text">seller_id: { row.sellerId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '商品状态',
      key: 'status',
    },
    {
      title: 'POI页面展示',
      key: 'isShow',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiGoods
          const scope: any = {
            'onUpdate:modelValue': async (isShow: boolean) => {
              if (!isShow) {
                await confirm('勾选取消后商品不会再升级POI露出，是否确定')
              }
              emit('loading', true)
              try {
                await updateGoodsStatus({
                  poiId: row.poiId,
                  productId: row.productId,
                  status: isShow ? 'ACTIVE' : 'DISABLE'
                })
                row.isShow = isShow
                emit('loading', false)
              } catch (err) {
                // eslint-disable-next-line no-console
                console.error(err)
              }
              emit('loading', false)
            }
          }
          return <BeerCheckbox modelValue={row.isShow} {...scope} />
        },
      }
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiGoods
          return (
          <div class="r-actions">
            <span class="primary" onClick={() => showGoods(row)}>查看商品</span>
          </div>
        )
        },
      }
    },
  ]

</script>

<style>
</style>
