<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card>
      <Search />
      <BaseButtonGroup v-model="payload.isShow" style="margin-bottom: 20px" :options="options" />
      <Table :data="list" @loading="loading = $event" />
    </Card>
  </PageLayout>
</template>

<script lang="tsx" setup>
  import { ref, computed } from 'vue'

  import useGoods from '@/composables/api/use-goods'
  import PageLayout from '@/components/page-layout/index.vue'
  import Card from '@/components/base-card/index.vue'

  import BaseButtonGroup from '@/components/base-button-group/index.vue'

  import Search from './goods-search.vue'
  import Table from './goods-table.vue'

  const payload = ref<{isShow: string}>({
    isShow: 'Y'
  })

  const navList = [
    {
      label: '商品管理',
    },
    {
      label: '商品信息',
    },
  ]

  const options = [
    { name: '全部', value: '' },
    { name: '已展示', value: 'Y' },
    { name: '未展示', value: 'N' },
  ]

  const { data, loading } = useGoods()

  const list = computed(() => {
    const isShow = payload.value.isShow
    if (isShow === '') return data.value
    return data.value.filter(item => (isShow === 'Y' ? item.isShow : !item.isShow))
  })

</script>

<style>
</style>
