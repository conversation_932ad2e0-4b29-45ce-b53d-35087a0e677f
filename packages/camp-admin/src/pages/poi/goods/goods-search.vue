<template>
  <BaseForm ok-text="查询" layout="inline" :action-visible="false" :payload="payload" :fields="fields" @submit="search" />
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { useRoute } from 'vue-router'
  import BaseForm from '@/components/base-form/index.vue'
  import BaseSelect from '@/components/base-select/index.vue'
  import { FormFields } from '@/types/index'

  const props = defineProps({
    payload: {
      type: Object as PropType<{poiName: string}>,
      default: () => ({}),
    },
  })

  const route = useRoute()

  // 基础表单
  const fields = computed<FormFields>(() => [
    {
      label: 'POI名称',
      name: 'PoiName',
      value: props.payload.poiName,
      is: BaseSelect,
      props: {
        options: [
          { name: route.query.poiName || '--', value: '' },
        ],
        disabled: true
      },
    },
  ])

  const emit = defineEmits(['search'])

  // 搜索
  const search = (payload: {poiName: string}) => {
    emit('search', payload)
  }

</script>

<style>
</style>
