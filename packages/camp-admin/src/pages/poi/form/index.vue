<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card>
      <h2 class="r-part-title">基础信息</h2>
      <div class="form-wrapper">
        <PoiInfo :detail="detail" :payload="payload" />
      </div>

      <h2 class="r-part-title">补充信息</h2>
      <div class="form-wrapper">
        <BaseForm
          ref="form"
          ok-text="提交"
          cancel-text="取消"
          :payload="payload"
          :fields="fields"
          :action-visible="false"
          @submit="submit"
          @cancel="cancel"
        />
      </div>

      <template v-if="poiId">
        <h2 id="part3-title" class="r-part-title">关联信息</h2>
        <div class="form-wrapper">
          <RelationInfo
            v-model:pass-list="detail.passList"
            v-model:processing-list="detail.processingList"
            :poi-id="poiId"
            :poi-name="payload.poiName"
            @refreshRelation="refreshRelation"
          />
        </div>
      </template>

      <div class="actions">
        <div @click="submit(payload)">
          <BeerButton>提交</BeerButton>
        </div>

        <div @click.prevent="cancel">
          <slot name="cancel">
            <BeerButton variant="secondary">取消</BeerButton>
          </slot>
        </div>
      </div>
    </Card>
  </PageLayout>
</template>

<script lang="tsx" setup>
  import { ref, computed, nextTick } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { BeerButton, showModal } from '@xhs/yam-beer'

  import PageLayout from '@/components/page-layout/index.vue'
  import Card from '@/components/base-card/index.vue'
  import BaseForm from '@/components/base-form/index.vue'
  import UploadImage from '@/components/upload-poi-image/index.vue'

  import { PoiForm, POI, ImageSource } from '@/types/poi'
  import { ImageItem, FormFields } from '@/types'
  import * as Api from '@/services/poi'
  import { refresh } from '@/composables/use-cross-refresh'
  import { getPoiSuiteImages } from '@/services/poi'
  import smoothScroll from '@/utils/smoothScroll'

  import PoiInfo from './components/poi-info.vue'
  import TypeSelect from './components/poi-type-select.vue'
  import BaseCheckBox from './components/base-checkbox.vue'
  import BaseCheckBox2 from './components/base-checkbox2.vue'
  import BaseCheckBox3 from './components/base-checkbox3.vue'
  import RelationInfo from './components/relation-info.vue'

  const navList = [
    {
      label: 'POI管理'
    },
    {
      label: 'POI信息'
    },
    {
      label: '编辑POI'
    }
  ]

  const router = useRouter()
  const route = useRoute()

  const loading = ref<boolean>(false)
  const form = ref<any>()

  const {
    poiId = '',
    poiName = '',
    province = '',
    city = '',
    district = '',
    address = '',
  } = route.query as { [key: string]: string }

  type PoiPayload = PoiForm<ImageItem>
  const payload = ref<PoiPayload>({
    poiId,
    poiName,
    province,
    city,
    district,
    address,
    type: !poiId ? 'CAMP' : '', // "CAMP" | "SKI"
    imageSource: 'upload',
    images: [],
    extend1: [],
    extend2: [],
    extend3: [],
  })
  const detail = ref({} as POI)

  const hasRelationSuite = computed(() => !!detail.value.passList?.length || !!detail.value.processingList.length)

  const fetchData = async () => {
    if (!poiId) return
    loading.value = true
    const res = await Api.detail(poiId)
    loading.value = false
    detail.value = res
    // 有可能返回空对象
    if (!res.poiId) return

    const images:ImageItem[] = (res.images || []).map(item => ({ link: item, path: '' }))
    payload.value = {
      poiId,
      poiName: res.poiName,
      province: res.province,
      city: res.city || '-',
      district: res.district || '-',
      address: res.address || '-',
      imageSource: res.imageSource || 'upload',
      type: res.type || 'CAMP', // "CAMP" | "SKI" , 默认露营选项
      images,
      extend1: res.extend1,
      extend2: res.extend2,
      extend3: res.extend3,
    }
    // 如果没有上传图片，并且有suite，根据商家营地自动补全
    if (!images.length && hasRelationSuite.value) {
      const suiteImages = await getPoiSuiteImages(poiId)
      const fistRelationImages = suiteImages[0]?.images || []
      if (fistRelationImages.length) {
        payload.value.imageSource = 'suite'
        payload.value.images = fistRelationImages.map(item => ({ link: item.path, path: '' })).slice(0, 10)
      }
    }

    // 自动滚动
    const scrollTargetId = route.query.target as string
    if (scrollTargetId) {
      await nextTick()
      const offsetTop = (document.querySelector(`#${scrollTargetId}`) as HTMLDivElement)?.offsetTop || 0
      smoothScroll(offsetTop, 100, undefined, document.getElementById('scroll-page-container') as HTMLElement)
    }
  }
  fetchData()

  const refreshRelation = async () => {
    if (!poiId) return
    const res = await Api.detail(poiId)
    detail.value.passList = res.passList
    detail.value.processingList = res.processingList
  }

  // 基础表单
  const fields = computed<FormFields>(() => {
    if (!payload.value.type) {
      return [
        {
          label: 'POI类型',
          name: 'type',
          value: payload.value.type,
          required: true,
          is: TypeSelect,
        },
      ]
    }
    return [
      {
        label: 'POI类型',
        name: 'type',
        value: payload.value.type,
        required: true,
        is: TypeSelect,
        props: {
          'onUpdate:modelValue': () => {
            payload.value.extend1 = []
            payload.value.extend2 = []
            payload.value.extend3 = []
          },
        }
      },
      {
        label: '图片',
        name: 'images',
        value: payload.value.images,
        required: true,
        is: UploadImage,
        props: {
          suiteDisabled: !hasRelationSuite.value ? '请先关联营地或者手动上传' : '',
          aspectRatio: [3 / 4],
          maxCount: 10,
          maxSize: 1024 * 1024 * 5, // 5M
          poiId,
          tip: '最多上传10张，支持png/jpg/jpeg，尺寸为3:4，大小不超过5M；可拖拽排序，POI主图取首图',
          source: payload.value.imageSource,
          'onUpdate:source': (e: ImageSource) => {
            payload.value.imageSource = e
          }
        },
        validate: (value: ImageItem[]) => {
          const error = value?.find(item => item.error)?.error
          if (error) return Promise.reject(error)
          return value?.length ? Promise.resolve(true) : Promise.reject('请上传图片')
        }
      },
      {
        label: payload.value.type === 'CAMP' ? '基础设施' : '基础设施',
        name: 'extend1',
        value: payload.value.extend1,
        required: true,
        is: BaseCheckBox,
        props: {
          type: payload.value.type,
        },
        validate: (value: any) => (value?.length ? Promise.resolve(true) : Promise.reject('请选择'))
      },
      {
        label: payload.value.type === 'CAMP' ? '营地设施' : '雪场设施',
        name: 'extend2',
        value: payload.value.extend2,
        required: true,
        is: BaseCheckBox2,
        props: {
          type: payload.value.type,
        },
        validate: (value: any) => (value?.length ? Promise.resolve(true) : Promise.reject('请选择'))
      },
      {
        label: payload.value.type === 'CAMP' ? '自然景观' : '雪道类型',
        name: 'extend3',
        value: payload.value.extend3,
        required: true,
        props: {
          type: payload.value.type,
        },
        is: BaseCheckBox3,
        validate: (value: any) => (value?.length ? Promise.resolve(true) : Promise.reject('请选择'))
      },
    ]
  })

  const cancel = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      // router.push({ name: 'poiList' })
      window.close()
    }
    refresh()
  }

  const submit = async (p: PoiPayload) => {
    if (detail.value.processingList.length) {
      showModal({
        props: {
          type: 'dialog',
          title: 'POI有待处理的关联关系，请处理后提交',
          cancelText: '',
          handleConfirm({ close }: {close: any}) {
            close()
          }
        }
      })
      return
    }
    await form.value?.validate()
    loading.value = true
    try {
      const images = p.images
        .filter(item => !item.error)
        .map(item => item.link)
      const finalPayload = {
        ...p,
        images
      }
      // 新增会传入poiName，更新不会
      if (!poiName) {
        await Api.update(finalPayload)
      } else {
        await Api.create(finalPayload)
      }
      window.close()
      refresh()
    } catch (e) {
    // @ts-ignore
    }
    loading.value = false
  }

</script>

<style lang="stylus" scoped>
.form-wrapper
  padding-left 20px
.actions
  display flex
  padding 15px 0
  padding-left 20px
</style>
