<template>
  <BaseForm :ok-text="''" :cancel-text="''" :fields="fields" />
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { BeerInput, Tag } from '@xhs/yam-beer'

  import { PoiForm, POI } from '@/types/poi'
  import { ImageItem, FormFields } from '@/types'

  import BaseForm from '@/components/base-form/index.vue'

  import AddressInfo from './address-info.vue'

  type PoiPayload = PoiForm<ImageItem>

  const props = defineProps({
    payload: {
      type: Object as PropType<PoiPayload>,
      default: () => ({})
    },
    detail: {
      type: Object as PropType<POI>,
      default: () => ({})
    },
  })

  const fields = computed<FormFields>(() => [
    {
      label: 'POIID',
      name: 'POIID',
      value: props.payload.poiId,
      required: true,
      is: () => (
      <div>
        <BeerInput disabled={true} clearable={false} modelValue={props.payload.poiId} />
        {
          !!props.detail.poiId && (
            <Tag style='margin-left: 1em' variant="color" size="s" modifier={props.detail.isTerminalShow ? 'success' : 'warning'}>
              {props.detail.isTerminalShow ? 'POI为升级样式' : 'POI非升级样式' }
            </Tag>
          )
        }
      </div>
    ),
    },
    {
      label: 'POI名称',
      name: 'POI名称',
      value: props.payload.poiName,
      required: true,
      is: BeerInput,
      props: {
        disabled: true,
        clearable: false
      }
    },
    {
      label: 'POI地址',
      name: 'POI地址',
      value: 'POI地址',
      required: true,
      is: AddressInfo,
      props: {
        province: props.payload.province,
        city: props.payload.city,
        distinct: props.payload.district,
        address: props.payload.address
      },
    },
  ])

</script>

<style>
</style>
