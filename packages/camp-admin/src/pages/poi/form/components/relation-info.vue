<template>
  <div>
    <div class="sub">待处理</div>
    <BeerTable size="s" border-style="full" :columns="columns" :data="processingList" />
    <div class="sub p-t">已处理</div>
    <BeerTable size="s" border-style="full" :columns="columns" :data="passList" />
    <div class="r-actions">
      <span class="primary" @click="addRelation">添加关联关系</span>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import { BeerTable } from '@xhs/yam-beer'

  import { PoiSuite } from '@/types/poi'
  import { auditStatusMap } from '@/constants/suite'
  import * as Api from '@/services/poi'

  import confirm from '@/utils/confirm'
  import getRelationSuite from '@/components/relation-suite/getRelationSuite'
  import loading from '@/utils/loading'

  const props = defineProps({
    passList: {
      type: Array as PropType<PoiSuite[]>,
      default: () => []
    },
    processingList: {
      type: Array as PropType<PoiSuite[]>,
      default: () => []
    },
    poiId: {
      type: String,
      default: ''
    },
    poiName: {
      type: String,
      default: ''
    },
  })

  const emit = defineEmits(['update:passList', 'update:processingList', 'refreshRelation'])

  const validateRelation = async (suiteId:string) => {
    loading.show()
    const { poinRelations } = await Api.todoList({
      suiteId,
      pageNum: 1,
      pageSize: 1,
      status: 'PASS'
    })
    loading.close()
    const poi = poinRelations[0]
    if (poi) {
      await confirm(`营地已经关联POI：${poi.poiName}(poiId: ${poi.poiId})，通过后将移除已有关联关系和已有商品，是否确定`)
    } else {
      await confirm('通过后营地将关联此POI并同步商品，是否确定')
    }
  }

  const addRelation = () => {
    getRelationSuite(async simpleSuite => {
      await validateRelation(simpleSuite.suiteId)
      await Api.addRelation({
        poiId: props.poiId,
        poiName: props.poiName,
        suiteId: simpleSuite.suiteId,
      })
      emit('refreshRelation')
    })
  }

  const remove = async (row: PoiSuite) => {
    await confirm('删除操作将会移除已关联POI的商品，是否删除')
    await Api.removePOIRelation({
      id: row.id,
      poiId: row.poiId,
      suiteId: row.suiteId
    })
    emit('update:passList', props.passList.filter(item => item.suiteId !== row.suiteId))
  }

  const pass = async (row:PoiSuite) => {
    await validateRelation(row.suiteId)
    await Api.auditRelation({
      id: row.id,
      poiId: row.poiId,
      suiteId: row.suiteId,
      status: 'PASS'
    })

    emit('update:processingList', props.processingList.filter(item => item.id !== row.id))

    row.status = 'PASS'
    const passList = [row, ...props.passList.filter(item => row.suiteId !== item.suiteId)]
    emit('update:passList', passList)
  }

  const forbid = async (row:PoiSuite) => {
    await confirm('拒绝后营地不会关联此POI并同步商品，是否确定')
    await Api.auditRelation({
      id: row.id,
      poiId: row.poiId,
      suiteId: row.suiteId,
      status: 'REJECT'
    })

    emit('update:processingList', props.processingList.filter(item => item.id !== row.id))
  }

  const columns = [
    {
      title: '序号',
      key: 'index',
      formatter: (_:number, i: number) => i + 1,
    },
    {
      title: '关联时间',
      key: 'relatedTime',
    },
    {
      title: '关联信息',
      key: 'connect',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiSuite
          return (
          <div>
            <div>{ row.suiteName || '-' }</div>
            <div class="plain-text">suite_id: { row.suiteId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '状态',
      key: 'audit',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiSuite
          return row.suiteStatus === 'ACTIVE' ? '有效' : '无效'
        }
      }
    },
    {
      title: '审核状态',
      key: 'audit',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiSuite
          return auditStatusMap[row.suiteAuditStatus]
        }
      }
    },
    {
      title: '在架商品数',
      key: 'buyableProductCount',
    },
    {
      title: '商家信息',
      key: 'seller',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiSuite
          return (
          <div>
            <div>{ row.sellerName || '-' }</div>
            <div class="plain-text">seller_id: { row.sellerId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as PoiSuite
          return (
          <div class="r-actions">
            {row.status === 'PROCESSING' && <span class="primary" onClick={() => pass(row)}>通过</span>}
            {row.status === 'PROCESSING' && <span class="primary" onClick={() => forbid(row)}>拒绝</span>}
            {row.status === 'PASS' && <span class="primary" onClick={() => remove(row)}>删除</span>}
          </div>
        )
        }
      }
    },
  ]
</script>

<style lang="stylus" scoped>
.sub
  padding .5em 0
  font-size 14px
  line-height 22px
.p-t
  padding-top 1em
</style>
