<template>
  <div class="ski-camp-base-select-outer">
    <CheckboxGroup v-model="value" :options="options" allow-all />
  </div>
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { CheckboxGroup } from '@xhs/yam-beer'

  const optionsMap = {
    CAMP: [
      { name: 'wifi', value: 'wifi' },
      { name: '停车场', value: '停车场' },
      { name: '餐厅', value: '餐厅' },
      { name: '卫生间', value: '卫生间' },
      { name: '沐浴设施', value: '沐浴设施' },
      { name: '可供电', value: '可供电' },
      { name: '移动电源', value: '移动电源' },
    ],
    SKI: [
      { name: '缆车', value: '缆车' },
      { name: '停车场', value: '停车场' },
      { name: '餐厅', value: '餐厅' },
      { name: '卫生间', value: '卫生间' },
      { name: '储物柜', value: '储物柜' },
      { name: '魔毯', value: '魔毯' },
    ]
  }

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    type: {
      type: String as PropType<'CAMP' | 'SKI'>,
      default: 'CAMP'
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const options = computed(() => optionsMap[props.type])

  const value = computed({
    get: () => props.modelValue || [],
    set: e => {
      emit('update:modelValue', e)
    }
  })
</script>

<style lang="stylus">
.ski-camp-base-select-outer
  .css-k008qs
    flex-wrap: wrap
</style>
