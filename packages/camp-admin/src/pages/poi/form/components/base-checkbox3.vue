<template>
  <div class="ski-camp-base-select-outer">
    <CheckboxGroup v-model="value" :options="options" allow-all />
  </div>
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { CheckboxGroup } from '@xhs/yam-beer'

  const optionsMap = {
    CAMP: [
      { name: '星空', value: '星空' },
      { name: '森林', value: '森林' },
      { name: '海岸', value: '海岸' },
      { name: '草原', value: '草原' },
      { name: '茶园', value: '茶园' },
      { name: '沙漠', value: '沙漠' },
      { name: '湖泊/溪水/河流', value: '湖泊/溪水/河流' },
      { name: '峡谷', value: '峡谷' },
    ],
    SKI: [
      { name: '初级雪道', value: '初级雪道' },
      { name: '中级雪道', value: '中级雪道' },
      { name: '高级雪道', value: '高级雪道' },
    ]
  }

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    type: {
      type: String as PropType<'CAMP' | 'SKI'>,
      default: 'CAMP'
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const options = computed(() => optionsMap[props.type])

  const value = computed({
    get: () => props.modelValue || [],
    set: e => {
      emit('update:modelValue', e)
    }
  })
</script>

<style lang="stylus">
.ski-camp-base-select-outer
  .css-k008qs
    flex-wrap: wrap
</style>
