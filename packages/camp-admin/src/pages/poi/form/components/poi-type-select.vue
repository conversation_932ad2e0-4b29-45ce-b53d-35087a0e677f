<template>
  <BaseSelect :options="options" :model-value="modelValue" @update:model-value="change" />
</template>

<script lang="tsx" setup>
  import { defineProps } from 'vue'
  import BaseSelect from '@/components/base-select/index.vue'

  import { POITypes as options } from '@/constants/POI'

  defineProps({
    modelValue: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const change = (value:string) => {
    emit('update:modelValue', value)
  }

</script>

<style>
</style>
