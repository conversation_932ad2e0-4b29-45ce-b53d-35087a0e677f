<template>
  <div class="ski-camp-base-select-outer">
    <CheckboxGroup v-model="value" :options="options" allow-all />
  </div>
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { CheckboxGroup } from '@xhs/yam-beer'

  const optionsMap = {
    CAMP: [
      { name: '可带宠物', value: '可带宠物' },
      { name: '可叫外卖', value: '可叫外卖' },
      { name: '可自带帐篷', value: '可自带帐篷' },
      { name: '可租赁帐篷设备', value: '可租赁帐篷设备' },
      { name: '可过夜', value: '可过夜' },
      { name: '可明火', value: '可明火' },
    ],
    SKI: [
      { name: '室外雪场', value: '室外雪场' },
      { name: '室内雪场', value: '室内雪场' },
      { name: '雪具租赁/售卖', value: '雪具租赁/售卖' },
    ]
  }

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    type: {
      type: String as PropType<'CAMP' | 'SKI'>,
      default: 'CAMP'
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const options = computed(() => optionsMap[props.type])

  const value = computed({
    get: () => props.modelValue || [],
    set: e => {
      emit('update:modelValue', e)
    }
  })
</script>

<style lang="stylus">
.ski-camp-base-select-outer
  .css-k008qs
    flex-wrap: wrap
</style>
