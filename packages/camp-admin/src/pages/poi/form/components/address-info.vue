<template>
  <div>
    <div class="flex">
      <div class="address-select">
        <BeerSingleSelect :model-value="province" disabled />
      </div>
      <div class="address-select">
        <BeerSingleSelect :model-value="city" disabled />
      </div>
      <div class="address-select">
        <BeerSingleSelect :model-value="distinct" disabled />
      </div>
    </div>
    <div style="margin-right: 20px">
      <BeerInput :bs="{ width: '100%' }" :model-value="address" disabled :clearable="false" />
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { BeerSingleSelect, BeerInput } from '@xhs/yam-beer'

  defineProps({
    province: {
      type: String,
      default: ''
    },
    city: {
      type: String,
      default: ''
    },
    distinct: {
      type: String,
      default: ''
    },
    address: {
      type: String,
      default: ''
    },
  })

</script>

<style lang="stylus" scoped>
.address-select
  margin-right: 20px !important
  margin-bottom: 20px !important
</style>
