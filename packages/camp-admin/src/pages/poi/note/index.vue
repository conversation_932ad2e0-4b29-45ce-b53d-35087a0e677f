<template>
  <PageLayout :loading="loading">
    <Card>
      <p class="r-plain-text">
        <span style="padding-right: 4em">POIID: {{ $route.query.poiId }}</span>
        <span>POI名称: {{ $route.query.poiName }}</span>
      </p>

      <h2 class="r-h2">关联笔记</h2>

      <BaseButtonGroup
        v-model="type"
        style="margin-bottom: 20px"
        :options="types"
        @update:model-value="refresh"
      />

      <div v-if="type === '1'" style="padding-bottom: 20px; display: flex; align-items: flex-end;">
        <BeerTextArea v-model="idsText" placeholder="请输入笔记ID，支持多行" :initial-rows="5" />
        <BeerButton style="margin-left: 20px" @click="query">查询</BeerButton>
      </div>

      <div style="min-height: 300px">
        <NoteTable
          :type="type"
          :payload="payload"
          :data="type === '0' ? data : queryData"
          :poi-id="poiId"
          @refresh="refresh"
        />
      </div>

      <Pagination v-if="type === '0'" v-model="payload" :total="total" @change="fetchData" />
    </Card>
  </PageLayout>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'
  import { toaster, BeerTextArea, BeerButton } from '@xhs/yam-beer'

  import PageLayout from '@/components/page-layout/index.vue'
  import Card from '@/components/base-card/index.vue'
  import BaseButtonGroup from '@/components/base-button-group/index.vue'
  import Pagination from '@/components/base-pagination/index.vue'

  import { types } from '@/constants/note'
  import * as Api from '@/services/note'
  import { Note } from '@/types/note'

  import NoteTable from './note-table.vue'

  const route = useRoute()
  const { poiId } = route.query as { poiId: string }

  const type = ref<string>('0')

  const payload = ref({
    pageNum: 1,
    pageSize: 20,
    poiId
  })

  const total = ref(0)
  const loading = ref(false)
  const idsText = ref('')

  const data = ref<Note[]>([])
  const queryData = ref<Note[]>([])

  const fetchData = async () => {
    loading.value = true
    try {
      const res = await Api.list(payload.value)
      // console.log(`res`, res)
      data.value = res.list
      total.value = res.total
    } catch (e) {
      // @ts-ignore
      toaster.danger(e.message)
    }
    loading.value = false
  }
  fetchData()

  const query = async () => {
    const noteIds = idsText.value.split(/(\n|\s)/).map(item => item.trim()).filter(Boolean)
    if (!noteIds.length) return
    loading.value = true
    try {
      const res = await Api.query({
        poiId,
        noteIds: [...new Set(noteIds)]
      })
      queryData.value = res
    } catch (e) {
      // @ts-ignore
      toaster.danger(e.message)
    }
    loading.value = false
  }

  const refresh = () => {
    if (type.value === '1') {
      query()
    } else {
      payload.value.pageNum = 1
      fetchData()
    }
  }
</script>

<style>
</style>
