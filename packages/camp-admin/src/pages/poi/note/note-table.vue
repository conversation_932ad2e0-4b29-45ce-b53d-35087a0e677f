<template>
  <BeerTable border-style="full" :columns="columns" :data="data" />
</template>

<script lang="tsx" setup>
  import { defineProps, computed } from 'vue'
  import { BeerTable } from '@xhs/yam-beer'
  import dayjs from 'dayjs'

  import * as Api from '@/services/note'
  // import { typeMap } from '@/constants/POI'
  import confirm from '@/utils/confirm'

  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    },
    payload: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: '0'
    },
    poiId: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['refresh'])

  const columns = computed(() => [
    {
      title: '排序',
      key: 'title1',
      formatter: (_: any, i: number) => (props.payload.pageSize * (props.payload.pageNum - 1)) + i + 1
    },
    {
      title: '笔记ID',
      key: 'title2',
      td: {
        is: (data: any) => {
          const row: any = data.props?.rowData
          const toDetail = () => {
            window.open(`https://www.xiaohongshu.com/discovery/item/${row.noteId}`)
          }
          return (
          <div>
            <div>{row.noteId}</div>
            <span class="primary-color pointer" onClick={toDetail}>查看笔记详情</span>
          </div>
        )
        }
      }
    },
    // {
    //   title: '标注分类',
    //   key: 'category',
    //   td: {
    //     is: (data: any) => {
    //       const row: any = data.props?.rowData
    //       return (
    //         <div>
    //           {typeMap[row.category as (keyof typeof typeMap)] || '-'}
    //         </div>
    //       )
    //     }
    //   }
    // },
    {
      title: '发布时间',
      key: 'publishTime',
      td: {
        is: (data: any) => {
          const row: any = data.props?.rowData
          const publishTime = row.publishTime
          return (
          <div>
            {publishTime ? dayjs(publishTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </div>
        )
        }
      }
    },
    {
      title: '点赞数',
      key: 'praiseCount',
      td: {
        is: (data: any) => {
          const row: any = data.props?.rowData
          return (
          <div>
            {row.praiseCount || 0}
          </div>
        )
        }
      }
    },
    {
      title: '评论数',
      key: 'commentCount',
      td: {
        is: (data: any) => {
          const row: any = data.props?.rowData

          return (
          <div>
            {row.commentCount || 0}
          </div>
        )
        }
      }
    },
    {
      title: '收藏数',
      key: 'collectCount',
      td: {
        is: (data: any) => {
          const row: any = data.props?.rowData
          return (
          <div>
            {row.collectCount || 0}
          </div>
        )
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const i = data.props?.rowIndex
          const { noteId, isRelated } = row
          const { poiId } = props

          if (row.isAuditPass === 'N') {
            return <span style="color: rgba(51, 51, 51, 0.4);">笔记审核未通过</span>
          }

          const setTop = async () => {
            try {
              await confirm('请确认是否置顶')
              await Api.setTop({ noteId, poiId })
              emit('refresh')
            } catch (err) {
            // @ts-ignore
            }
          }

          const deleteTop = async () => {
            try {
              await confirm('请确认是否取消精选')
              await Api.deleteTop({ noteId, poiId })
              emit('refresh')
            } catch (err) {
            // @ts-ignore
            }
          }

          const create = async () => {
            try {
              await confirm('请确认是否设为精选')
              await Api.create({ noteId, poiId })
              emit('refresh')
            } catch (err) {
            // @ts-ignore
            }
          }

          return props.type === '0' ? (
          <div class="r-actions">
            <span class={(props.payload.pageSize * (props.payload.pageNum - 1)) + i ? 'primary' : 'disabled'} onClick={setTop}>置顶</span>
            <span class="primary" onClick={deleteTop}>取消精选</span>
          </div>
            ) : (
          <div class="r-actions">
            {
              isRelated === 'Y'
              ? <span style="color: rgba(51, 51, 51, 0.4);">笔记已设为精选</span>
              : <span class="primary" onClick={create}>设为精选</span>
            }
          </div>
        )
        },
      }
    },
  ])
</script>

<style scoepd>
.disabled {
  color: #999;
  pointer-events: none;
}
</style>
