<template>
  <div class="seller-poi-container">
    <Table
      size="large"
      :columns="tableColumns"
      :data-source="listSource"
      :loading="loading"
    />
    <Pagination
      v-model="filterParam.pageNo"
      v-model:page-size="filterParam.pageSize"
      :total="filterParam.total"
      style="margin-top: 24px"
      @change="fetchList(false)"
    />

    <ShopModal v-if="detail" v-model:visible="visible" :detail="detail" />
  </div>
</template>
<script setup lang="ts">
  import {
    Table, Pagination,
  } from '@xhs/delight'

  import {
    useTableColumns,
  } from './listProps'
  import { useList } from './listHooks'

  import ShopModal from './ShopModal.vue'

  const props = defineProps<{
    sellerId: string
  }>()

  const {
    listSource, // 列表数据
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
  } = useList(props.sellerId)

  const { tableColumns, visible, detail } = useTableColumns({
    loading
  })
  fetchList()
</script>

<style lang="stylus" scoped>
.seller-poi-container
  width 100%
  padding 20px
  background rgba(14, 131, 248, 0.03);
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
