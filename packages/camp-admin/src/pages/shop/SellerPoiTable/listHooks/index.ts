import { ref } from 'vue'

import { PoiItem, fetchSellerClaimedPoi } from '@/services/poiClaim'

export const useList = (sellerId: string) => {
  const listSource = ref<PoiItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const filterParam = ref({ // 搜索参数
    pageSize: 10,
    pageNo: 1,
    total: 0,
    sellerId,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }

    if (!filterParam.value.sellerId) {
      listSource.value = []
      filterParam.value.total = 0
      loading.value = false
      return
    }

    const { total, poiList } = await fetchSellerClaimedPoi({
      ...filterParam.value,
      total: undefined
    })
    loading.value = false
    filterParam.value.total = total
    listSource.value = poiList
  }

  return {
    listSource,
    loading,
    filterParam,
    fetchList,
  }
}
