import { computed, ref, Ref } from 'vue'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'

import { fetchShopDetail, PoiDetail } from '@/services/shop'

export const useTableColumns = ({ loading }: {loading: Ref<boolean>}) => { // 列表的配置
  const visible = ref(false)

  const detail = ref<PoiDetail>()

  const showDetail = async (poiId?:string) => {
    if (!poiId) return
    if (detail.value?.poiId === poiId) {
      visible.value = true
      return
    }
    loading.value = true
    try {
      const res = await fetchShopDetail(poiId)
      visible.value = true
      detail.value = res
    } finally {
      loading.value = false
    }
  }

  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: 'POI ID',
      dataIndex: 'poiId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiId}</Text>),
    },
    {
      title: '门店名称',
      dataIndex: 'poiName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiName || '-'}</Text>),
    },
    {
      title: '省市区',
      dataIndex: 'provinceName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>
        {[rowData?.provinceName, rowData?.cityName, rowData?.districtName].filter(Boolean).join('') || '-'}
      </Text>),
    },
    {
      title: '门店地址',
      dataIndex: 'address',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>
        { rowData?.address || '-' }
      </Text>),
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: ({ rowData }) => (<Text link onClick={() => showDetail(rowData?.poiId)}>
        查看装修
      </Text>),
    },
  ]))
  return {
    tableColumns,
    visible,
    detail,
  }
}
