<template>
  <Modal
    v-model:visible="editVisible"
    title="门店装修"
    :size="425"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <div class="iphone-container">
      <!-- banner -->
      <img class="phone-header" src="https://picasso-static.xiaohongshu.com/fe-platform/59c9dd8a41db40fd329f7d4afc403c28193dce39.png" />
      <div class="banner">
        <img v-for="(item, i) in detail.storeBasedFeature?.images" :key="i" class="banner-img" :src="item.url" />
      </div>

      <!-- 标题 -->
      <div class="title-wrap">
        <Text type="h3" bold ellipsis style="width: 100%;">{{ detail.poiName }}</Text>
        <div class="tag-list">
          <div class="tag">人均¥{{ detail.storeBasedFeature?.avgPrice || 0 }}</div>
          <div v-if="(detail.storeBasedFeature?.openTime?.length || 0) < 40" class="tag">{{ detail.storeBasedFeature?.openTime }}</div>
        </div>
        <div v-if="(detail.storeBasedFeature?.openTime?.length || 0) >= 40" class="time-box">
          {{ detail.storeBasedFeature?.openTime }}
        </div>
      </div>

      <!-- 地址 -->
      <div class="address-container clip-line" @click="openMap">
        <div>
          <Text color="text-title" ellipsis style="width: 295px;">
            {{
              [detail.provinceName, detail.cityName, detail.districtName, detail.addressDetail].filter(Boolean).join('')
            }}
          </Text>
          <Text class="distinct" color="text-description">距您当前 xx km</Text>
        </div>
        <img src="https://picasso-static.xiaohongshu.com/fe-platform/5c4d56235e78216a671c019d6cc92a71afbcf629.png" />
      </div>

      <!-- 超值团购 -->
      <div class="gropu-title-wrap clip-line">
        <Text color="text-title">超值团购（0）</Text>
      </div>

      <!-- 推荐菜单 -->
      <div class="clip-line">
        <div class="gropu-title-wrap">
          <Text color="text-title">推荐菜（{{ detail.storeBasedFeature?.recommendCaters?.length || '0' }}）</Text>
          <img src="https://picasso-static.xiaohongshu.com/fe-platform/2c9a0e901d06b53b6683668328f139498af28aa5.png" />
        </div>

        <div class="menus">
          <div v-for="(item, i) in detail.storeBasedFeature?.recommendCaters" :key="i" class="menu">
            <img class="menu-img" :src="item.url" />
            <Text class="menu-title" ellipsis style="width: 100%" color="text-title">{{ item.name }}</Text>
          </div>
        </div>
      </div>

      <!-- 相关笔记 -->
      <div class="gropu-title-wrap">
        <Text color="text-title">相关笔记</Text>
      </div>
    </div>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed } from 'vue'
  import { Modal, Text, toast2 } from '@xhs/delight'

  import { PoiDetail } from '@/services/shop'

  const props = defineProps<{
    visible: boolean
    detail: PoiDetail
  }>()

  const emit = defineEmits(['update:visible'])

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const openMap = () => {
    const position = [props.detail.longitude, props.detail.latitude].filter(Boolean).join(',')
    if (!position) {
      toast2.info('改门店地址未录入')
    } else {
      window.open(`https://uri.amap.com/marker?position=${position}`)
    }
  }

</script>

<style lang="stylus" scoped>
.iphone-container
  width 375px
  overflow hidden
  background-color white
  position relative
  padding-bottom 20px
  border 1px solid #dbdbdb
  box-sizing content-box
.phone-header
  width 100%
  height 211px
  position absolute
  left 0
  top 0
  width 100%
  pointer-events none
.banner
  white-space nowrap
  overflow-x auto
.banner-img
  line-height 0
  width 375px
  height 211px
  object-fit cover
  object-position center
  &:not-last-child
    margin-right 5px
.title-wrap
  display flex
  flex-direction column
  align-items flex-start
  padding 16px 16px 8px
  gap 8px
  background-color #fff
.tag-list
  display flex
  align-items center
  gap 8px
  .tag
    display flex
    align-items center
    padding 2px 4px
    height 18px
    border 1px solid rgba(0, 0, 0, 0.1)
    border-radius 2px
    font-size 12px
    font-weight: 500
    color: rgba(0, 0, 0, 0.62)
.time-box
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 8px 12px;
  gap: 4px;
  background: #F5F5F5;
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;

.address-container
  display: flex;
  align-items: center;
  padding: 8px 16px 16px;
  gap: 16px;
  background-color #fff
  display flex
  align-items center
  .distinct
    font-size 14px
.clip-line
  border-bottom 10px solid #F5F5F5
.gropu-title-wrap
  width: 375px;
  height: 46px;
  display flex
  align-items center
  justify-content space-between
  padding 4px 16px 0
  background-color #fff
.menus
  overflow-x auto
  padding: 0 16px
  white-space nowrap
  .menu
    display inline-flex
    flex-direction column
    width 104px
    box-sizing content-box
    padding-right 8px
  .menu-img
    width 104px
    height 78px
    border-radius 3px
    object-fit cover
    object-position center
  .menu-title
    font-weight 400
    font-size 14px
    line-height 2

</style>
