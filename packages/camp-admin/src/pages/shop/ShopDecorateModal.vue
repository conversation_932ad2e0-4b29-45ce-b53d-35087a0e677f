<template>
  <Modal
    v-model:visible="editVisible"
    title="门店装修"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Form v-if="editVisible" label-width="73px">
      <FormItem label="模板下载">
        <Link
          target="_blank"
          href="https://fe-video-qc.xhscdn.com/fe-platform/bdcc66e1d8d8757a7234302aaf7ce213581bedc1.xlsx?attname=%E9%97%A8%E5%BA%97%E8%A3%85%E4%BF%AE%E6%A8%A1%E6%9D%BF.xlsx"
        >
          门店装修模板.xlsx
        </Link>
        <Text>（据模版，填写要装修的门店信息）</Text>
      </FormItem>
      <FormItem label="批量上传">
        <div v-if="!result" class="upload-container">
          <Spinner :spinning="loading">
            <FileUploader
              v-model="fileList"
              accept=".xlsx"
              :uploading="loading"
              :percent="percent"
              @update:modelValue="complete"
            />
          </Spinner>
          <div v-if="failed" class="message">
            <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
            <Text color="text-description">信息识别失败，请重新上传</Text>
          </div>
        </div>
        <UploadResult v-if="result" text="上传" :result="result" />
      </FormItem>
    </Form>
  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, ref, watch, onUnmounted
  } from 'vue'
  import {
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Link,
    toast,
    Spinner
  } from '@xhs/delight'
  import { ClaimResult } from '@/services/poiClaim'
  import UploadResult from '@/components/upload-result/index.vue'
  import FileUploader from '@/components/upload-file/index.vue'
  import { isFinished, computePercent } from '@/utils/longTask'
  import { LONG_TASK_STATUS, LONG_TASK_NAME } from '@/constants/longTask'
  import { postLongTask, getLongTask, GetLongTaskRes } from '@/services/longTaskv2'

  const props = defineProps<{
    visible: boolean
  }>()

  const emit = defineEmits(['update:visible'])

  const failed = ref(false)
  const loading = ref(false)

  const result = ref<ClaimResult | undefined>()
  let timer:number
  const fileList = ref<any[]>([])
  const percent = ref(0)

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
        window.clearTimeout(timer)
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
      window.clearTimeout(timer)
    }
  })

  onUnmounted(() => {
    window.clearTimeout(timer)
  })

  const complete = async () => {
    loading.value = true
    try {
      const { taskId } = await postLongTask({
        input: {
          file_url: fileList.value[0]?.downloadUrl,
          extra: undefined // extra 需要以 map 的形式进行传递
        },
        task_name: LONG_TASK_NAME.LIFE_POI_FIT_UP
      })
      // 心跳请求
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        percent.value = computePercent(res?.finishedCount, res?.totalCount) || percent.value
        if (isFinished(res?.status)) {
          loading.value = false
          if (res?.status === LONG_TASK_STATUS.FAIL) {
            failed.value = true
            toast.danger('上传失败，请重新上传')
            return
          }
          failed.value = false
          const { fileUrl = '', extraJson = '{}' } = res?.result
          const extra = JSON.parse(extraJson)
          result.value = {
            failReasonPath: fileUrl,
            successNum: extra?.success_count ?? 0,
            failNum: extra?.failed_count ?? 0
          }
          return
        }
        timer = window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error: any) {
      loading.value = false
      failed.value = true
      toast.danger(error?.message || '服务器异常，请重新操作')
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
</style>
