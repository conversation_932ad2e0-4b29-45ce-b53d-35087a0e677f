<template>
  <PageLayout :nav-list="navList">
    <Space
      class="shop-container"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
      />

      <div class="list-content">
        <Toolbar :config="toolBarConfig" />
        <div style="margin: -16px -16px 0; min-height: 400px">
          <CollapseGroup accordion>
            <Collapse v-for="(item, i) in listSource" :key="item.sellerId" :collapse="i !== 0">
              <template #title>
                <div style="display: flex; align-items: center;">
                  <Text type="h6">{{ item.publicName }}</Text>
                  <Text color="text-description">（{{ item.sellerId }}）</Text>
                </div>
              </template>

              <SellerPoiTable :seller-id="item.sellerId" />
            </Collapse>
          </CollapseGroup>
        </div>
      </div>
    </Space>

    <ShopDecorateModal v-model:visible="editVisible" />
  </PageLayout>
</template>
<script setup lang="ts">
  import {
    Space, CollapseGroup, Collapse, Text
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import PageLayout from '@/components/page-layout/index.vue'

  import {
    spaceProps, useToolBar, useFilter, navList,
  } from './listProps'
  import { useList } from './listHooks'

  import SellerPoiTable from './SellerPoiTable/index.vue'
  import ShopDecorateModal from './ShopDecorateModal.vue'

  const {
    listSource, // 列表数据
    filterParam, // 查询条件
    editVisible, // 编辑弹窗展示标识
    fetchList, // 获取列表方法
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { toolBarConfig } = useToolBar({
    editVisible
  })

  if (process.env.NODE_ENV === 'development') {
    fetchList()
  }
</script>

<style lang="stylus" scoped>
.shop-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 0 24px 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
  &:deep(.d-collapse-content)
    display block
</style>
