import { computed, Ref } from 'vue'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: '门店装修',
  },
  {
    label: '商家列表',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: fetchList,
    filterItems: [
      {
        name: 'sellerId',
        label: '商家ID',
        component: {
          props: {
            placeholder: '请输入商家ID',
          },
        },
      },
      {
        name: 'sellerName',
        label: '商家名称',
        component: {
          props: {
            placeholder: '请输入商家名称',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = (
  props: {
    editVisible: Ref<boolean>
  }
) => { // 工具栏的配置
  const { editVisible } = props
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '批量装修',
        onClick: () => {
          editVisible.value = true
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}
