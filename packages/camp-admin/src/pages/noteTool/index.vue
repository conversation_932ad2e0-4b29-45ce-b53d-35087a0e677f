<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
      <OutlineFilter
        v-model="searchParam"
        :config="filterConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Toolbar
          :config="toolBarConfig"
        />
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="searchParam.pageNum"
          v-model:pageSize="searchParam.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
  </Space>
  <Modal
    v-model:visible="editVisible"
    :title="`笔记${lookItemId}绑定信息操作记录 `"
    size="large"
    :with-cancel="false"
    @confirm="editVisible = false"
  >
    <Table
      :data-source="lookItem"
      :columns="historyTableColumns"
      :loading="historyLoading"
    />
  </Modal>
  <ImportNoteModal v-model:visible="importVisible" :is-del="isDel" />
</template>
<script setup lang="tsx">
  import {
    Space, Table, Pagination, Modal, Text
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import dayjs from 'dayjs'
  import {
    spaceProps, pageHeaderProps, useToolBar, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'
  import ImportNoteModal from './ImportNoteModal.vue'

  const {
    listSource, // 列表数据
    total,
    loading, // 列表加载标识
    historyLoading,
    searchParam, // 查询条件
    editVisible,
    lookItem,
    lookItemId,
    fetchList, // 获取列表方法
    handleDelete, // 删除某列的方法
    // handleEdit, // 处理编辑方法
    handleExport, // 导出表格
    handelLookHistory, // 查看操作记录
    toNoteDetail
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { toolBarConfig, importVisible, isDel } = useToolBar(handleExport)
  const { tableColumns } = useTableColumns({
    handleDelete,
    handelLookHistory,
    toNoteDetail
  })

  const historyTableColumns = [
    {
      title: '操作人',
      dataIndex: 'createdBy',
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      render: ({ rowData }:any) => <Text>{dayjs(rowData?.createTime).locale('zh-cn').format('YYYY-MM-DD HH:mm:ss')}</Text>,
      minWidth: 240
    },
    {
      title: '操作类型',
      dataIndex: 'eventType',
      render: ({ rowData }:any) => <Text>{rowData.eventType === 'ADD' ? '新增' : '解绑'}</Text>
    },
    {
      title: '商品ID',
      dataIndex: 'contentId',
      minWidth: 240
    },
  ]

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
.historyItem
  margin-top 20px
</style>
