import { ref, computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import dayjs from 'dayjs'

export const spaceProps:any = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
}

export const pageHeaderProps:PageHeaderProps = {
  title: '笔记刷数',
}

export const useFilter = (fetchList: () => Promise<any>) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    expandedCount: 3,
    filterItems: [
      {
        name: 'noteId',
        label: '笔记ID',
        component: {
          props: {
            placeholder: '请输入笔记ID',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = (handleExport: () => void) => { // 工具栏的配置
  const importVisible = ref<boolean>(false)
  const isDel = ref<boolean>(false)
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '笔记刷数',
        onClick: () => {
          importVisible.value = true
          isDel.value = false
        },
      },
      {
        text: '批量删除',
        onClick: () => {
          importVisible.value = true
          isDel.value = true
        },
      },
      {
        text: '导出',
        onClick: () => {
          handleExport()
        },
      },
    ]
  }))
  return {
    toolBarConfig,
    importVisible,
    isDel
  }
}

export const useTableColumns = ({
  handleDelete,
  handelLookHistory,
  toNoteDetail
}: {
  handleDelete: (item:any) => void
  handelLookHistory: (item:any) => void
  toNoteDetail: (item:any) => () => void
}) => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => [{
    title: '笔记ID',
    dataIndex: 'noteId',
    render: ({ rowData }) => <Text style={{ cursor: 'pointer' }} onClick={toNoteDetail(rowData?.noteId)}>{rowData?.noteId}</Text>,
    minWidth: 240,
  },
  {
    title: '商品ID',
    minWidth: 240,
    dataIndex: 'contentId',
  },
  {
    title: '行业',
    dataIndex: 'businessType',
    width: 100
  },
  {
    title: '刷数理由',
    dataIndex: 'reason',
    width: 200,
    render: ({ rowData }) => <Text ellipsis tooltip>{rowData?.reason}</Text>,
  },
  {
    title: '更新人',
    dataIndex: 'createdBy',
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'createTime',
    minWidth: 200,
    render: ({ rowData }) => <Text>{dayjs(rowData?.createTime).locale('zh-cn').format('YYYY-MM-DD HH:mm:ss')}</Text>,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    fixed: 'right',
    minWidth: 135,
    render: ({ rowData }) => <TableCell
      type="action"
      action-props={{
        actionOptions: [{
          visible: rowData?.eventType !== 'DELETE',
          text: '删除',
          popConfirmProps: {
            outsideCloseable: true,
            placement: 'top-end',
            title: '确认删除',
            description: '确定要为该笔记解除挂链吗？',
            width: 250,
            onConfirm: () => handleDelete(rowData),
          },
        }, {
          text: '操作记录',
          onClick: () => handelLookHistory(rowData),
        }],
      }} />,
  }])
  return {
    tableColumns,
  }
}
