import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'
import {
  getNoteList, NoteList, NoteItem, HistoryItem, exportNote, lookHistory, noteUnbind
} from '@/services/noteTool'

export const useList = () => {
  const listSource = ref<NoteList>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const historyLoading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑抽屉展示标识
  const lookItem = ref<HistoryItem[]>([]) // 查看的Item
  const lookItemId = ref('')
  const total = ref(0)
  const searchParam = ref({ // 搜索参数
    pageSize: 50,
    pageNum: 1,
    noteId: '',
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    listSource.value = []
    total.value = 0
    loading.value = true
    if (isResetPageNum) {
      searchParam.value.pageNum = 1
    }
    try {
      const res = await getNoteList(searchParam.value)
      total.value = res.total
      listSource.value = res.brushNotePageList
    } finally {
      loading.value = false
    }
  }

  const handleDelete = async (deleteItem: NoteItem) => { // 删除方法
    await noteUnbind({ noteId: deleteItem.noteId })
    toast.success(`${deleteItem.noteId}删除成功!`)
    fetchList(false)
  }

  const handleExport = async () => { // 导出表格
    const res = await exportNote()
    window.open(res?.url)
  }

  const handelLookHistory = async (item: any) => { // 查看操作记录
    editVisible.value = true
    historyLoading.value = true
    try {
      const res = await lookHistory({ noteId: item?.noteId })
      lookItemId.value = item?.noteId
      lookItem.value = res
    } finally {
      historyLoading.value = false
    }
  }

  const toNoteDetail = (itemId: string) => () => {
    window.open(`//www.xiaohongshu.com/explore/${itemId}`, '_blank')
  }
  return {
    listSource,
    total,
    loading,
    historyLoading,
    searchParam,
    editVisible,
    lookItem,
    lookItemId,
    fetchList,
    handleDelete,
    // handleEdit,
    handleExport,
    handelLookHistory,
    toNoteDetail
  }
}
