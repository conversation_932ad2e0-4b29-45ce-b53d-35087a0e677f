<template>
  <Modal
    v-model:visible="editVisible"
    :title="isDel ? '批量删除' : '笔记刷数'"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Form v-if="editVisible" label-width="73px">
      <FormItem label="模板下载">
        <Link
          target="_blank"
          :href="isDel ? unbindModel : noteModel"
        >
          {{ isDel ? '解绑笔记' : '笔记刷数' }}信息模板.xlsx
        </Link>
        <!-- <Text>（据模版，填写要刷的区域资质信息模板）</Text> -->
      </FormItem>
      <FormItem label="批量上传">
        <div v-if="!result" class="upload-container">
          <Button
            style="position: relative"
            :icon="Upload"
            :loading="loading"
            @click="upload"
          >
            {{ failed || result ? '重新' : '' }}上传 Excel 文件
          </Button>
          <div v-if="failed" class="message">
            <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
            <Text color="text-description">信息识别失败，请重新上传</Text>
          </div>
        </div>
        <UploadResult v-if="result" text="上传" :result="result" />
      </FormItem>
    </Form>

  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, ref, watch } from 'vue'
  import {
    Button,
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Link,
  } from '@xhs/delight'
  import { Upload } from '@xhs/delight/icons'
  import { getFiles } from '@/utils/getFiles'
  import { ClaimResult } from '@/services/poiClaim'
  import { importNoteInfo, notesUnbind } from '@/services/noteTool'
  import UploadResult from '@/components/upload-result/index.vue'

  const props = defineProps<{
    visible: boolean
    isDel: boolean
  }>()

  const noteModel = 'https://fe-video-qc.xhscdn.com/fe-platform/f8cc567b861fae0069c0f08e1571cc7c12ae9007/刷数笔记模板.xlsx'
  const unbindModel = 'https://fe-video-qc.xhscdn.com/fe-platform/055e1d112fd4a0c586dce166798736e5d7430783/解绑笔记模板.xlsx'

  const emit = defineEmits(['update:visible'])

  const failed = ref(false)
  const loading = ref(false)

  const result = ref<ClaimResult | undefined>() // todo

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const upload = async () => {
    const [file] = await getFiles()
    if (!file) return
    loading.value = true

    const f = new FormData()
    f.append('file', file)
    try {
      const res = props.isDel ? await notesUnbind(f) : await importNoteInfo(f)
      result.value = res
    } catch (e) {
      loading.value = false
      failed.value = true
    } finally {
      loading.value = false
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
</style>
