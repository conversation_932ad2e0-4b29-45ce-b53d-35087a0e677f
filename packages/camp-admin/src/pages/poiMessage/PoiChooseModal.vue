<template>
  <Modal
    v-model:visible="editVisible"
    title="批量上传"
    confirm-text="确认上传"
    :size="580"
    :confirm-button-props="{
      disabled: disabled,
      loading: submitLoading
    }"
    @cancel="editVisible = false"
    @confirm="submit"
  >
    <Form v-if="editVisible" label-position="top">
      <FormItem label="第一步，选择POI圈选规则" required>
        <Text class="desc">上传成功所有POI适用选取的规则</Text>
        <Select v-model="selectValue" placeholder="请选择" :options="poiRulesOptions" />
      </FormItem>
      <FormItem label="第二步，下载Excel模板">
        <Text class="desc">请根据模板要求填写信息</Text>
        <Button style="width: 120px" type="primary" :icon="Download" @click="download">下载</Button>
      </FormItem>
      <FormItem label="第三步，上传Excel文件" required>
        <Text class="desc">请选择xlsx格式的文件上传</Text>
        <Button :disabled="!!fileTmp" style="width: 120px" type="primary" :icon="Upload" :loading="uploading" @click="upload">上传</Button>
        <div style="margin-top: 20px;">
          <Text v-show="fileTmp?.name" style="margin-right: 50px;">已上传 <span style="color: var(--color-primary);">{{ fileTmp?.name }}</span></Text>
          <Text v-show="fileTmp" class="clear" @click="fileTmp = null">删除</Text>
        </div>
      </FormItem>
    </Form>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, ref, watch } from 'vue'
  import {
    Button,
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Select,
    toast
  } from '@xhs/delight'
  import { Upload, Download } from '@xhs/delight/icons'
  import { getFiles } from '@/utils/getFiles'
  import { uploadPoiChoose } from '@/services/poiMessage'
  import { poiRulesOptions, excelModel } from './constant'

  const props = defineProps<{
    visible: boolean
  }>()

  const selectValue = ref('')

  const download = () => {
    window.open(excelModel)
  }

  const emit = defineEmits(['update:visible'])

  const uploading = ref(false) // 上传按钮loading
  const submitLoading = ref(false) // 弹窗确认按钮loading
  const fileTmp = ref() // 上传好的文件

  const disabled = computed(() => {
    if (selectValue.value && fileTmp.value) return false
    return true
  })

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        submitLoading.value = false
        uploading.value = false
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const upload = async () => {
    uploading.value = true
    try {
      const [file] = await getFiles()
      fileTmp.value = file
    } finally {
      uploading.value = false
    }
  }

  const submit = async () => {
    submitLoading.value = true
    const f = new FormData()
    f.append('file', fileTmp.value)
    f.append('poiRule', selectValue.value)
    try {
      const res = await uploadPoiChoose(f)
      fileTmp.value = null
      selectValue.value = ''
      editVisible.value = false
      toast.success(`已上传${res.total}条数据，成功添加${res.successNum}个POI`)
    } finally {
      submitLoading.value = false
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
:deep(.d-form-item__content)
  margin-top 10px
.clear
  color: var(--color-primary);
  transition: color 0.1s;
  cursor: pointer;
  font-weight: var(--size-text-font-weight-bold);
  &:hover
    color: var(--color-primary-hover);
:deep(.d-form-item__wrapper)
  flex-direction column
  align-items flex-start !important
:deep(.d-form-item__content)
  margin-top 0
  padding-left 28px
.desc
  margin-bottom 10px
</style>
