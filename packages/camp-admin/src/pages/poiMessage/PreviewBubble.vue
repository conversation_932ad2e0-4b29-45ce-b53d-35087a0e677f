<template>
  <Tooltip theme="light" style="padding: 20px; box-sizing: content-box;">
    <Text type="paragraph" style="color: var(--color-primary); font-weight: var(--size-text-font-weight-bold); cursor: pointer;">预览</Text>
    <template #content>
      <div class="preview-modal">
        <Text type="h5" style="font-weight: 500;">请使用小红书APP扫码预览</Text>
        <DelightArkQrcode :link="`xhsdiscover://rn/poi/detail?poiId=${previewId}&geolocation=0`" :size="128" />
      </div>
    </template>
  </Tooltip>
</template>

<script setup lang="ts">
  import { Tooltip, Text } from '@xhs/delight'
  import DelightArkQrcode from '@xhs/delight-ark-qrcode'

  defineProps<{
    previewId: string
  }>()
</script>

<style scoped lang="stylus">
.preview-modal
  display flex
  flex-direction column
  justify-content center
  align-items center
  gap 20px
</style>
