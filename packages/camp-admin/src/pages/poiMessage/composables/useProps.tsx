import { Ref, computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
// import CityCascader from '@/components/city-cascader/Cascader2-Delight.vue'
import CityCascader from '@/components/city-cascader/Cascader1-Delight.vue'
import { poiRulesOptions } from '../constant'
import PreviewBubble from '../PreviewBubble.vue'

export const spaceProps:any = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
}

export const pageHeaderProps:PageHeaderProps = {
  title: 'POI圈选',
}

export const useFilter = (
  fetchList: () => Promise<any>,
  selectChange: (ids: string[], names: string[]) => void,
  searchParam: Ref<any>
) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'poiIds',
        label: 'POIID',
        component: {
          props: {
            placeholder: '请输入',
            clearable: true
          },
        },
      },
      {
        name: 'poiName',
        label: 'POI名称',
        component: {
          props: {
            placeholder: '请输入',
            clearable: true
          },
        },
      },
      {
        name: 'cascader',
        label: '省市区',
        component: {
          is: CityCascader,
          props: {
            modelValue: searchParam.value.cascader,
            onChange: selectChange,
            clearable: true
          },
        },
      },
      {
        name: 'poiRule',
        label: '圈选规则',
        component: {
          is: 'Select',
          props: {
            clearable: true,
            options: [
              {
                value: '',
                label: '全部'
              },
              ...poiRulesOptions,
            ],
            placeholder: '请选择'
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = (
  download: () => void,
  visible: Ref<boolean>
) => { // 工具栏的配置
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '批量上传',
        onClick: () => {
          visible.value = true
        },
      },
      {
        text: '数据下载',
        onClick: () => download(),
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = ({
  handleDelete,
}: {
  handleDelete: (item:any) => void
}) => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => [
    {
      title: 'POIID',
      dataIndex: 'poiId',
      render: ({ rowData }) => <Text>{ rowData?.poiId || '-' }</Text>,
    },
    {
      title: 'POI名称',
      dataIndex: 'poiName',
      render: ({ rowData }) => <Text>{ rowData?.poiName || '-' }</Text>,
    },
    {
      title: '省市区',
      dataIndex: 'addressName',
      width: 150,
      render: ({ rowData }) => <Text ellipsis tooltip>{ rowData?.addressName || '-' }</Text>,
    },
    {
      title: 'POI地址',
      dataIndex: 'addressDetail',
      width: 150,
      render: ({ rowData }) => <Text ellipsis tooltip>{ rowData?.addressDetail || '-' }</Text>,
    },
    {
      title: 'POI类目',
      dataIndex: 'poiType',
      render: ({ rowData }) => <Text>{ rowData?.poiType || '-' }</Text>,
    },
    {
      title: '风险等级',
      dataIndex: 'poiRiskLevel',
      width: 80,
      render: ({ rowData }) => <Text>{ rowData?.poiRiskLevel || '-' }</Text>,
    },
    {
      title: '圈选规则',
      dataIndex: 'poiRule',
      width: 80,
      render: ({ rowData }) => <Text>{ rowData?.poiRule || '-' }</Text>,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      align: 'center',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => (
        <>
          <PreviewBubble previewId={rowData?.poiId}/>
          <TableCell
            type="action"
            action-props={{
              actionOptions: [
                {
                  text: '删除',
                  onClick: () => handleDelete(rowData),
                }],
            }} />
        </>
      ),
    }])
  return {
    tableColumns,
  }
}
