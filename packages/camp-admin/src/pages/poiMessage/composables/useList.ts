import { ref, h } from 'vue'
import { toast2 as toast, Modal, Text } from '@xhs/delight'
import {
  getPoiMessageList, deletePoiMessageItem, downloadPoiMessageList, transformParams
} from '@/services/poiMessage'
import type { GetPoiMessageListParamsTemp, poiMessageListItem } from '@/services/poiMessage'

export const useList = () => {
  const listSource = ref<poiMessageListItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const downloading = ref(false) // 数据下载弹窗的loading
  const visible = ref(false) // 批量上传弹窗展示标识
  const total = ref(0)
  const searchParam = ref<GetPoiMessageListParamsTemp>({ // 搜索参数
    pageSize: 20,
    pageNum: 1,
    cascader: ''
    // cascader: []
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取列表的方法
    loading.value = true
    if (isResetPageNum) {
      searchParam.value.pageNum = 1
    }
    try {
      const res = await getPoiMessageList(transformParams(searchParam.value))
      listSource.value = res?.queryPoiMessageVOList || []
      total.value = res?.total || 0
    } finally {
      loading.value = false
    }
  }

  const handleDelete = (deleteItem: poiMessageListItem) => { // 删除方法
    Modal.danger({
      title: '确认删除',
      content: h(Text, {}, {
        default: () => `请确认是否要删除 ${deleteItem.poiName}`
      }),
      confirmButtonProps: {
        loading: downloading.value,
      },
      async onConfirm(close) {
        downloading.value = true
        try {
          await deletePoiMessageItem({ id: deleteItem.id })
          toast.success('删除成功！')
          fetchList(false)
        } finally {
          downloading.value = false
          close?.()
        }
      },
      onCancel(close) {
        close?.()
      }
    })
  }

  // toolbar-数据下载
  const download = () => {
    Modal.info({
      title: '数据下载',
      content: h(Text, {}, {
        default: () => '最多可导出20000行数据，确定下载吗？'
      }),
      confirmButtonProps: {
        loading: downloading.value,
      },
      async onConfirm(close) {
        downloading.value = true
        try {
          const res = await downloadPoiMessageList(searchParam.value)
          window.open(res.url)
          toast.success('下载成功！')
        } finally {
          downloading.value = false
          close?.()
        }
      },
      onCancel(close) {
        close?.()
      }
    })
  }

  // 监听省市区选择框变化的事件
  const selectChange = (names: string[]) => {
    searchParam.value.provinceName = names[0]
    searchParam.value.cityName = names[1]
    searchParam.value.districtName = names[2]
  }

  return {
    listSource,
    total,
    loading,
    searchParam,
    visible,
    fetchList,
    handleDelete,
    download,
    selectChange,
  }
}
