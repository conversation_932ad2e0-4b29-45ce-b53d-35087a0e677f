<template>
  <PageLayout :nav-list="navList">
    <Space
      class="list-container"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
      />

      <div class="list-content">
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="filterParam.pageNo"
          v-model:page-size="filterParam.pageSize"
          :total="total"
          style="margin-top: 24px"
          @change="fetchList(false)"
        />
      </div>
    </Space>
  </PageLayout>

  <EditModal
    v-model:visible="editVisible"
    v-model="partnerConfig"
    :loading="loading"
    @ok="handleEdit"
  />
</template>
<script setup lang="ts">
  import {
    Table, Pagination, Space,
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import PageLayout from '@/components/page-layout/index.vue'

  import {
    spaceProps, navList, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  import EditModal from './components/EditModal.vue'

  const {
    listSource, // 列表数据
    total, // 数据总数
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
    handleModal, // 处理弹窗
    editVisible,
    partnerConfig,
    handleEdit
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { tableColumns } = useTableColumns({
    handleModal,
  })

  fetchList()
</script>

<style lang="stylus" scoped>
.list-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
