import { computed } from 'vue'
import { Text, Tag } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

import PartnerTypeTag from '../components/PartnerTypeTag.vue'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: '技术管理',
  },
  {
    label: '服务商列表',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'partnerAppId',
        label: '服务商AppId',
        component: {
          props: {
            placeholder: '请输入服务商AppId',
          },
        },
      },
      {
        name: 'partnerName',
        label: '服务商名称',
        component: {
          props: {
            placeholder: '请输入服务商名称',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useTableColumns = () => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '服务商名称',
      dataIndex: 'partnerName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.partnerName || '-'}</Text>),
    },
    {
      title: '服务商主体名称',
      dataIndex: 'partnerCompanyName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.partnerCompanyName || '-'}</Text>),
    },
    {
      title: '服务商AppId',
      dataIndex: 'appId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.appId || '-'}</Text>),
    },
    {
      title: '曾经收款到服务商',
      dataIndex: 'partnerRoleType',
      render: ({ rowData }) => <PartnerTypeTag type={rowData?.partnerRoleType} />
    },
    {
      title: '服务商sellerId',
      dataIndex: 'partnerSellerId',
      render: ({ rowData }) => (rowData?.partnerSellerId
        ? <Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.partnerSellerId}</Text>
        : <Tag>未配置</Tag>),
    }]))
  return {
    tableColumns,
  }
}
