import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'

import {
  Partner, fetchPartner, PartnerParams, PartnerConfig, configPartner
} from '@/services/partner'

export const useList = () => {
  const listSource = ref<Partner[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const partnerConfig = ref<Partial<PartnerConfig>>() // 编辑的Item
  const total = ref(0)
  const filterParam = ref<PartnerParams>({ // 搜索参数
    pageSize: 10,
    pageNo: 1,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }
    loading.value = false

    const res = await fetchPartner(filterParam.value)

    total.value = res.total
    listSource.value = res.partnerList
  }

  const handleDelete = (deleteItem:any) => { // 删除方法
    toast.success({ // 规范Toast交互，要求带背景色
      content: `${deleteItem?.good}删除成功`,
      strong: true,
    })
    fetchList()
  }
  const handleModal = (item:Partner) => { // 处理编辑弹窗
    editVisible.value = true
    partnerConfig.value = {
      partnerAppId: item.appId,
      partnerSellerId: item.partnerSellerId,
      partnerRoleType: item.partnerRoleType,
    }
  }
  const handleEdit = async () => { // 处理编辑
    loading.value = true
    try {
      await configPartner(partnerConfig.value as PartnerConfig)
      await fetchList(false)
      editVisible.value = false
    } finally {
      loading.value = false
    }
  }

  return {
    listSource,
    total,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleDelete,
    handleEdit,
    handleModal,
    partnerConfig,
  }
}
