<template>
  <Tag :color="color">{{ text }}</Tag>
</template>

<script lang="tsx" setup>
  import { computed } from 'vue'
  import { Tag } from '@xhs/delight'
  import { TagColor } from '@xhs/delight/types/enums'

  import { PartnerRoleType } from '@/services/partner'
  import { partnerTypes } from '@/constants/partner'

  const props = defineProps<{
    type?: PartnerRoleType
  }>()

  const color = computed<TagColor | undefined>(() => {
    if (!props.type) return undefined
    const colorMap = {
      1: 'purple',
      2: 'teal'
    } as const
    return colorMap[props.type]
  })

  const text = computed(() => {
    if (!props.type) return '未配置'

    return partnerTypes.find(t => t.value === props.type)?.label || props.type
  })

</script>

<style>
</style>
