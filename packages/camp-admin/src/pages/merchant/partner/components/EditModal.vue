<template>
  <Modal
    v-model:visible="modalVisible"
    title="配置服务商身份"
    size="550px"
    :loading="loading"
    @confirm="handleOk"
    @cancel="modalVisible = false"
  >
    <Form label-width="150px">
      <FormItem label="是否收款到服务商" name="partnerRoleType">
        <RadioGroup
          v-model="model.partnerRoleType"
          :options="partnerTypes"
        />
      </FormItem>
      <FormItem label="收款账户sellerId" name="partnerSellerId">
        <SellerIdInput
          v-model="model.partnerSellerId"
          v-model:seller-name="sellerName"
          :disabled="model.partnerRoleType === 1"
          placeholder="收款账户sellerId"
        />
      </FormItem>
    </Form>
  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, reactive, ref, watch
  } from 'vue'
  import {
    Modal, Form2 as Form, FormItem2 as FormItem, RadioGroup, toast2 as toast
  } from '@xhs/delight'

  import { partnerTypes } from '@/constants/partner'
  import { PartnerConfig } from '@/services/partner'

  import SellerIdInput from '@/components/sellect-id-input/index.vue'

  const props = defineProps<{
    visible: boolean
    loading: boolean
    modelValue?: Partial<PartnerConfig>
  }>()

  const emit = defineEmits(['update:visible', 'update:modelValue', 'ok'])

  const model = reactive<Partial<PartnerConfig>>({ ...props.modelValue })

  const sellerName = ref('')

  watch(
    () => props.visible,
    () => {
      if (props.visible) {
        Object.assign(model, props.modelValue)
      }
    }
  )

  watch(
    () => model.partnerRoleType,
    (newType, oldType) => {
      if (oldType === 2 && newType === 1) {
        model.partnerSellerId = ''
      }
    }
  )

  const modalVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const handleOk = () => {
    if (!partnerTypes.some(t => t.value === model.partnerRoleType)) {
      toast.info('请选择是否是代运营服务商')
      return
    }

    if (model.partnerRoleType !== 1 && !sellerName.value) {
      toast.info('请输入正确代运营服务商sellerId')
      return
    }

    emit('update:modelValue', model)
    emit('ok', model)
  }

</script>
