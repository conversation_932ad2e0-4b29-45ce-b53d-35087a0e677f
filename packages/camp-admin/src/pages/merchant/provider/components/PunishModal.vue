<template>
  <Modal
    v-model:visible="computedVisible"
    title="服务商平台处罚"
    :confirm-button-props="{
      loading: loading
    }"
    @confirm="submit"
    @cancel="computedVisible = false"
  >
    <Form class="punish-modal-form">
      <FormItem label="处罚方式" required>
        <CheckboxGroup
          v-model="value"
          :options="options"
          direction="vertical"
        />
      </FormItem>
    </Form>
  </Modal>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import {
    Modal, FormItem2 as FormItem, CheckboxGroup, Form2 as Form, toast2 as toast
  } from '@xhs/delight'
  import { PunishmentPayload, punishment } from '@/services/provinder'
  import { tryCatch } from 'shared/utils'

  const props = defineProps<{
    visible: boolean
    punishItem: PunishmentPayload
  }>()

  const emit = defineEmits(['update:visible', 'finish'])

  const value = ref<string[]>([
    (props.punishItem.officialHidden ? 'officialHidden' : ''),
    (props.punishItem.banNewSeller ? 'banNewSeller' : '')
  ].filter(Boolean))
  const loading = ref(false)

  const computedVisible = computed({
    get: () => props.visible,
    set: newVal => emit('update:visible', newVal)
  })

  const options = [
    {
      label: '不进入官方服务商列表',
      description: '勾选则代表该服务商无法在代运营服务商平台首页被查询到，取消勾选服务商可恢复此权益',
      value: 'officialHidden'
    },
    {
      label: '禁止新增商家',
      description: '勾选则代表该服务商无法再新增合作商家，取消勾选服务商可恢复此权益',
      value: 'banNewSeller'
    }
  ]

  const submit = async () => {
    const payload: PunishmentPayload = {
      providerId: props.punishItem.providerId,
      officialHidden: value.value.includes('officialHidden'),
      banNewSeller: value.value.includes('banNewSeller')
    }
    loading.value = true
    const [, err] = await tryCatch(() => punishment(payload))
    loading.value = false
    if (err) return
    computedVisible.value = false
    emit('finish')
    toast.success({
      content: '操作成功！',
      strong: true
    })
  }
</script>

<style scoped lang="stylus">
.punish-modal-form
  :deep(.d-form-item__content)
    padding 6px 0
</style>
