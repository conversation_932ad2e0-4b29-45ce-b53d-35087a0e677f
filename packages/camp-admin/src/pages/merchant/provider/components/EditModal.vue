<template>
  <Modal
    v-model:visible="modalVisible"
    title="配置代运营服务商身份"
    size="550px"
    :loading="loading"
    @confirm="handleOk"
    @cancel="modalVisible = false"
  >
    <Form
      ref="formRef"
      label-width="150px"
      :model="model"
      :rules="rules"
    >
      <FormItem label="主账号uid" name="providerUid" required description="主账号为服务商管理员，可首次登录代运营服务商平台，需要认证企业身份专业号">
        <Input
          v-model="model.providerUid"
          :disabled="!!modelValue?.providerId"
          placeholder="请输入"
          @blur="queryUser"
        />
      </FormItem>
      <FormItem label="服务商主体名称" name="spCompanyName" required>
        <Input
          v-model="model.spCompanyName"
          placeholder="请输入"
          disabled
          :loading="searchLoading"
        />
      </FormItem>
      <FormItem label="服务商名称" name="providerName" required>
        <Input
          v-model="model.providerName"
          placeholder="请输入"
          :max-length="15"
          :loading="searchLoading"
        />
      </FormItem>
    </Form>
  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, reactive, ref, watch
  } from 'vue'
  import {
    Modal, Input, Form2 as Form, FormItem2 as FormItem, FormRule
  } from '@xhs/delight'

  import { ProviderPayload, fetchUser } from '@/services/provinder'

  const props = defineProps<{
    visible: boolean
    loading: boolean
    modelValue?: Partial<ProviderPayload>
  }>()

  const emit = defineEmits(['update:visible', 'update:modelValue', 'ok'])

  const model = reactive<Partial<ProviderPayload>>({ ...props.modelValue })

  const searchLoading = ref(false)
  const formRef = ref()

  watch(
    () => props.visible,
    () => {
      if (props.visible) {
        Object.assign(
          model,
          {
            providerId: '', // 服务商id（修改时必填）
            providerName: '', // 服务商名称
            spCompanyName: '', // 服务商公司名称
            providerUid: '', // 服务主账号uid（小红书uid）
          },
          props.modelValue
        )
      }
    }
  )

  const modalVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const rules:FormRule = {
    providerUid: {
      required: true,
      message: '请输入',
    },
    providerName: {
      required: true,
      message: '请输入',
    },
    spCompanyName: {
      required: true,
      message: '请输入',
    },
  }

  const handleOk = async () => {
    await formRef.value?.validate()
    emit('update:modelValue', model)
    emit('ok', model)
  }

  const queryUser = async () => {
    if (model.providerUid) {
      try {
        model.spCompanyName = ''
        model.providerName = ''
        searchLoading.value = true
        const res = await fetchUser(model.providerUid)
        if (res) {
          model.spCompanyName = res?.companyName
          model.providerName = res?.nickname
        }
      } finally {
        searchLoading.value = false
      }
    }
  }
</script>
