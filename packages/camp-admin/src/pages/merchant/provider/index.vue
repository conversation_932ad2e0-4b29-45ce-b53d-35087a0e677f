<template>
  <PageLayout :nav-list="navList">
    <Space
      class="list-container"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
      />

      <div class="list-content">
        <Toolbar :config="toolBarConfig" />

        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="filterParam.pageNo"
          v-model:page-size="filterParam.pageSize"
          :total="total"
          style="margin-top: 24px"
          @change="fetchList(false)"
        />
      </div>
    </Space>
  </PageLayout>

  <EditModal
    v-model:visible="editVisible"
    v-model="providerPayload"
    :loading="modalLoading"
    @ok="handleEdit"
  />
  <PunishModal v-if="punishModalVisible" v-model:visible="punishModalVisible" :punish-item="punishItem" @finish="fetchList" />
</template>
<script setup lang="ts">
  import { computed } from 'vue'
  import {
    Table, Pagination, Space
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'

  import PageLayout from '@/components/page-layout/index.vue'

  import {
    spaceProps, navList, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  import EditModal from './components/EditModal.vue'
  import PunishModal from './components/PunishModal.vue'

  const {
    listSource, // 列表数据
    total, // 数据总数
    loading, // 列表加载标识
    modalLoading,
    filterParam, // 查询条件
    punishItem,
    fetchList, // 获取列表方法
    handleModal, // 处理弹窗
    handlePunishModal,
    editVisible,
    punishModalVisible,
    providerPayload,
    handleEdit
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { tableColumns } = useTableColumns({
    handleModal,
    handlePunishModal
  })

  fetchList()

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '代运营服务商配置',
        onClick: () => {
          handleModal({})
        },
      },
    ],
  }))
</script>

<style lang="stylus" scoped>
.list-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 0 24px 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
