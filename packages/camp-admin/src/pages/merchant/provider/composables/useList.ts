import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'

import {
  Provider, fetchProvider, ProviderParams, ProviderPayload, updateProvider, PunishmentPayload
} from '@/services/provinder'

export const useList = () => {
  const listSource = ref<Provider[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const modalLoading = ref(false)
  const editVisible = ref(false) // 编辑弹窗展示标识
  const punishModalVisible = ref(false) // 平台处罚弹窗展示标识

  const providerPayload = ref<Partial<ProviderPayload>>() // 编辑的Item
  const punishItem = ref<PunishmentPayload>({
    providerId: '',
    officialHidden: false,
    banNewSeller: false
  })
  const total = ref(0)
  const filterParam = ref<ProviderParams>({ // 搜索参数
    pageSize: 10,
    pageNo: 1,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }
    const res = await fetchProvider(filterParam.value)
    loading.value = false
    total.value = res?.total || 0
    listSource.value = res?.providerList || []
  }

  const handleDelete = (deleteItem:any) => { // 删除方法
    toast.success({ // 规范Toast交互，要求带背景色
      content: `${deleteItem?.good}删除成功`,
      strong: true,
    })
    fetchList()
  }
  const handleModal = (item:Partial<Provider>) => { // 处理编辑弹窗
    editVisible.value = true
    providerPayload.value = {
      providerId: item.providerId, // 服务商id（修改时必填）
      providerName: item.providerName, // 服务商名称
      spCompanyName: item.providerCompanyName, // 服务商公司名称
      providerUid: item.providerUid, // 服务主账号uid（小红书uid）
    }
  }
  const handlePunishModal = (item: Provider) => {
    if (!item.providerId) return
    punishItem.value = {
      providerId: item.providerId,
      officialHidden: !!item.officialHidden,
      banNewSeller: !!item.banNewSeller
    }
    punishModalVisible.value = true
  }
  const handleEdit = async () => { // 处理编辑
    modalLoading.value = true
    try {
      await updateProvider(providerPayload.value as ProviderPayload)
      await fetchList(false)
      editVisible.value = false
    } finally {
      modalLoading.value = false
    }
  }

  return {
    modalLoading,
    listSource,
    total,
    loading,
    editVisible,
    punishModalVisible,
    filterParam,
    punishItem,
    fetchList,
    handleDelete,
    handleEdit,
    handleModal,
    handlePunishModal,
    providerPayload,
  }
}
