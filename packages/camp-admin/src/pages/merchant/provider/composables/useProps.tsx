import { computed } from 'vue'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text, Tag } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

import { Provider, AuditStatus, AUDITSTATUS_TEXT_MAP } from '@/services/provinder'
import useUser from '@/composables/use-user'
import openSupportLogin from '@/utils/openPartnerLogin'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: '代运营管理',
  },
  {
    label: '服务商列表',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'providerId',
        label: '服务商providerId',
        component: {
          props: {
            placeholder: '服务商providerId',
          },
        },
      },
      {
        name: 'providerName',
        label: '服务商名称',
        component: {
          props: {
            placeholder: '请输入服务商名称',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useTableColumns = ({
  handleModal,
  handlePunishModal,
}: {
  handleModal: (item:Provider) => void
  handlePunishModal: (item:Provider) => void
}) => {
  const { userInfo } = useUser()

  // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '服务商名称',
      dataIndex: 'providerName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.providerName || '-'}</Text>),
    },
    {
      title: '服务商主体名称',
      dataIndex: 'providerCompanyName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.providerCompanyName || '-'}</Text>),
    },
    {
      title: '服务商主账号uid',
      dataIndex: 'providerUid',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.providerUid || '-'}</Text>),
    },
    {
      title: '服务商providerId',
      dataIndex: 'providerId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.providerId || '-'}</Text>),
    },
    {
      title: '合作商家数',
      dataIndex: 'sellerNum',
      minWidth: 100,
      render: ({ rowData }) => <Text>{rowData?.sellerNum || 0}</Text>,
    },
    {
      title: '入驻状态',
      minWidth: 100,
      dataIndex: 'auditStatus',
      render: ({ rowData }) => <Tag color={rowData?.auditStatus === AuditStatus.hasConfirmed ? 'green' : 'orange'}>{ AUDITSTATUS_TEXT_MAP[rowData?.auditStatus as AuditStatus] || '-'}</Tag>,
    },
    {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [
            {
              text: '修改信息',
              onClick: () => handleModal(rowData as Provider),
            },
            {
              text: '附身登陆',
              visible: process.env.NODE_ENV === 'development' || userInfo.value?.permissions.includes('partner_super_login'),
              onClick: () => openSupportLogin((rowData as Provider).providerId),
            },
            {
              text: '平台处罚',
              visible: rowData?.auditStatus === AuditStatus.hasConfirmed,
              onClick: () => handlePunishModal(rowData as Provider),
            },
          ],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
