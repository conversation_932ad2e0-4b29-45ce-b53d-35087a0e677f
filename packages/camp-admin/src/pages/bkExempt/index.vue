<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
      <OutlineFilter
        v-model="searchParam"
        :config="filterConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Toolbar
          :config="toolBarConfig"
        />
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="searchParam.pageNum"
          v-model:pageSize="searchParam.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
  </Space>
  <ImportCancelModal v-model:visible="importVisible" :is-del="isDel" />
</template>
<script setup lang="tsx">
  import {
    Space, Table, Pagination
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import {
    spaceProps, pageHeaderProps, useToolBar, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'
  import ImportCancelModal from './ImportCancelModal.vue'

  const {
    listSource, // 列表数据
    total,
    loading, // 列表加载标识
    searchParam, // 查询条件
    fetchList, // 获取列表方法
    handleExport, // 导出表格
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { toolBarConfig, importVisible, isDel } = useToolBar(handleExport)
  const { tableColumns } = useTableColumns()

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
.historyItem
  margin-top 20px
</style>
