import { ref, computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import dayjs from 'dayjs'

export const spaceProps:any = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
}

export const pageHeaderProps:PageHeaderProps = {
  title: '招募合作惩罚自助豁免工具',
}

export const useFilter = (fetchList: () => Promise<any>) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'cooperateNo',
        label: '合作编号',
        component: {
          props: {
            placeholder: '请输入合作编号',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const importVisible = ref<boolean>(false)
  const isDel = ref<boolean>(false)
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '豁免刷数',
        onClick: () => {
          importVisible.value = true
          isDel.value = false
        },
      },
      // {
      //   text: '批量删除',
      //   onClick: () => {
      //     importVisible.value = true
      //     isDel.value = true
      //   },
      // },
      // {
      //   text: '导出',
      //   onClick: () => {
      //     handleExport()
      //   },
      // },
    ]
  }))
  return {
    toolBarConfig,
    importVisible,
    isDel
  }
}

export const useTableColumns = () => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => [{
    title: '合作编号',
    dataIndex: 'cooperateNo',
    render: ({ rowData }) => <Text>{rowData?.cooperateNo}</Text>,
  },
  {
    title: '用户ID',
    dataIndex: 'kolUserId',
    render: ({ rowData }) => <Text>{rowData?.kolUserId}</Text>,
  },
  {
    title: '豁免理由',
    dataIndex: 'cancelReason',
    render: ({ rowData }) => <Text ellipsis tooltip>{rowData?.cancelReason}</Text>,
  },
  {
    title: '豁免判定人',
    dataIndex: 'cancelBy',
    width: 100,
    render: ({ rowData }) => <Text>{rowData?.cancelBy}</Text>,
  },
  {
    title: '更新人',
    dataIndex: 'createdBy',
    width: 100,
    render: ({ rowData }) => <Text>{rowData?.createdBy}</Text>,
  },
  {
    title: '更新时间',
    dataIndex: 'createTime',
    minWidth: 200,
    render: ({ rowData }) => <Text>{dayjs(rowData?.createTime).locale('zh-cn').format('YYYY-MM-DD HH:mm:ss')}</Text>,
  }])
  return {
    tableColumns,
  }
}
