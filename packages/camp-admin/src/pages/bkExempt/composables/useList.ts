import { ref } from 'vue'
import { getCancelPunishmentList } from '@/services/bkExempt'
import type { CancelPunishmentItem } from '@/services/bkExempt'

export const useList = () => {
  const listSource = ref<CancelPunishmentItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const total = ref(0)
  const searchParam = ref<{
    pageSize: number
    pageNum: number
    cooperateNo?: string
  }>({ // 搜索参数
    pageSize: 50,
    pageNum: 1,
    cooperateNo: '',
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    listSource.value = []
    total.value = 0
    loading.value = true
    if (isResetPageNum) {
      searchParam.value.pageNum = 1
    }
    try {
      const res = await getCancelPunishmentList({
        ...searchParam.value,
        cooperateNo: searchParam.value.cooperateNo || ''
      })
      total.value = res.total
      listSource.value = res.cancelPunishmentList
    } finally {
      loading.value = false
    }
  }

  const handleExport = async () => { // 导出表格
    // const res = await exportNote()
    // window.open(res?.url)
  }

  return {
    listSource,
    total,
    loading,
    searchParam,
    fetchList,
    handleExport,
  }
}
