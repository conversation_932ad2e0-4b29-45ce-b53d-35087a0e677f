import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'
import { pageQueryPackage, batchUpdatePackageStatus, CommonStatusEnum } from '@/services/open'
import { tryCatch } from 'shared/utils'
import { openServiceAdminManagerAuth } from '@/services/user'

export const useList = () => {
  const listSource = ref<any[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const editItem = ref() // 编辑的Item
  const total = ref(0)
  const filterParam = ref({ // 搜索参数
    pageParam: {
      pageSize: 10,
      pageNo: 1,
    },
    packageId: undefined,
    packageName: undefined,
    status: undefined,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageParam.pageNo = 1
    }
    const [res, err] = await tryCatch(pageQueryPackage, filterParam.value)
    if (!err) {
      listSource.value = res?.openPackages || []
      total.value = res?.total || 0
    }
    loading.value = false
  }

  const handleDelete = (deleteItem:any) => { // 删除方法
    toast.success({ // 规范Toast交互，要求带背景色
      content: `${deleteItem?.good}删除成功`,
      strong: true,
    })
    fetchList(false)
  }
  const handleModal = (item:any) => { // 处理编辑弹窗
    editVisible.value = true
    editItem.value = item
  }
  const handleEdit = () => { // 处理编辑
    toast.info({
      content: `编辑${editItem.value?.good}`,
      strong: true,
    })
    editVisible.value = false
  }
  const handleStatus = async (rowData: any) => {
    try {
      await openServiceAdminManagerAuth()
    } catch (e) {
      toast.warning('您没有管理员权限，请联系@阿力')
      return
    }
    const [, err] = await tryCatch(batchUpdatePackageStatus, { packageIds: [rowData?.packageId], status: rowData?.status === CommonStatusEnum.INVALID ? CommonStatusEnum.VALID : CommonStatusEnum.INVALID })
    if (!err) {
      toast.success('操作成功')
      fetchList()
    }
  }

  return {
    listSource,
    total,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleDelete,
    handleEdit,
    handleModal,
    handleStatus,
  }
}
