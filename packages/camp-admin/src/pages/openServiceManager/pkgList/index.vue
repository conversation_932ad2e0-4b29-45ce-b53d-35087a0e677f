<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Toolbar :config="toolBarConfig" />
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
        </Table>
        <Pagination
          v-model="filterParam.pageParam.pageNo"
          v-model:pageSize="filterParam.pageParam.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
  </Space>
  <Modal
    v-model:visible="editVisible"
    title="编辑弹窗"
    @confirm="handleEdit"
    @cancel="editVisible = false"
  >
    <Text>
      自定义编辑弹窗内容
    </Text>
  </Modal>
</template>
<script setup lang="ts">
  import {
    Space, Table, Pagination, Modal, Text,
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import {
    spaceProps, pageHeaderProps, useToolBar, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  const {
    listSource, // 列表数据
    total, // 数据总数
    loading, // 列表加载标识
    filterParam, // 查询条件
    editVisible, // 编辑弹窗展示标识
    fetchList, // 获取列表方法
    handleDelete, // 删除方法
    handleEdit, // 处理编辑方法
    handleModal, // 处理弹窗
    handleStatus, // 处理状态方法
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { toolBarConfig } = useToolBar()
  const { tableColumns } = useTableColumns({
    handleDelete,
    handleModal,
    handleStatus,
  })

  fetchList()
</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  :deep(.ultra-material-toolbar-wrap)
    padding 0
</style>
