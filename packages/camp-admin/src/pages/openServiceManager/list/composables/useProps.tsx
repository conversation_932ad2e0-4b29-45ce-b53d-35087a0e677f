import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import { CommonStatusEnum } from '@/services/open'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const pageHeaderProps:PageHeaderProps = { // 页头的配置
  type: 'title',
  items: [
    { title: '接口列表' },
    // { title: '基础列表' },
  ],
}

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'interfaceId',
        label: '接口ID',
        component: {
          props: {
            placeholder: '请输入接口ID',
          },
        },
      },
      {
        name: 'interfaceName',
        label: '接口名称',
        component: {
          props: {
            placeholder: '请输入接口名称',
          },
        },
      },
      {
        name: 'apiRequestPath',
        label: '请求路径',
        component: {
          props: {
            placeholder: '请输入请求路径',
          },
        },
      },
      {
        name: 'status',
        label: '接口状态',
        width: 200,
        component: {
          is: 'Select',
          props: {
            options: [
              { label: '可用', value: 'VALID' },
              { label: '不可用', value: 'INVALID' },
            ],
            placeholder: '请输入接口状态'
          },
        },
      }
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const router = useRouter()

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新建接口',
        onClick: () => {
          router.push({ name: 'openServiceManagerCreate' })
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = ({
  handleStatus,
}: {
  handleDelete: (item:any) => void
  handleModal: (item:any) => void
  handleStatus: (item:any) => void
}) => { // 列表的配置
  const router = useRouter()
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '接口id',
      dataIndex: 'interfaceId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.interfaceId}</Text>),
    },
    {
      title: '系统Code',
      dataIndex: 'sysCode',
    },
    {
      title: '接口类型',
      dataIndex: 'interfaceType',
    },
    {
      title: '接口名称',
      dataIndex: 'interfaceName',
    },
    {
      title: '请求路径',
      dataIndex: 'apiRequestPath',
    },
    {
      title: '请求路径别名',
      dataIndex: 'apiRequestPathAlias',
    },
    {
      title: '接口状态',
      dataIndex: 'status',
      render: ({ rowData }) => (<Text ellipsis tooltip >{rowData?.status === CommonStatusEnum.INVALID ? '不可用' : '可用'}</Text>),
    },
    {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [{
            text: '编辑',
            onClick: () => router.push({ name: 'openServiceManagerEdit', params: { id: rowData?.interfaceId } }),
          }, {
            text: rowData?.status === CommonStatusEnum.INVALID ? '启用' : '停用',
            onClick: () => {
              handleStatus(rowData)
            },
          }, {
            text: '复制并创建',
            onClick: () => router.push({ name: 'openServiceManagerEdit', params: { id: rowData?.interfaceId }, query: { type: 'create' } }),
          }],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
