<template>
  <div class="parameter-management">
    <!-- 顶层参数渲染 -->
    <div v-for="(element, index) in modelValue" :key="index">
      <ParameterItem
        :type="type"
        :item="element"
        :index="index"
        @addChild="addChildParameter(index)"
        @remove="removeParameter(index)"
      />
    </div>

    <!-- 添加根参数 -->
    <Button @click="addRootParameter">新增根参数</Button>
  </div>
</template>

<script setup lang="ts">
  import { Button, toast2 } from '@xhs/delight'
  import { InterfaceParamDTO } from '@/services/open'
  import { cloneDeep } from 'lodash'
  import ParameterItem from './components/ParameterItem.vue' // 递归子组件
  import { defaultInterfaceValue, structList } from '../constant'

  const props = defineProps<{
    modelValue: InterfaceParamDTO[]
    type: 'params' | 'response'
  }>()

  // 添加根参数
  const addRootParameter = () => {
    props.modelValue.push(cloneDeep(defaultInterfaceValue))
  }

  // 添加子参数的逻辑，确保子参数添加到正确的父级参数中
  const addChildParameter = index => {
    const parent = props.modelValue[index]
    // 只有当参数类型为 struct 或 loop struct 时，才能添加子参数
    if (structList.includes(parent.fieldType)) {
      if (!parent.fieldChildren) {
        parent.fieldChildren = []
      }
      parent.fieldChildren.push(cloneDeep(defaultInterfaceValue))
    } else {
      toast2.danger('请选择正确的参数类型')
    }
  }
  // 删除参数
  const removeParameter = index => {
    props.modelValue.splice(index, 1)
  }
</script>

<style scoped lang="stylus">
  .parameter-management {
    overflow-x: scroll;
    padding-bottom:10px;
    flex:1;
  }

</style>
