<template>
  <div class="parameter-item">
    <!-- 每个参数的输入区域 -->
    <div class="parameter-fields">
      <Input v-model="item.fieldName" clearable :style="{ width: '200px' }" placeholder="字段名称" />
      <Input v-model="item.fieldComment" :disabled="disabled" clearable :style="{ width: '200px' }" placeholder="字段描述" />
      <Select v-model="item.fieldType" :disabled="disabled" filterable :style="{ width: '200px' }" placeholder="请输入接口类型" :options="fieldTypeOptions" @change="fieldTypeChange" />
      <!-- 网关字段 -->
      <Input v-model="item.fieldApiName" clearable :style="{ width: '200px' }" placeholder="网关接口字段" />
      <Input v-model="item.fieldApiComment" :disabled="disabled" clearable :style="{ width: '200px' }" placeholder="网关接口字段描述" />

      <Select v-if="type === 'params'" v-model="item.sourceFrom" :disabled="disabled" filterable :style="{ width: '200px' }" placeholder="请输入请求来源" :options="ApiParamSourceFromEnumData" />

      <!-- 是否必填 -->
      <span class="require">
        <Text>必填</Text>
        <Checkbox v-model:checked="item.fieldRequired" :disabled="disabled" :style="{ marginLeft: '6px' }" /> </span>

      <span v-if="type === 'response'" class="require">
        <Text>通用result</Text>
        <Checkbox v-model:checked="item.fieldCommonResult" :disabled="disabled" :style="{ marginLeft: '6px' }" />
      </span>

      <!-- 添加子参数按钮，仅当类型为struct时可见 -->
      <Button v-if="structList.includes(item.fieldType)" :disabled="disabled" @click="addChild">添加参数</Button>
      <Button @click="remove">删除</Button>
    </div>

    <!-- 渲染嵌套的子参数 -->
    <div v-if="item.fieldChildren && item.fieldChildren.length">
      <div v-for="(child, index2) in item.fieldChildren" :key="`${index}-${index2}`">
        <ParameterItem :disabled="disabled" :type="type" :item="child" :index="index2" @remove="removeChild(index2)" @addChild="addChildParameter(index2)" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits, computed } from 'vue'
  import {
    Input, Button, Select, Checkbox, Text
  } from '@xhs/delight'
  import { InterfaceParamDTO } from '@/services/open'
  import { cloneDeep } from 'lodash'
  import {
    interfaceFieldTypeEnumData, ApiParamSourceFromEnumData, defaultInterfaceValue, structList
  } from '../../constant'

  const props = defineProps<{
    item: InterfaceParamDTO
    index: Number
    type: 'params' | 'response'
    disabled: boolean
  }>()

  const fieldTypeOptions = interfaceFieldTypeEnumData

  const disabled = computed(() => props.item.fieldName === 'base.Context' || props.item.fieldApiName === 'base.Context')

  const emit = defineEmits(['addChild', 'remove'])

  // 添加子参数
  const addChild = () => {
    emit('addChild')
  }

  // 删除当前参数
  const remove = () => {
    emit('remove')
  }

  const fieldTypeChange = value => {
    if (!structList.includes(value)) {
      props.item.fieldChildren = []
    }
  }

  // 添加子参数
  const addChildParameter = (index: number) => {
    if (!props?.item?.fieldChildren) return
    // 只有当参数类型为 struct 或 loop struct 时，才能添加子参数
    if (structList.includes(props?.item?.fieldChildren[index].fieldType)) {
      if (!props?.item?.fieldChildren[index].fieldChildren) {
        props.item.fieldChildren[index].fieldChildren = []
      }
      props?.item?.fieldChildren[index].fieldChildren.push(cloneDeep(defaultInterfaceValue))
    }
  }

  // 删除子参数
  const removeChild = index => {
    props?.item?.fieldChildren?.splice(index, 1)
  }
</script>

<style scoped lang="stylus">

.parameter-item {
  margin-bottom: 10px;
  padding-left: 20px
  border-left: 1px solid #ccc;
  :deep(.d-select-wrapper .d-select){
    width: 200px;
  }
}
.parameter-fields {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}
.require {
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}
</style>
