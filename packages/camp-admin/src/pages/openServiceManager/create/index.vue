<template>
  <Space
    ref="FormContainer"
    class="ultra-form-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-form-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
    </Space>
    <Space class="ultra-form-body">
      <Space
        class="ultra-form-content"
      >
        <Space
          class="ultra-form-items"
          v-bind="spaceProps"
        >
          <Form ref="formRef" :model="model" label-width="120px" label-position="left" :rules="rules">
            <FormItem required label="接口类型" name="interfaceType">
              <Select v-model="model.interfaceType" style="width: 400px" disabled placeholder="请输入接口类型" :options="interfaceTypeOptions" />
            </FormItem>
            <FormItem label="系统Code" description="应用服务树的code" name="sysCode">
              <Input v-model="model.sysCode" placeholder="请输入系统Code" :max-length="20" />
            </FormItem>
            <FormItem label="接口名称" name="interfaceName">
              <Input v-model="model.interfaceName" placeholder="请输入接口名称" :max-length="100" />
            </FormItem>
            <FormItem label="接口描述" name="interfaceDesc">
              <TextArea v-model="model.interfaceDesc" style="width: 400px" placeholder="请输入接口描述" :max-length="500" />
            </FormItem>
            <FormItem required label="支持三方" name="supportThird">
              <Select v-model="model.supportThird" style="width: 400px" placeholder="请输入选择是否支持三方" :options="supportThirdOptions" />
            </FormItem>
            <template v-if="model.interfaceType === InterfaceTypeEnum.CALL_RPC">
              <FormItem label="服务名" name="serviceName">
                <Input v-model="model.serviceName" placeholder="请输入服务名" :max-length="100" />
              </FormItem>
              <FormItem label="类全路径" name="clazzFullPath">
                <Input v-model="model.clazzFullPath" placeholder="请输入类全路径" :max-length="100" />
              </FormItem>
              <FormItem label="方法名" name="methodName">
                <Input v-model="model.methodName" placeholder="请输入方法名" :max-length="100" />
              </FormItem>
              <FormItem label="超时时间" name="timeoutMilliSeconds">
                <InputNumber v-model="model.timeoutMilliSeconds" placeholder="请输入超时时间" suffix="ms" :min="100" :max="10000" />
              </FormItem>
              <FormItem label="网关请求路径" name="apiRequestPath">
                <Input v-model="model.apiRequestPath" placeholder="请输入网关请求路径" :max-length="100" @blur="onBlurPathInput" />
              </FormItem>
              <FormItem label="网关请求别名" name="apiRequestPathAlias">
                <Input v-model="model.apiRequestPathAlias" placeholder="请输入网关请求别名" :max-length="100" />
              </FormItem>
              <FormItem label="网关请求方法" name="apiRequestMethod">
                <Select v-model="model.apiRequestMethod" style="width: 400px" placeholder="请选择网关请求方法" :options="[
                  { label: 'GET', value: ApiRequestMethodEnum.GET },
                  { label: 'POST', value: ApiRequestMethodEnum.POST }
                ]"
                />
              </FormItem>
              <Button
                @click="editVisible = true"
              >快速导入参数</Button>
              <FormItem label="请求参数" name="requestParams">
                <ParamsCmp v-model="model.requestParams" :type="'params'"></ParamsCmp>
              </FormItem>
              <FormItem label="返回参数" name="responseParams">
                <ParamsCmp v-model="model.responseParams" name="responseParams" :type="'response'"></ParamsCmp>
              </FormItem>
            </template>
            <!-- <template>
              <FormItem required label="topic">
                <Input v-model="model.topic" placeholder="请输入topic" />
              </FormItem>
              <FormItem required label="tag">
                <Input v-model="model.tag" placeholder="请输入tag" />
              </FormItem>
            </template> -->
          </Form>
        </Space>
      </Space>
    </Space>
    <Space
      class="ultra-form-footer"
      block
      justify="center"
      :style="{ width: `${footerWidth}px` }"
    >
      <!-- <Button @click="reset">重置</Button> -->
      <Button
        type="primary"
        @click="preSubmit"
      >提交</Button>
    </Space>
  </Space>
  <Modal
    v-model:visible="editVisible"
    :style="{ width: '800px', height: '600px' }"
    title="快速导入参数"
    @confirm="handleEdit"
    @cancel="editVisible = false"
  >
    <Link href="https://docs.xiaohongshu.com/doc/238bdd4cc305908ccf5c3b2044be035a" target="_blank">📕快速创建接口指南</Link>
    <TextArea v-model="interfaceJson" :style="{ width: '100%', height: '90%' }" placeholder="请输入" />
  </Modal>
</template>
<script setup lang="tsx">
  import {
    ref, onMounted, onUnmounted, h
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import {
    Link, Space, Button, Form2 as Form, FormItem2 as FormItem, Input, TextArea, Select, toast2 as toast, Modal, InputNumber, Text
  } from '@xhs/delight'
  import PageHeader, { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
  import {
    CreateInterfaceRequest, parseEdithParams, InterfaceTypeEnum, ApiRequestMethodEnum, createInterface, updateInterface, queryInterfaceByInterfaceId
  } from '@/services/open'
  import { tryCatch } from 'shared/utils'
  import { cloneDeep } from 'lodash'
  import { openServiceAdminManagerAuth } from '@/services/user'
  import ParamsCmp from './paramsCmp/index.vue'
  import { defaultInterfaceValue, supportThirdOptions } from './constant'
  import { env } from '../../../config/http.config'

  const route = useRoute()
  const router = useRouter()

  const id = route.params.id
  const type = route.query.type

  const editVisible = ref(false)
  const interfaceJson = ref('')

  const handleEdit = async () => {
    if (!parseEdithParams) return
    const [res, err] = await tryCatch(parseEdithParams, interfaceJson.value)
    if (!err && res) {
      model.value.requestParams = res?.requestParams || []
      model.value.responseParams = res?.responseParams || []
      editVisible.value = false
    }
  }

  onMounted(async () => {
    if (id) {
      const [res, err] = await tryCatch(queryInterfaceByInterfaceId, id as string)
      if (!err && res) {
        model.value = res?.interfaces[0] || {}
      }
    }
  })

  function validateInterfaceParamDTOArray(params) {
    if (!Array.isArray(params)) return false

    for (const param of params) {
      if (!validateInterfaceParamDTO(param)) {
        return false
      }
    }

    return true
  }
  function validateInterfaceParamDTO(param) {
    if (!param) return false

    if (!param.fieldName) {
      return false
    }

    if (!param.fieldApiName) {
      return false
    }

    // 如果有嵌套参数，则递归检查它们
    if (param.fieldChildren && Array.isArray(param.fieldChildren) && param.fieldChildren.length > 0) {
      for (const childParam of param.fieldChildren) {
        if (!validateInterfaceParamDTO(childParam)) {
          return false
        }
      }
    }
    return true
  }

  const validateRequestParams = (rule, value, callback) => {
    if (value.length === 0) {
      callback(new Error('参数不能为空'))
    } else if (!validateInterfaceParamDTOArray(value)) {
      callback(new Error('请完善必填参数'))
    } else {
      callback()
    }
  }

  const rules = {
    sysCode: [
      { required: true, message: '系统Code不能为空', trigger: 'change' },
    ],
    interfaceName: [
      { required: true, message: '接口名称不能为空', trigger: 'change' },
    ],
    serviceName: [
      {
        required: true, message: '服务名不能为空', trigger: 'change',
      },
    ],
    supportThird: [
      {
        required: true, message: '支持三方不能为空', trigger: 'change',
      }
    ],
    clazzFullPath: [
      {
        required: true, message: '类全路径不能为空', trigger: 'change',
      }
    ],
    timeoutMilliSeconds: [
      {
        required: true, message: '超时时间不能为空', trigger: 'change',
      }
    ],
    apiRequestPath: [
      {
        required: true, message: '网关接口请求路径不能为空', trigger: 'change',
      },
    ],
    methodName: [
      {
        required: true, message: '方法名不能为空', trigger: 'change',
      },
    ],
    apiRequestMethod: [
      {
        required: true, message: '网关请求方法不能为空', trigger: 'change',
      },
    ],
    requestParams: [
      { required: true, validator: validateRequestParams, trigger: 'change' }
    ],
    responseParams: [
      { required: true, validator: validateRequestParams, trigger: 'change' }
    ],
  }

  const spaceProps = { // page容器的配置
    direction: 'vertical',
    size: 'large',
    align: 'unset',
  } as const

  const pageHeaderProps: PageHeaderProps = { // 页头的配置
    type: 'combination',
    items: [
      { title: '接口列表', to: { name: 'openServiceManagerList' } },
      { title: id && type !== 'create' ? '编辑接口' : '新建接口' },
    ],
  }

  const interfaceTypeOptions = [
    { label: 'RPC调用', value: InterfaceTypeEnum.CALL_RPC },
    // { label: 'HTTP调用', value: InterfaceTypeEnum.CALL_HTTP },
    // { label: '回调事件', value: InterfaceTypeEnum.CALLBACK_EVENT },
  ]

  const formRef = ref() // 表单的Ref对象

  const model = ref<CreateInterfaceRequest>({
    methodName: '',
    interfaceDesc: '',
    sysCode: '',
    interfaceName: '',
    clazzFullPath: '',
    timeoutMilliSeconds: undefined,
    interfaceType: InterfaceTypeEnum.CALL_RPC, // Replace with an actual enum value
    serviceName: '',
    apiRequestPath: '',
    apiRequestPathAlias: '',
    apiRequestMethod: ApiRequestMethodEnum.GET, // Replace with an actual enum value
    requestParams: [cloneDeep(defaultInterfaceValue)],
    responseParams: [cloneDeep(defaultInterfaceValue)],
    supportThird: true,
  })

  const preSubmit = async () => {
    try {
      const res = await openServiceAdminManagerAuth()
      console.log(res)
    } catch (e) {
      toast.warning('您没有管理员权限，请联系@阿力')
      console.error(e)
      return
    }

    formRef.value.validate().then(async () => {
      console.log(env)
      if (env === 'production' || env === 'prerelease') {
        Modal.warning({
          title: '请确认',
          content: h(Text, { color: 'danger' }, {
            default: () => '你正在操作生产环境的数据，提交后即生效，请谨慎操作'
          }),
          async onConfirm(close) {
            close?.()
            submit()
          },
          onCancel(close) {
            close?.()
          }
        })
      } else {
        submit()
      }
    }).catch(err => {
      const errList: any = Object.values(err) || []
      if (errList[0]) {
        toast.warning(errList[0][0]?.message || '请检查必填字段')
      }
    })
  }

  const submit = async () => {
    if (id && type !== 'create') {
      const [, err] = await tryCatch(updateInterface, {
        ...model.value,
        interfaceId: id as string
      })
      if (!err) {
        toast.success('编辑成功')
        router.push({
          name: 'openServiceManagerList'
        })
      }
    } else {
      const [, err] = await tryCatch(createInterface, model.value)
      if (!err) {
        toast.success('新建成功')
        router.push({
          name: 'openServiceManagerList'
        })
      }
    }
  }

  const onBlurPathInput = () => {
    if (model.value.apiRequestPath && !model.value.apiRequestPathAlias) {
      model.value.apiRequestPathAlias = model.value.apiRequestPath
    }
  }

  // const { formProps, formItems } = useDelightForm({
  //   selectOptions,
  //   checkOptions,
  //   handleSelect,
  // })

  /** 吸顶按钮模块的宽度自适应获取Start */
  const FormContainer = ref() // 容器的Ref对象
  const footerWidth = ref() // 吸底的按钮区域宽度
  const handleResize = () => { // 处理布局的自适应
    footerWidth.value = FormContainer.value?.$el?.parentElement?.clientWidth
  }
  onMounted(() => {
    handleResize() // 初始化
    window.addEventListener('resize', handleResize)
  })
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
  /** 吸顶按钮模块的宽度自适应获取End */
</script>

<style lang="stylus" scoped>
.ultra-form-container
  width 100%
  padding-bottom 80px
  &.d-space-vertical
    gap 24px
.ultra-form-body
  background #FFFFFF
  border-radius 8px
  padding 24px
.ultra-form-items
  width 100%
.ultra-form-content
  width 100%
  :deep(
    .d-input-wrapper.d-inline-block,
    .d-select-wrapper.d-inline-block,
    .d-textarea-wrapper.d-inline-block,
    .d-datepicker-wrapper.d-inline-block,
    .d-daterangepicker-wrapper.d-inline-block)
      width 400px
  :deep(.d-new-form .d-form-item__content) {
    flex: 1;
    overflow-x: hidden; /* 防止外层滚动 */
    display: flex;
    align-items: stretch;
    flex-direction: column;
  }
  :deep(.d-new-form .d-form-item__content .d-form-item__wrapper) {
    flex: 1;
    overflow-x: hidden; /* 防止外层滚动 */
    display: flex;
    align-items: stretch;
  }
.ultra-form-footer
  position fixed
  bottom 0
  right 0
  background-color #fff
  z-index 99
  border-top 1px solid #e8e8e8
  padding 18px 0 22px
  :deep(.d-button.d-button-default)
    width 108px
</style>
