import { InterfaceFieldTypeEnum, ApiParamSourceFrom } from '@/services/open'

export const interfaceFieldTypeEnumData = [
  { label: 'BOOL', value: InterfaceFieldTypeEnum.BOOL },
  { label: 'BYTE', value: InterfaceFieldTypeEnum.BYTE },
  { label: 'INT16', value: InterfaceFieldTypeEnum.INT16 },
  { label: 'INT32', value: InterfaceFieldTypeEnum.INT32 },
  { label: 'INT64', value: InterfaceFieldTypeEnum.INT64 },
  { label: 'FLOAT64', value: InterfaceFieldTypeEnum.FLOAT64 },
  { label: 'STRING', value: InterfaceFieldTypeEnum.STRING },
  { label: 'STRUCT', value: InterfaceFieldTypeEnum.STRUCT },
  // { label: 'LOOPSTRUCT', value: InterfaceFieldTypeEnum.LOOPSTRUCT },
  // { label: 'MAP', value: InterfaceFieldTypeEnum.MAP },
  { label: 'LIST_BOOL', value: InterfaceFieldTypeEnum.LIST_BOOL },
  { label: 'LIST_BYTE', value: InterfaceFieldTypeEnum.LIST_BYTE },
  { label: 'LIST_INT16', value: InterfaceFieldTypeEnum.LIST_INT16 },
  { label: 'LIST_INT32', value: InterfaceFieldTypeEnum.LIST_INT32 },
  { label: 'LIST_INT64', value: InterfaceFieldTypeEnum.LIST_INT64 },
  { label: 'LIST_FLOAT64', value: InterfaceFieldTypeEnum.LIST_FLOAT64 },
  { label: 'LIST_STRING', value: InterfaceFieldTypeEnum.LIST_STRING },
  { label: 'LIST_STRUCT', value: InterfaceFieldTypeEnum.LIST_STRUCT },
  // { label: 'LIST_LOOPSTRUCT', value: InterfaceFieldTypeEnum.LIST_LOOPSTRUCT },
  // { label: 'LIST_MAP', value: InterfaceFieldTypeEnum.LIST_MAP },
  { label: 'ENUM_BOOL', value: InterfaceFieldTypeEnum.ENUM_BOOL },
  { label: 'ENUM_BYTE', value: InterfaceFieldTypeEnum.ENUM_BYTE },
  { label: 'ENUM_INT16', value: InterfaceFieldTypeEnum.ENUM_INT16 },
  { label: 'ENUM_INT32', value: InterfaceFieldTypeEnum.ENUM_INT32 },
  { label: 'ENUM_INT64', value: InterfaceFieldTypeEnum.ENUM_INT64 },
  { label: 'ENUM_FLOAT64', value: InterfaceFieldTypeEnum.ENUM_FLOAT64 },
  { label: 'ENUM_STRING', value: InterfaceFieldTypeEnum.ENUM_STRING },
  { label: 'ENUM_STRUCT', value: InterfaceFieldTypeEnum.ENUM_STRUCT },
  { label: 'ENUM_LOOPSTRUCT', value: InterfaceFieldTypeEnum.ENUM_LOOPSTRUCT },
  { label: 'ENUM_MAP', value: InterfaceFieldTypeEnum.ENUM_MAP },
  { label: 'JSON', value: InterfaceFieldTypeEnum.JSON },
]

export const ApiParamSourceFromEnumData = [
  {
    label: 'QUERY', value: ApiParamSourceFrom.QUERY
  },
  {
    label: 'HEADER', value: ApiParamSourceFrom.HEADER

  },
  {
    label: 'BODY', value: ApiParamSourceFrom.BODY
  }
]
export const defaultInterfaceValue = {
  fieldName: '',
  fieldType: InterfaceFieldTypeEnum.STRING,
  fieldComment: '',
  fieldApiName: '',
  fieldRequired: true,
  fieldApiComment: '',
  fieldChildren: [],
  sourceFrom: ApiParamSourceFrom.QUERY,
  fieldCommonResult: true
}

export const supportThirdOptions = [
  {
    label: '是', value: true
  },
  {
    label: '否', value: false
  }
]

export const structList = [InterfaceFieldTypeEnum.LOOPSTRUCT, InterfaceFieldTypeEnum.STRUCT, InterfaceFieldTypeEnum.LIST_STRUCT, InterfaceFieldTypeEnum.ENUM_LOOPSTRUCT]
