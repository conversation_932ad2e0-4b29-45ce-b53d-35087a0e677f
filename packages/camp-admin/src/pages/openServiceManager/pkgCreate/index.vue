<template>
  <Space
    ref="FormContainer"
    class="ultra-form-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-form-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
    </Space>
    <Space class="ultra-form-body">
      <Space
        class="ultra-form-content"
        justify="left"
      >
        <Space
          class="ultra-form-items"
          v-bind="spaceProps"
        >
          <Form ref="formRef" :model="model" label-width="120px" label-position="left" :rules="rules">
            <FormItem label="接口名称" name="packageName">
              <Input v-model="model.packageName" style="width: 400px" placeholder="请输入接口包名称" :max-length="50" />
            </FormItem>
            <FormItem label="接口描述" name="packageDesc">
              <TextArea v-model="model.packageDesc" style="width: 400px" placeholder="请输入接口包描述" :max-length="500" />
            </FormItem>
            <FormItem label="接口包列表" name="interfaceIds">
              <Transfer
                v-model:selected-keys="model.interfaceIds"
                style="width: 800px"
                :data-source="interfaces"
                filterable
                :filter="handleFilter"
              />
            </FormItem>
          </Form>
        </Space>
      </Space>
    </Space>
    <Space
      class="ultra-form-footer"
      block
      justify="center"
      :style="{ width: `${footerWidth}px` }"
    >
      <!-- <Button @click="reset">重置</Button> -->
      <Button
        :loading="loading"
        type="primary"
        @click="submit"
      >提交</Button>
    </Space>
  </Space>
</template>
<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import {
    Space, Button, Form2 as Form, Transfer, FormItem2 as FormItem, Input, TextArea, toast2 as toast
  } from '@xhs/delight'
  import PageHeader, { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
  import {
    CreatePackageRequest, createPackage, updatePackage, queryPackageByPackageId, pageQueryInterface, CommonStatusEnum
  } from '@/services/open'
  import { tryCatch } from 'shared/utils'
  import { cloneDeep } from 'lodash'
  import { openServiceAdminManagerAuth } from '@/services/user'
  import { compareIdLists } from '../utils'

  const route = useRoute()
  const router = useRouter()

  const id = route.params.id
  const loading = ref(false)
  const interfaces = ref([])
  const details = ref()

  onMounted(async () => {
    if (id) {
      const [res, err] = await tryCatch(queryPackageByPackageId, id as string)
      if (!err && res) {
        model.value = res?.openPackages[0] || {}
        details.value = cloneDeep(res.openPackages[0])
      }
    }
    const [res, err] = await tryCatch(pageQueryInterface, { pageParam: { pageSize: 9999, pageNo: 1 }, status: CommonStatusEnum.VALID })
    if (!err) {
      interfaces.value = res?.interfaces?.map(item => ({ key: item.interfaceId, title: item.interfaceName })) || [] as any
    }
  })

  const rules = {
    packageName: [
      { required: true, message: '接口包名称不能为空', trigger: 'change' },
    ],
    packageDesc: [
      { required: true, message: '接口包描述不能为空', trigger: 'change' },
    ],
    interfaceIds: [
      {
        required: true, type: Array, message: '接口包描述不能为空', trigger: 'change'
      },
    ],
  }

  const handleFilter = (v, item) => item.title.toLowerCase().includes(v.toLowerCase())

  const spaceProps = { // page容器的配置
    direction: 'vertical',
    size: 'large',
    align: 'unset',
  } as const

  const pageHeaderProps: PageHeaderProps = { // 页头的配置
    type: 'combination',
    items: [
      { title: '接口包列表', to: { name: 'openServicePkgManagerList' } },
      { title: id ? '编辑接口包' : '新建接口包' },
    ],
  }

  const formRef = ref() // 表单的Ref对象

  const model = ref<CreatePackageRequest>({
    packageName: '',
    packageDesc: '',
    interfaceIds: [],
  })

  const submit = async () => {
    try {
      await openServiceAdminManagerAuth()
    } catch (e) {
      toast.warning('您没有管理员权限，请联系@阿力')
      return
    }
    formRef.value.validate().then(async () => {
      loading.value = true
      if (id) {
        const { addedIds, removedIds } = compareIdLists(details?.value?.interfaceIds, model.value.interfaceIds)
        const [, err] = await tryCatch(updatePackage, {
          packageName: model.value.packageName,
          packageDesc: model.value.packageDesc,
          addInterfaceIds: addedIds,
          removeInterfaceIds: removedIds,
          packageId: id as string
        })
        if (!err) {
          toast.success('编辑成功')
        }
      } else {
        const [, err] = await tryCatch(createPackage, model.value)
        if (!err) {
          toast.success('新建成功')
        }
      }
      loading.value = false
      router.push({
        name: 'openServicePkgManagerList'
      })
    }).catch(err => {
      const errList: any = Object.values(err) || []
      if (errList[0]) {
        toast.warning(errList[0][0]?.message || '请检查必填字段')
      }
    })
  }

  /** 吸顶按钮模块的宽度自适应获取Start */
  const FormContainer = ref() // 容器的Ref对象
  const footerWidth = ref() // 吸底的按钮区域宽度
  const handleResize = () => { // 处理布局的自适应
    footerWidth.value = FormContainer.value?.$el?.parentElement?.clientWidth
  }
  onMounted(() => {
    handleResize() // 初始化
    window.addEventListener('resize', handleResize)
  })
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
  /** 吸顶按钮模块的宽度自适应获取End */
</script>

<style lang="stylus" scoped>
.ultra-form-container
  width 100%
  padding-bottom 80px
  &.d-space-vertical
    gap 24px
.ultra-form-body
  background #FFFFFF
  border-radius 8px
  padding 24px
.ultra-form-content
  width 100%
  :deep(
    .d-input-wrapper.d-inline-block,
    .d-select-wrapper.d-inline-block,
    .d-textarea-wrapper.d-inline-block,
    .d-datepicker-wrapper.d-inline-block,
    .d-daterangepicker-wrapper.d-inline-block)
      // width 400px
  :deep(.d-transfer .d-transfer-panel)
    width: 50%
  :deep(.d-transfer .d-transfer-panel__simple)
    width: 50%
.ultra-form-footer
  position fixed
  bottom 0
  right 0
  background-color #fff
  z-index 99
  border-top 1px solid #e8e8e8
  padding 18px 0 22px
  :deep(.d-button.d-button-default)
    width 108px
</style>
