<template>
  <Modal
    v-model:visible="editVisible"
    :title="isClaim ? '批量认领' : '门店匹配'"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Form v-if="editVisible" label-width="73px">
      <FormItem label="模板下载">
        <Link
          target="_blank"
          :href="isClaim ? claimExlModel : skaExlModel"
          download="POI 批量认领模板.xlsx"
        >
          {{ isClaim ? 'POI 批量认领模板.xlsx' : '门店匹配模板.xlsx' }}
        </Link>
        <Text>{{ `（据模版，填写要${isClaim ? '认领' : '匹配'}的门店信息）` }}</Text>
      </FormItem>
      <FormItem label="批量上传">
        <div v-if="!result" class="upload-container">
          <Spinner :spinning="loading">
            <FileUploader
              v-model="fileList"
              accept=".xlsx"
              :uploading="loading"
              :percent="percent"
              @update:modelValue="complete"
            />
          </Spinner>
          <div v-if="failed" class="message">
            <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
            <Text color="text-description">信息识别失败，请重新上传</Text>
          </div>
        </div>
        <UploadResult v-if="result" :result="result" :text="isClaim ? '认领' : '匹配'">
          <template v-if="isClaim" #info-row>
            <div class="info-row">
              待补充资质：
              <span class="error-message">
                {{ result?.claimInitNum }}
              </span>
            </div>
          </template>
          <template #result>
            <div
              v-if="result.failReasonPath"
              class="info-row"
            >
              下载结果：
              <Link
                class="error-link"
                target="_blank"
                :href="result.failReasonPath"
              >
                校验.xlsx
              </Link>
              <span class="comment-message">（查看结果明细和失败原因）</span>
            </div>
          </template>
        </UploadResult>
      </FormItem>
    </Form>

  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, ref, watch, onUnmounted
  } from 'vue'
  import {
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Link,
    toast,
    Spinner,
  } from '@xhs/delight'
  import FileUploader from '@/components/upload-file/index.vue'
  import UploadResult from '@/components/upload-result/index.vue'
  import { isFinished, computePercent } from '@/utils/longTask'
  import { LONG_TASK_STATUS, LONG_TASK_NAME } from '@/constants/longTask'
  import { postLongTask, getLongTask, GetLongTaskRes } from '@/services/longTaskv2'
  import { ClaimResult } from '@/services/poiClaim'

  const props = defineProps<{
    visible: boolean
    flag: 'claim' | 'ska'
  }>()

  const isClaim = computed(() => props.flag === 'claim')

  const emit = defineEmits(['update:visible'])

  const fileList = ref<any[]>([])
  const failed = ref(false)
  const loading = ref(false)
  const percent = ref(0)

  const claimExlModel = 'https://fe-video-qc.xhscdn.com/fe-platform/759c268a11ea06f73ea169b6d8cba20c2d8a6405/POI认领模板.xlsx'
  const skaExlModel = 'https://fe-video-qc.xhscdn.com/fe-platform/b7440406726715df80645b0b70733640d6e3803e/POI匹配模板.xlsx'

  const result = ref<ClaimResult | undefined>()
  let timer:number

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
        window.clearTimeout(timer)
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
      window.clearTimeout(timer)
    }
  })

  onUnmounted(() => {
    window.clearTimeout(timer)
  })

  const complete = async () => {
    loading.value = true
    try {
      const { taskId } = await postLongTask({
        input: {
          file_url: fileList.value[0]?.downloadUrl,
          extra: undefined // extra 需要以 map 的形式进行传递
        },
        task_name: isClaim.value ? LONG_TASK_NAME.LIFE_POI_CLAIM_TASK : LONG_TASK_NAME.LIFE_POI_MATCH_BEFORE_CLAIM
      })
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        percent.value = computePercent(res?.finishedCount, res?.totalCount) || percent.value
        if (isFinished(res?.status)) {
          loading.value = false
          if (res?.status === LONG_TASK_STATUS.FAIL) {
            failed.value = true
            toast.danger('上传失败，请重新上传')
            return
          }
          failed.value = false
          const { fileUrl = '', extraJson = '{}' } = res?.result
          const extra = JSON.parse(extraJson)
          result.value = {
            failReasonPath: fileUrl,
            successNum: extra?.success_count ?? 0,
            failNum: extra?.failed_count ?? 0,
            claimInitNum: extra?.claim_init_count ?? 0
          }
          return
        }
        timer = window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error: any) {
      loading.value = false
      failed.value = true
      toast.danger(error?.message || '服务器异常，请重新操作')
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
.info-row
  margin-bottom 8px
  display flex
  align-items flex-end
  .error-link
    color #FF4949
    text-decoration underline
  .error-message
    color #F24444
  .comment-message
    color #999999
    font-size 12px
    line-height 20px
    margin-left 8px
</style>
