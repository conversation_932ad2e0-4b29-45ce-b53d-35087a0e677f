import { computed, Ref } from 'vue'
import { useRouter } from 'vue-router'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: 'POI认领',
  },
  {
    label: '商家列表',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: fetchList,
    filterItems: [
      {
        name: 'sellerId',
        label: '商家ID',
        component: {
          props: {
            placeholder: '请输入商家ID',
          },
        },
      },
      {
        name: 'sellerName',
        label: '商家名称',
        component: {
          props: {
            placeholder: '请输入商家名称',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = (
  props: {
    editVisible: Ref<boolean>
    flag: Ref<'claim' | 'ska'>
  }
) => { // 工具栏的配置
  const { editVisible, flag } = props
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '批量认领',
        onClick: () => {
          editVisible.value = true
          flag.value = 'claim'
        },
      },
      {
        text: '门店匹配',
        onClick: () => {
          editVisible.value = true
          flag.value = 'ska'
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = () => { // 列表的配置
  const router = useRouter()

  const toDetail = (rowData?:{sellerId:string;publicName:string}) => {
    router.push({
      name: 'sellerClaimedPoi',
      query: {
        sellerId: (rowData?.sellerId || '') as string,
        sellerName: (rowData?.publicName || '') as string,
      }
    })
  }

  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '商家ID',
      dataIndex: 'sellerId',
      render: ({ rowData }) => (
        <Text
          link
          ellipsis
          tooltip
          style={{ maxWidth: '250px' }}
          onClick={() => toDetail(rowData as {sellerId:string;publicName:string})}
        >{rowData?.sellerId}</Text>),
    },
    {
      title: '商家名称',
      dataIndex: 'publicName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.publicName}</Text>),
    },
    {
      title: '查看',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [
            {
              text: '认领POI',
              onClick: () => toDetail(rowData as {sellerId:string;publicName:string})
            },
          ],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
