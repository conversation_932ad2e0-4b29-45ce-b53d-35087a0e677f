import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'

import { SellerItem, fetchSellerList } from '@/services/poiClaim'

export const useList = () => {
  const listSource = ref<SellerItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const editItem = ref() // 编辑的Item
  const flag = ref<'claim' | 'ska'>('claim')
  const filterParam = ref({ // 搜索参数
    pageSize: 10,
    pageNo: 1,
    total: 0,
    sellerId: process.env.NODE_ENV === 'development' ? '614aef1e03590e0001f566b2' : '',
    sellerName: ''
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }

    if (!filterParam.value.sellerId && !filterParam.value.sellerName) {
      listSource.value = []
      filterParam.value.total = 0
      loading.value = false
      return
    }

    const { total, sellerInfoList } = await fetchSellerList({
      ...filterParam.value,
      total: undefined
    })
    loading.value = false
    filterParam.value.total = total
    listSource.value = sellerInfoList
  }

  const handleEdit = () => { // 处理编辑
    toast.info({
      content: `编辑${editItem.value?.good}`,
      strong: true,
    })
    editVisible.value = false
  }

  return {
    listSource,
    loading,
    flag,
    editVisible,
    filterParam,
    fetchList,
    handleEdit,
  }
}
