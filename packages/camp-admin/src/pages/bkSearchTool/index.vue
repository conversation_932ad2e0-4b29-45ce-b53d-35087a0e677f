<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" :tab-active="tabActive" @click="tabClick" />
      <OutlineFilter
        v-if="isNoteTab"
        v-model="filterNoteParam"
        :config="filterNoteIdsConfig"
      />
      <OutlineFilter
        v-else
        v-model="filterCooperateParam"
        :config="filterCooperateNosConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Table
          size="large"
          :columns="isNoteTab? noteTableColumns: cooperateTableColumns"
          :data-source="listSource"
          :loading="loading"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
        </Table>
      </Space>
    </div>
  </Space>
</template>
<script setup lang="ts">
  import {
    Space, Table, Text,
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import {
    spaceProps, pageHeaderProps, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  const {
    listSource, // 列表数据
    loading, // 列表加载标识
    filterNoteParam,
    filterCooperateParam, // 查询条件
    tabActive,
    isNoteTab,
    fetchList, // 获取列表方法
    tabClick
  } = useList()

  const { filterNoteIdsConfig, filterCooperateNosConfig } = useFilter(fetchList)
  const { noteTableColumns, cooperateTableColumns } = useTableColumns()

  fetchList()
</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
</style>
