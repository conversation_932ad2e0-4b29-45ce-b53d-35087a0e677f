import { ref, computed } from 'vue'
import { searchBkNoteDetail, searchCooperateDetail } from '@/services/bkSearchTool'

export const useList = () => {
  const listSource = ref<any[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const filterNoteParam = ref()
  const filterCooperateParam = ref()
  const tabActive = ref('noteInfo')
  const isNoteTab = computed(() => tabActive.value === 'noteInfo')
  const fetchList = async () => {
    loading.value = true
    try {
      let res
      if (isNoteTab.value) {
        res = await searchBkNoteDetail(filterNoteParam.value)
      } else {
        res = await searchCooperateDetail(filterCooperateParam.value)
      }
      listSource.value = res || []
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  function tabClick(id) {
    tabActive.value = id
    listSource.value = []
  }

  return {
    listSource, // 列表数据
    loading, // 列表加载标识
    filterNoteParam, // 查询条件
    filterCooperateParam, // 查询条件
    tabActive,
    isNoteTab,
    fetchList,
    tabClick
  }
}
