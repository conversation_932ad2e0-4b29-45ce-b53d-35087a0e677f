import { computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import { Text, Link } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import { ContentType } from '@/services/bkSearchTool'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const pageHeaderProps:PageHeaderProps = { // 页头的配置
  type: 'tabs',
  tabItems: [
    { label: '笔记信息', id: 'noteInfo', },
    { label: '合作信息', id: 'operationInfo' },
  ],
}

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterNoteIdsConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'noteIds',
        label: '笔记ID',
        component: {
          props: {
            placeholder: '请输入笔记ID并以英文逗号隔开',
          },
        },
      },
    ],
  }))

  const filterCooperateNosConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'cooperateNos',
        label: '合作ID',
        component: {
          props: {
            placeholder: '请输入合作编号并以英文逗号隔开',
          },
        },
      },
    ],
  }))

  return {
    filterNoteIdsConfig,
    filterCooperateNosConfig
  }
}

export const useTableColumns = () => { // 列表的配置
  const noteTableColumns = computed<ThContent[]>(() => ([
    {
      title: '笔记ID',
      dataIndex: 'noteId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '220px' }}>{rowData?.noteId}</Text>),
    },
    {
      title: '笔记标题',
      dataIndex: 'title',
      // @ts-ignore
      render: ({ rowData }) => (<Link style={{ maxWidth: '180px' }} onClick={() => window.open(`https://www.xiaohongshu.com/explore/${rowData?.noteId}`)}>{rowData?.title}</Link>),
    },
    {
      title: '用户昵称',
      dataIndex: 'kolUserName',
      // @ts-ignore
      render: ({ rowData }) => (<Link style={{ maxWidth: '180px' }} onClick={() => window.open(`https://www.xiaohongshu.com/user/profile/${rowData?.kolUserId}`)}>{rowData?.kolUserName}</Link>),
    },
    {
      title: '笔记发布时间',
      dataIndex: 'createDate',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.createDate}</Text>),
    },
    {
      title: '笔记当前挂链',
      dataIndex: 'createDate',
      render({ rowData }) {
        const nameTag = rowData?.contentType === ContentType.goodsSellerOutdoor ? '商品' : '门店'
        const idTag = rowData?.contentType === ContentType.goodsSellerOutdoor ? 'spuId' : 'poiId'
        return <div>
          {rowData?.contentName && <Text ellipsis tooltip>{nameTag}名称:{rowData?.contentName}</Text>}
          {rowData?.contentId && <Text ellipsis tooltip>{idTag}:{rowData?.contentId}</Text>}
        </div>
      },
    },
    // {
    //   title: '合作编号',
    //   dataIndex: 'cooperateNo',
    //   render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.cooperateNo}</Text>),
    // },
    {
      title: '操作时间',
      dataIndex: 'chainTime',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.chainTime}</Text>),
    },
    {
      title: '社区审核状态',
      dataIndex: 'level',
    },
    {
      title: 'BK履约状态',
      dataIndex: 'extend',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ minWidth: '80px' }}>{rowData?.extend}</Text>),
    },
  ]))
  const cooperateTableColumns = computed<ThContent[]>(() => ([
    {
      title: '合作ID',
      dataIndex: 'cooperateId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.cooperateId}</Text>),
    },
    // {
    //   title: '合作编号',
    //   dataIndex: 'cooperateNo',
    //   render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.cooperateNo}</Text>),
    // },
    {
      title: '商家信息',
      dataIndex: 'sellerInfo',
      render: ({ rowData }) => (
        <div>
          {rowData?.sellerName && <Text block ellipsis tooltip >商家名称:{rowData?.sellerName}</Text>}
          {rowData?.sellerId && <Text block ellipsis tooltip >商家ID:{rowData?.sellerId}</Text>}
          {rowData?.campaignName && <Text block ellipsis tooltip >计划名称:{rowData?.campaignName}</Text>}
          {rowData?.campaignId && <Text block ellipsis tooltip >计划ID:{rowData?.campaignId}</Text>}
          {rowData?.businessType && <Text block ellipsis tooltip >行业:{rowData?.businessType}</Text>}
          {rowData?.campaignType && <Text block ellipsis tooltip >计划类型:{rowData?.campaignType}</Text>}
        </div>
      ),
    },
    {
      title: '博主信息',
      dataIndex: 'kolUserInfo',
      // @ts-ignore
      render: ({ rowData }) => (<Link style={{ maxWidth: '180px' }} onClick={() => window.open(`https://www.xiaohongshu.com/user/profile/${rowData?.kolUserId}`)}>{rowData?.kolUserName}</Link>),
    },
    {
      title: '合作开启时间',
      dataIndex: 'fulfillmentStartTime',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.fulfillmentStartTime}</Text>),
    },
    {
      title: '笔记发布周期',
      dataIndex: 'notePublishTime',
      render({ rowData }) {
        let notePublishTime
        if (rowData?.campaignType === '招募计划') {
          notePublishTime = rowData?.publishEndTime ? `${rowData?.publishStartTime}~${rowData?.publishEndTime}` : ''
        } else {
          notePublishTime = rowData?.publishEndTime ? `${rowData?.publishEndTime}之前` : '无时间限制'
        }
        return (<Text ellipsis tooltip style={{ maxWidth: '180px' }}>{notePublishTime}</Text>)
      }
    },
    {
      title: '合作可挂链周期',
      dataIndex: 'chainTime',
      render: ({ rowData }) => rowData?.chainEndTime && <Text ellipsis tooltip style={{ maxWidth: '180px' }}>{rowData?.chainStartTime}~{rowData?.chainEndTime}</Text>,
    },
    {
      title: '合作状态',
      dataIndex: 'fulfillmentStatus',
    },
  ]))
  return {
    noteTableColumns,
    cooperateTableColumns
  }
}
