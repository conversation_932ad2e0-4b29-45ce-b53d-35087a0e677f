<template>
  <div />
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { getPorchLoginUrl, getCurrentUrl } from '../../config/porch.config'
  import { loginSSO } from '../../services/user'

  export default defineComponent({
    name: 'Login',
    setup() {
      const route = useRoute()
      const router = useRouter()
      const ticket = route.query.ticket || ''
      if (!ticket) {
        window.location.href = getPorchLoginUrl()
        return
      }
      loginSSO(getCurrentUrl(), ticket)
        .then(() => {
          router.push({
            name: 'Index',
          })
        })
        .catch(err => {
          if (err.statusCode === 403) {
            router.push({
              name: 'Page403',
            })
            return
          }
          if (err.statusCode === 401) {
            window.location.href = getPorchLoginUrl()
            return
          }
          router.push({
            name: 'Page500',
          })
        })
    },
  })
</script>

<style></style>
