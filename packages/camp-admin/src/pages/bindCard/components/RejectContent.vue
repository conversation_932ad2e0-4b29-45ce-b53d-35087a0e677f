<template>
  <Text>审核驳回后，商家可在商家后台-财务模块修改信息，
    <span :style="{color: 'red' }">修改后运营需重新审核</span>
  </Text>
  <Form ref="formRef" :model="model" style="margin-top: 10px;">
    <FormItem :rules="rules" label="驳回原因" name="reason" style="align-items: baseline;">
      <TextArea v-model="model.reason" :fade="{ blankHighlight: true }" :max-length="100" />
    </FormItem>
  </Form>
</template>

<script lang="tsx" setup>
  import { ref, watch, reactive } from 'vue'
  import {
    Text,
    TextArea,
    Form2 as Form,
    FormItem2 as FormItem,
  } from '@xhs/delight'

  const rules = ref({
    trigger: ['blur', 'change'],
    required: true,
    validator: (rule, val, callback) => {
      if (!val) {
        callback(new Error('请填写拒绝原因'))
        return
      }
      callback()
    }
  })

  const emit = defineEmits(['confirm'])

  const model = reactive({
    reason: '',
  })

  const formRef = ref()

  watch(() => model, val => {
    formRef.value?.validate().then(() => {
      emit('confirm', val)
    })
  }, {
    deep: true
  })

</script>
