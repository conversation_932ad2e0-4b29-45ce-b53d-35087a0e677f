<template>
  <Modal
    v-model:visible="editVisible"
    title="异名绑卡开白"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Text>仅支持个体工商户总店/门店开白，开白后，商家需在商家后台-财务管理模块填写相应信息。填写完成后，<span style="color: red; font-weight: bold;">本地运营需要在hera后台完成信息审核</span>，平台运营审核通过后信息会推送平安银行审核，平安银行审核通过后，商家才可操作绑卡</Text>
    <Form v-if="editVisible" label-width="73px" style="margin-top: 10px;">
      <FormItem label="模板下载" style="align-items: baseline;">
        <Link target="_blank" href="https://fe-video-qc.xhscdn.com/fe-platform/be11cee7ff29865d107dc8f1c64e2ec20f517c10/异名绑卡开白模板.xlsx">异名绑卡开白模版.xlsx</Link>
        <Text style="font-size: 12px;">（据模板，若为门店账户开白则填写门店id，若为总店账户开白则填写sellerId）</Text>
      </FormItem>
      <FormItem label="批量上传">
        <div v-if="!result" class="upload-container">
          <Spinner :spinning="loading">
            <FileUploader
              v-model="fileList"
              accept=".xlsx"
              :uploading="loading"
              :percent="percent"
              @update:modelValue="complete"
            />
          </Spinner>
          <div v-if="failed" class="message">
            <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
            <Text color="text-description">信息识别失败，请重新上传</Text>
          </div>
        </div>
        <UploadResult v-if="result" text="操作" :result="result" />
      </FormItem>
    </Form>

  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, ref, watch, onUnmounted
  } from 'vue'
  import {
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Link,
    Spinner,
    toast2 as toast
  } from '@xhs/delight'
  import FileUploader from '@/components/upload-file/index.vue'
  import { ClaimResult } from '@/services/poiClaim'
  import { isFinished, computePercent } from '@/utils/longTask'
  import { postLongTask, getLongTask, GetLongTaskRes } from '@/services/longTaskv2'
  import { LONG_TASK_STATUS, LONG_TASK_NAME } from '@/constants/longTask'
  import UploadResult from '@/components/upload-result/index.vue'

  const props = defineProps<{
    visible: boolean
  }>()

  const emit = defineEmits(['update:visible'])

  const failed = ref(false)
  const loading = ref(false)
  let timer:number
  const result = ref<ClaimResult | undefined>()
  const fileList = ref<any[]>([])
  const percent = ref(0)

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
        window.clearTimeout(timer)
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
      window.clearTimeout(timer)
    }
  })

  onUnmounted(() => {
    window.clearTimeout(timer)
  })

  const complete = async () => {
    loading.value = true
    try {
      const { taskId } = await postLongTask({
        input: {
          file_url: fileList.value[0]?.downloadUrl,
          extra: undefined // extra 需要以 map 的形式进行传递
        },
        task_name: LONG_TASK_NAME.LIFE_SERVICE_SYNONYMS_BIND_CARD
      })
      // 心跳请求
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        percent.value = computePercent(res?.finishedCount, res?.totalCount) || percent.value
        if (isFinished(res?.status)) {
          loading.value = false
          if (res?.status === LONG_TASK_STATUS.FAIL) {
            failed.value = true
            toast.danger('上传失败，请重新上传')
            return
          }
          failed.value = false
          const { fileUrl = '', extraJson = '{}' } = res?.result
          const extra = JSON.parse(extraJson)
          result.value = {
            failReasonPath: fileUrl,
            successNum: extra?.success_count ?? 0,
            failNum: extra?.failed_count ?? 0
          }
          return
        }
        timer = window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error: any) {
      loading.value = false
      failed.value = true
      toast.danger(error?.message || '服务器异常，请重新操作')
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
</style>
