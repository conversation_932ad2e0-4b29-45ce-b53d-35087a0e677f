<template>
  <Tag :color="colorMap[rowData[dataIndex]]" size="small">{{ statusDescMap[rowData[dataIndex]] }}</Tag>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import { Tag } from '@xhs/delight'

  defineProps({
    rowData: {
      type: Object,
      default() {
        return {}
      }
    },
    statusDescMap: {
      type: Object,
      default() {
        return {}
      }
    },
    colorMap: {
      type: Object,
      default() {
        return {}
      }
    },
    dataIndex: {
      type: String as PropType<string>,
      default: ''
    },
  })

</script>
