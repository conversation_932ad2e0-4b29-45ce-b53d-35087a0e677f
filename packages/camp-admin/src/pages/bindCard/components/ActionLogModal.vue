<template>
  <Modal
    v-model:visible="editVisible"
    title="操作日志"
    size="large"
    @cancel="editVisible = false"
    @confirm="editVisible = false"
  >
    <Space class="ultra-list-content" v-bind="spaceProps">
      <Table size="large" :columns="tableColumns" :data-source="listSource" :loading="loading">
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
      </Table>
      <Pagination
        v-model="baseHumpParams.pageNum"
        v-model:pageSize="baseHumpParams.pageSize"
        :total="total"
        @change="fetchList(false)"
      />
    </Space>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, watch } from 'vue'
  import {
    Text,
    Modal,
    Table,
    Pagination,
    Space
  } from '@xhs/delight'
  import {
    queryOperationLog,
  } from '../list/service'
  import {
    spaceProps, useTableColumns
  } from '../../../composables/use-props'
  import { useList } from '../../../composables/use-list'
  import { logScheme } from '../scheme'

  const props = defineProps<{
    visible: boolean
    logContentRowData: any
  }>()

  const emit = defineEmits(['update:visible'])

  const scheme = computed(() => logScheme)

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const { tableColumns } = useTableColumns(scheme)

  const {
    listSource,
    total,
    loading,
    baseHumpParams,
    filterParams,
    fetchList
  } = useList(scheme, queryOperationLog, true)

  watch(() => props.visible, v => {
    if (v) {
      filterParams.value = {
        sellerId: props.logContentRowData.sellerId,
        accountId: props.logContentRowData.accountId,
      }
      fetchList()
    }
  }, {
    immediate: true
  })

</script>

<style lang="stylus" scoped>
.ultra-list-content
  width 100%
  background #FFFFFF
  /deep/.ultra-material-toolbar-wrap
    padding 0
  /deep/.camp-table-action>.table-action-cell>.d-space
    align-items flex-start
</style>
