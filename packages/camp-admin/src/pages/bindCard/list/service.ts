import http from '@/utils/http'
import dayjs from 'dayjs'

// 运营审核状态。1：未推送；2：待审核；3：审核通过；4：审核驳回'
export enum OperatorAuditStatusEnum {
  UNPUSHED = 1,
  AWAIT_REVIEW = 2,
  REVIEW_PASSED = 3,
  REVIEW_REFUSED = 4
}

export const OPERATOR_AUDIT_STATUS_DESC_MAP = {
  [OperatorAuditStatusEnum.UNPUSHED]: '-',
  [OperatorAuditStatusEnum.AWAIT_REVIEW]: '待审核',
  [OperatorAuditStatusEnum.REVIEW_PASSED]: '审核通过',
  [OperatorAuditStatusEnum.REVIEW_REFUSED]: '审核驳回',
}
export const OPERATOR_AUDIT_COLOR_MAP = {
  [OperatorAuditStatusEnum.UNPUSHED]: 'grey',
  [OperatorAuditStatusEnum.AWAIT_REVIEW]: 'orange',
  [OperatorAuditStatusEnum.REVIEW_PASSED]: 'green',
  [OperatorAuditStatusEnum.REVIEW_REFUSED]: 'red',
}

// 平安银行运营审核状态。1：未推送；2：待审核；3：审核通过；4：审核驳回',
export enum BankAuditStatusEnum {
  UNPUSHED = 1,
  AWAIT_REVIEW = 2,
  REVIEW_PASSED = 3,
  REVIEW_REFUSED = 4
}

export const BANK_AUDIT_STATUS_DESC_MAP = {
  [BankAuditStatusEnum.UNPUSHED]: '未审核',
  [BankAuditStatusEnum.AWAIT_REVIEW]: '审核中',
  [BankAuditStatusEnum.REVIEW_PASSED]: '审核通过',
  [BankAuditStatusEnum.REVIEW_REFUSED]: '审核驳回',
}

export const BANK_AUDIT_COLOR_MAP = {
  [BankAuditStatusEnum.UNPUSHED]: 'grey',
  [BankAuditStatusEnum.AWAIT_REVIEW]: 'orange',
  [BankAuditStatusEnum.REVIEW_PASSED]: 'green',
  [BankAuditStatusEnum.REVIEW_REFUSED]: 'red',
}

interface ISynonymsBindCard{
  // 商家ID
  sellerId: string
  // 门店ID
  shopId: string
  // 平台运营审核状态
  operatorAuditStatus: OperatorAuditStatusEnum
  // 平安银行审核状态
  bankAuditStatus: BankAuditStatusEnum
  pageNum: number
  pageSize: number
}
interface RSynonymsBindCard{
  // 商家ID
  sellerId: string
  sellerName: string
  // 门店ID
  shopId: string
  shopName: string
  accountId: string
  bindCardPersonName: string
  certificationImage: string
  // 平台运营审核状态
  operatorAuditStatus: OperatorAuditStatusEnum
  // 平安银行审核状态
  bankAuditStatus: BankAuditStatusEnum
}

export enum resultEnum {
  AGREE = 'AGREE',
  REJECT = 'REJECT'
}

interface IOperationLog {
  sellerId: string
  accountId: string
  pageNum: number
  pageSize: number
}
interface ROperationLog {
  bizName: string
  operateType: string
  operator: string
  operateTime: string
}

interface IBindCardAudit {
  result: resultEnum
  reason?: string
  sellerId: string
  shopId: string
  accountId: string
}

// 查询异名绑卡信息接口
export const querySynonymsBindCard = async (params: ISynonymsBindCard): Promise<{
  total: number
  listSource:RSynonymsBindCard[]
}> => {
  const res = await http.get<{total: number; synonymsBindCardInfoList: RSynonymsBindCard[]}>('/api/hera/localLife/poi/finance/synonyms_bind_card', { params, transform: false })
  return {
    total: res?.total || 0,
    listSource: res?.synonymsBindCardInfoList
  }
}

// 审核异名绑卡申请
export const applySynonymsBindCardAudit = (params: IBindCardAudit) => http.post('/api/hera/localLife/poi/finance/synonyms_bind_card/audit', params, { transform: false })

// 操作日志查询
// export const queryOperationLog = (params: IOperationLog): Promise<ROperationLog> => http.get('/api/hera/localLife/poi/finance/synonyms_bind_card/log', { params, transform: false })
export const queryOperationLog = async (params: IOperationLog): Promise<{
  total: number
  listSource:ROperationLog[]
}> => {
  const res = await http.get<{total: number;bizLogList: ROperationLog[]}>('/api/hera/localLife/poi/finance/synonyms_bind_card/log', { params, transform: false })
  return {
    total: res?.total || 0,
    listSource: res?.bizLogList.map(item => ({
      ...item,
      operateTime: dayjs(item.operateTime).format('YYYY-MM-DD HH:mm:ss')
    }))
  }
}
