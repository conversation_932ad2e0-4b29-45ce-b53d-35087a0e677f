<template>
  <Space class="ultra-list-container" v-bind="spaceProps">
    <Space class="ultra-list-header" v-bind="spaceProps">
      <PageHeader title="异名绑卡管理" />
    </Space>
  </Space>
  <Space class="ultra-list-container" v-bind="spaceProps">
    <Space class="ultra-list-header" v-bind="spaceProps">
      <OutlineFilter v-model="filterParams" :config="filterConfig" />
    </Space>
  </Space>

  <div class="ultra-list-body">
    <Space class="ultra-list-content" v-bind="spaceProps">
      <Toolbar :config="toolBarConfig" />
      <Table size="large" :columns="tableColumns" :data-source="listSource" :loading="loading">
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
      </Table>
      <Pagination
        v-model="baseHumpParams.pageNum"
        v-model:pageSize="baseHumpParams.pageSize"
        :total="total"
        @change="fetchList(false)"
      />
    </Space>
  </div>
  <ImportAreaQualModal v-model:visible="importVisible" />
  <ActionLogModal v-if="actionLogVisible" v-model:visible="actionLogVisible" :log-content-row-data="logContentRowData" />
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import {
    Space, Table, Pagination, Text,
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import {
    spaceProps, useFilter, useToolBar, useTableColumns
  } from '../../../composables/use-props'
  import {
    querySynonymsBindCard, applySynonymsBindCardAudit, resultEnum
  } from './service'
  import { useList } from '../../../composables/use-list'
  import { scheme as schemeGeneral } from '../scheme'
  import ImportAreaQualModal from '../components/ImportAreaQualModal.vue'
  import ActionLogModal from '../components/ActionLogModal.vue'

  const scheme = computed(() => schemeGeneral)

  const {
    listSource,
    total,
    loading,
    baseHumpParams,
    filterParams,
    fetchList
  } = useList(scheme, querySynonymsBindCard, true)

  const importVisible = ref(false)
  const actionLogVisible = ref(false)

  function handleOpenWhiteList() {
    importVisible.value = true
  }

  const { filterConfig } = useFilter(scheme, fetchList)
  const { toolBarConfig } = useToolBar(scheme, [handleOpenWhiteList])

  const handleAgree = rowData => {
    const params = {
      result: resultEnum.AGREE,
      sellerId: rowData?.sellerId,
      shopId: rowData?.shopId,
      accountId: rowData?.accountId,
    }
    applySynonymsBindCardAudit(params).then(() => {
      fetchList()
    })
  }

  const logContentRowData = ref()
  const handleLog = rowData => {
    logContentRowData.value = rowData
    actionLogVisible.value = true
  }
  const { tableColumns } = useTableColumns(scheme, [handleAgree, null, handleLog])

  fetchList()

</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  margin-top 24px
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
  /deep/.camp-table-action>.table-action-cell>.d-space
    align-items flex-start
</style>
../scheme
