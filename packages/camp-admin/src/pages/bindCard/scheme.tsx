import Image from '@xhs/delight-material-life-image'
import { Text, toast2 as toast } from '@xhs/delight'
import { dictMaterials as sharedDictMaterials } from 'shared/dictMaterials/camp-admin/activityLife/index'
import { h, ref } from 'vue'
import AuditStatus from './components/AuditStatus.vue'
import RejectContent from './components/RejectContent.vue'
import {
  OperatorAuditStatusEnum, BankAuditStatusEnum,
  OPERATOR_AUDIT_STATUS_DESC_MAP, BANK_AUDIT_STATUS_DESC_MAP,
  OPERATOR_AUDIT_COLOR_MAP, BANK_AUDIT_COLOR_MAP,
  applySynonymsBindCardAudit, resultEnum, querySynonymsBindCard
} from './list/service'
import { useList } from '../../composables/use-list'

export interface FilterPayload {
  condition_type: number
  data: string
}

const {
  TableColumn
} = sharedDictMaterials

const {
  fetchList
} = useList(null, querySynonymsBindCard, true)

const handleReject = (rowData, rejectText) => {
  if (!rejectText.value) {
    toast.warning('请填写驳回原因')
  } else {
    const params = {
      result: resultEnum.REJECT,
      reason: rejectText.value,
      sellerId: rowData?.sellerId,
      shopId: rowData?.shopId,
      accountId: rowData?.accountId,
    }
    applySynonymsBindCardAudit(params).then(() => {
      fetchList()
      rejectText.value = ''
      toast.success('驳回成功')
    })
  }
}

export const scheme = {
  filterScheme: [
    {
      name: 'sellerId',
      defaultValue: '',
      width: 100,
      component: {
        is: 'Input',
        props: {
          prefix: '商家ID',
          placeholder: '请输入商家ID',
          clearable: true,
        },
      },
    },
    {
      name: 'shopId',
      defaultValue: '',
      width: 100,
      component: {
        is: 'Input',
        props: {
          prefix: '门店ID',
          placeholder: '请输入门店ID',
          clearable: true,
        },
      },
    },
    {
      name: 'operatorAuditStatus',
      width: 100,
      component: {
        is: 'Select',
        props: {
          clearable: true,
          prefix: '平台运营审核状态',
          options: [
            { label: OPERATOR_AUDIT_STATUS_DESC_MAP[OperatorAuditStatusEnum.UNPUSHED], value: OperatorAuditStatusEnum.UNPUSHED },
            { label: OPERATOR_AUDIT_STATUS_DESC_MAP[OperatorAuditStatusEnum.AWAIT_REVIEW], value: OperatorAuditStatusEnum.AWAIT_REVIEW },
            { label: OPERATOR_AUDIT_STATUS_DESC_MAP[OperatorAuditStatusEnum.REVIEW_PASSED], value: OperatorAuditStatusEnum.REVIEW_PASSED },
            { label: OPERATOR_AUDIT_STATUS_DESC_MAP[OperatorAuditStatusEnum.REVIEW_REFUSED], value: OperatorAuditStatusEnum.REVIEW_REFUSED },
          ]
        },
      },
    },
    {
      name: 'bankAuditStatus',
      width: 100,
      component: {
        is: 'Select',
        props: {
          clearable: true,
          prefix: '平安银行审核状态',
          options: [
            { label: BANK_AUDIT_STATUS_DESC_MAP[BankAuditStatusEnum.UNPUSHED], value: BankAuditStatusEnum.UNPUSHED },
            { label: BANK_AUDIT_STATUS_DESC_MAP[BankAuditStatusEnum.AWAIT_REVIEW], value: BankAuditStatusEnum.AWAIT_REVIEW },
            { label: BANK_AUDIT_STATUS_DESC_MAP[BankAuditStatusEnum.REVIEW_PASSED], value: BankAuditStatusEnum.REVIEW_PASSED },
            { label: BANK_AUDIT_STATUS_DESC_MAP[BankAuditStatusEnum.REVIEW_REFUSED], value: BankAuditStatusEnum.REVIEW_REFUSED },
          ]
        },
      },
    },
  ],
  toolbarActions: [{ text: '异名绑卡开白' }],
  tableColumnScheme: [
    {
      field: 'sellerId',
      title: '商家ID',
      dataIndex: 'sellerId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="sellerId" />
    },
    {
      field: 'sellerName',
      title: '商家名称',
      dataIndex: 'sellerName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="sellerName" />
    },
    {
      field: 'shopId',
      title: '门店ID',
      dataIndex: 'shopId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="shopId" />
    },
    {
      field: 'shopName',
      title: '门店名称',
      dataIndex: 'shopName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="shopName" />
    },
    {
      field: 'accountId',
      title: '账户ID',
      dataIndex: 'accountId',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="accountId" />
    },
    {
      field: 'bindCardPersonName',
      title: '绑卡人姓名',
      dataIndex: 'bindCardPersonName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="bindCardPersonName" />
    },

    {
      field: 'certificationImage',
      title: '平安银行商户关系证明',
      dataIndex: 'certificationImage',
      minWidth: 88,
      render: ({ rowData }) => (rowData?.certificationImage ? <Image src={rowData?.certificationImage} style={{ width: '80px', height: '60px', borderRadius: '4px' }} transfer="imageView2" width={80 * 3} height={60 * 3} preview /> : <Text>-</Text>)
    },
    {
      field: 'operatorAuditStatus',
      title: '平台运营审核状态',
      dataIndex: 'operatorAuditStatus',
      minWidth: 88,
      render: ({ rowData }) => <AuditStatus rowData={rowData} dataIndex="operatorAuditStatus" statusDescMap={OPERATOR_AUDIT_STATUS_DESC_MAP} colorMap={OPERATOR_AUDIT_COLOR_MAP}/>
    },
    {
      field: 'bankAuditStatus',
      title: '平安银行审核状态',
      dataIndex: 'bankAuditStatus',
      minWidth: 88,
      render: ({ rowData }) => <AuditStatus rowData={rowData} dataIndex="bankAuditStatus" statusDescMap={BANK_AUDIT_STATUS_DESC_MAP} colorMap={BANK_AUDIT_COLOR_MAP}/>
    },
  ],
  tableActionScheme: [
    {
      text: '审核通过',
      visible: rowData => rowData?.operatorAuditStatus === OperatorAuditStatusEnum.AWAIT_REVIEW,
      popConfirmProps: () => ({
        size: 350,
        slots: {
          title: () => <Text type={'h6'} bold>确认绑卡信息审核通过？</Text>,
          description: () => <Text>审核通过代表您<span style={{ color: 'red' }}>已核实商户和资金管理方（绑卡人）之间的关系，如出现风险，平台将承担相应资金风险损失。</span></Text>,
        },
      }),
    },
    {
      text: '审核驳回',
      visible: rowData => rowData?.operatorAuditStatus === OperatorAuditStatusEnum.AWAIT_REVIEW,
      popConfirmProps: rowData => {
        const rejectText = ref('')
        const returnObj = {
          size: 500,
          slots: {
            title: () => <Text type={'h6'} bold>确认信息审核驳回？</Text>,
            description: () => h(<RejectContent/>, {
              onConfirm: val => {
                rejectText.value = val.reason
              }
            })
          },
          onConfirm: () => {
            handleReject(rowData, rejectText)
          }
        }
        return returnObj
      }
    },
    {
      text: '操作日志',
    },
  ],
}

export const logScheme = {
  tableColumnScheme: [
    {
      field: 'bizName',
      title: '操作对象',
      dataIndex: 'bizName',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="bizName" />
    },
    {
      field: 'operateType',
      title: '操作项目',
      dataIndex: 'operateType',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="operateType" />
    },
    {
      field: 'operator',
      title: '操作人',
      dataIndex: 'operator',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="operator" />
    },
    {
      field: 'operateTime',
      title: '操作时间',
      dataIndex: 'operateTime',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="operateTime" />
    },
  ],

}
