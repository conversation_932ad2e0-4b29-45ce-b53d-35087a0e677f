import { computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { toast2 as toast, Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const pageHeaderProps:PageHeaderProps = { // 页头的配置
  type: 'combination',
  items: [
    { title: 'POI黑名单解除' },
    { title: '黑名单列表' },
  ],
}

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'poiInfo',
        label: 'POI名称/ID',
        component: {
          props: {
            placeholder: '请输入POI名称/ID',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '主要操作',
        onClick: () => {
          toast.warning('主要操作')
        },
      },
      {
        text: '次要操作',
        onClick: () => {
          toast.warning('次要操作')
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = ({
  handleDelete,
}: {
  handleDelete: (item:any) => void
}) => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: 'POI ID',
      dataIndex: 'poiId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.poiId}</Text>),
    },
    {
      title: '门店名称',
      dataIndex: 'poiName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ width: '250px' }}>{rowData?.poiName}</Text>),
    },
    {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [{
            text: '删除',
            popConfirmProps: {
              outsideCloseable: true,
              placement: 'top-end',
              title: '确认删除',
              description: `您确认是否要删除${rowData?.poiName}`,
              width: 250,
              onConfirm: () => handleDelete(rowData),
            },
          }],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
