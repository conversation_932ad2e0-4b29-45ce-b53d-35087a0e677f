import http from '@/utils/http'
import { transformParams, transformRes } from './dtos'
import { GeneralActivityStatusEnum } from '../list/service'

export enum ActivityStatusEnum {
  UNPUBLISHED = 1, // 已发布待生效
  PUBLISHED = 3, // 生效中
  OFFLINE = 4// 已下线
}
export const activityStatusMap = {
  [ActivityStatusEnum.UNPUBLISHED]: '已发布待生效',
  [ActivityStatusEnum.PUBLISHED]: '生效中',
  [ActivityStatusEnum.OFFLINE]: '已下线',
}

export enum ActivityType {
  // 通兑活动
  'UNIVERSAL_EXCHANGE_ACTIVITIES' = 'UNIVERSAL_EXCHANGE_ACTIVITIES',
  // 普通主题活动
  'GENERAL_THEME_ACTIVITIES' = 'GENERAL_THEME_ACTIVITIES',
}

type ImgType = {
  url: string
  width: number
  height: number
}

export interface IActivity {
  activity_id?: string
  activity_name?: string
  activity_status: GeneralActivityStatusEnum
  already_fill_product_extra_info: number
  seller_info?: {
    invitation_short_desc: string
    area_list: string[]
  }
  activity_desc?: {
    activity_main_images: {url: string}[]
    desc: string
  }
  product_extra_info?: {
    product_main_images: {url: string}[]
    product_long_images: {url: string}[]
  }
  main_item_id?: string
}

// 活动信息响应体，可选参数参照上边的IActivity
export interface GetGeneralActivityInfoRes {
  activity_id: string
  activity_name?: string
  activity_time:{
    start_time: string
    end_time: string
  }
  city_list: string[]
  team: string
  activity_status?: GeneralActivityStatusEnum
  create_time: string
  activity_desc:{
    act_venue_config: {
      activity_icon: ImgType[]
      activity_background_img: ImgType[]
      activity_slogan: string // 新增：活动会场入口slogn
      activity_entrance_link: string // 新增：活动会场入口跳转链接
      activity_item_icon: ImgType[] // 活动会场商品卡icon
    }
  }
  item_info:{
    item_ids: string[]
    item_desc: string
  }
}

// 活动创建/编辑参数
export interface CreateActivityParams {
  activity_id: string
  activity_name: string
  activity_time:{
    start_time: string
    end_time: string
  }
  team: string
  city_list: string[]
  activity_status: GeneralActivityStatusEnum
  create_time: string
  activity_desc:{
    act_venue_config: {
      activity_icon: ImgType[]
      activity_background_img: ImgType[]
      activity_slogan: string // 活动会场入口slogn
      activity_entrance_link: string // 活动会场入口跳转链接
      activity_item_icon: ImgType[]
    }
  }
  item_info:{
    item_ids: string[]
    item_desc: string
  }
}

/**
 * 获取活动信息
 * @param params
 * @returns
 */
export const getActivityInfo = async params => {
  const res = await http.get<GetGeneralActivityInfoRes>('/api/hera/localLife/general/activity/detail', { params, transform: false })
  return transformRes(res)
}

/**
 * 创建/编辑活动
 * @param data
 * @returns
 */
export const createActivity = data => {
  const transformedData: CreateActivityParams = transformParams(data)
  return http.post('/api/hera/localLife/general/activity/submit', transformedData, { transform: false })
}
