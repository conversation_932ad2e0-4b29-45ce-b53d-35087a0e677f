import { ref, Ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'
import { Router } from 'vue-router'
import { CreateActivityParams, ActivityType } from './service'

export const useForm = (formRef: Ref<any>) => {
  const subBtnLoading = ref(false)
  const model = ref({
    activity_time: {
      start_time: '',
      end_time: '',
    }
  } as unknown as CreateActivityParams)

  const submit = async (router: Router, createActivity) => { // 表单提交方法
    formRef.value.validate()
      .then(async () => {
        try {
          subBtnLoading.value = true
          const res = await createActivity(model.value)
          if (!res?.activityId) {
            router.push({
              name: 'activityList',
              query: {
                activeTab: ActivityType.GENERAL_THEME_ACTIVITIES,
              }
            })
          } else {
            toast.success({ content: '编辑活动成功' })
            router.push({
              name: 'activityList',
              query: {
                activeTab: ActivityType.GENERAL_THEME_ACTIVITIES,
              }
            })
          }
        } catch (err: any) {
          // eslint-disable-next-line no-console
          console.error(err)
        } finally {
          subBtnLoading.value = false
        }
      }).catch(err => {
        const errList: any = Object.values(err) || []
        if (errList[0]) {
          toast.warning(errList[0][0]?.message || '请检查必填字段')
        }
      })
  }

  return {
    model,
    subBtnLoading,
    submit,
  }
}
