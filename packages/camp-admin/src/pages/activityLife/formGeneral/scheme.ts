import { computed, Ref } from 'vue'
import { IActivity, CreateActivityParams } from './service'

export const useFormItemLayout = (activity: Ref<IActivity>, model: Ref<CreateActivityParams>) => {
  const schemeInfo = computed(() => ({
    layoutScheme: [
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '活动基本信息',
          props: {
            type: 'h6',
            bold: true
          }
        },
        formItemList: [
          {
            filed: 'activity_name',
            componentProps: {
              is: 'ActivityNameInput',
              props: {}
            }
          },
          {
            // 是否为正式活动
            filed: 'test_mode',
            componentProps: {
              is: 'TestModeRadio',
            }
          },
        ]
      },
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '',
          props: {
            type: 'h6',
            bold: true
          }
        },
        formItemList: [
          {
            // 活动时间
            filed: 'activity_time',
            componentProps: {
              is: 'ActivityTimePicker',
            }
          }
        ]
      },
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '',
          props: {
            type: 'h6',
            bold: true
          }
        },
        formItemList: [
          {
            // 批量导入商品id
            filed: 'item_info',
            componentProps: {
              is: 'BatchImportProductId',
              props: {
                activityTimeValue: model.value.activity_time,
              }
            }
          }
        ]
      },
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '',
          props: {
            type: 'h6',
            bold: true
          },
          visible: info => !!info?.activity_id
        },
        formItemList: [
          {
            // 查看已经导入商品id
            filed: 'download_item',
            componentProps: {
              is: 'ViewImportedProductId',
            }
          }
        ],
      },
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '',
          props: {
            type: 'h6',
            bold: true
          }
        },
        formItemList: [
          {
            // 选择活动所属团队
            filed: 'team',
            componentProps: {
              is: 'ActivityTeamSelect',
              props: {
                readonly: false
              }
            }
          }
        ]
      },
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '',
          props: {
            type: 'h6',
            bold: true
          }
        },
        formItemList: [
          {
            // 选择活动所属城市
            filed: 'city_list',
            componentProps: {
              is: 'ActivityCitySelect',
              props: {
                readonly: false
              }
            }
          }
        ]
      },
      {
        groupTitleInfo: {
          is: 'Text',
          slot: '活动介绍',
          props: {
            type: 'h6',
            bold: true
          }
        },
        formItemList: [
          {
            // 活动会场入口icon
            filed: 'activity_desc.act_venue_config.activity_icon',
            componentProps: {
              is: 'EnterIconImageUpload',
              props: {
                readonly: false,
                required: true
              }
            }
          },
          {
            // 活动会场入口底色图
            filed: 'activity_desc.act_venue_config.activity_background_img',
            componentProps: {
              is: 'EnterBackImageUpload',
              props: {
                readonly: false,
                required: true
              }
            }
          },
          {
            // 活动会场入口slogn
            filed: 'activity_desc.act_venue_config.activity_slogan',
            componentProps: {
              is: 'ActivityEnterSlogn',
              props: {
                readonly: false,
                required: true
              }
            }
          },
          {
            // 活动会场入口跳转链接
            filed: 'activity_desc.act_venue_config.activity_entrance_link',
            componentProps: {
              is: 'ActivityEnterLink',
              props: {
                readonly: false,
                required: true
              }
            }
          },
          {
            // 活动会场商品卡icon
            filed: 'activity_desc.act_venue_config.activity_item_icon',
            componentProps: {
              is: 'ActivityItemIconImageUpload',
              props: {
                readonly: false,
                required: false
              }
            }
          },
        ]
      },
    ]
  }))

  return {
    schemeInfo,
  }
}
