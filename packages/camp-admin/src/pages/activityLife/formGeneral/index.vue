<template>
  <Space ref="FormContainer" class="ultra-form-container" v-bind="spaceProps">
    <Space class="ultra-form-header" v-bind="spaceProps">
      <PageHeader type="breadcrumb" :items="[{ title: '交易活动管理', to: { name: 'activityList' } }, { title: `${statusText}活动` }]" />
    </Space>
    <Space v-loading="formLoading" class="ultra-form-body">
      <Space class="ultra-form-content" justify="center">
        <Space class="ultra-form-items" v-bind="spaceProps">
          <Space v-if="activityId" align="center">
            <Text type="h5" bold>活动详情</Text>
            <Text color="primary" bold>状态：{{ activityStatusDesc }}</Text>
          </Space>
          <Form ref="formRef" :model="model" label-width="260px" style="min-width: 990px">
            <FormContentEnhance v-for="(item, index) in layoutScheme" :key="index" v-bind="item" :readonly="readonly" />
          </Form>
        </Space>
      </Space>
    </Space>
    <Space class="ultra-form-footer" block justify="center" :style="{ width: `${footerWidth}px`, marginLeft: `${footerRight}px` }">
      <template v-if="!readonly || editAll">
        <Button :loading="actionLoading" @click="handleAction('cancel')">取消</Button>
        <Button type="primary" :loading="subBtnLoading" @click="handleAction('submit')">{{ submitText }}</Button>
      </template>
      <template v-else>
        <Button type="primary" :loading="editLoading" @click="handleAction('editAll')">编辑活动</Button>
      </template>
    </Space>
  </Space>
</template>
<script setup lang="tsx">
  import {
    ref, onMounted, onUnmounted, computed, h, nextTick
  } from 'vue'
  import {
    Space, Button, Form2 as Form, vLoading, Text, Modal,
  } from '@xhs/delight'
  import { useRoute, useRouter } from 'vue-router'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import { get, set, cloneDeep } from 'lodash'
  import { spaceProps } from '../../../composables/use-props'
  import { useForm } from './useForm'
  import { useFormItemLayout } from './scheme'
  import { dictMaterials } from '../dictMaterials/index'
  import {
    IActivity,
    CreateActivityParams,
    activityStatusMap,
    getActivityInfo,
    createActivity,
    ActivityType
  } from './service'
  import { GeneralActivityStatusEnum } from '../list/service'

  const route = useRoute()
  const router = useRouter()

  const readonly = ref(false)
  const editAll = ref(false)

  const activityInfo = ref<IActivity>({} as IActivity)

  const activityId = computed(() => route.params?.id as string)

  const statusText = computed(() => (activityId.value ? '编辑' : '创建'))
  const submitText = computed(() => {
    if (activityId.value && activityInfo.value?.activity_status !== GeneralActivityStatusEnum.OFFLINE) {
      return '提交'
    }
    return '发布'
  })

  const activityStatusDesc = computed(() => activityStatusMap[activityInfo.value?.activity_status])

  const formRef = ref() // 表单的Ref对象

  const formLoading = ref(false)
  const actionLoading = ref(false)
  const editLoading = ref(false)

  const {
    model, // 表单数据对象
    subBtnLoading,
    submit, // 提交方法
  } = useForm(formRef)

  const { schemeInfo } = useFormItemLayout(activityInfo, model)

  // 行业元信息
  const layoutScheme = computed(() => schemeInfo.value.layoutScheme)

  const FormDictItem = ({ item }) => {
    const Cmp = dictMaterials[item?.componentProps?.is]
    const modelVal = computed({
      get: () => get(model.value, item.filed),
      set: val => set(model.value, item.filed, val),
    })
    const finalReadOnly = ref(readonly.value || item?.componentProps?.props?.readonly)
    return <Cmp v-model={modelVal.value} {...item.componentProps?.props} readonly={finalReadOnly.value} formRef={formRef} editAll={editAll.value}/>
  }

  const FormContentEnhance = ItemProps => {
    const {
      groupTitleInfo,
      formItemList,
    } = ItemProps
    const GroupTitleCmp = dictMaterials[groupTitleInfo?.is]

    if (groupTitleInfo?.visible) {
      if (!groupTitleInfo.visible(activityInfo.value)) {
        return null
      }
    }

    return (
    <>
      <GroupTitleCmp {...groupTitleInfo?.props} >{groupTitleInfo?.slot}</GroupTitleCmp>
      {formItemList?.map(item => (<FormDictItem item={item} />))}
    </>
  )
  }

  async function fetchActivityInfo() {
    if (activityId.value) {
      formLoading.value = true
      try {
        const res = await getActivityInfo({ activity_id: activityId.value })
        activityInfo.value = cloneDeep(res)
        model.value = {
          ...model.value,
          ...res
        } as unknown as CreateActivityParams
        readonly.value = true
      } catch (error) {
        console.error(error)
      }
      formLoading.value = false
    }
  }

  function handleCancel() {
    Modal.warning({
      title: '确认离开么',
      content: h(Text, {}, {
        default: () => `活动${statusText.value}未提交，你所填写的信息将不会保存`
      }),
      cancelText: `放弃${statusText.value}`,
      confirmText: '我再想想',
      onConfirm: close => {
        close?.()
      },
      onCancel(close) {
        close?.()
        router.push({ name: 'activityList', query: { activeTab: ActivityType.GENERAL_THEME_ACTIVITIES, } })
      }
    })
  }

  function handleSubmit() {
    submit(router, createActivity)
  }

  /**
   * 分发动作
   * @param actionType
   */
  function handleAction(actionType) {
    switch (actionType) {
      case 'cancel':
        handleCancel()
        break
      case 'submit':
        handleSubmit()
        break
      case 'editAll':
        readonly.value = false
        editAll.value = true
        break
      default:
        break
    }
  }

  /** 吸顶按钮模块的宽度自适应获取Start */
  const FormContainer = ref() // 容器的Ref对象
  const footerWidth = ref() // 吸底的按钮区域宽度
  const footerRight = ref() // 吸底的按钮区域宽度

  const handleResize = () => { // 处理布局的自适应
    footerWidth.value = FormContainer.value?.$el?.clientWidth
    footerRight.value = FormContainer.value.$el?.getBoundingClientRect()?.right - FormContainer.value?.$el?.clientWidth
  }

  onMounted(() => {
    fetchActivityInfo()
    handleResize() // 初始化
    window.addEventListener('resize', handleResize)
    nextTick(() => {
      const scrollElement = document.querySelector('#scroll-page-container')
      if (scrollElement) {
        scrollElement.scrollTop = 0
      }
    })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
/** 吸顶按钮模块的宽度自适应获取End */
</script>

<style lang="stylus" scoped>
.ultra-form-container
  width 100%
  padding-bottom 100px
  &.d-space-vertical
    gap 24px

.ultra-form-body
  background #FFFFFF
  border-radius 8px
  padding 24px
.ultra-form-content
  width 100%
.ultra-form-footer
  position fixed
  bottom 0
  left 0
  background-color #fff
  z-index 99
  border-top 1px solid #e8e8e8
  padding 18px 0 22px
  /deep/.d-button.d-button-default
    width 108px
.qrcode-desc
  display flex
  flex-direction column
  gap 10px
  margin 0 auto
  .item
    margin 0 auto
</style>
