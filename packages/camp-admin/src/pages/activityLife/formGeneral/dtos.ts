import { cloneDeep } from 'lodash'

export const transformParams = params => {
  const formValue = cloneDeep(params)
  const { item_info } = formValue
  if (item_info?.import_item_ids?.length) {
    item_info.item_ids = [...new Set([
      ...(item_info.item_ids || []),
      ...(item_info?.import_item_ids.map(item => item.item_id) || [])])]
  }
  delete item_info?.import_item_ids
  if (formValue.activity_desc?.act_venue_config?.activity_item_icon) {
    formValue.activity_desc.act_venue_config.activity_item_icon = formValue.activity_desc?.act_venue_config?.activity_item_icon.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.act_venue_config?.activity_icon) {
    formValue.activity_desc.act_venue_config.activity_icon = formValue?.activity_desc.act_venue_config?.activity_icon.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.act_venue_config?.activity_background_img) {
    formValue.activity_desc.act_venue_config.activity_background_img = formValue?.activity_desc.act_venue_config?.activity_background_img.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.act_venue_config?.activity_item_icon) {
    formValue.activity_desc.act_venue_config.activity_item_icon = formValue?.activity_desc.act_venue_config?.activity_item_icon.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  return formValue
}

export const transformRes = res => res
