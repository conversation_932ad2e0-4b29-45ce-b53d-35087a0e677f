import http from '@/utils/http'
import dayjs from 'dayjs'

import { ActivityStatusEnum, TestModeEnum } from 'shared/dictMaterials/camp-admin/activityLife/type'
import { Activity, CreatorTypeEnum } from 'shared/scheme/camp-admin/activityLife/type'

export enum GeneralActivityStatusEnum {
  UNPUBLISHED = 1,
  PUBLISHED = 3,
  OFFLINE = 4
}

export const enum IsPendingProcessEnum {
  NO = 0,
  YES = 1,
  ALL = 2
}

interface ActivityListParams {
  page_num: number
  page_size: number
  activity_id?: string
  activity_name?: string
  creator?: string
  test_mode?: TestModeEnum
  wait_staff_approve?: IsPendingProcessEnum // 是否待运营审核：是/否
  creator_type?: CreatorTypeEnum // 组织方：平台运营、代运营服务商
}

interface ListResponse {
  total: number
  listSource: Activity[]
}

export enum ActivityType {
  // 通兑活动
  'UNIVERSAL_EXCHANGE_ACTIVITIES' = 'UNIVERSAL_EXCHANGE_ACTIVITIES',
  // 普通主题活动
  'GENERAL_THEME_ACTIVITIES' = 'GENERAL_THEME_ACTIVITIES',
}

/**
 * 获取所有通兑活动列表
 * @param params
 * @returns
 */
export async function queryList(params: ActivityListParams): Promise<ListResponse> {
  const res = await http.get<{total: number; activities: Activity[]}>('/api/hera/localLife/recruit/activity/list', {
    params: {
      ...params,
      platform_type: 'OPERATION'
    },
    transform: false
  })
  const transformedRes = {
    total: res?.total || 0,
    listSource: res?.activities?.map(item => {
      item.activity_start_time = dayjs(item?.activity_start_time).format('YYYY-MM-DD HH:mm:ss')
      item.activity_end_time = dayjs(item?.activity_end_time).format('YYYY-MM-DD HH:mm:ss')
      item.create_time = dayjs(item?.create_time).format('YYYY-MM-DD HH:mm:ss')
      item.activity_status = Number(item?.activity_status) as ActivityStatusEnum
      return item
    }) || []
  }
  return transformedRes
}

interface GeneralActivity {
  activity_id: string
  activity_name: string
  activity_time: {
    start_time: string
    end_time: string
  }
  team: string
  activity_entrance_link: string
  activity_status: GeneralActivityStatusEnum
  create_time: string
  city_list: string[]
  creator: string
}

interface GeneralListResponse {
  total: number
  listSource: GeneralActivity[]
}

/**
 * 获取所有普通主题活动列表
 * @param params
 * @returns
 */
export async function queryGeneralThemeList(params: ActivityListParams & {condition_type: 'ACTIVITY_ID' | 'ACTIVITY_NAME' | 'ACTIVITY_CREATOR' }): Promise<GeneralListResponse> {
  const res = await http.get<{total: number; activities: GeneralActivity[]}>('/api/hera/localLife/general/activity/list', { params, transform: false })
  const transformedRes = {
    total: res?.total || 0,
    listSource: res?.activities
  }
  return transformedRes
}

interface IActivityUpDownParams {
  activity_id: string
  activity_status: GeneralActivityStatusEnum // 3发布，4下线
}

interface IExtendActivityEndTimeParams {
  activity_id: string
  end_time: string
}

/**
 * 发布、下线接口
 * @param params
 * @returns
 */
export const upDownActivity = (params: IActivityUpDownParams) => http.post('/api/hera/localLife/general/activity/updown', params, { transform: false })

/**
 * 延长活动结束时间接口
 * @param params
 * @returns
 */
export const extendActivityEndTime = (params: IExtendActivityEndTimeParams) => http.post('/api/hera/localLife/general/activity/extend_time', params, { transform: false })

export {
  ActivityStatusEnum,
  Activity
}
