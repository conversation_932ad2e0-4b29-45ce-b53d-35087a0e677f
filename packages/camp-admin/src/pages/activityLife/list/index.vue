<template>
  <Space class="ultra-list-container" v-bind="spaceProps">
    <Space class="ultra-list-header" v-bind="spaceProps">
      <PageHeader title="交易活动管理" />
      <Space class="ultra-list-toptype-button" v-bind="spaceProps">
        <Space>
          <Text type="h6" bold>交易活动类型</Text>
          <Button :type="activityType===ActivityType.UNIVERSAL_EXCHANGE_ACTIVITIES ? 'primary': 'default'" @click="clickActType(ActivityType.UNIVERSAL_EXCHANGE_ACTIVITIES)">通兑活动</Button>
          <Button :type="activityType===ActivityType.GENERAL_THEME_ACTIVITIES ? 'primary': 'default'" @click="clickActType(ActivityType.GENERAL_THEME_ACTIVITIES)">普通主题活动</Button>
        </Space>
      </Space>
    </Space>
  </Space>

  <component :is="listComponent" />
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { Space, Text, Button } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import { spaceProps } from '../../../composables/use-props'
  import { ActivityType } from './service'
  import UniversalList from './UniversalList.vue'
  import GeneralList from './GeneralList.vue'

  const activityType = ref(useRoute()?.query?.activeTab ?? ActivityType.UNIVERSAL_EXCHANGE_ACTIVITIES)
  const listComponent = computed(() => (activityType.value === ActivityType.GENERAL_THEME_ACTIVITIES ? GeneralList : UniversalList))

  const clickActType = type => {
    activityType.value = type
  }
</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-toptype-button
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 24px
</style>
