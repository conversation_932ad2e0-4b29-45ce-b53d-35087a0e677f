import { Text, Link } from '@xhs/delight'
import dayjs from 'dayjs'
import {
  useFilterScheme,
  useToolbarActions,
} from 'shared/scheme/camp-admin/activityLife/listBaseScheme'
import { GeneralActivityStatusEnum } from './service'
import { dictMaterials } from '../dictMaterials/index'

const {
  TableColumnTestMode,
  TableColumn,
  TableColumnCommonActivityStatus,
  TableColumnActivityTime,
} = dictMaterials

export interface FilterPayload {
  condition_type: number
  data: string
}

export enum ConditionType {
  'ACTIVITY_ID' = 'ACTIVITY_ID',
  'ACTIVITY_NAME' = 'ACTIVITY_NAME',
  'ACTIVITY_CREATOR' = 'ACTIVITY_CREATOR',
}

export const scheme = {
  filterScheme: useFilterScheme(),
  toolbarActions: useToolbarActions(),
  tableColumnScheme: [
    {
      field: 'activity_id',
      title: '活动ID',
      dataIndex: 'activity_id',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="activity_id" />
    },
    {
      field: 'activity_name',
      title: '活动名称',
      dataIndex: 'activity_name',
      minWidth: 88,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="activity_name" />
    },
    {
      field: 'activity_time',
      title: '活动时间',
      dataIndex: 'activity_time',
      width: 188,
      render: ({ rowData }) => <TableColumnActivityTime startTime={rowData?.activity_time?.start_time} endTime={rowData?.activity_time?.end_time} />,
    },
    {
      field: 'team',
      title: '活动所属团队',
      dataIndex: 'team',
      minWidth: 120,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="team" />
    },
    {
      field: 'activity_entrance_link',
      title: '活动关联链接',
      dataIndex: 'activity_entrance_link',
      minWidth: 120,
      // @ts-ignore
      render: ({ rowData }) => (<Link style={{ maxWidth: '180px' }} onClick={() => window.open(`${rowData?.activity_entrance_link}`)}>{rowData?.activity_entrance_link}</Link>),
    },
    {
      field: 'activity_status',
      title: '活动状态',
      dataIndex: 'activity_status',
      minWidth: 88,
      render: ({ rowData }) => <TableColumnCommonActivityStatus rowData={rowData} dataIndex="activity_status" />
    },
    {
      field: 'creator',
      title: '创建人',
      dataIndex: 'creator',
      minWidth: 74,
      render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="creator" />
    },
    {
      field: 'test_mode',
      title: '是否是正式活动',
      dataIndex: 'test_mode',
      minWidth: 140,
      render: ({ rowData }) => <TableColumnTestMode rowData={rowData} dataIndex="test_mode" />
    },
    {
      field: 'create_time',
      title: '创建时间',
      dataIndex: 'create_time',
      width: 180,
      render({ rowData }) {
        const create_time = dayjs(rowData?.create_time).format('YYYY-MM-DD HH:mm:ss')
        return <Text>{ create_time }</Text>
      }
    },
    {
      field: 'city_list',
      title: '所属城市',
      dataIndex: 'city_list',
      minWidth: 80,
      render({ rowData }) {
        const citys = rowData?.city_list?.length ? rowData.city_list.join('、') : '-'
        return <Text>{ citys }</Text>
      }
    },
  ],
  tableActionScheme: [
    {
      text: '编辑',
      visible: rowData => rowData?.activity_status !== GeneralActivityStatusEnum.OFFLINE,
    },
    {
      text: '重新发布',
      visible: rowData => rowData?.activity_status === GeneralActivityStatusEnum.OFFLINE,
    },
    {
      text: '下线',
      visible: rowData => rowData?.activity_status !== GeneralActivityStatusEnum.OFFLINE,
      tooltip: rowData => ([GeneralActivityStatusEnum.UNPUBLISHED].includes(rowData?.activity_status) ? '活动生效后可操作下线' : ''),
      disabled: rowData => [GeneralActivityStatusEnum.UNPUBLISHED].includes(rowData?.activity_status),
      popConfirmProps: () => ({
        title: '确认下线该活动吗？',
        description: '请再次确认是否下线',
        width: 250,
      }),
    },
    {
      text: '延长活动时间',
      visible: rowData => rowData?.activity_status === GeneralActivityStatusEnum.PUBLISHED,
    },
  ],
}
