<template>
  <Space class="ultra-list-container" v-bind="spaceProps">
    <Space class="ultra-list-header" v-bind="spaceProps">
      <OutlineFilter v-model="filterParams" :config="filterConfig" />
    </Space>
  </Space>

  <div class="ultra-list-body">
    <Space class="ultra-list-content" v-bind="spaceProps">
      <Toolbar :config="toolBarConfig" />
      <Table size="large" :columns="finalTableColumns" :data-source="listSource" :loading="loading">
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
      </Table>
      <Pagination
        v-model="baseParams.page_num"
        v-model:pageSize="baseParams.page_size"
        :total="total"
        @change="fetchList(false)"
      />
    </Space>
  </div>
</template>

<script setup lang="tsx">
  import { computed } from 'vue'
  import {
    Space, Table, Pagination, Text
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import TableCell from '@xhs/delight-material-ultra-table-cell'
  import { useRouter } from 'vue-router'
  import { queryList } from './service'
  import { useList } from '../../../composables/use-list'
  import {
    spaceProps, useFilter, useToolBar, useTableColumns
  } from '../../../composables/use-props'
  import { scheme as schemeNormal } from './scheme'

  const router = useRouter()

  const scheme = computed(() => schemeNormal)

  const {
    listSource,
    total,
    loading,
    baseParams,
    filterParams,
    fetchList
  } = useList(scheme, queryList)

  function handleCreate() {
    router.push({ name: 'activityDetail' })
  }

  function handleDetail(rowData) {
    router.push({ name: 'activityDetail', params: { id: rowData?.activity_id } })
  }

  function handlePreview(rowData) {
    router.push({ name: 'previewList', params: { id: rowData?.activity_id, creatorType: rowData?.creator_type } })
  }

  function handelCopy(rowData) {
    router.push({ name: 'activityDetail', params: { id: rowData?.activity_id }, query: { type: 'copy' } })
  }

  const { filterConfig } = useFilter(scheme, fetchList)
  const { toolBarConfig } = useToolBar(scheme, [handleCreate])
  const { tableColumns } = useTableColumns(scheme, [handleDetail], { already_enroll_num: handlePreview })

  const funcs = [handleDetail, handelCopy]

  const finalTableColumns = computed(() => {
    const list = tableColumns.value
    list.splice(-1, 1, {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => (rowData && scheme.value?.tableActionScheme?.filter(t => t?.visible?.(rowData) !== false)?.length > 0 ? (
        <Space direction="vertical" size="small" align='start'>
          { scheme.value?.tableActionScheme?.map((action, index) => (
            <Space direction="vertical" size="2px" align="start">
              <TableCell
                class={'camp-table-action'}
                type="action"
                action-props={{
                  direction: 'vertical',
                  actionOptions: [{
                    text: action?.text,
                    disabled: action?.disabled?.(rowData),
                    tooltip: action?.tooltip?.(rowData),
                    visible: action?.visible?.(rowData),
                    popConfirmProps: action?.popConfirmProps?.(rowData),
                    onClick: funcs?.[index]?.bind(null, rowData)
                  }],
                }} />
                { action.bottomTip ? action.bottomTip(rowData) : null }
              </Space>
          ))}
        </Space>) : <Text>{ '' }</Text>)
    })
    return list
  })

  fetchList()
</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  margin-top 24px
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
</style>
