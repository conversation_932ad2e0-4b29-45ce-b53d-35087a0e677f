import { dictMaterials } from '@/pages/activityLife/dictMaterials/index'

import {
  useFilterScheme,
  useToolbarActions,
  useTableColumnScheme,
  useTableActionScheme,
} from 'shared/scheme/camp-admin/activityLife/listBaseScheme'
import { CreatorTypeStringEnum } from 'shared/scheme/camp-admin/activityLife/type'
import { ActivityStatusEnum, IsPendingProcessEnum } from './service'

const { TableColumnOrganizer, Text } = dictMaterials

export const scheme = {
  filterScheme: (() => {
    const baseScheme = useFilterScheme()
    baseScheme.push({
      name: 'creator_type',
      width: 100,
      component: {
        is: 'Select',
        props: {
          prefix: '组织方',
          options: [
            { label: '平台运营', value: CreatorTypeStringEnum.HERA },
            { label: '代运营服务商', value: CreatorTypeStringEnum.PROVIDER },
          ]
        },
      },
    })
    baseScheme.push({
      name: 'wait_staff_approve',
      width: 100,
      defaultValue: IsPendingProcessEnum.ALL,
      component: {
        is: 'Select',
        props: {
          prefix: '是否待运营审核',
          options: [
            { label: '是', value: IsPendingProcessEnum.YES },
            { label: '否', value: IsPendingProcessEnum.NO },
            { label: '所有', value: IsPendingProcessEnum.ALL },
          ]
        },
      },
    })
    return baseScheme
  })(),
  toolbarActions: useToolbarActions(),
  tableColumnScheme: (() => {
    const baseScheme = useTableColumnScheme()
    const creatorIndex = baseScheme.findIndex(item => item.field === 'creator')
    baseScheme.splice(creatorIndex, 0, {
      field: 'creator_type',
      title: '组织方',
      dataIndex: 'creator_type',
      minWidth: 115,
      render: ({ rowData }) => <TableColumnOrganizer rowData={rowData} dataIndex="creator_type" />
    })
    return baseScheme
  })(),
  tableActionScheme: (() => {
    const baseScheme = useTableActionScheme()
    baseScheme.splice(0, 1, {
      text: '活动详情',
      bottomTip: rowData => (<div>
        {rowData.activity_status === ActivityStatusEnum.PROCESSING_ACTIVITY ? <Text size="small" color="danger" >请及时审核服务商创建的活动信息</Text> : null}
        {rowData.activity_status === ActivityStatusEnum.PROCESSING_PUBLISH ? <Text size="small" color="danger" >请及时审核服务商的活动商品组品信息</Text> : null}
      </div>),
    })
    return baseScheme
  })(),
}
