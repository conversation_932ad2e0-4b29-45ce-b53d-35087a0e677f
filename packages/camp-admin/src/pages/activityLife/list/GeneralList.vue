<template>
  <Space class="ultra-list-container" v-bind="spaceProps">
    <Space class="ultra-list-header" v-bind="spaceProps">
      <OutlineFilter v-model="filterParams" :config="filterConfig" />
    </Space>
  </Space>

  <div class="ultra-list-body">
    <Space class="ultra-list-content" v-bind="spaceProps">
      <Toolbar :config="toolBarConfig" />
      <Table size="large" :columns="tableColumns" :data-source="listSource" :loading="loading">
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
      </Table>
      <Pagination
        v-model="baseParams.page_num"
        v-model:pageSize="baseParams.page_size"
        :total="total"
        @change="fetchList(false)"
      />
    </Space>
  </div>
  <ExtendActTime
    v-model:visible="actTimeVisible"
    :loading="modalLoading"
    :original-end-time="originalEndTime"
    @handleExtendActTime="handleExtendActTime"
  />
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import {
    Space, Table, Pagination, Text
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import { useRouter } from 'vue-router'
  import ExtendActTime from 'shared/dictMaterials/camp-admin/activityLife/ExtendActTime/index.vue'
  import {
    queryGeneralThemeList, upDownActivity, GeneralActivityStatusEnum, extendActivityEndTime
  } from './service'
  import { useList } from '../../../composables/use-list'
  import {
    spaceProps, useFilter, useToolBar, useTableColumns
  } from '../../../composables/use-props'
  import { scheme as schemeGeneral } from './schemeGeneral'

  const router = useRouter()

  const scheme = computed(() => schemeGeneral)

  const {
    listSource,
    total,
    loading,
    baseParams,
    filterParams,
    fetchList
  } = useList(scheme, queryGeneralThemeList)

  function handleCreate() {
    router.push({ name: 'activityGeneralDetail' })
  }

  function handleDetail(rowData) {
    router.push({ name: 'activityGeneralDetail', params: { id: rowData?.activity_id } })
  }

  const { filterConfig } = useFilter(scheme, fetchList)
  const { toolBarConfig } = useToolBar(scheme, [handleCreate])

  function handleDown(rowData) {
    upDownActivity({ activity_id: rowData?.activity_id, activity_status: GeneralActivityStatusEnum.OFFLINE }).then(() => {
      fetchList()
    })
  }
  const actTimeVisible = ref(false)
  const activityId = ref() // 延长的活动id
  const originalEndTime = ref() // 活动的原始结束时间
  const modalLoading = ref(false)

  function toShowExtendActTimeModal(rowData) {
    activityId.value = rowData?.activity_id
    originalEndTime.value = rowData?.activity_time?.end_time
    actTimeVisible.value = true
  }

  const handleExtendActTime = async actExtendTime => {
    const params = {
      activity_id: activityId.value,
      end_time: actExtendTime
    }
    modalLoading.value = true
    try {
      await extendActivityEndTime(params)
      await fetchList()
      actTimeVisible.value = false
    } finally {
      modalLoading.value = false
    }
  }
  const { tableColumns } = useTableColumns(scheme, [handleDetail, handleDetail, handleDown, toShowExtendActTimeModal])

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  margin-top 24px
  &.d-space-vertical
    gap 24px
.ultra-list-toptype-button
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
  /deep/.camp-table-action>.table-action-cell>.d-space
    align-items flex-start
</style>
