<template>
  <FormItem label="商家邀约" name="seller_info.participant_group_list" required>
    <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 7px;">
      <Item
        v-for="(item, index) in participantGroupList"
        :key="item.local_id"
        :model-value="item"
        :index="index"
        :readonly="readonly"
        :item-list-length="modelValue.length"
        :activity-status="activityStatus"
        @update:model-value="changeItem($event, index)"
        @delete-item="handleDeleteItem"
        @down-item="handleDownItem"
        @up-item="handleUpItem"
      />
      <Link v-show="participantGroupList.length < 8" :icon="AddOne" :disabled="addDisabled" @click="handleAddGroup">新增一组</Link>
    </div>
  </FormItem>
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue'
  import { FormItem2 as FormItem, Link, toast2 as toast } from '@xhs/delight'
  import { AddOne } from '@xhs/delight/icons'
  import { ActivityStatusEnum } from 'shared/dictMaterials/camp-admin/activityLife/type'

  import Item from './Item.vue'
  import { swapArrayElements } from './utils'
  import type { ParticipantGroupItem } from './type'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<ParticipantGroupItem[]>,
      required: true
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    activityStatus: {
      type: Number as PropType<ActivityStatusEnum>,
      required: true
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const addDisabled = computed(() => props.readonly)

  const participantGroupList = computed({
    get() {
      return props.modelValue?.map(item => ({
        ...item,
        local_id: item.local_id || Math.random().toString()
      }))
    },
    set(newVal) {
      emit('update:modelValue', newVal)
    }
  })

  const handleAddGroup = () => {
    if (participantGroupList.value.length >= 8) {
      toast.warning('最多添加8组品类')
      return
    }
    participantGroupList.value = [
      ...participantGroupList.value,
      {
        config_name: '',
        participant_id_list: [],
        local_id: Math.random().toString()
      }
    ]
  }

  const changeItem = (item, index: number) => {
    const { modelValue } = props
    modelValue[index] = item
  }

  const handleDeleteItem = (local_id: string) => {
    const index = participantGroupList.value.findIndex(val => val.local_id === local_id)
    const tmpList = [...participantGroupList.value]
    if (tmpList[index]) {
      tmpList.splice(index, 1)
      participantGroupList.value = tmpList
    }
  }
  const handleUpItem = (local_id: string) => {
    const index = participantGroupList.value.findIndex(val => val.local_id === local_id)
    swapArrayElements(participantGroupList.value, index, index - 1)
    participantGroupList.value = [...participantGroupList.value]
  }
  const handleDownItem = (local_id: string) => {
    const index = participantGroupList.value.findIndex(val => val.local_id === local_id)
    swapArrayElements(participantGroupList.value, index, index + 1)
    participantGroupList.value = [...participantGroupList.value]
  }
</script>
