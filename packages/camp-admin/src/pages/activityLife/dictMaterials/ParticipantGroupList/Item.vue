<template>
  <div class="item-container">
    <FormItem name="seller_info" :rules="rules">
      <Input
        v-model="groupName"
        :disabled="readonly"
        :max-length="4"
        clearable
        placeholder="请输入商家品类名称，最多输入4个字符，最多配置8个品类"
        required
      />
    </FormItem>
    <SellerImport
      v-model="sellerInfo"
      :show-label="false"
      :readonly="readonly"
      :activity-status="activityStatus"
    />
    <div v-if="!readonly" class="footer-tool">
      <div class="right-wrapper">
        <Link :disabled="itemListLength < 2 || index === 0 " class="link" @click="emit('upItem', modelValue.local_id)">上移</Link>
        <Link :disabled="itemListLength < 2 || index === itemListLength - 1" class="link" @click="emit('downItem', modelValue.local_id)">下移</Link>
        <Link :disabled="itemListLength < 2 || !clearable" class="link" @click="emit('deleteItem', modelValue.local_id)">删除组</Link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, PropType } from 'vue'
  import { Input, Link, FormItem2 as FormItem } from '@xhs/delight'
  import { ActivityStatusEnum } from 'shared/dictMaterials/camp-admin/activityLife/type'

  import SellerImport from '../SellerImport/index.vue'
  import type { ParticipantGroupItem } from './type'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<ParticipantGroupItem>,
      required: true
    },
    index: {
      type: Number as PropType<number>,
      required: true
    },
    itemListLength: {
      type: Number as PropType<number>,
      required: true
    },
    activityStatus: {
      type: Number as PropType<ActivityStatusEnum>,
      required: true
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
  })

  const emit = defineEmits(['update:modelValue', 'upItem', 'downItem', 'deleteItem'])

  const clearable = computed(() => {
    if (!props.activityStatus) return true
    if (props.activityStatus < ActivityStatusEnum.SIGNING_UP && !props.readonly) return true
    return false
  })

  const groupName = computed({
    get() {
      return props.modelValue.config_name
    },
    set(newVal) {
      emit('update:modelValue', {
        ...props.modelValue,
        config_name: newVal
      })
    }
  })

  const sellerInfo = computed({
    get() {
      return {
        invitation_short_desc: props.modelValue.invitation_short_desc,
        seller_list: props.modelValue.seller_list,
        tag_config_id: props.modelValue.tag_config_id,
      }
    },
    set(newVal) {
      emit('update:modelValue', {
        ...props.modelValue,
        ...newVal,
      })
    }
  })

  const rules = ref({
    required: true,
    validator: (rule, val, callback) => {
      if (val.participant_group_list.length) {
        if (!groupName.value) return callback(new Error('请输入商家品类名称'))
        const length = val.participant_group_list.map(item => item.config_name).length
        const setLength = [...new Set(val.participant_group_list.map(item => item.config_name))].length
        return length === setLength && (length !== 0) ? callback() : callback(new Error('品类名称不能重复'))
      }
      callback()
    }
  })

</script>

<style scoped lang="stylus">
.item-container
  display: flex
  flex-direction: column
  gap: 10px
  min-width 600px
  padding 15px
  background-color var(--color-grey-1)
  border-radius: 4px
.footer-tool
  display: flex
  justify-content: flex-end
  .right-wrapper
    .link
      margin-left: 24px
:deep(.d-input-wrapper)
  width 430px
</style>
