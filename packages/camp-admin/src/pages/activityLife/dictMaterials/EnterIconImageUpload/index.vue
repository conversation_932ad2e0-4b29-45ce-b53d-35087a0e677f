<template>
  <FormItem ref="uploadRef" name="activity_desc.act_venue_config.activity_icon" label="活动会场入口icon" :rules="rules">
    <UploadImage
      v-model="imageList"
      :readonly="readonly"
      :aspect-ratio="[132 / 108]"
      :max-count="1"
      tip="最多上传1张，图片尺寸132*108px，建议png、jpg格式"
    />
  </FormItem>
</template>

<script setup lang="ts">
  import {
    computed, PropType, ref, watch
  } from 'vue'
  import { FormItem2 as FormItem } from '@xhs/delight'
  import UploadImage from '@/components/upload-image/index.vue'
  import { ImageList } from '../model'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<ImageList[]>,
      default() {
        return []
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    required: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const uploadRef = ref()

  const imageList = computed({
    get: () => props.modelValue,
    set: value => {
      emit('update:modelValue', [...value])
    },
  })

  const rules = ref({
    trigger: ['blur', 'change'],
    required: props.required,
    validator: (rule, val, callback) => {
      if (props.required) {
        if (!val?.length) {
          callback(new Error('请上传活动会场入口icon'))
          return
        }
        if (val?.length && !(val[0]?.url || val[0]?.link)) {
          callback(new Error('图片尺寸不符合要求'))
          return
        }
      }
      callback()
    }
  })

  watch(() => props.modelValue, () => {
    uploadRef.value?.validate()
  }, {
    deep: true
  })
</script>

<style scoped lang="stylus">
:deep(.d-form-item__content)
  padding-top 7px
</style>
