<template>
  <FormItem ref="uploadRef" class="item-icon-wrap" name="activity_desc.act_venue_config.activity_item_icon" label="活动会场商品卡icon" :rules="rules">
    <UploadImage
      v-model="imageList"
      :readonly="readonly"
      :aspect-ratio="[76 / 36]"
      :max-count="1"
      tip="最多上传1张，图片尺寸76*36px，建议png、jpg格式"
    />
    <div class="demo-text">应用于商品卡，<span
      class="link"
      @click="handleViewImage('https://picasso-static.xiaohongshu.com/fe-platform/e953cccdb402f902ff6274c4dd83ad59a77f68c6.png')"
    >查看示例</span></div>
  </FormItem>
</template>
<script setup lang="ts">
  import {
    computed, PropType, ref, watch
  } from 'vue'
  import { FormItem2 as FormItem, viewImgs } from '@xhs/delight'
  import UploadImage from '@/components/upload-image/index.vue'
  import { ImageList } from '../model'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<ImageList[]>,
      default() {
        return []
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    required: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  })
  const emit = defineEmits(['update:modelValue'])
  const uploadRef = ref()
  const imageList = computed({
    get: () => props.modelValue,
    set: value => {
      emit('update:modelValue', [...value])
    },
  })
  const rules = ref({
    trigger: ['blur', 'change'],
    required: props.required,
    validator: (rule, val, callback) => {
      if (props.required && !val?.length) {
        callback(new Error('请上传活动会场商品卡icon'))
        return
      }
      if (val?.length && !(val[0]?.url || val[0]?.link)) {
        callback(new Error('图片尺寸不符合要求'))
        return
      }
      callback()
    }
  })
  watch(() => props.modelValue, () => {
    uploadRef.value?.validate()
  }, {
    deep: true
  })
  const handleViewImage = (link: string) => {
    viewImgs([link], {
      closeOnMask: true,
    })
  }
</script>
<style lang="stylus" scoped>
.item-icon-wrap
  margin-bottom 20px
.demo-text
  font-size 12px
  color rgba(102, 102, 102, 1)
  position: absolute;
  bottom: -15px;
  .link
    color: var(--color-primary);
    margin-right 6px
    font-weight 400
    font-size 12px
    cursor pointer
</style>

<style scoped lang="stylus">
:deep(.d-form-item__content)
  padding-top 7px
</style>
