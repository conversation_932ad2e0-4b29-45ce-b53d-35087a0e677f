import { dictMaterials as sharedDictMaterials } from 'shared/dictMaterials/camp-admin/activityLife/index'

import ActivityCitySelect from './ActivityCitySelect/index.vue'
import ActivityItemIconImageUpload from './ActivityItemIconImageUpload/index.vue'
import BatchImportProductId from './BatchImportProductId/index.vue'
import CategorySelect from './CategorySelect/index.vue'
import EnterBackImageUpload from './EnterBackImageUpload/index.vue'
import EnterIconImageUpload from './EnterIconImageUpload/index.vue'
import ImageListUpload from './ImageListUpload/index.vue'
import LocationSelect from './LocationSelect/index.vue'
import ViewImportedProductId from './ViewImportedProductId/index.vue'
import SellerImport from './SellerImport/index.vue'
import ParticipantGroupList from './ParticipantGroupList/index.vue'
import ProductMainImages from './ProductMainImages/index.vue'
import ProductLongImages from './ProductLongImages/index.vue'
import TableColumnOrganizer from './TableColumnOrganizer/index.vue'
import ProcessModal from './ProcessModal/index.vue'
import ActivityBkData from './ActivityBkData/index.vue'
import RegionCascader from './RegionCascader/index.vue'

export const dictMaterials = {
  ...sharedDictMaterials,
  ActivityCitySelect,
  ActivityItemIconImageUpload,
  BatchImportProductId,
  CategorySelect,
  EnterBackImageUpload,
  EnterIconImageUpload,
  ImageListUpload,
  LocationSelect,
  ViewImportedProductId,
  SellerImport,
  ParticipantGroupList,
  ProductMainImages,
  ProductLongImages,
  TableColumnOrganizer,
  ProcessModal,
  ActivityBkData,
  RegionCascader,
}
