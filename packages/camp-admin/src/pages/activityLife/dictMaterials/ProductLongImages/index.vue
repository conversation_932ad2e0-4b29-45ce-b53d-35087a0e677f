<template>
  <FormItem ref="uploadRef" name="product_extra_info.product_long_images" label="活动商品长图" :rules="rules">
    <UploadImage
      v-model="imageList"
      :readonly="readonly"
      :max-count="10"
      :max-size="4 * 1024 * 1024"
      tip="最多上传 10 张，支持 png / jpg /jpeg 尺寸，大小不超过4M；"
    />
  </FormItem>
</template>

<script setup lang="ts">
  import {
    computed, PropType, ref, watch
  } from 'vue'
  import { FormItem2 as FormItem } from '@xhs/delight'
  import UploadImage from '@/components/upload-image/index.vue'
  import { ImageList } from '../model'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<ImageList[]>,
      default() {
        return []
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const uploadRef = ref()

  const imageList = computed({
    get: () => props.modelValue,
    set: val => {
      emit('update:modelValue', val)
    },
  })

  const rules = ref({
    trigger: 'change',
    required: true,
    validator: (rule, val, callback) => {
      if (!val?.length) {
        callback(new Error('请上传活动商品长图'))
        return
      }
      const allSuccess = val.every(item => !item.ratioError && !!(item?.url || item?.link))
      if (!allSuccess) {
        callback(new Error('请上传活动商品长图'))
      }
      callback()
    }
  })

  watch(() => props.modelValue, () => {
    uploadRef.value?.validate()
  }, {
    deep: true
  })
</script>

<style scoped lang="stylus">
:deep(.d-form-item__content)
  padding-top 7px
</style>
