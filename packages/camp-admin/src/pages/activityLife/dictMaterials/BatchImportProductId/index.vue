<template>
  <Space>
    <Text bold type="h6" class="label">批量导入商品ID</Text>
    <FormItem name="item_info" :rules="rules">
      <Space v-if="itemIdList?.length || modelValue?.item_desc">
        <Text style="max-width: 328px;">
          <Text style="padding-right: 10px;">
            {{ desc }}
            <Link v-if="!modelValue?.item_desc && itemIdList?.length" @click="handleClear">
              清空
              <Icon :icon="Clear" />
            </Link>
          </Text>
        </Text>
      </Space>
      <div v-else class="item-import-content">
        <Tooltip v-if="tooltipContent" :content="tooltipContent">
          <Space justify="center">
            <Link disabled class="disable-link">批量导入商品ID</Link>
          </Space>
        </Tooltip>
        <template v-else>
          <Link @click="showModal">批量导入商品ID</Link>
        </template>
        <span class="import-text">重新上传即可修改已导入商品ID</span>
      </div>
    </FormItem>
    <ImportProductId v-model:visible="editVisible" v-model:itemIdList="itemIdList" :activity-time-value="activityTimeValue" :activity-id="paramsActivityId" />
  </Space>
</template>

<script setup lang="ts">
  import {
    ref, PropType, computed, watch
  } from 'vue'
  import { useRoute } from 'vue-router'
  import {
    FormItem2 as FormItem, Space, Text, Link, Tooltip, Icon
  } from '@xhs/delight'
  import { Clear } from '@xhs/delight/icons'
  import ImportProductId from './ImportProductId.vue'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      default() {
        return {}
      }
    },
    activityTimeValue: {
      type: Object as PropType<{
        start_time?: string
        end_time?: string
      }>,
      default() {
        return {}
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    editAll: {
      // 是否编辑全部字段
      type: Boolean as PropType<boolean>,
      default: false
    }
  })
  const paramsActivityId = useRoute().params?.id || '' as string
  const emit = defineEmits(['update:modelValue'])

  const itemIdList = computed({
    get: () => props.modelValue?.import_item_ids,
    set: val => {
      const updateParams = val?.length ? {
        import_item_ids: val,
      } : {
        ...props.modelValue,
      }
      emit('update:modelValue', updateParams)
    }
  })

  const tooltipContent = computed(() => {
    if (props.activityTimeValue?.start_time && props.activityTimeValue?.end_time) {
      return ''
    }
    return '请先设置活动时间'
  })
  const rules = ref({
    trigger: 'blur',
    required: true,
    message: '请导入商品id'
  })

  const editVisible = ref(false)

  function showModal() {
    editVisible.value = true
  }

  function handleClear() {
    emit('update:modelValue', {
      ...props.modelValue,
      import_item_ids: [],
      item_desc: ''
    })
  }

  const desc = computed(() => {
    const newText = (() => {
      const addText = props.modelValue?.item_desc ? '新增' : ''
      if (itemIdList.value?.length > 5) {
        const itemNames = itemIdList.value?.map(item => item.item_name).slice(0, 5)
        return `已选${addText}${itemNames.join('、')}等${itemIdList.value.length}个商品`
      }
      if (itemIdList.value?.length >= 1) {
        const itemNames = itemIdList.value?.map(item => item.item_name)
        return `已选${addText}${itemNames.join('、')}商品`
      }
      return ''
    })()

    return props.modelValue?.item_desc ? props.modelValue?.item_desc : newText
  })

  watch(() => [props.editAll], editAllVal => {
    if (editAllVal) {
      handleClear()
    }
  })

</script>
<style lang="stylus" scoped>
.label
  width: 248px;

.item-import-content
  display flex
  align-items center
  .disable-link
    pointer-events auto
  .import-text
    margin-left 10px
    font-size 12px
    color rgba(102, 102, 102, 1)
</style>
