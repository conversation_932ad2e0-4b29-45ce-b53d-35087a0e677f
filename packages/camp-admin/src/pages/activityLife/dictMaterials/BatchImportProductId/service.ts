import http from '@/utils/http'

export interface RItem {
  item_id: string
  item_name: string
}

export interface ImportResult {
  success_num: number
  fail_num: number
  fail_reason_path: string
  items: RItem[]
}

export interface IImportParams {
  file: string
  activity_time: {
    start_time: number | string | undefined
    end_time: number | string | undefined
  }
}

export function importExcel(data, {
  start_time,
  end_time,
  activity_id,
}: {
  start_time: number | string | undefined
  end_time: number | string | undefined
  activity_id: string| undefined
}) {
  return http.post<ImportResult>(`/api/hera/localLife/general/activity/upload_item?start_time=${start_time}&end_time=${end_time}&activity_id=${activity_id || ''}`, data, { transform: false })
}
