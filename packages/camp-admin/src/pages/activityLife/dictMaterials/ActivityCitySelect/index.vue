<template>
  <ActivityCitySelect v-model="cityList" :readonly="readonly" :get-cities="getCities" />
</template>

<script setup lang="ts">
  import { PropType, computed } from 'vue'
  import ActivityCitySelect from 'shared/dictMaterials/camp-admin/activityLife/ActivityCitySelect/index.vue'
  import { getCities } from './services'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default() {
        return []
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const cityList = computed({
    set: value => {
      emit('update:modelValue', value)
    },
    get: () => props.modelValue
  })
</script>
