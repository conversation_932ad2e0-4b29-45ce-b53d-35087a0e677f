<template>
  <div class="import-kol-container">
    <Text type="h6">1. 下载批量添加商家模版</Text>
    <div>
      <Button type="secondary" :icon="Download" @click="download">下载</Button>
    </div>
    <Text type="h6">2. 上传文件</Text>
    <Text type="description">{{ !sellerList?.length ? '请选择 xlsx 格式文件上传' : '文件已上传' }}</Text>
    <div>
      <Button
        :type="loading ? 'primary' : 'secondary'"
        :loading="loading"
        :icon="Upload"
        style="min-width: 82px;"
        @click="handleLoad"
      >
        上传
      </Button>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { Text, Button, toast2 as toast } from '@xhs/delight'
  import { Download, Upload } from '@xhs/delight/icons'
  import { getFiles } from 'shared/dictMaterials/camp-admin/activityLife/utils'
  import { ISeller } from 'shared/dictMaterials/camp-admin/activityLife/type'
  import { importExcel } from './service'

  const emit = defineEmits(['change'])

  const url = 'https://fe-video-qc.xhscdn.com/fe-platform/34d3976af1a4d2fb815afd413716d81c7d035212.xlsx?attname=fe-platform/34d3976af1a4d2fb815afd413716d81c7d035212.xlsx'
  const sellerList = ref<ISeller[]>([])
  const loading = ref(false)

  function download() {
    window.open(url)
  }

  async function handleLoad() {
    const [file] = await getFiles()
    if (!file) return

    loading.value = true
    const formData = new FormData()
    formData.append('file', file)
    try {
      const res = await importExcel(formData)
      sellerList.value = res
      if (res?.length === 0) {
        toast.warning('解析商家列表为空')
      }
      emit('change', res)
    } catch (err: any) {
      sellerList.value = []
      toast.warning(err?.message || '上传失败')
    } finally {
      loading.value = false
    }
  }
</script>

<style lang="stylus" scoped>
.import-kol-container
  display flex
  flex-direction column
  gap 15px
</style>
