import http from '@/utils/http'
import { ISeller } from 'shared/dictMaterials/camp-admin/activityLife/type'

export function importExcel(data) {
  return http.post<ISeller[]>('/api/hera/localLife/recruit/activity/importSeller', data, { transform: false })
}

export function exportExcel(params) {
  return http.get<{file_url: string}>('/api/hera/localLife/recruit/activity/downloadActivitySeller', { params, transform: false })
}
