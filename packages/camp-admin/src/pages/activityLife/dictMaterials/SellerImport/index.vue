<template>
  <FormItem ref="uploadRef" name="seller_info" :label="showLabel ? '商家邀约' : ''" :rules="rules">
    <Space>
      <Space v-if="sellerList?.length || modelValue?.invitation_short_desc">
        <Text :block="readonly || modelValue?.invitation_short_desc" style="max-width: 328px;">
          <Text style="padding-right: 10px;">
            {{ desc }}
          </Text>

          <Text>
            <Link v-if="clearable" style="margin-right: 10px" @click="handleClear">
              清空
              <Icon :icon="Clear" />
            </Link>

            <Space v-if="modelValue?.invitation_short_desc" style="margin-right: 10px">
              <Link @click="handleExport">点击导出</Link>
            </Space>
            <Link v-if="!readonly" @click="addVisible = true">点击新增</Link>
          </Text>
        </Text>
      </Space>
      <template v-else>
        <Link :disabled="readonly" @click="showModal">批量导入商家</Link>
        <Link :disabled="readonly" @click="addVisible = true">点击新增</Link>
      </template>
    </Space>
  </FormItem>

  <AddSellerModal v-model:show="addVisible" :seller-list="sellerList" @confirm="addSeller" />
</template>

<script lang="tsx" setup>
  import {
    h, ref, PropType, computed, watch
  } from 'vue'
  import {
    FormItem2 as FormItem, Link, Modal, Text, Space, Icon
  } from '@xhs/delight'
  import { ActivityStatusEnum } from 'shared/dictMaterials/camp-admin/activityLife/type'
  import { useRoute } from 'vue-router'
  import { Clear } from '@xhs/delight/icons'
  import throttle from 'lodash/throttle'
  import ImportSeller from './ImportSeller.vue'
  import AddSellerModal from './AddSellerModal.vue'
  import { exportExcel } from './service'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      default() {
        return {}
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    showLabel: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    activityStatus: {
      type: Number as PropType<ActivityStatusEnum>,
    },
  })

  const clearable = computed(() => ((props.activityStatus && props.activityStatus < ActivityStatusEnum.SIGNING_UP) && !props.readonly) || !props.activityStatus) // 邀约前（未开始）并且是编辑模式 || 创建

  const emit = defineEmits(['update:modelValue'])

  const route = useRoute()
  const uploadRef = ref()
  const tempList = ref([])
  const addVisible = ref(false)

  const sellerList = computed({
    get: () => props.modelValue?.seller_list,
    set: val => {
      emit('update:modelValue', {
        ...props.modelValue,
        seller_list: val
      })
    }
  })

  const desc = computed(() => {
    const newText = (() => {
      const addText = props.modelValue?.invitation_short_desc ? '新增' : ''
      if (sellerList.value?.length > 5) {
        const sellerNames = sellerList.value?.map(item => item.seller_name).slice(0, 5)
        return `已选${addText}${sellerNames.join('、')}等${sellerList.value.length}个商家`
      }
      if (sellerList.value?.length >= 1) {
        const sellerNames = sellerList.value?.map(item => item.seller_name)
        return `已选${addText}${sellerNames.join('、')}商家`
      }
      return ''
    })()

    return [props.modelValue?.invitation_short_desc, newText].filter(Boolean).join('，')
  })

  const handleExport = throttle(async () => {
    // 导出接口延迟1s，1s内多次点击会有后端告警
    const res = await exportExcel({ activity_id: route.params?.id.toString(), group_id: props.modelValue.tag_config_id })
    if (res?.file_url) {
      window.open(res?.file_url)
    }
  }, 1000)

  function showModal() {
    return new Promise(resolve => {
      const modal = Modal.confirm({
        title: '批量导入指定商家',
        size: 450,
        content: h(ImportSeller, {
          onChange: e => {
            tempList.value = e
          },
        }),
        onConfirm() {
          sellerList.value = tempList.value
          resolve(true)
          modal.destroy()
        },
        onCancel() {
          resolve(false)
          modal.destroy()
        }
      })
    })
  }

  function handleClear() {
    emit('update:modelValue', {
      ...props.modelValue,
      participant_id_list: [],
      seller_list: [],
      import_seller_id_list: [],
      invitation_short_desc: ''
    })
  }

  const rules = ref({
    required: true,
    validator: (rule, val, callback) => {
      if (val.participant_group_list?.length) { // 分组编辑场景
        if (!sellerList.value?.length && !props.modelValue.invitation_short_desc) {
          callback(new Error('请导入邀约商家'))
        }
      } else if (!val.participant_group_list?.length && !sellerList.value?.length && !props.modelValue.invitation_short_desc) { // 分组新增场景
        callback(new Error('请导入邀约商家'))
      } else if (!val?.seller_list?.length && !val?.invitation_short_desc) { // 不分组
        callback(new Error('请导入邀约商家'))
      }
      callback()
    }
  })

  watch(() => props.modelValue, () => {
    uploadRef.value?.validate()
  })

  const addSeller = e => {
    sellerList.value = e
  }
</script>
