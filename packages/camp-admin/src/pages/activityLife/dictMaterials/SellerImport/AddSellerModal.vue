<template>
  <Modal
    v-model:visible="visible"
    title="新增商家"
    :size="380"
    @confirm="confirm"
    @cancel="visible = false"
  >
    <Select
      v-model="modelValue"
      :options="options"
      multiple
      placeholder="请输入商家名称"
      filterable
      :filter="filter"
      :loading="loading"
      remote
      :max-tag-count="4"
    >
      <template #empty>
        <div style="padding: var(--size-space-large) 0;">
          <Result sub-title="请输入筛选项进行搜索" />
        </div>
      </template>
    </Select>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, ref, onBeforeMount } from 'vue'
  import {
    Select, Modal, useDebounce, Result
  } from '@xhs/delight'

  import { ISeller } from 'shared/dictMaterials/camp-admin/activityLife/type'
  import { fetchSellerList } from '@/services/poiClaim'

  const props = defineProps<{
    show: boolean
    sellerList: ISeller[]
  }>()

  const emit = defineEmits(['update:show', 'confirm'])

  const options = ref<{label:string;value:string}[]>([])
  const loading = ref(false)
  const modelValue = ref<string[]>([])
  const allSellers = ref<{label:string;value:string}[]>([])

  onBeforeMount(() => { // 切换是否需要区分商家品类时的回显与同步
    options.value = props.sellerList?.map(item => ({
      label: item.seller_name,
      value: item.seller_id,
    })) || []
    modelValue.value = options.value.map(o => o.value)
    allSellers.value = [...options.value]
  })

  const filter = useDebounce(
    async filterValue => {
      if (filterValue) {
        loading.value = true
        try {
          const res = await fetchSellerList(
            {
              pageNo: 1,
              pageSize: 10,
              sellerName: filterValue,
            },
            true
          )
          options.value = res.sellerInfoList.map(item => ({
            label: item.publicName,
            value: item.sellerId,
          }))
          options.value.forEach(o => {
            if (!allSellers.value.find(s => s.value === o.value)) {
              allSellers.value.push(o)
            }
          })
        } finally {
          loading.value = false
        }
      }
    },
    { delay: 300 },
  )

  const visible = computed({
    get: () => props.show,
    set: val => {
      emit('update:show', val)
    }
  })

  const confirm = () => {
    visible.value = false
    const res = allSellers.value
      .filter(s => modelValue.value.includes(s.value))
      .map(s => ({
        seller_id: s.value,
        seller_name: s.label,
      }))
    emit('confirm', res)
  }
</script>
