<template>
  <FormItem ref="uploadRef" name="activity_desc.activity_main_images" label="活动报名页主图" :rules="rules">
    <UploadImage
      v-model="imageList"
      :max-count="1"
      :aspect-ratio="[4 / 3]"
      :max-size="4 * 1024 * 1024"
      tip="最多上传1张，支持 png / jpg /jpeg 尺寸，比例为4:3，大小不超过4M；"
      :readonly="readonly"
    />
  </FormItem>
</template>

<script setup lang="ts">
  import {
    computed, PropType, ref, watch
  } from 'vue'
  import { FormItem2 as FormItem } from '@xhs/delight'
  import UploadImage from '@/components/upload-image/index.vue'
  import { ImageList } from '../model'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<ImageList[]>,
      default() {
        return []
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const uploadRef = ref()

  const imageList = computed({
    get: () => props.modelValue,
    set: value => {
      emit('update:modelValue', [...value])
    },
  })

  const rules = ref({
    trigger: ['blur', 'change'],
    required: true,
    validator: (rule, val, callback) => {
      if (!val?.length) {
        callback(new Error('请上传活动报名页主图'))
        return
      }
      const allSuccess = val.every(item => !item.ratioError && !!(item?.url || item?.link))
      if (!allSuccess) {
        callback(new Error('请上传活动报名页主图'))
      }
      callback()
    }
  })

  watch(() => props.modelValue, () => {
    uploadRef.value?.validate()
  }, {
    deep: true
  })
</script>

<style scoped lang="stylus">
:deep(.d-form-item__content)
  padding-top 7px
</style>
