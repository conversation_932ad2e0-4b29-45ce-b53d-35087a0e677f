export enum ProcessActivityOrPublishEnum {
  ACTIVITY_INFO = 'ACTIVITY_INFO',
  ACTIVITY_PUBLISH = 'ACTIVITY_PUBLISH',
}

export const processModalTextMap = {
  [ProcessActivityOrPublishEnum.ACTIVITY_INFO]: {
    PASS_MODAL_TITLE: '确认活动信息审核通过？',
    PASS_MODAL_TIP: '审核通过过后，商家可开始报名活动',
    REJECT_MODAL_TITLE: '确认活动信息审核驳回？',
    REJECT_MODAL_TIP: '审核驳回后，服务商可修改信息，修改后运营需重新审核，审核通过后商家才可开始报名',
  },
  [ProcessActivityOrPublishEnum.ACTIVITY_PUBLISH]: {
    PASS_MODAL_TITLE: '确认审核通过活动组品信息？',
    PASS_MODAL_TIP: '通过后，活动可按设置时间点正常开售',
    REJECT_MODAL_TITLE: '确认组品信息审核驳回？',
    REJECT_MODAL_TIP: '驳回后，服务商可修改组品信息，修改后运营需重新审核，审核通过后才算组品完成可开售',
  }
}
