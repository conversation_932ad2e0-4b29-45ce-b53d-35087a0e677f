<template>
  <Modal
    v-if="action === 'pass'"
    v-model:visible="computedVisible"
    :title="processModalTextMap[processingActivityOrPublish].PASS_MODAL_TITLE"
    :confirm-button-props="{ loading }"
    @confirm="toProcess('pass')"
    @cancel="computedVisible = false"
  >
    <Text>{{ processModalTextMap[processingActivityOrPublish].PASS_MODAL_TIP }}</Text>
  </Modal>
  <Modal
    v-if="action === 'reject'"
    v-model:visible="computedVisible"
    :title="processModalTextMap[processingActivityOrPublish].REJECT_MODAL_TITLE"
    :confirm-button-props="{ loading }"
    @confirm="toProcess('reject')"
    @cancel="computedVisible = false"
  >
    <div>
      <div class="modalCard">
        {{ processModalTextMap[processingActivityOrPublish].REJECT_MODAL_TIP }}
      </div>
      <Form inline>
        <FormItem label="驳回原因" required>
          <TextArea ref="textAreaRef" v-model="rejectReason" required :max-length="100" />
        </FormItem>
      </Form>
    </div>
  </Modal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Modal, Text, TextArea, Form2 as Form, FormItem2 as FormItem, toast2 as toast
  } from '@xhs/delight'
  import { ActivityStatusEnum } from '@/pages/activityLife/form/service'
  import { process } from './service'
  import { AuditActionStatusEnum, AuditTypeEnum } from './model'
  import { processModalTextMap, ProcessActivityOrPublishEnum } from './constants'

  const props = withDefaults(defineProps<{
    visible: boolean
    activityStatus: ActivityStatusEnum
    action: 'pass' | 'reject'
    activityId: string
  }>(), {
    visible: false,
  })

  const processingActivityOrPublish = computed(() => {
    if (props.activityStatus === ActivityStatusEnum.PROCESSING_ACTIVITY) {
      return ProcessActivityOrPublishEnum.ACTIVITY_INFO
    }
    return ProcessActivityOrPublishEnum.ACTIVITY_PUBLISH
  })

  const emit = defineEmits(['update:visible'])
  const router = useRouter()

  const loading = ref(false)

  const computedVisible = computed({
    get: () => props.visible,
    set: val => emit('update:visible', val)
  })

  const textAreaRef = ref()
  const rejectReason = ref('')

  const toProcess = async (flag: 'pass' | 'reject') => {
    if (loading.value) return
    if (flag === 'reject' && !rejectReason.value) {
      toast.danger('请填写驳回原因')
      return
    }
    try {
      loading.value = true
      await process({
        status: flag === 'reject' ? AuditActionStatusEnum.AUDIT_REJECT : AuditActionStatusEnum.AUDIT_PASS,
        message: flag === 'reject' ? rejectReason.value : '',
        activity_id: props.activityId,
        review_type: props.activityStatus === ActivityStatusEnum.PROCESSING_ACTIVITY ? AuditTypeEnum.CREATE_ACTIVITY : AuditTypeEnum.COMPLETE_PRODUCT_BUNDLE
      })
      router.push({ name: 'activityList' })
      emit('update:visible', false)
      toast.success('审核成功')
    } catch (error) {
      textAreaRef.value.reset()
    } finally {
      loading.value = false
    }
  }

</script>

<style scoped lang="stylus">
.modalCard
  padding 10px 30px 5px 15px
  background-color #eefbfc
  border-radius 7px
  margin-bottom 30px
  line-height 20px
  font-size 14px
</style>
