<template>
  <LocationSelect v-model="areaList" :readonly="readonly" :disabled-ids="disabledIds" :get-area-list="getCityList" />
</template>

<script setup lang="ts">
  import { PropType, computed } from 'vue'
  import LocationSelect from 'shared/dictMaterials/camp-admin/activityLife/LocationSelect/index.vue'
  import { getCityList } from './service'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default() {
        return []
      }
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
    disabledIds: {
      type: Array as PropType<string[]>,
      default() {
        return []
      }
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const areaList = computed({
    get: () => props.modelValue,
    set: value => {
      emit('update:modelValue', value)
    }
  })

</script>
