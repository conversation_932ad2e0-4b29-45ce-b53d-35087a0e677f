<template>
  <RegionCascader v-model="list" :readonly="readonly" :data-source="data" :loading="loading" />
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { RegionCascader } from 'shared/dictMaterials/camp-admin/activityLife/RegionCascader'
  import { tryCatch } from 'shared/utils'
  import { queryRegionList, RegionList } from './service'

  const props = defineProps<{
    modelValue: string[][]
    readonly: boolean
  }>()

  const emit = defineEmits(['update:modelValue'])

  const data = ref<RegionList>([])
  const loading = ref(false)

  const list = computed({
    get: () => props.modelValue,
    set: value => {
      emit('update:modelValue', value)
    }
  })

  const getRegionList = async () => {
    loading.value = true
    const [res, err] = await tryCatch(queryRegionList)
    loading.value = false
    if (err) return
    data.value = res?.region_list || []
  }

  getRegionList()

</script>
