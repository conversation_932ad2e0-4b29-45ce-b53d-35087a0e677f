import http from '@/utils/http'

export interface BkDataItem {
  campaignName: string // 计划名称
  totalNoteCnt: number // 笔记投稿数
  totalDgmv: number // 售出金额
  totalCheckDgmv: number // 核销金额
  totalRecruitNum: number // 报名人数
  totalEnrollNum: number // 确认报名人数
}

export const getBkList = (params?: { item_id: string }) => http.get<BkDataItem[]>('/api/hera/localLife/general/activity/campaign_analysis', {
  params,
  transform: false
})
