<template>
  <Form>
    <FormItem class="big-title" label="当前是否创建定向带货计划">
      <Text type="h6">{{ dataList.length ? '是' : '否' }}</Text>
    </FormItem>
    <FormItem v-for="item in dataList" :key="item.campaignName" :label="item.campaignName" class="second-form">
      <Form>
        <FormItem label="指定带货博主数量">
          <Text>{{ item.totalRecruitNum }}</Text>
        </FormItem>
        <FormItem label="已确认报名博主数量">
          <Text>{{ item.totalEnrollNum }}</Text>
        </FormItem>
        <FormItem label="笔记投稿数">
          <Text>{{ item.totalNoteCnt }}</Text>
        </FormItem>
        <FormItem label="博主笔记售卖总金额">
          <Text>{{ item.totalDgmv }}</Text>
        </FormItem>
        <FormItem label="博主笔记核销总金额">
          <Text>{{ item.totalCheckDgmv }}</Text>
        </FormItem>
      </Form>
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue'
  import { Form2 as Form, FormItem2 as FormItem, Text } from '@xhs/delight'

  import { tryCatch } from 'shared/utils/tryCatch'
  import { BkDataItem, getBkList } from './service'

  const props = defineProps<{
    id: string
  }>()

  const dataList = ref<BkDataItem[]>([])

  const fetchList = async () => {
    if (!props.id) return
    const [res, err] = await tryCatch(getBkList, { item_id: props.id })
    if (!err && res) {
      dataList.value = res
    }
  }

  onBeforeMount(() => {
    fetchList()
  })
</script>

<style scoped lang="stylus">
:deep(.big-title .d-form-item__title)
  font-size 16px
.second-form
  margin-left 90px
  :deep(.d-form-item__content .d-form-item__title)
    font-weight normal
  :deep(.d-form-item__content .d-new-form)
    gap 12px
</style>
