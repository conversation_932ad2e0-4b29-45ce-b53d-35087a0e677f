<template>
  <CategorySelect v-model="categoryId" :readonly="readonly" :get-categories="getCategories" />
</template>

<script setup lang="ts">
  import { PropType, computed } from 'vue'
  import CategorySelect from 'shared/dictMaterials/camp-admin/activityLife/CategorySelect/index.vue'
  import { getCategories } from './services'

  const props = defineProps({
    modelValue: {
      type: String as PropType<string>,
      default: ''
    },
    readonly: {
      type: Boolean as PropType<boolean>,
      default: false
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const categoryId = computed({
    set: value => {
      emit('update:modelValue', value)
    },
    get: () => props.modelValue
  })
</script>
