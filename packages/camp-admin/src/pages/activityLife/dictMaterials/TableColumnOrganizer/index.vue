<template>
  <Text>{{ OrganizerTextMap[rowData[dataIndex]] }}</Text>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import { Text } from '@xhs/delight'
  import { OrganizerTextMap } from './model'

  defineProps({
    rowData: {
      type: Object,
      default() {
        return {}
      }
    },
    dataIndex: {
      type: String as PropType<string>,
      default: ''
    },
  })
</script>
