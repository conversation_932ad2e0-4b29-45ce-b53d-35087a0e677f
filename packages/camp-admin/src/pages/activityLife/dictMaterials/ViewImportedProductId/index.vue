<template>
  <ViewImportedProductId v-model="value" :export-excel="exportExcel" />
</template>

<script setup lang="ts">
  import { PropType, computed } from 'vue'
  import ViewImportedProductId from 'shared/dictMaterials/camp-admin/activityLife/ViewImportedProductId/index.vue'
  import { exportExcel } from './service'

  const props = defineProps({
    modelValue: {
      type: String as PropType<string>,
      default: ''
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const value = computed({
    get: () => props.modelValue,
    set: val => emit('update:modelValue', val)
  })
</script>
