<template>
  <Space class="ultra-list-container" v-bind="spaceProps">
    <Space class="ultra-list-header" v-bind="spaceProps">
      <PageHeader
        type="breadcrumb"
        :items="[
          { title: '交易活动管理', to: { name: 'activityList' } },
          { title: '编辑活动', to: { name: 'activityDetail', params: { id: activityId } } },
          { title: '商家提报列表' }
        ]"
      />
    </Space>
    <Text type="h6" bold>商家提报商品详情</Text>
  </Space>

  <div class="ultra-list-body">
    <Space class="ultra-list-content" v-bind="spaceProps">
      <Table size="large" :columns="tableColumns" :data-source="listSource" :loading="loading">
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
      </Table>
      <Pagination
        v-model="baseParams.page_num"
        v-model:pageSize="baseParams.page_size"
        :total="total"
        @change="fetchList(false, { activity_id: activityId })"
      />
    </Space>
  </div>

  <Modal
    v-model:visible="visible"
    title="请输入要求商家修改的内容"
    confirm-text="确认发起修改"
    @confirm="handleConfirm"
    @cancel="closeModalHandler"
  >
    <TextArea v-model="message" placeholder="请输入修改建议" style="width: 100%" />
  </Modal>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import {
    Space, Table, Pagination, Text, Modal, TextArea, toast2 as toast
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import { useRoute } from 'vue-router'
  import { CreatorTypeEnum } from 'shared/scheme/camp-admin/activityLife/type'
  import { IProductRecord, getProductList, doAuditAction } from './service'
  import { useList } from '../../../composables/use-list'
  import { spaceProps, useTableColumns } from '../../../composables/use-props'
  import { scheme as schemeNormal } from './scheme'

  const route = useRoute()

  const creatorType = computed(() => Number(route.params.creatorType) as CreatorTypeEnum || CreatorTypeEnum.HERA)
  const scheme = computed(() => ({
    ...schemeNormal,
    tableActionScheme: creatorType.value === CreatorTypeEnum.PROVIDER ? null : schemeNormal.tableActionScheme
  }))

  const {
    listSource,
    total,
    loading,
    baseParams,
    fetchList
  } = useList(scheme, getProductList)

  const visible = ref(false)
  const message = ref('')
  const rowData = ref<IProductRecord | null>(null)

  const activityId = computed(() => route.params.id)

  const closeModalHandler = () => {
    visible.value = false
    message.value = ''
  }

  async function handleConfirm() {
    try {
      if (!message.value) {
        toast.warning('请输入修改建议')
        return
      }
      await doAuditAction({
        activity_id: activityId.value,
        biz_order_id: rowData.value?.biz_order_id,
        status: 2,
        message: message.value
      })
      toast.success('发送成功')
      message.value = ''
      visible.value = false
      rowData.value = null
      fetchList(true, { activity_id: activityId.value })
    } catch (err: any) {
      console.log(err)
    }
  }

  function handleModify(row) {
    visible.value = true
    rowData.value = row
  }

  async function agreeGroup(row) {
    try {
      rowData.value = row
      await doAuditAction({
        activity_id: activityId.value,
        biz_order_id: rowData.value?.biz_order_id,
        status: 3,
      })
      toast.success('确认组品成功')
      rowData.value = null
      fetchList(true, { activity_id: activityId.value })
    } catch (err: any) {
      console.log(err)
    }
  }

  async function refuseGroup(row) {
    try {
      rowData.value = row
      await doAuditAction({
        activity_id: activityId.value,
        biz_order_id: rowData.value?.biz_order_id,
        status: 4,
      })
      toast.success('拒绝组品成功')
      rowData.value = null
      fetchList(true, { activity_id: activityId.value })
    } catch (err: any) {
      console.log(err)
    }
  }

  async function revokeAgreeGroup(row) {
    try {
      rowData.value = row
      await doAuditAction({
        activity_id: activityId.value,
        biz_order_id: rowData.value?.biz_order_id,
        status: 1,
      })
      toast.success('撤销确认组品成功')
      rowData.value = null
      fetchList(true, { activity_id: activityId.value })
    } catch (err: any) {
      console.log(err)
    }
  }

  async function revokeRefuseGroup(row) {
    try {
      rowData.value = row
      await doAuditAction({
        activity_id: activityId.value,
        biz_order_id: rowData.value?.biz_order_id,
        status: 1,
      })
      toast.success('撤销拒绝组品成功')
      rowData.value = null
      fetchList(true, { activity_id: activityId.value })
    } catch (err: any) {
      console.log(err)
    }
  }
  const { tableColumns } = useTableColumns(scheme, [handleModify, agreeGroup, refuseGroup, revokeAgreeGroup, revokeRefuseGroup])

  fetchList(true, { activity_id: activityId.value })
</script>
<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  margin-top 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
</style>
