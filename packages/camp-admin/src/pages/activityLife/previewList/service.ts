import http from '@/utils/http'
import { StatusEnum, CommodityType, IProductRecord } from 'shared/dictMaterials/camp-admin/activityLife/type'

/**
 * 运营进行审核-商家商品列表
 * @param params
 * @returns
 */
export const getProductList = async params => {
  const res = await http.get<{ total: number; activity_status: number; audit_list: IProductRecord[] }>('/api/hera/localLife/recruit/activity/audit/productList', { params, transform: false })

  const transformedRes = {
    total: res.total,
    listSource: res.audit_list.map(item => ({ ...item, activity_status: res?.activity_status }))
  }

  return transformedRes
}

/**
 * 运营进行审核-要求商家修改、拒绝、撤回、同意（4合1）
 * @param data
 * @returns
 */
export const doAuditAction = data => http.post('/api/hera/localLife/recruit/activity/audit/doAudit', data)

export {
  StatusEnum, CommodityType, IProductRecord
}
