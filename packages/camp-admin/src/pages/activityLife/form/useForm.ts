import { ref, Ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'
import { Router } from 'vue-router'

import { ConfigRole } from 'shared/dictMaterials/camp-admin/activityLife/DateConfigRole/DateConfigRole'
import { CreateActivityParams, UnuseCanRefundType } from './service'
import { initTimeRangeFactory } from '../utils/time'

export const useForm = (formRef: Ref<any>) => {
  const subBtnLoading = ref(false)
  const model = ref({
    unuse_can_refund: 0,
    seller_info: {
      is_distinguish_partici_categories: 'false',
      participant_group_list: [{
        config_name: '',
        participant_id_list: [],
      }]
    },
    product_info: {
      repeat_selectable: 1,
      product_unit: '次',
      no_use_date_config_role: ConfigRole.BUSINESS_OPERATIONS_STAFF,
      use_time_config_role: ConfigRole.BUSINESS_OPERATIONS_STAFF,
      product: {
        attr_key_value_map: {
          can_no_use_date: {
            days_of_week: [],
            holidays: [],
            date_list: []
          },
          use_date: {
            use_date_type: 3,
            use_start_date: undefined,
            use_end_date: undefined,
            day_duration: undefined,
          },
          use_time: [
            initTimeRangeFactory()
          ],
        }
      }
    },
    area_info: {
      region_list: []
    }
  } as unknown as CreateActivityParams)

  const submit = async (router: Router, createActivity) => { // 表单提交方法
    formRef.value.validate()
      .then(async () => {
        try {
          // 校验“未使用次数不可退时，单商家可重复核销次数”为1
          if (model.value?.unuse_can_refund === UnuseCanRefundType.CANNOT_REFUND && model.value?.product_info?.seller_can_repeat_verify_count !== 1) {
            return toast.warning('单商家可重复核销次数仅支持1次')
          }
          // 单品类可重复核销次数（若有） 需要大于等于 单商家可重复核销次数
          if (model.value?.seller_info?.is_distinguish_partici_categories === 'true') {
            const { repeat_veri_cnt_of_single_seller_group, seller_can_repeat_verify_count } = model.value?.product_info
            if (Number.isFinite(repeat_veri_cnt_of_single_seller_group) && (repeat_veri_cnt_of_single_seller_group! < seller_can_repeat_verify_count)) {
              return toast.warning('单品类可重复核销次数需大于等于单商家可重复核销次数')
            }
          }
          subBtnLoading.value = true
          const res = await createActivity(model.value)
          if (!res?.activityId) {
            router.push({ name: 'activityList' })
          } else {
            toast.success({ content: '编辑活动成功' })
            router.push({ name: 'activityList' })
          }
        } catch (err: any) {
          // eslint-disable-next-line no-console
          console.error(err)
          // toast.warning(err?.message)
        } finally {
          subBtnLoading.value = false
        }
      }).catch(err => {
        const errList: any = Object.values(err) || []
        if (errList[0]) {
          toast.warning(errList[0][0]?.message || '请检查必填字段')
        }
      })
  }

  return {
    model,
    subBtnLoading,
    submit,
  }
}
