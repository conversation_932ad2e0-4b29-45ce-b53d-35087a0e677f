import http from '@/utils/http'
import { UnuseCanRefundType, IActivity, CreateActivityParams } from 'shared/scheme/camp-admin/activityLife/type'
import { ActivityStatusEnum, TestModeEnum } from 'shared/dictMaterials/camp-admin/activityLife/type'
import { activityStatusMap } from 'shared/dictMaterials/camp-admin/activityLife/constant'
import { transformParams, transformRes } from './dtos'

export enum ActivityType {
  // 通兑活动
  'UNIVERSAL_EXCHANGE_ACTIVITIES' = 'UNIVERSAL_EXCHANGE_ACTIVITIES',
  // 普通主题活动
  'GENERAL_THEME_ACTIVITIES' = 'GENERAL_THEME_ACTIVITIES',
}

export interface ParticipantGroupItem {
  config_name: string
  participant_id_list: string[]
  seller_list?: string[]
  invitation_short_desc?: string
  local_id?: string
  tag_config_id?: string
}

export interface GetActivityInfoRes {
  short_name: string
  unuse_can_refund: UnuseCanRefundType
  already_fill_product_extra_info: number
  activity_name?: string
  test_mode?: TestModeEnum
  seller_info?: {
    import_seller_id_list: string[]
    invitation_short_desc: string
    area_list: string[]
    is_distinguish_partici_categories: string
    participant_group_list?: ParticipantGroupItem[]
  }
  activity_desc?: {
    activity_main_images: {
      width: number
      url: string
      height: number
    }[]
    act_venue_config: {
      activity_icon: {
        url: string
        width: number
        height: number
      }[]
      activity_background_img: {
        url: string
        width: number
        height: number
      }[]
      activity_item_icon: {
        url: string
        width: number
        height: number
      }[]
      activity_slogan: string
      activity_entrance_link: string
      activity_id_and_name_map: {
        id: string
        name: string
      }[]
    }
    desc: string
  }
  activity_id: string
  product_info: {
    no_use_date_config_role: string
    use_time_config_role: string
    product_unit: string
    seller_need_provider_day_min_stock: number
    seller_can_repeat_verify_count: number
    product: {
      sold_end_time: number
      product_type: number
      category_id: string
      sold_start_time: number
      attr_key_value_map: {
        can_no_use_date: string
        use_time: string
        use_date: string
      }
    }
    user_purchase_limit: number
    seller_need_provider_min_stock: number
    discount_standard: {
      lower: number
      upper: number
    }
    unit_price: number
    can_select_count: number
    whole_count: number
    can_use_count: number
    repeat_selectable: number
  }
  product_extra_info?: {
    current_stock: number
    change_stock: number
  }
  activity_status?: ActivityStatusEnum
}

/**
 * 获取活动信息
 * @param params
 * @returns
 */
export const getActivityInfo = async params => {
  const res = await http.get<GetActivityInfoRes>('/api/hera/localLife/recruit/activity/queryActivityDetail', { params, transform: false })
  return transformRes(res)
}

/**
 * 创建/编辑活动
 * @param data
 * @returns
 */
export const createActivity = data => {
  const transformedData: CreateActivityParams = transformParams({ ...data, creator_type: 'STAFF' })
  return http.post('/api/hera/localLife/recruit/activity/createOrUpdateActivity', transformedData, { transform: false })
}

/**
 * 更新活动动作
 * @param data
 * @returns
 */
export const updateActivityAction = data => http.post('/api/hera/localLife/recruit/activity/activityAct', data, { transform: false })

/**
 * 运营编辑活动品补充信息（包括库存）
 * @param data
 * @returns
 */
export const submitExtraInfo = data => {
  const payload = {
    activity_id: data?.activity_id,
    product_extra_info: {
      product_main_images: data?.product_extra_info?.product_main_images?.map(item => ({ width: item.width, height: item.height, url: item.url })),
      product_long_images: data?.product_extra_info?.product_long_images?.map(item => ({ width: item.width, height: item.height, url: item.url })),
      current_stock: data?.product_extra_info?.current_stock,
      change_stock: data?.product_extra_info?.change_stock
    }
  }
  return http.post('/api/hera/localLife/recruit/activity/fillExtraInfo', payload, { transform: false })
}

type bundleCreatedType = 0 | 1 // 0: 未完成组品；1: 完成组品
// 查看活动的大品组品是否已成功
export const hasProductBundleCreated = async (activityId: string) => {
  const res = await http.get<{ has_product_bundle_created: bundleCreatedType}>('/api/hera/localLife/recruit/activity/hasProductBundleCreated', { params: { activity_id: activityId }, transform: false })
  return Boolean(res?.has_product_bundle_created)
}

export {
  UnuseCanRefundType,
  IActivity,
  CreateActivityParams,
  ActivityStatusEnum,
  activityStatusMap
}
