import { computed, Ref } from 'vue'
import {
  useActivityBaseInfoScheme,
  useSellerInfoScheme,
  useActivityProductInfoScheme,
  useSaleRuleScheme,
  useUseRuleScheme,
  useActivityDescScheme,
  useRecommendActivityScheme,
  useActivityProductExtraInfoScheme
} from 'shared/scheme/camp-admin/activityLife/formBaseScheme'
import { CreatorTypeEnum } from 'shared/scheme/camp-admin/activityLife/type'
import { IActivity, CreateActivityParams, ActivityStatusEnum } from './service'

export const useFormItemLayout = (activity: Ref<IActivity>, model: Ref<CreateActivityParams>, isProductBundleCreated: Ref<boolean>) => {
  // 完成组品后不可编辑
  const editDisabled = computed(() => !!activity.value?.activity_status && [ActivityStatusEnum.SUCCEED, ActivityStatusEnum.FINISHED].includes(activity.value?.activity_status))
  // 开始报名后不可编辑
  const editDisabledAfterApply = computed(() => !!activity.value?.activity_status && [ActivityStatusEnum.SIGNING_UP, ActivityStatusEnum.EDITING_PUBLISH, ActivityStatusEnum.PROCESSING_PUBLISH, ActivityStatusEnum.SUCCEED, ActivityStatusEnum.FINISHED].includes(activity.value?.activity_status))

  const isProvider = computed(() => activity.value.creator_type === CreatorTypeEnum.PROVIDER)

  const schemeInfo = computed(() => ({
    layoutScheme: [
      useActivityBaseInfoScheme(),
      useSellerInfoScheme(editDisabled.value, model, activity),
      useActivityProductInfoScheme(model, editDisabled, isProductBundleCreated),
      useSaleRuleScheme(isProvider.value),
      useUseRuleScheme(model),
      useActivityDescScheme(),
      useRecommendActivityScheme(),
      useActivityProductExtraInfoScheme(activity),
    ]
  }))

  return {
    schemeInfo,
    editDisabledAfterApply,
    editDisabled,
  }
}
