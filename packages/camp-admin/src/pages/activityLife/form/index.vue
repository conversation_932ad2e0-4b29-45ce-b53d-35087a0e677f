<template>
  <Space ref="FormContainer" class="ultra-form-container" v-bind="spaceProps">
    <Space class="ultra-form-header" v-bind="spaceProps">
      <PageHeader type="breadcrumb" :items="[{ title: '交易活动管理', to: { name: 'activityList' } }, { title: `${statusText}活动` }]" />
    </Space>
    <Space v-loading="formLoading" class="ultra-form-body">
      <Space class="ultra-form-content" justify="center">
        <Space class="ultra-form-items" v-bind="spaceProps">
          <Space v-if="activityId" align="center">
            <Text type="h5" bold>活动详情</Text>
            <Text v-if="!isCopy" color="primary" bold>状态：{{ activityStatusDesc }}</Text>
          </Space>
          <Form ref="formRef" :model="model" label-width="260px" style="min-width: 990px">
            <FormContentEnhance v-for="(item, index) in layoutScheme" :key="index" v-bind="item" :readonly="readonly" />
          </Form>
          <ActivityBkData v-if="activityInfo.activity_status === ActivityStatusEnum.SUCCEED && activityInfo.main_item_id && isProvider" :id="activityInfo.main_item_id" />
        </Space>
      </Space>
    </Space>
    <Space class="ultra-form-footer" block justify="center" :style="{ width: `${footerWidth}px`, marginLeft: `${footerRight}px` }">
      <template v-if="isProvider && !isCopy">
        <Button type="primary" :disabled="!isProcessing" @click="handleAction('processPass')">审核通过</Button>
        <Button :disabled="!isProcessing" @click="handleAction('processReject')">审核驳回</Button>
        <Button v-if="activityInfo?.main_item_id" @click="qrcodeModalVisible = true">预览</Button>
      </template>
      <template v-else-if="!readonly">
        <Button :loading="actionLoading" @click="handleAction('cancel')">取消</Button>
        <Button type="primary" :loading="subBtnLoading" @click="handleAction('submit')">提交</Button>
      </template>
      <template v-else-if="activityInfo?.activity_status === ActivityStatusEnum.UN_START">
        <Button type="primary" :loading="enableLoading" @click="handleAction('invite')">开启邀约</Button>
        <Button type="primary" :loading="editLoading" @click="handleAction('edit')">编辑活动</Button>
        <Button type="danger" :loading="terminateLoading" @click="handleAction('terminate')">终止活动</Button>
      </template>
      <template v-else-if="activityInfo?.activity_status === ActivityStatusEnum.SIGNING_UP">
        <Button type="primary" :loading="editLoading" @click="handleAction('edit')">编辑活动</Button>
        <Button type="primary" :loading="completeLoading" @click="handleAction('completeGroup')">完成组品</Button>
        <Button type="danger" :loading="terminateLoading" @click="handleAction('terminate')">终止活动</Button>
      </template>
      <template v-else-if="activityInfo?.activity_status === ActivityStatusEnum.SUCCEED">
        <Button type="primary" :loading="editLoading" @click="handleAction('edit')">编辑活动</Button>
        <Button v-if="activityInfo?.main_item_id" @click="qrcodeModalVisible = true">预览</Button>
        <Button v-if="!!activityInfo?.already_fill_product_extra_info" type="danger" :loading="stopLoading" @click="handleAction('stop')">停售商品</Button>
        <Button type="primary" :loading="completeConfigLoading" @click="handleAction('completeConfig')">完成配置</Button>
      </template>
    </Space>
    <Modal
      v-model:visible="qrcodeModalVisible"
      :with-footer="false"
      :width="500"
      title="预览商品"
    >
      <div>
        <QrcodeCard
          :link="previewItemLink"
          :download-visible="false"
        />
        <div class="qrcode-desc">
          <div class="item">
            <Text tooltip ellipsis style="width: 300px;">商品链接：{{ previewItemLink }}</Text>
            <Icon icon="copy" color="primary" style="cursor: pointer;" @click="doCopy(previewItemLink)" />
          </div>
          <div class="item">
            <Text tooltip ellipsis style="width: 300px;">SKUid：{{ activityInfo?.main_sku_id }}</Text>
            <Icon icon="copy" color="primary" style="cursor: pointer;" @click="doCopy(activityInfo?.main_sku_id)" />
          </div>
          <div class="item">
            <Text tooltip ellipsis style="width: 300px;">商品id：{{ activityInfo?.main_item_id }}</Text>
            <Icon icon="copy" color="primary" style="cursor: pointer;" @click="doCopy(activityInfo?.main_item_id)" />
          </div>
        </div>
      </div>
    </Modal>
    <ProcessModal
      v-if="isProcessing && activityInfo?.activity_status"
      v-model:visible="processModalVisible"
      :activity-status="activityInfo?.activity_status"
      :action="processAction"
      :activity-id="activityId"
    />
  </Space>
</template>
<script setup lang="tsx">
  import {
    ref, onMounted, onUnmounted, computed, h, nextTick
  } from 'vue'
  import {
    Space, Button, Form2 as Form, vLoading, Text, toast2 as toast, Modal
  } from '@xhs/delight'
  import { useRoute, useRouter } from 'vue-router'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import { get, set, cloneDeep } from 'lodash'
  import QrcodeCard from '@xhs/delight-material-life-show-qrcode/src/QrcodeCard.vue'
  import copy from '@/utils/copy'
  import { Icon } from '@xhs/yam-beer'
  import { CreatorTypeEnum } from 'shared/scheme/camp-admin/activityLife/type'
  import { activityStatusMapDetail } from 'shared/dictMaterials/camp-admin/activityLife/constant'
  import { spaceProps } from '../../../composables/use-props'
  import { useForm } from './useForm'
  import { useFormItemLayout } from './scheme'
  import { dictMaterials } from '../dictMaterials/index'
  import ProcessModal from '../dictMaterials/ProcessModal/index.vue'
  import ActivityBkData from '../dictMaterials/ActivityBkData/index.vue'
  import {
    IActivity,
    CreateActivityParams,
    getActivityInfo,
    updateActivityAction,
    createActivity,
    submitExtraInfo,
    hasProductBundleCreated,
    ActivityStatusEnum
  } from './service'

  const route = useRoute()
  const router = useRouter()

  const readonly = ref(false)

  const activityInfo = ref<IActivity>({} as IActivity)

  const activityId = computed(() => route.params?.id as string)

  const statusText = computed(() => (activityId.value && !isCopy.value ? '编辑' : '创建'))

  const isCopy = computed(() => route.query?.type === 'copy')

  const activityStatusDesc = computed(() => {
    if (!activityInfo.value?.activity_status) return ''
    return activityStatusMapDetail[activityInfo.value?.activity_status]
  })

  const formRef = ref() // 表单的Ref对象

  const formLoading = ref(false)
  const actionLoading = ref(false)
  const editLoading = ref(false)
  const enableLoading = ref(false)
  const terminateLoading = ref(false)
  const completeLoading = ref(false)
  const stopLoading = ref(false)
  const completeConfigLoading = ref(false)
  const isProductBundleCreated = ref(false)// 大品组品是否已成功

  const qrcodeModalVisible = ref(false)
  const previewItemLink = computed(() => `xhsdiscover://rn/lancer-life/camping/exchange-product/${activityInfo.value?.main_sku_id}?geolocation=0&sourceFrom=default`)

  const processModalVisible = ref(false)
  const processAction = ref<'pass' | 'reject'>('pass')
  const isProcessing = computed(() => (activityInfo.value?.activity_status === ActivityStatusEnum.PROCESSING_ACTIVITY || activityInfo.value?.activity_status === ActivityStatusEnum.PROCESSING_PUBLISH))
  const isProvider = computed(() => activityInfo.value?.creator_type === CreatorTypeEnum.PROVIDER)

  const {
    model, // 表单数据对象
    subBtnLoading,
    submit, // 提交方法
  } = useForm(formRef)

  const { schemeInfo, editDisabledAfterApply } = useFormItemLayout(activityInfo, model, isProductBundleCreated)

  // 行业元信息
  const layoutScheme = computed(() => schemeInfo.value.layoutScheme)

  const FormDictItem = ({ item }) => {
    const Cmp = dictMaterials[item?.componentProps?.is]
    const modelVal = computed({
      get: () => get(model.value, item.filed),
      set: val => set(model.value, item.filed, val),
    })
    let finalReadOnly
    if (isCopy.value) {
      finalReadOnly = false
    } else {
      finalReadOnly = isProvider.value || readonly.value || ('readonly' in (item?.componentProps?.props || {}) ? item?.componentProps?.props?.readonly : editDisabledAfterApply.value)
    }
    return <Cmp v-model={modelVal.value} {...item.componentProps?.props} readonly={finalReadOnly} formRef={formRef} product={model.value?.product_info?.product} />
  }

  const FormContentEnhance = ItemProps => {
    const {
      groupTitleInfo,
      formItemList,
    } = ItemProps
    const GroupTitleCmp = dictMaterials[groupTitleInfo?.is]

    if (groupTitleInfo?.visible) {
      if (!groupTitleInfo.visible(activityInfo.value)) {
        return null
      }
    }

    return (
    <>
      <GroupTitleCmp {...groupTitleInfo?.props} >{groupTitleInfo?.slot}</GroupTitleCmp>
      {formItemList?.map(item => (<FormDictItem item={item} />))}
    </>
  )
  }

  async function fetchActivityInfo() {
    if (activityId.value) {
      formLoading.value = true
      try {
        const res = await getActivityInfo({ activity_id: activityId.value })
        if (isCopy.value) { // 复制活动时，只复制第一次创建时可填的字段
          if (res.activity_id) delete res.activity_id
          if (res.activity_status) delete res.activity_status
          if (res.main_item_id) delete res.main_item_id
          if (res.main_sku_id) delete res.main_sku_id
          if (res.product_extra_info) delete res.product_extra_info
          if (res.audit_info) delete res.audit_info
          if (res.seller_info?.participant_group_list) {
            res.seller_info.participant_group_list = res.seller_info.participant_group_list.map(item => {
              if (item.tag_config_id) delete item.tag_config_id
              return item
            })
          }
        }
        activityInfo.value = cloneDeep(res)
        model.value = {
          ...model.value,
          ...res
        } as unknown as CreateActivityParams
        if (!res.seller_info?.is_distinguish_partici_categories) {
          model.value.seller_info.is_distinguish_partici_categories = 'false'
        }
        // 如果当前不区分商家品类，则默认给区分品类时增加一组默认值，方便切换到需要区分品类时，有一个空白分组可以直接填写
        if (model.value.seller_info?.is_distinguish_partici_categories === 'false') {
          model.value.seller_info.participant_group_list = [{
            config_name: '',
            participant_id_list: [],
          }]
        }
        readonly.value = !isCopy.value
        isProductBundleCreated.value = await hasProductBundleCreated(activityId.value)
      } catch (error) {
        console.error(error)
      }
      formLoading.value = false
    }
  }

  function handleCancel() {
    // if (activityId.value) {
    //   readonly.value = true
    //   return
    // }
    Modal.warning({
      title: '确认离开么',
      content: h(Text, {}, {
        default: () => `活动${statusText.value}未提交，你所填写的信息将不会保存`
      }),
      cancelText: `放弃${statusText.value}`,
      confirmText: '我再想想',
      onConfirm: close => {
        close?.()
      },
      onCancel(close) {
        close?.()
        router.push({ name: 'activityList' })
      }
    })
  }

  function handleSubmit() {
    submit(router, createActivity)
  }

  async function handleActivity(act_scene) {
    try {
      if (act_scene === 1) {
        enableLoading.value = true
      } else if (act_scene === 2) {
        // if (model.value.product_info.can_use_count ===) {
        //   toast.danger('请先完成组品')
        //   return
        // }
        completeLoading.value = true
      } else if (act_scene === 3) {
        stopLoading.value = true
      } else if (act_scene === 4) {
        terminateLoading.value = true
      }
      await updateActivityAction({
        activity_id: activityId.value,
        act_scene
      })
      fetchActivityInfo()
    } catch (err: any) {
    // toast.danger(err?.message)
    } finally {
      if (act_scene === 1) {
        enableLoading.value = false
      } else if (act_scene === 2) {
        completeLoading.value = false
      } else if (act_scene === 3) {
        stopLoading.value = false
      } else if (act_scene === 4) {
        terminateLoading.value = false
      }
    }
  }

  async function handleConfig() {
    try {
      completeConfigLoading.value = true
      formRef.value.validate().then(async () => {
        await submitExtraInfo(model.value)
        completeConfigLoading.value = false
        toast.success('完成配置补充成功')
        // delay 1s
        await new Promise(resolve => setTimeout(resolve, 1000))
        fetchActivityInfo()
      }).catch(() => {
        completeConfigLoading.value = false
      })
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err)
    }
  }

  /**
   * 分发动作
   * @param actionType
   */
  function handleAction(actionType) {
    switch (actionType) {
      case 'cancel':
        handleCancel()
        break
      case 'submit':
        handleSubmit()
        break
      case 'invite':
        handleActivity(1)
        break
      case 'edit':
        readonly.value = false
        break
      case 'terminate':
        handleActivity(4)
        break
      case 'completeGroup':
        handleActivity(2)
        break
      case 'stop':
        handleActivity(3)
        break
      case 'completeConfig':
        handleConfig()
        break
      case 'processPass':
        processModalVisible.value = true
        processAction.value = 'pass'
        break
      case 'processReject':
        processModalVisible.value = true
        processAction.value = 'reject'
        break
      default:
        break
    }
  }

  /** 吸顶按钮模块的宽度自适应获取Start */
  const FormContainer = ref() // 容器的Ref对象
  const footerWidth = ref() // 吸底的按钮区域宽度
  const footerRight = ref() // 吸底的按钮区域宽度

  const handleResize = () => { // 处理布局的自适应
    footerWidth.value = FormContainer.value?.$el?.clientWidth
    footerRight.value = FormContainer.value.$el?.getBoundingClientRect()?.right - FormContainer.value?.$el?.clientWidth
  }

  const doCopy = (str?:string) => {
    if (!str) return
    if (copy(str)) {
      toast.success('复制成功')
    } else {
      toast.danger('复制失败')
    }
  }

  onMounted(() => {
    fetchActivityInfo()
    handleResize() // 初始化
    window.addEventListener('resize', handleResize)
    nextTick(() => {
      const scrollElement = document.querySelector('#scroll-page-container')
      if (scrollElement) {
        scrollElement.scrollTop = 0
      }
    })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
/** 吸顶按钮模块的宽度自适应获取End */
</script>

<style lang="stylus" scoped>
.ultra-form-container
  width 100%
  padding-bottom 100px
  &.d-space-vertical
    gap 24px

.ultra-form-body
  background #FFFFFF
  border-radius 8px
  padding 24px
.ultra-form-content
  width 100%
.ultra-form-footer
  position fixed
  bottom 0
  left 0
  background-color #fff
  z-index 99
  border-top 1px solid #e8e8e8
  padding 18px 0 22px
  /deep/.d-button.d-button-default
    width 108px
.qrcode-desc
  display flex
  flex-direction column
  gap 10px
  margin 0 auto
  .item
    margin 0 auto
</style>
