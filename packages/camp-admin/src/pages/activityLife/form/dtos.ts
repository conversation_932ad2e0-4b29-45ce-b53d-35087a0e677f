import { cloneDeep } from 'lodash'
import { priceToCentByYuan, priceToYuanByCent } from '@/utils/price'
import { initTimeRangeFactory } from '../utils/time'

export const transformParams = params => {
  const formValue = cloneDeep(params)
  const { seller_info, product_info } = formValue

  if (formValue.area_info?.region_list?.length) { // edith不支持二维数组，需要转换
    formValue.area_info.region_list = formValue.area_info.region_list.map(item => ({ regions: item }))
  }

  if (product_info?.unit_price) {
    product_info.unit_price = priceToCentByYuan(product_info?.unit_price)
  }

  if (seller_info?.seller_list?.length) {
    seller_info.import_seller_id_list = [...new Set([
      ...(seller_info.import_seller_id_list || []),
      ...(seller_info?.seller_list.map(item => item.seller_id) || [])])]
    delete seller_info?.seller_list
  }

  if (seller_info?.is_distinguish_partici_categories === 'false') {
    delete seller_info?.participant_group_list
    delete product_info?.repeat_veri_cnt_of_single_seller_group
  }
  if (seller_info?.participant_group_list?.length) {
    seller_info.participant_group_list.forEach(item => {
      if (item.seller_list) {
        item.participant_id_list = item.participant_id_list.concat([...new Set([...(item.seller_list?.map((item2: { seller_name: string; seller_id: string }) => item2.seller_id) || [])])])
      }
      delete item?.seller_list
      delete item?.local_id
    })
  }

  if (formValue.activity_desc?.act_venue_config?.activity_item_icon) {
    formValue.activity_desc.act_venue_config.activity_item_icon = formValue.activity_desc?.act_venue_config?.activity_item_icon.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.activity_main_images) {
    formValue.activity_desc.activity_main_images = formValue.activity_desc.activity_main_images.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.act_venue_config?.activity_icon) {
    formValue.activity_desc.act_venue_config.activity_icon = formValue?.activity_desc.act_venue_config?.activity_icon.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.act_venue_config?.activity_background_img) {
    formValue.activity_desc.act_venue_config.activity_background_img = formValue?.activity_desc.act_venue_config?.activity_background_img.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }
  if (formValue.activity_desc?.act_venue_config?.activity_id_and_name_map?.length) {
    // formValue.activity_desc.act_venue_config.recommend_act_id_list = formValue?.activity_desc.act_venue_config?.activity_id_and_name_map.map(item => {
    //   if (item.name && item.id && item.name !== '未查询到哦') {
    //     return item.id
    //   }
    //   return undefined // 过滤掉无效的数据
    // }).filter(item => item)
    formValue.activity_desc.act_venue_config.recommend_act_id_list = formValue?.activity_desc.act_venue_config?.activity_id_and_name_map.map(item => item.id).filter(item => item)
    formValue.activity_desc.act_venue_config.activity_id_and_name_map = undefined
  } else if (formValue.activity_desc.act_venue_config) {
    formValue.activity_desc.act_venue_config.recommend_act_id_list = []
    formValue.activity_desc.act_venue_config.activity_id_and_name_map = undefined
  }

  return formValue
}

export const transformRes = res => {
  if (res.activity_desc?.activity_main_images) {
    res.activity_desc.activity_main_images = res.activity_desc.activity_main_images.map(item => ({ width: item.width, height: item.height, url: item.url }))
  }

  res.activity_status = +res.activity_status

  const { product_info } = res
  const { product, unit_price } = product_info || {}
  const { attr_key_value_map = {} } = product || {}

  product_info.unit_price = priceToYuanByCent(unit_price)

  const { can_no_use_date, use_date, use_time } = attr_key_value_map

  if (res.area_info?.region_list?.length) { // edith不支持二维数组，需要转换
    res.area_info.region_list = res.area_info.region_list.map(item => item.regions)
  }

  try {
    const canNoUseDate = JSON.parse(can_no_use_date)
    attr_key_value_map.can_no_use_date = canNoUseDate
  } catch (e) {
    console.log(e)
  }

  try {
    const useDate = JSON.parse(use_date)
    attr_key_value_map.use_date = useDate
  } catch (e) {
    console.log(e)
  }

  try {
    const useDate = JSON.parse(use_date)
    attr_key_value_map.use_date = useDate
  } catch (e) {
    console.log(e)
  }

  try {
    const useTime = JSON.parse(use_time)
    if (useTime.length === 0) {
      useTime.push(initTimeRangeFactory())
    }
    attr_key_value_map.use_time = useTime
  } catch (e) {
    console.log(e)
  }

  const activityMap = res?.activity_desc?.act_venue_config?.activity_id_and_name_map as Record<string, string>
  if (activityMap) {
    res.activity_desc.act_venue_config.activity_id_and_name_map = Object.keys(activityMap).map(key => ({
      id: key,
      name: activityMap[key],
    }))
  }

  // 实在没有办法了，妥协一下。。
  const roleMap = {
    1: 'BUSINESS_OPERATIONS_STAFF',
    2: 'SELLER'
  }
  if ([1, 2].includes(res?.product_info?.no_use_date_config_role)) {
    res.product_info.no_use_date_config_role = roleMap[res?.product_info?.no_use_date_config_role]
  }
  if ([1, 2].includes(res?.product_info?.use_time_config_role)) {
    res.product_info.use_time_config_role = roleMap[res?.product_info?.use_time_config_role]
  }

  return res
}
