import { ref } from 'vue'
import TableColumn from '../../../components/table-column/index.vue'

export const scheme = ref({
  tableColumnScheme: [
    {
      title: '序号',
      dataIndex: 'key',
      minWidth: 80,
      render: ({ rowData }) => <TableColumn dataIndex='key' rowData={rowData} />
    },
    {
      title: '问题描述',
      dataIndex: 'question_description',
      minWidth: 120,
      render: ({ rowData }) => <TableColumn dataIndex='question_description' rowData={rowData} />
    },
    {
      title: '问题回答',
      dataIndex: 'question_answer',
      width: 700,
      render: ({ rowData }) => <TableColumn dataIndex='question_answer' rowData={rowData} />
    },
  ],
  tableActionScheme: [
    { text: '编辑' },
    { text: '删除' },
  ]
})
