<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <div class="toolbar">
          <Button
            class="btn"
            type="primary"
            @click="handleModal"
          >
            新增问题
          </Button>
        </div>
        <Table
          size="large"
          :loading="loading"
          :columns="tableColumns"
          :data-source="listSource"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
        </Table>
      </Space>
    </div>
    <Modal
      v-model:visible="modalVisible"
      :title="`${model.id ? '编辑' : '新增'}回复问题`"
      :confirm-button-props="{ loading: submitLoading }"
      @cancel="modalVisible = false"
      @confirm="submit"
    >
      <Form ref="formRef" :model="model" :rules="rules" label-position="top">
        <FormItem label="问题描述" name="question_description" required>
          <Input v-model="model.question_description" :max-length="20" placeholder="问题描述请保持在20个字数以内" class="modal-input" />
        </FormItem>
        <FormItem label="问题回答" name="question_answer" required>
          <TextArea v-model="model.question_answer" :max-length="400" placeholder="问题回答请保持在400个字数以内" class="modal-input" />
        </FormItem>
      </Form>
    </Modal>
  </Space>
</template>

<script lang="ts" setup>
  import {
    Space, Table, Text, toast2 as toast, Modal, Button, FormItem2 as FormItem, Form2 as Form, Input, TextArea
  } from '@xhs/delight'
  import { ref, h } from 'vue'
  import { tryCatch } from 'shared/utils'
  import {
    getQAList, deleteQA, QAItem, upsertQA
  } from '../../../services/uniReplyManage'
  import { spaceProps, useTableColumns } from '../../../composables/use-props'
  import { useList } from '../../../composables/use-list'
  import { scheme } from './scheme'

  const {
    listSource,
    loading,
    fetchList,
  } = useList(scheme, getQAList)

  const formRef = ref()

  const handleDelete = (row: QAItem) => {
    if (!row.id) return
    const modal = Modal.warning({
      title: '确认删除？',
      content: h(Text, {}, {
        default: () => '确定删除此问题？'
      }),
      onConfirm: async () => {
        modal.update({
          confirmButtonProps: {
            loading: true
          }
        })
        const [, err] = await tryCatch(() => deleteQA(row.id!))
        modal.update({
          confirmButtonProps: {
            loading: false
          }
        })
        if (err) return
        toast.success('删除成功')
        fetchList()
        modal.destroy()
      },
      onCancel: () => modal.destroy()
    })
  }

  const submitLoading = ref<boolean>(false)
  const modalVisible = ref<boolean>(false)

  const handleModal = (row?: QAItem) => {
    modalVisible.value = true
    model.value.id = row?.id
    model.value.question_description = row?.question_description || ''
    model.value.question_answer = row?.question_answer || ''
  }

  const { tableColumns } = useTableColumns(scheme, [handleModal, handleDelete])

  const model = ref<QAItem>({
    question_description: '',
    question_answer: ''
  })

  const rules = {
    question_description: [
      { required: true, message: '问题描述不能为空', trigger: 'blur' }
    ],
    question_answer: [
      { required: true, message: '问题回答不能为空', trigger: 'blur' }
    ]
  }

  const submit = () => {
    formRef.value.validate().then(async () => {
      submitLoading.value = true
      const [, err] = await tryCatch(() => upsertQA(model.value))
      submitLoading.value = false
      if (err) return
      toast.success(`${model.value.id ? '编辑' : '新增'}成功`)
      modalVisible.value = false
      fetchList()
    }).catch(() => {})
  }

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  :deep(.ultra-material-toolbar-wrap)
    padding 0
.toolbar
  display flex
  .btn
    width 88px
  .tip
    height 100%
    margin-left 20px
    line-height 32px
.modal-input
  width 100%
</style>
