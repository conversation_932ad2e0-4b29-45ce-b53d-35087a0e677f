<template>
  <Tabs v-model="activeTabId">
    <TabPane label="自动回复配置">
      <AutoReplyList />
    </TabPane>
    <TabPane label="用户问题收集">
      <UserQuestionsList />
    </TabPane>
  </Tabs>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Tabs, TabPane } from '@xhs/delight'
  import AutoReplyList from './AutoReplyList/index.vue'
  import UserQuestionsList from './UserQuestionsList/index.vue'

  const activeTabId = ref('自动回复配置')
</script>
