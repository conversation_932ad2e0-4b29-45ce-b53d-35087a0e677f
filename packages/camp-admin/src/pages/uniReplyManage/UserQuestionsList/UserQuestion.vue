<template>
  <Space style="width: 100%" direction="vertical" :gap="5" align="start">
    <div v-for="item in rowData" :key="item.content" style="width: 100%">
      <Image v-if="item.type === QuestionTypeEnum.IMG" :src="item.content" style="width: 60px; height: 60px" preview />
      <Text v-else ellipsis tooltip style="width: 100%">{{ item.content ?? '-' }}</Text>
    </div>
  </Space>
</template>

<script setup lang="ts">
  import Image from '@xhs/delight-material-life-image'
  import { Text, Space } from '@xhs/delight'
  import { QuestionTypeEnum } from '@/services/uniReplyManage'

  defineProps<{
    rowData: {
      type: QuestionTypeEnum
      content: string
    }[]
  }>()
</script>
