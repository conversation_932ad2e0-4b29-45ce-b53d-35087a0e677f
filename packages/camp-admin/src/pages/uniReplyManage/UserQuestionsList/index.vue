<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Table
          size="large"
          :loading="loading"
          :columns="tableColumns"
          :data-source="listSource"
        >
          <template #td="{ data }">
            <Text ellipsis>{{ data }}</Text>
          </template>
        </Table>
        <Pagination
          v-model="baseParams.page_num"
          v-model:pageSize="baseParams.page_size"
          :total="total"
          style="margin-top: 24px"
          @change="fetchList(false)"
        />
      </Space>
    </div>
    <Modal
      v-model:visible="modalVisible"
      title="回复问题"
      :confirm-button-props="{ loading: submitLoading }"
      @cancel="modalVisible = false"
      @confirm="submit"
    >
      <Form ref="formRef" :model="model" label-position="top" :rules="{answer: { required: true, message: '问题回答不能为空！', trigger: 'blur' }}">
        <FormItem
          label="问题回答"
          name="answer"
          required
        >
          <TextArea v-model="model.answer" :max-length="400" placeholder="问题回答请保持在400个字数以内" class="modal-input" />
        </FormItem>
      </Form>
    </Modal>
  </Space>
</template>

<script lang="ts" setup>
  import {
    Space, Table, Text, toast2 as toast, Modal, FormItem2 as FormItem, Form2 as Form, TextArea, Pagination
  } from '@xhs/delight'
  import { ref } from 'vue'
  import { tryCatch } from 'shared/utils'
  import { getQuestionList, replyQuestion, QuestionItem } from '@/services/uniReplyManage'
  import { spaceProps, useTableColumns } from '../../../composables/use-props'
  import { useList } from '../../../composables/use-list'
  import { scheme } from './scheme'

  const formRef = ref()
  const model = ref({
    answer: ''
  })

  const {
    listSource,
    loading,
    total,
    baseParams,
    fetchList,
  } = useList(scheme, getQuestionList)

  const submitLoading = ref<boolean>(false)
  const modalVisible = ref<boolean>(false)
  const editItemId = ref('')

  const handleModal = (row?: QuestionItem) => {
    modalVisible.value = true
    editItemId.value = row?.user_id || ''
    model.value.answer = row?.latest_answer || ''
  }

  const { tableColumns } = useTableColumns(scheme, [handleModal])

  const submit = () => {
    formRef.value.validate().then(async () => {
      submitLoading.value = true
      const [, err] = await tryCatch(() => replyQuestion({
        user_id: editItemId.value,
        answer: model.value.answer
      }))
      submitLoading.value = false
      if (err) return
      toast.success('回复成功')
      modalVisible.value = false
      fetchList()
    }).catch(() => {})
  }

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  :deep(.ultra-material-toolbar-wrap)
    padding 0
.modal-input
  width 100%
</style>
