import { ref } from 'vue'
import TableColumn from '../../../components/table-column/index.vue'
import UserQuestion from './UserQuestion.vue'

export const scheme = ref({
  tableColumnScheme: [
    {
      title: '用户id',
      dataIndex: 'user_id',
      minWidth: 210,
      render: ({ rowData }) => <TableColumn dataIndex='user_id' rowData={rowData} />
    },
    {
      title: '最新提问时间',
      dataIndex: 'latest_time',
      minWidth: 190,
      render: ({ rowData }) => <TableColumn dataIndex='latest_time' rowData={rowData} />
    },
    {
      title: '用户问题',
      dataIndex: 'questions',
      width: 800,
      render: ({ rowData }) => <UserQuestion rowData={rowData.questions} />
    },
  ],
  tableActionScheme: [
    { text: '回复' },
  ]
})
