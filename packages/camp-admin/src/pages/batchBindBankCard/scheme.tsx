import { CardConfirmStatus, CardBindStatus, getSecureUserData } from '@/services/batchBindBankCard'
import TableColumn from '@/components/table-column/index.vue'
import {
  changeDesensitize,
  TableCellDesensitizeUI as TableCellDesensitize,
  // BizTypeEnum
} from 'shared/components/TableCellDesensitize'
import TableColumnCardBindStatus from './dictMaterials/TableColumnCardBindStatus/index.vue'
import TableColumnCardConfirmStatus from './dictMaterials/TableColumnCardConfirmStatus/index.vue'

export const scheme = {
  filterScheme: [
    {
      name: 'sellerId',
      label: '商家ID',
      component: {
        props: {
          placeholder: '请输入商家ID',
          clearable: true
        },
      },
    },
    {
      name: 'shopId',
      label: '门店ID',
      component: {
        props: {
          placeholder: '请输入门店ID',
          clearable: true
        },
      },
    },
    {
      name: 'cardConfirmStatus',
      label: '绑卡信息确认',
      component: {
        is: 'Select',
        props: {
          options: [
            { label: '未确认', value: CardConfirmStatus.NOT_CONFIRM },
            { label: '已确认', value: CardConfirmStatus.CONFIRM },
            { label: '已拒绝', value: CardConfirmStatus.REFUSE },
            { label: '已失效', value: CardConfirmStatus.INVALID },
          ],
        },
      },
    },
    {
      name: 'cardBindStatus',
      label: '绑卡是否成功',
      component: {
        is: 'Select',
        props: {
          options: [
            { label: '是', value: CardBindStatus.BIND },
            { label: '否', value: CardBindStatus.UN_BIND },
            { label: '其他', value: CardBindStatus.WAIT_BIND },
          ],
        },
      },
    },
  ],
  tableColumnScheme: [{
    title: '商家ID',
    minWidth: 100,
    dataIndex: 'sellerId',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="sellerId" />
  },
  {
    title: '商家名称',
    minWidth: 100,
    dataIndex: 'sellerName',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="sellerName" />
  },
  {
    title: '门店ID',
    minWidth: 100,
    dataIndex: 'shopId',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="shopId" />
  },
  {
    title: '门店名称',
    minWidth: 100,
    dataIndex: 'shopName',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="shopName" />
  },
  {
    title: '绑卡人名称',
    minWidth: 100,
    dataIndex: 'cardUserName',
    render: ({ rowData }) => <TableCellDesensitize
      content={rowData?.cardUserName}
      fullContent={rowData?.desensitizeData?.['dataList.cardUserName']}
      status={rowData?.secureStatus}
      secureFields={['dataList.cardUserName', 'dataList.cardNo', 'dataList.mobile', 'dataList.cardUserCertNo']}
      // @ts-ignore
      onClick={() => changeDesensitize(rowData, getSecureUserData(rowData))}
    />
  },
  {
    title: '身份证号',
    minWidth: 150,
    dataIndex: 'cardUserCertNo',
    render: ({ rowData }) => <TableCellDesensitize
      content={rowData?.cardUserCertNo}
      fullContent={rowData?.desensitizeData?.['dataList.cardUserCertNo']}
      status={rowData?.secureStatus}
      secureFields={['dataList.cardUserName', 'dataList.cardNo', 'dataList.mobile', 'dataList.cardUserCertNo']}
      // @ts-ignore
      onClick={() => changeDesensitize(rowData, getSecureUserData(rowData))}
    />
  },
  {
    title: '银行卡号',
    minWidth: 150,
    dataIndex: 'cardNo',
    render: ({ rowData }) => <TableCellDesensitize
      content={rowData?.cardNo}
      fullContent={rowData?.desensitizeData?.['dataList.cardNo']}
      status={rowData?.secureStatus}
      secureFields={['dataList.cardUserName', 'dataList.cardNo', 'dataList.mobile', 'dataList.cardUserCertNo']}
      // @ts-ignore
      onClick={() => changeDesensitize(rowData, getSecureUserData(rowData))}
    />
  },
  {
    title: '开户行名称',
    minWidth: 100,
    dataIndex: 'bankName',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="bankName" />
  },
  {
    title: '银行预留手机号',
    minWidth: 150,
    dataIndex: 'mobile',
    render: ({ rowData }) => <TableCellDesensitize
      content={rowData?.mobile}
      fullContent={rowData?.desensitizeData?.['dataList.mobile']}
      status={rowData?.secureStatus}
      // secureFields={rowData?.secureFields}
      secureFields={['dataList.cardUserName', 'dataList.cardNo', 'dataList.mobile', 'dataList.cardUserCertNo']}
      // @ts-ignore
      onClick={() => changeDesensitize(rowData, getSecureUserData(rowData))}
    />
  },
  {
    title: '支行信息',
    minWidth: 100,
    dataIndex: 'bankSubBranchName',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="bankSubBranchName" />
  },
  {
    title: '绑卡信息上传人',
    minWidth: 125,
    dataIndex: 'cardInfoOperator',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="cardInfoOperator" />
  },
  {
    title: '绑卡信息链接',
    minWidth: 100,
    dataIndex: 'bankConfirmUrl',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="bankConfirmUrl" />
  },
  {
    title: '绑卡信息确认',
    minWidth: 110,
    dataIndex: 'cardConfirmStatus',
    render: ({ rowData }) => <TableColumnCardConfirmStatus rowData={rowData} dataIndex="cardConfirmStatus" />
  },
  {
    title: '绑卡信息确认人',
    minWidth: 125,
    dataIndex: 'confirmAccountName',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="confirmAccountName" />
  },
  {
    title: '批量绑卡操作人',
    minWidth: 125,
    dataIndex: 'cardBindOperator',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="cardBindOperator" />
  },
  {
    title: '绑卡是否成功',
    minWidth: 110,
    dataIndex: 'cardBindStatus',
    render: ({ rowData }) => <TableColumnCardBindStatus rowData={rowData} dataIndex="cardBindStatus" />
  },
  {
    title: '绑卡失败原因',
    minWidth: 150,
    dataIndex: 'cardBindFailedReason',
    render: ({ rowData }) => <TableColumn rowData={rowData} dataIndex="cardBindFailedReason" />
  }]
}
