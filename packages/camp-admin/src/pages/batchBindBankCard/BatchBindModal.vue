<template>
  <Modal
    v-model:visible="editVisible"
    title="绑卡信息"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Space direction="vertical" align="start" style="margin-bottom: 10px;">
      <Space align="start" size="2px">
        <Text type="description">1、</Text>
        <Text type="description">仅支持批量绑个体工商户法人卡，请先确保<span class="point">门店主体资质和法人资质已正确提交</span></Text>
      </Space>
      <Space align="start" size="2px">
        <Text type="description">2、</Text>
        <Text type="description">绑卡信息刷入后，请运营及时导出门店绑卡信息确认短链提供给品牌方，并督促门店/总店-财务子账号点击短链操作确认，<span class="point">门店未确认绑卡信息则平台无法协助批量绑卡</span></Text>
      </Space>
      <Space align="start" size="2px">
        <Text type="description">3、</Text>
        <Text type="description"><span class="point">输入绑卡信息后，门店请勿修改主体五要素信息，若修改，则该条绑卡信息失效，需要运营重新刷最新的绑卡信息</span></Text>
      </Space>
    </Space>
    <Form v-if="editVisible" label-width="73px">
      <FormItem label="模板下载">
        <Link
          target="_blank"
          href="https://fe-video-qc.xhscdn.com/fe-platform/75ea46b9b1d09ea29569123fb0dc19d44668d2b3/ska批量绑卡模板.xlsx?attname=fe-platform/75ea46b9b1d09ea29569123fb0dc19d44668d2b3/ska批量绑卡模板.xlsx.xlsx"
          download="绑卡信息模板.xlsx"
        >
          绑卡信息模板.xlsx
        </Link>
        <Text>据模板，填写要刷的绑卡信息</Text>
      </FormItem>
      <FormItem label="批量上传">
        <div v-if="!result" class="upload-container">
          <Spinner :spinning="loading">
            <FileUploader
              v-model="fileList"
              accept=".xlsx"
              :uploading="loading"
              :percent="percent"
              @update:modelValue="complete"
            />
          </Spinner>
          <div v-if="failed" class="message">
            <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
            <Text color="text-description">信息识别失败，请重新上传</Text>
          </div>
        </div>
        <UploadResult v-if="result" :result="result" text="操作" />
      </FormItem>
    </Form>
  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, ref, watch, onUnmounted
  } from 'vue'
  import {
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Link,
    toast,
    Spinner,
    Space
  } from '@xhs/delight'

  import { ClaimResult } from '@/services/poiClaim'
  import FileUploader from '@/components/upload-file/index.vue'
  import UploadResult from '@/components/upload-result/index.vue'
  import { getLongTask, postLongTask } from '@/services/longTaskv2'
  import { isFinished, computePercent } from '@/utils/longTask'
  import { LONG_TASK_STATUS } from '@/constants/longTask'
  import { GetLongTaskRes } from '@/services/batchBindBankCard'
  import { LONG_TASK_NAME } from './constant'

  const props = defineProps<{
    visible: boolean
  }>()

  const emit = defineEmits(['update:visible'])

  const fileList = ref<any[]>([])
  const failed = ref(false)
  const loading = ref(false)
  const percent = ref(0)

  const result = ref<ClaimResult | undefined>()
  let timer:number

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
        window.clearTimeout(timer)
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
      window.clearTimeout(timer)
    }
  })

  onUnmounted(() => {
    window.clearTimeout(timer)
  })

  const complete = async () => {
    loading.value = true
    try {
      const { taskId } = await postLongTask({
        input: {
          file_url: fileList.value[0]?.downloadUrl,
          extra: undefined
        },
        task_name: LONG_TASK_NAME.IMPORT
      })
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        percent.value = computePercent(res?.finishedCount, res?.totalCount) || percent.value
        if (isFinished(res?.status)) {
          if (res?.status === LONG_TASK_STATUS.FAIL) {
            toast.danger('上传失败，请重新上传')
            return
          }
          const { fileUrl = '', extraJson = '{}' } = res?.result
          const extra = JSON.parse(extraJson)
          result.value = {
            failReasonPath: fileUrl,
            successNum: extra?.success_count ?? 0,
            failNum: extra?.failed_count ?? 0
          }
          loading.value = false
          return
        }
        timer = window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error) {
      loading.value = false
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
.point
  color #FF2442
  font-weight bold
</style>
