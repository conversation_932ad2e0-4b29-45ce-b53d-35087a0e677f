<template>
  <Tooltip class="container" content="正在为您批量绑卡中，请勿进行其他操作" :validate="() => loading">
    <div class="container">
      <Button type="primary" :loading="loading" :disabled="!isOpen" @click="batchBind">批量绑卡</Button>
      <Progress v-show="loading" size="large" class="progress" :percent="percent" />
    </div>
  </Tooltip>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Button, toast2 as toast, Progress, Tooltip
  } from '@xhs/delight'

  import { postLongTask, getLongTask } from '@/services/longTaskv2'
  import { GetLongTaskRes, getPoiBankCardBindSwitch } from '@/services/batchBindBankCard'
  import { computePercent, isFinished } from '@/utils/longTask'
  import { LONG_TASK_STATUS } from '@/constants/longTask'
  import { LONG_TASK_NAME } from '../../constant'

  const loading = ref(false)
  const percent = ref(0)
  const isOpen = ref(false)

  async function batchBind() {
    loading.value = true
    try {
      const { taskId } = await postLongTask({
        task_name: LONG_TASK_NAME.BIND,
        input: {
          extra: {}
        }
      })
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        percent.value = computePercent(res?.finishedCount, res?.totalCount) || percent.value
        if (isFinished(res?.status)) {
          toast.info(`绑定${res?.status === LONG_TASK_STATUS.SUCCESS ? '成功！' : '失败'}`)
          loading.value = false
          return
        }
        window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error) {
      loading.value = false
    }
  }

  const getOpenPermission = async () => {
    try {
      const res = await getPoiBankCardBindSwitch()
      isOpen.value = res
    } catch (error) {
      isOpen.value = false
    }
  }

  getOpenPermission()

</script>

<style scoped lang="stylus">
.container
  display flex
  gap 10px
  align-items center
.progress
  width 120px
</style>
