<template>
  <Button type="primary" :loading="loading" @click="exportAllLink">导出全部确认链接</Button>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Button, toast2 as toast } from '@xhs/delight'

  import { postLongTask, getLongTask } from '@/services/longTaskv2'
  import { GetBankCardOptionalParams, GetLongTaskRes } from '@/services/batchBindBankCard'
  import { isFinished } from '@/utils/longTask'
  import { LONG_TASK_STATUS } from '@/constants/longTask'
  import { LONG_TASK_NAME } from '../../constant'

  const props = defineProps<{
    filterParams: GetBankCardOptionalParams
  }>()

  const loading = ref(false)

  async function exportAllLink() {
    loading.value = true
    try {
      const { taskId } = await postLongTask<GetBankCardOptionalParams>({
        task_name: LONG_TASK_NAME.DOWNLOAD,
        input: { extra: props.filterParams }
      })
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        if (isFinished(res?.status)) {
          toast.info(`导出${res?.status === LONG_TASK_STATUS.SUCCESS ? '成功！' : '失败'}`)
          if (res?.result?.fileUrl) {
            const url = res?.result?.fileUrl
            window.open(url)
          }
          loading.value = false
          return
        }
        window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error) {
      loading.value = false
    }
  }
</script>

<style scoped lang="stylus">

</style>
