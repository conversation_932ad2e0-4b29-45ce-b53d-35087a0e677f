<template>
  <Tag v-if="rowData[dataIndex] === CardBindStatus.BIND" color="green" size="small">{{ cardBindStatusMaps[CardBindStatus.BIND] }}</Tag>
  <Tag v-if="rowData[dataIndex] === CardBindStatus.UN_BIND" color="orange" size="small">{{ cardBindStatusMaps[CardBindStatus.UN_BIND] }}</Tag>
  <Text v-if="rowData[dataIndex] === CardBindStatus.WAIT_BIND">{{ cardBindStatusMaps[CardBindStatus.WAIT_BIND] }}</Text>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'

  import { Tag } from '@xhs/delight'
  import { CardBindStatus } from '@/services/batchBindBankCard'
  import { cardBindStatusMaps } from './model'

  defineProps({
    rowData: {
      type: Object,
      default() {
        return {}
      }
    },
    dataIndex: {
      type: String as PropType<string>,
      default: ''
    },
  })
</script>
