<template>
  <Tag v-if="rowData[dataIndex] === CardConfirmStatus.CONFIRM" color="green" size="small">{{ cardConfirmStatusMaps[CardConfirmStatus.CONFIRM] }}</Tag>
  <Tag v-if="rowData[dataIndex] === CardConfirmStatus.NOT_CONFIRM" color="orange" size="small">{{ cardConfirmStatusMaps[CardConfirmStatus.NOT_CONFIRM] }}</Tag>
  <Tag v-if="rowData[dataIndex] === CardConfirmStatus.REFUSE" color="red" size="small">{{ cardConfirmStatusMaps[CardConfirmStatus.REFUSE] }}</Tag>
  <Tag v-if="rowData[dataIndex] === CardConfirmStatus.INVALID" size="small">{{ cardConfirmStatusMaps[CardConfirmStatus.INVALID] }}</Tag>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'

  import { Tag } from '@xhs/delight'
  import { CardConfirmStatus } from '@/services/batchBindBankCard'
  import { cardConfirmStatusMaps } from './model'

  defineProps({
    rowData: {
      type: Object,
      default() {
        return {}
      }
    },
    dataIndex: {
      type: String as PropType<string>,
      default: ''
    },
  })
</script>
