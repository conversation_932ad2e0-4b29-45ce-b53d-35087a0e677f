import { computed, Ref } from 'vue'
import { GetBankCardOptionalParams } from '@/services/batchBindBankCard'
import BatchBindButton from '../dictMaterials/BatchBindButton/index.vue'
import ExportAllLinkBtn from '../dictMaterials/ExportAllLinkBtn/index.vue'

export const useToolBar = (
  uploadVisible: Ref<boolean>,
  filterParams: Ref<GetBankCardOptionalParams>
) => {
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '刷绑卡信息',
        onClick: () => {
          uploadVisible.value = true
        },
      },
      {
        is: ExportAllLinkBtn,
        props: {
          filterParams: filterParams.value,
        },
      },
      {
        is: BatchBindButton
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}
