<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader title="批量绑卡" />
      <OutlineFilter
        v-model="filterParams"
        :config="filterConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Toolbar
          v-model:selected="rowSelected"
          :config="toolBarConfig"
        />
        <Table
          v-model:selected="rowSelected"
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :row-selection="rowSelection"
          :loading="loading"
        />
        <Pagination
          v-model="baseParams.page_num"
          v-model:pageSize="baseParams.page_size"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
    <BatchBindModal v-model:visible="uploadVisible" />
  </Space>

</template>
<script setup lang="ts">
  import { computed, ref } from 'vue'
  import {
    Space, Table, Pagination,
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import { useList } from '@/composables/use-list'
  import { getPoiBankCardList } from '@/services/batchBindBankCard'
  import { spaceProps, useFilter, useTableColumns } from '@/composables/use-props'
  import { scheme as schemeNormal } from './scheme'
  import BatchBindModal from './BatchBindModal.vue'
  import { useToolBar } from './compsables/useToolBar'

  const scheme = computed(() => schemeNormal)

  const uploadVisible = ref(false)

  const {
    listSource,
    total,
    loading,
    baseParams,
    filterParams,
    fetchList
  } = useList(scheme, getPoiBankCardList)

  const rowSelected = ref<any[]>([])
  const rowSelection = {
    getCheckboxProps: () => ({
      selectable: true,
    }),
    onSelect: () => {},
  }

  const { toolBarConfig } = useToolBar(uploadVisible, filterParams)
  const { filterConfig } = useFilter(scheme, fetchList)
  const { tableColumns } = useTableColumns(scheme, [])

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  :deep(.ultra-material-toolbar-wrap)
    padding 0
</style>
