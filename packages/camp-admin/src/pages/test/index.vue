<template>
  <Cascader
    v-model="value"
    :options="options"
    expand-trigger="hover"
    check-strictly
    change-on-select
  />
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { Cascader } from '@xhs/delight'

  const value = ref('')

  const options = [
    {
      label: 'tomoe',
      value: 'tomoe',
      children: [
        {
          label: 'tomoe',
          value: 'tomoe',
          children: [
            {
              label: 'yokushiro',
              value: 'yokushiro'
            }
          ]
        }
      ]
    }
  ]

</script>

<style>
</style>
