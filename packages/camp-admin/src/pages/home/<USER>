<template>
  <div class="center-flex">
    <Skeleton />
    <Modal
      v-if="userInfo"
      v-model:visible="guideToOa"
      :mask-closeable="false"
      title="权限申请"
      :with-cancel="false"
      :with-confirm="false"
    >
      <Text>
        请在OA系统申请<Link href="https://city.xiaohongshu.com/oa/oa-next/tailor/RBACNEW" target="_blank">hera商业化运营管理平台</Link>系统本地生活相关权限
      </Text>
    </Modal>
  </div>
</template>

<script lang="tsx" setup>
  import { onBeforeMount, ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { Skeleton } from '@xhs/yam-beer'
  import { useStore } from 'vuex'
  import {
    Modal,
    Text,
    Link
  } from '@xhs/delight'

  const router = useRouter()
  const store = useStore()
  const guideToOa = ref(false)

  const userInfo = computed(() => store.state.Auth?.userInfo)

  const autoHome = () => {
    const permissions:string[] = userInfo.value?.permissions || []
    // eslint-disable-next-line no-nested-ternary
    const path = permissions.includes('outdoor') ? '/camp/qualification/manage' : ''
    if (path) {
      router.replace({
        path
      })
    } else {
      guideToOa.value = true
    }
  }

  onBeforeMount(autoHome)
</script>

<style>
</style>
