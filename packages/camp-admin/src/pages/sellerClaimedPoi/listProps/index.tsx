import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { toast2 as toast, Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: 'POI认领',
  },
  {
    label: '商家列表',
  },
  {
    label: '已认领POI',
  },
]

export const useFilter = () => { // 筛选器的配置
  const route = useRoute()

  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {},
    filterItems: [
      {
        name: 'sellerId',
        label: '商家ID',
        component: {
          props: {
            placeholder: '请输入商家ID',
            modelValue: route.query.sellerId,
          },
        },
      },
      {
        name: 'sellerName',
        label: '商家名称',
        component: {
          props: {
            placeholder: '请输入商家名称',
            modelValue: route.query.sellerName,
          },
        },
      },
    ],
    showManual: false
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '批量认领',
        onClick: () => {
          toast.warning('批量认领')
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = () => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: 'POI ID',
      dataIndex: 'poiId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiId}</Text>),
    },
    {
      title: '门店名称',
      dataIndex: 'poiName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiName || '-'}</Text>),
    },
    {
      title: '省市区',
      dataIndex: 'provinceName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>
        {[rowData?.provinceName, rowData?.cityName, rowData?.districtName].filter(Boolean).join('') || '-'}
      </Text>),
    },
    {
      title: '门店地址',
      dataIndex: 'address',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>
        { rowData?.address || '-' }
      </Text>),
    },
  ]))
  return {
    tableColumns,
  }
}
