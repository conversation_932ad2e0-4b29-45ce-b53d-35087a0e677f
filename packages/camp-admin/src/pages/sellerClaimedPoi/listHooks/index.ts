import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { toast2 as toast } from '@xhs/delight'

import { PoiItem, fetchSellerClaimedPoi } from '@/services/poiClaim'

export const useList = () => {
  const route = useRoute()
  const listSource = ref<PoiItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const editItem = ref() // 编辑的Item
  const filterParam = ref({ // 搜索参数
    pageSize: 10,
    pageNo: 1,
    total: 0,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }
    const { total, poiList } = await fetchSellerClaimedPoi({
      ...filterParam.value,
      total: undefined,
      sellerId: route.query.sellerId as string
    })
    loading.value = false
    filterParam.value.total = total
    listSource.value = poiList
  }

  const handleEdit = () => { // 处理编辑
    toast.info({
      content: `编辑${editItem.value?.good}`,
      strong: true,
    })
    editVisible.value = false
  }

  return {
    listSource,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleEdit,
  }
}
