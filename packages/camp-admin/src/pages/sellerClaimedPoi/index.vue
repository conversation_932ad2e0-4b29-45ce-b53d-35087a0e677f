<template>
  <PageLayout :nav-list="navList">
    <Space
      class="poi-claim-container"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParam"
        class="seller-claimed-poi-filter"
        :config="filterConfig"
      />

      <div class="list-content">
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="filterParam.pageNo"
          v-model:page-size="filterParam.pageSize"
          :total="filterParam.total"
          style="margin-top: 24px"
          @change="fetchList(false)"
        />
      </div>
    </Space>
  </PageLayout>
</template>
<script setup lang="ts">
  import {
    Space,
    Table,
    Pagination,
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import PageLayout from '@/components/page-layout/index.vue'

  import {
    spaceProps, useFilter, useTableColumns, navList,
  } from './listProps'
  import { useList } from './listHooks'

  const {
    listSource, // 列表数据
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
  } = useList()

  const { filterConfig } = useFilter()
  const { tableColumns } = useTableColumns()

  fetchList()
</script>

<style lang="stylus" scoped>
.poi-claim-container
  width 100%
  .list-content
    background #fff
    border-radius 8px
    padding 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
.seller-claimed-poi-filter
  pointer-events none
  user-select none
</style>
