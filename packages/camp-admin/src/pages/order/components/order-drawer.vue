<template>
  <Drawer
    v-model:show="visible"
    :width="500"
    title="订单快照"
    class="order-drawer"
  >
    <FormGroup
      label="订单号"
      layout="table"
    >
      <div class="text pointer" @click="doCopy(data.packageId)">
        <span class="pr">{{ data.packageId }}</span>
        <Icon icon="copy" />
      </div>
    </FormGroup>
    <FormGroup
      label="订单状态"
      layout="table"
    >
      <div class="text">
        <OrderStatus :status="data.status" />
      </div>
    </FormGroup>
    <FormGroup
      label="商品名称"
      layout="table"
    >
      <Tooltip content="点击查看更多快照信息" placement="top">
        <div class="text primary-color pointer" @click="goodsVisible = true">{{ skuName }}</div>
      </Tooltip>
    </FormGroup>
    <FormGroup
      label="商品价格信息"
      layout="table"
    >
      <div class="column-text">
        <div>价格：{{ fenToYuan(sku.price) }}</div>
        <div>数量：{{ sku.quantity || '-' }}</div>
      </div>
    </FormGroup>
    <FormGroup
      label="退款金额"
      layout="table"
    >
      <div class="text">¥{{ fenToYuan(data.orderRefundInfo?.totalRefund) }}</div>
    </FormGroup>
    <FormGroup
      :label="sku.suiteType === 'CAMP' ? '规格信息' : '销售信息'"
      layout="table"
    >
      <div class="text">{{ skuVariant }}</div>
    </FormGroup>
    <FormGroup
      v-if="sku.suiteType === 'CAMP'"
      label="入住时间段"
      layout="table"
    >
      <div class="text">{{ liveTimeRange }}</div>
    </FormGroup>
    <FormGroup
      label="创建时间"
      layout="table"
    >
      <div class="text">{{ formatDateTime(data.orderTimeInfo?.createTime) }}</div>
    </FormGroup>
    <FormGroup
      v-if="isCancel"
      label="取消时间"
      layout="table"
    >
      <div class="text">{{ cancelTime }}</div>
    </FormGroup>
    <FormGroup
      v-if="sku.suiteType !== 'CAMP'"
      label="核销时间"
      layout="table"
    >
      <div class="text">{{ formatDateTime(data?.orderTimeInfo?.realCheckInTime) || '-' }}</div>
    </FormGroup>
    <FormGroup
      label="用户信息"
      layout="table"
    >
      <div class="column-text">
        <div>{{ data.userInfo?.userName }} - {{ data.userInfo?.userMobile }}</div>
      </div>
    </FormGroup>
    <FormGroup
      label="商家手机号"
      layout="table"
    >
      <div class="text">{{ sku.sellerTel || '-' }}</div>
    </FormGroup>
  </Drawer>
  <GoodsSnapshot v-model:show="goodsVisible" :sku="sku" :sku-name="skuName" :sku-variant="skuVariant" />
</template>

<script lang="tsx" setup>
  import { computed, PropType, ref } from 'vue'
  import {
    Drawer, FormGroup, Icon, toaster, Tooltip
  } from '@xhs/yam-beer'
  import dayjs from 'dayjs'

  import { OrderSnapshot } from '@/types/order'

  import copy from '@/utils/copy'
  import { fenToYuan } from '@/utils/price'

  import GoodsSnapshot from './goods-snapshot.vue'
  import OrderStatus from './order-status.vue'

  const formatDateTime = (date?: number) => (date ? dayjs.unix(date).format('YYYY-MM-DD HH:mm:ss') : '')

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object as PropType<OrderSnapshot>,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update:show'])

  // 商品快照显示
  const goodsVisible = ref(false)

  const visible = computed({
    get: () => props.show,
    set: e => {
      emit('update:show', e)
    }
  })

  const doCopy = (str:string) => {
    if (copy(str)) {
      toaster.success('复制成功')
    } else {
      toaster.danger('复制失败')
    }
  }

  // 商品
  const sku = computed(() => props.data.skuList?.[0] || {})
  const skuName = computed(() => {
    const { skuName: name, suiteName } = sku.value
    return [suiteName, name].filter(Boolean).join('') || '-'
  })
  const skuVariant = computed(() => {
    const { variantData = [] } = sku.value
    return variantData.map(variant => `${variant.cname || ''}: ${variant.value || ''}`).filter(Boolean).join(' ') || '-'
  })

  // 入住时间段
  const liveTimeRange = computed(() => {
    const { checkInTime, checkOutTime } = props.data?.orderTimeInfo || {}
    return [checkInTime, checkOutTime].filter(Boolean).join(' - ') || '-'
  })

  // 是否是取消订单
  const isCancel = computed(() => [998, 71].includes(Number(props.data.status)))
  // 订单取消时间
  const cancelTime = computed(() => {
    const { updateTime } = props.data.orderTimeInfo
    return updateTime && isCancel.value ? formatDateTime(updateTime) : '-'
  })
</script>

<style lang="stylus" scoped>
.text
  padding-top 5px
  display flex
  align-items center
.column-text
  padding-top 5px
  display flex
  flex-direction column
  justify-content center
.pr
  padding-right .5em
.order-drawer
    &:deep(.label-container)
      justify-content flex-end
</style>
