<template>
  <Tag
    variant="fill"
    :bs="{
      ...statusStyle,
      fontWeight: 500,
    }"
  >{{ statusValue }}</Tag>
</template>

<script lang="ts">
  import { defineComponent, computed } from 'vue'
  import { Tag } from '@xhs/yam-beer'

  import { statusList, EOrderStatus } from '@/constants/order'

  export default defineComponent({
    components: {
      Tag,
    },
    props: {
      status: {
        type: Number || String,
        required: true,
      },
    },
    setup(props) {
      const statusStyles = {
        [EOrderStatus.PAID]: {
          backgroundColor: 'rgba(255, 150, 53, 0.1)',
          color: '#FF9635',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(255, 150, 53, 0.1)',
          },
        },
        [EOrderStatus.CHECK_IN]: {
          backgroundColor: 'rgba(48, 218, 106, 0.1)',
          color: '#42C9A0',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(48, 218, 106, 0.1)',
          },
        },
        [EOrderStatus.CHECK_OUT]: {
          backgroundColor: 'rgba(51, 51, 51, 0.05)',
          color: '#999',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(51, 51, 51, 0.05)',
          },
        },
        [EOrderStatus.CANCEL]: {
          backgroundColor: 'rgba(255, 36, 66, 0.1)',
          color: '#FF2442',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(255, 36, 66, 0.1)',
          },
        },
        [EOrderStatus.CANCEL71]: {
          backgroundColor: 'rgba(255, 36, 66, 0.1)',
          color: '#FF2442',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(255, 36, 66, 0.1)',
          },
        },
      }
      // @ts-ignore
      const statusStyle = computed(() => statusStyles[props.status])

      return {
        statusStyle,
        statusValue: computed(() => statusList.find(s => Number(s.value) === props.status)?.label || '-'),
      }
    },
  })
</script>

<style lang="stylus" scoped>
</style>
