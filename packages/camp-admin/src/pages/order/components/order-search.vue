<template>
  <BaseForm ok-text="查询" layout="inline" :payload="payload" :fields="fields" @submit="search" />
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'

  import BaseForm from '@/components/base-form/index.vue'

  import { OrderListParams } from '@/types/order'
  import { FormFields } from '@/types'

  const props = defineProps({
    payload: {
      type: Object as PropType<OrderListParams>,
      default: () => ({}),
    },
  })

  // 基础表单
  const fields = computed<FormFields>(() => [
    {
      label: '店铺ID',
      name: 'sellerId',
      value: props.payload.sellerId,
    },
    {
      label: '卖家账号ID',
      name: 'sellerUserId',
      value: props.payload.sellerUserId,
    },
    {
      label: '买家账号ID',
      name: 'buyerUserId',
      value: props.payload.buyerUserId,
    },
    {
      label: '订单ID',
      name: 'id',
      value: props.payload.id,
    },
  ])

  const emit = defineEmits(['search'])

  // 搜索
  const search = (payload: OrderListParams) => {
    emit('search', payload)
  }

</script>
