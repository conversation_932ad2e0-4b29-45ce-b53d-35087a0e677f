<template>
  <Modal
    v-model:show="visible"
    title="商品快照"
    :bs="{width: '500px'}"
    @confirm="({ close }) => close()"
  >
    <template #footer>
      <div></div>
    </template>
    <FormGroup
      label="商品图片"
      layout="table"
    >
      <Image :src="sku.image" />
    </FormGroup>
    <FormGroup
      label="商品名称"
      layout="table"
    >
      <div class="text">{{ skuName || '-' }}</div>
    </FormGroup>
    <FormGroup
      label="商品规格"
      layout="table"
    >
      <div class="text">{{ skuVariant }}</div>
    </FormGroup>
    <FormGroup
      v-if="sku.attributeDTO?.cancellationPolicyType"
      label="退款政策"
      layout="table"
    >
      <div class="column-text">
        <div>{{ cancelTypeMap[sku.attributeDTO.cancellationPolicyType] || '-' }}</div>
        <div v-if="ladderCancelRulesList?.length">
          <div v-for="(item, i) in ladderCancelRulesList" :key="i" class="item">
            <span v-if="i" class="prefix">
              使用日期前{{ ladderCancelRulesList[i - 1]?.day || 0 }}天0点 -
            </span>
            <span> 使用日期前{{ item?.day }}天0点之前可退款{{ item.percent }}%</span>
          </div>
        </div>
      </div>
    </FormGroup>
    <FormGroup
      label="退改说明"
      layout="table"
    >
      <div class="text">{{ sku.attributeDTO?.cancellationPolicy || sku.attributeDTO?.skiingCancellationPolicy || '-' }}</div>
    </FormGroup>
    <FormGroup
      label="商品介绍"
      layout="table"
    >
      <div class="text">{{ sku.description || '-' }}</div>
    </FormGroup>
    <FormGroup
      label="营地名称"
      layout="table"
    >
      <div class="text">{{ sku.suiteName || '-' }}</div>
    </FormGroup>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { Modal, FormGroup } from '@xhs/yam-beer'

  import { OrderSku } from '@/types/order'

  import Image from '@/components/base-image/index.vue'

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    sku: {
      type: Object as PropType<OrderSku>,
      default: () => ({})
    },
    skuName: {
      type: String,
      default: '',
    },
    skuVariant: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['update:show'])

  const visible = computed({
    get: () => props.show,
    set: e => {
      emit('update:show', e)
    }
  })

  const cancelTypeMap = {
    1: '下单后用户不可取消',
    2: '随时退(使用日期当天0点之前可全款退款，其他时间不可取消)',
    3: '阶梯退',
  }

  const ladderCancelRulesList = computed(() => {
    const { ladderCancelRules } = props.sku.attributeDTO || {}
    // const ladderCancelRules = JSON.stringify([
    //   { day: 1, percent: 10 },
    //   { day: 3, percent: 10 },
    //   { day: 2, percent: 10 },
    // ])
    return ladderCancelRules || []
  })

</script>

<style lang="stylus" scoped>
.text
  padding-top 5px
  display flex
  align-items center
.column-text
  padding-top 5px
  display flex
  flex-direction column
  justify-content center
</style>
