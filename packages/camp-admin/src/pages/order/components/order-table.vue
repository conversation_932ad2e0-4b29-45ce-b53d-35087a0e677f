<template>
  <BeerTable :scroll-x="2660" border-style="full" :columns="columns" :data="data" />
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import dayjs from 'dayjs'
  import { BeerTable, Button } from '@xhs/yam-beer'

  import { OrderItem } from '@/types/order'

  import OrderStatus from './order-status.vue'

  defineProps({
    data: {
      type: Array as PropType<OrderItem[]>,
      defautl: () => []
    }
  })

  const emit = defineEmits(['action'])

  const columns = [
    {
      title: '订单ID',
      key: 'id',
    },
    {
      title: '店铺名称',
      key: 'sellerName',
    },
    {
      title: '店铺ID',
      key: 'sellerId',
    },
    {
      title: '卖家账号ID',
      key: 'sellerUserId',
    },
    {
      title: '商品名称',
      key: 'skuName',
    },
    {
      title: '商品ID',
      key: 'skuId',
    },
    {
      title: '入住时间段',
      key: 'checkInTime',
    },
    {
      title: '联系人',
      key: 'userName',
      th: {
        bs: {
          width: 100,
        },
      },
    },
    {
      title: '联系电话',
      key: 'userMobile',
    },
    {
      title: '买家账号ID',
      key: 'userId',
    },
    {
      title: '订单金额',
      key: 'totalPrice',
      th: {
        bs: {
          width: 100,
        },
      },
    },
    {
      title: '订单状态',
      key: 'status',
      th: {
        bs: {
          width: 100,
        },
      },
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
          <OrderStatus status={row.status} />
        )
        }
      }
    },
    {
      title: '创建时间',
      key: 'createTime',
      td: {
        is: (data: any) => {
          const row = data.props.rowData as OrderItem
          return dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        }
      }
    },
    {
      title: '操作',
      key: 'status',
      fixed: 'right',
      th: {
        bs: {
          width: 160,
          textAlign: 'center',
        },
      },
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
          <div class='action-wrap'>
            <Button bs={{ marginRight: 0 }} onClick={() => emit('action', row)}>订单快照</Button>
          </div>
        )
        }
      }
    },
  ]

</script>

<style scoped>
.action-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160px;
}
</style>
