<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card style="min-height: auto;margin-bottom: 20px;padding-bottom: 0;">
      <OrderSearch :payload="payload" @search="search" />
    </Card>

    <Card>
      <OrderTables :data="data" @action="showSnapshot" />
      <BasePagination v-model="payload" :show-pager-option="false" :total="total" @change="fetchOrderList" />
    </Card>

    <OrderDrawer v-model:show="visible" :data="orderSnapshot" />
  </PageLayout>
</template>

<script setup lang="tsx">
  import { ref } from 'vue'

  import { OrderItem, OrderListParams, OrderSnapshot } from '@/types/order'
  import { orderPackageSnapshot, orderList } from '@/services/order'
  import { workerOrder_snapshot_click } from '@xhs/worker-event-camp-admin'

  import Card from '@/components/base-card/index.vue'
  import PageLayout from '@/components/page-layout/index.vue'
  import BasePagination from '@/components/base-pagination/index.vue'

  import OrderTables from './components/order-table.vue'
  import OrderSearch from './components/order-search.vue'
  import OrderDrawer from './components/order-drawer.vue'

  const navList = [
    {
      label: '订单查询'
    },
    {
      label: '生活服务订单'
    }
  ]

  const payload = ref<OrderListParams>({
    pageNum: 1,
    pageSize: 10,
    sellerId: process.env.NODE_ENV === 'development' ? '614aef1e03590e0001f566b2' : undefined,
  })

  const total = ref(0) // 订单总数量
  const data = ref<OrderItem[]>([]) // 订单列表数据
  const orderSnapshot = ref<OrderSnapshot>() // 订单详情快照信息

  const loading = ref<boolean>(true) // 加载中
  const visible = ref(false) // 详情弹窗是否显示

  const fetchOrderList = async () => {
    loading.value = true
    try {
      const res = await orderList(payload.value)
      data.value = res.items
      total.value = res.pagination.total
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('err', err)
    }
    loading.value = false
  }
  fetchOrderList()

  const search = async () => {
    payload.value.pageNum = 1
    fetchOrderList()
  }

  const showSnapshot = async ({ id }: {id:string}) => {
    // 快照点位数据
    workerOrder_snapshot_click({ id })
    loading.value = true
    try {
      const res = await orderPackageSnapshot(id)
      orderSnapshot.value = res.orderSnapshot
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('err', err)
    }
    loading.value = false
    visible.value = true
  }

</script>

<style>
</style>
