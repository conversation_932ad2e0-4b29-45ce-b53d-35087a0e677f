<template>
  <Form ref="formRef" :model="formData" :rules="rules" class="allot-form" label-width="200px">
    <FormItem label="负责区域" name="operateManagerRegionName" placeholder="请选择">
      <Select v-model="formData.operateManagerRegionName" :options="options" :loading="loading" />
    </FormItem>
    <FormItem label="店铺运营负责人" name="operateManagerId" placeholder="请输入">
      <ManagerSelector v-model="formData.operateManagerId" @change="changeFormData" />
    </FormItem>
  </Form>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Form2 as Form, FormItem2 as FormItem, FormRule, Select
  } from '@xhs/delight'
  import { getOperateConfig } from '@/services/operateClaim'
  import ManagerSelector from './ManagerSelector.vue'

  const formRef = ref()
  const loading = ref<boolean>(false)
  const options = ref<{label: string; value: string}[]>([])
  const formData = ref({
    operateManagerRegionName: '',
    operateManagerId: '',
    operateManagerName: ''
  })

  const fetchRegion = async () => {
    loading.value = true
    try {
      const res = await getOperateConfig()
      options.value = res.operateManagerRegionList.map(item => ({
        label: item,
        value: item
      }))
    } finally {
      loading.value = false
    }
  }

  const rules: FormRule = {
    operateManagerRegionName: {
      required: true,
      message: '门店所在城市不能为空！'
    },
    operateManagerId: {
      required: true, message: '店铺运营负责人不能为空！'
    }
  }

  const changeFormData = (newVal: any) => {
    formData.value.operateManagerId = newVal.value
    formData.value.operateManagerName = newVal.label
  }

  defineExpose({
    formRef,
    formData
  })

  fetchRegion()

</script>

<style scoped lang="stylus">
.allot
  margin 0 auto
</style>
