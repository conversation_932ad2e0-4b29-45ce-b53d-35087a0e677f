<template>
  <Select v-model="value" :options="options" filterable :filter="filter" :loading="loading" remote />
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { Select, useDebounce } from '@xhs/delight'
  import { searchEmployee } from '@/services/operateClaim'

  import useFormTrigger from '@/composables/use-form-trigger'

  const value = ref('')
  const loading = ref<boolean>(false)
  const options = ref<any[]>([])

  const props = defineProps<{
    modelValue: string
    changeManagerSelector?:(newVal: any) => void
  }>()

  const emit = defineEmits(['update:modelValue', 'change'])

  const { triggerValidate } = useFormTrigger()

  const filter = useDebounce(
    async newFilterValue => {
      if (newFilterValue) {
        loading.value = true
        try {
          const res = await searchEmployee({
            key: newFilterValue,
            includeLeaveEmployee: true
          })
          options.value = res.employeeInfos.map(item => ({
            label: item.userName,
            value: item.userId
          }))
        } finally {
          loading.value = false
        }
      }
    },
    { delay: 300 },
  )

  watch(value, newVal => {
    if (props.changeManagerSelector) {
      props.changeManagerSelector(options.value.find(item => item.value === value.value))
    }
    emit('update:modelValue', newVal)
    emit('change', options.value.find(item => item.value === newVal))
    setTimeout(() => {
      triggerValidate()
    }, 100)
  })

</script>

<style scoped lang="stylus">
</style>
