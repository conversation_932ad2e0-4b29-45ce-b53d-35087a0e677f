<template>
  <Space
    class="ultra-list-container"
    v-bind="spaceProps"
  >
    <Space
      class="ultra-list-header"
      v-bind="spaceProps"
    >
      <PageHeader v-bind="pageHeaderProps" />
      <OutlineFilter
        v-model="searchParam"
        :config="filterConfig"
      />
    </Space>

    <div class="ultra-list-body">
      <Space
        class="ultra-list-content"
        v-bind="spaceProps"
      >
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="searchParam.pageNo"
          v-model:pageSize="searchParam.pageSize"
          :total="total"
          @change="fetchList(false)"
        />
      </Space>
    </div>
    <Modal
      v-model:visible="editVisible"
      size="large"
      @confirm="handleAllot(formRef)"
      @cancel="editVisible = false"
    >
      <AllotModal ref="formRef" />
    </Modal>
  </Space>
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Space, Table, Pagination, Modal
  } from '@xhs/delight'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import AllotModal from './components/AllotModal.vue'
  import {
    spaceProps, pageHeaderProps, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  const {
    listSource, // 列表数据
    total,
    loading, // 列表加载标识
    searchParam, // 查询条件
    editVisible,
    handleAllot,
    changeManagerSelector,
    fetchList, // 获取列表方法
    handleShowModal,
  } = useList()

  const { filterConfig } = useFilter(
    fetchList,
    changeManagerSelector
  )
  const { tableColumns } = useTableColumns({
    handleShowModal,
  })

  const formRef = ref()

  fetchList()
</script>

<style lang="stylus" scoped>
.ultra-list-container
  width 100%
  &.d-space-vertical
    gap 24px
.ultra-list-content
  width 100%
  background #FFFFFF
  border-radius 8px
  padding 24px
  /deep/.ultra-material-toolbar-wrap
    padding 0
</style>
