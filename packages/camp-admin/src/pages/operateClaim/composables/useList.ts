import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'
import { getClaimManageList, submitSellerOperateManager } from '@/services/operateClaim'
import type { ClaimManageListItem, GetClaimManageListParams } from '@/services/operateClaim'

export const useList = () => {
  const listSource = ref<ClaimManageListItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // modal展示标识
  const total = ref(0)
  const searchParam = ref<GetClaimManageListParams>({
    sellerId: '',
    shopName: '',
    operateManagerId: '',
    poiClaimStatus: '',
    productOnShelfStatus: '',
    settlementTimeFrom: '',
    settlementTimeTo: '',
    pageSize: 10,
    pageNo: 1,
  })
  const allotParams = ref({
    sellerId: '',
    operateManagerId: '', // 运营负责人id
    operateManagerName: '', // 运营负责人薯名(姓名)
    operateManagerRegionName: '' // 运营负责人负责区域
  })

  const changeManagerSelector = (val: string) => {
    searchParam.value.operateManagerId = val
  }

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      searchParam.value.pageNo = 1
    }
    try {
      const res = await getClaimManageList({
        ...searchParam.value,
        settlementTimeFrom: searchParam.value.settlementTimeFrom?.start,
        settlementTimeTo: searchParam.value.settlementTimeFrom?.end,
      })
      listSource.value = res.sellerList
      total.value = res.total
    } finally {
      loading.value = false
    }
  }

  const handleAllot = async (formRef:any) => {
    loading.value = true
    try {
      await formRef.formRef.validate()
      await submitSellerOperateManager({
        ...allotParams.value,
        operateManagerId: formRef.formData.operateManagerId, // 运营负责人id
        operateManagerName: formRef.formData.operateManagerName, // 运营负责人薯名(姓名)
        operateManagerRegionName: formRef.formData.operateManagerRegionName
      })
      editVisible.value = false
      toast.success('分配成功！')
      fetchList()
    } finally {
      loading.value = false
    }
  }
  const handleShowModal = (item:any) => {
    editVisible.value = true
    allotParams.value.sellerId = item.sellerId
  }

  return {
    listSource,
    total,
    loading,
    searchParam,
    editVisible,
    allotParams,
    changeManagerSelector,
    fetchList,
    handleShowModal,
    handleAllot
  }
}
