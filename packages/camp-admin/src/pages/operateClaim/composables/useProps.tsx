import { computed } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import { poiClaimStatusMap, productOnShelfStatusMap } from '@/services/operateClaim'
import { useStore } from 'vuex'
import ManagerSelectorVue from '../components/ManagerSelector.vue'

export const spaceProps: any = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
}

export const pageHeaderProps: PageHeaderProps = {
  title: '运营认领管理',
}

export const useFilter = (
  fetchList: () => Promise<any>,
  changeManagerSelector: (val: string) => void
) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'sellerId',
        label: '店铺ID',
        component: {
          props: {
            placeholder: '请输入',
          },
        },
      },
      {
        name: 'shopName',
        label: '店铺名称',
        component: {
          props: {
            placeholder: '请输入',
          },
        },
      },
      {
        name: 'operateManagerId',
        label: '店铺运营负责人',
        component: {
          is: ManagerSelectorVue,
          props: {
            changeManagerSelector,
          }
        },
      },
      {
        name: 'poiClaimStatus',
        label: '门店认领状态',
        component: {
          is: 'Select',
          props: {
            options: [
              { label: '已认领门店', value: 'CLAIMED' },
              { label: '未认领门店', value: 'NOT_CLAIM' },
            ],
            placeholder: '请选择',
          },
        },
      },
      {
        name: 'productOnShelfStatus',
        label: '商品上架状态',
        component: {
          is: 'Select',
          props: {
            options: [
              { label: '有在架商品', value: 'ON_SHELF' },
              { label: '没有在架商品', value: 'OFF_SHELF' },
            ],
            placeholder: '请选择',
          },
        },
      },
      {
        name: 'settlementTimeFrom',
        label: '店铺入驻日期',
        component: {
          is: 'DateRangePicker',
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useTableColumns = ({
  handleShowModal,
}: {
  handleShowModal: (item: any) => void
}) => { // 列表的配置
  const store = useStore()
  const permissions:string[] = store.state.Auth?.userInfo?.permissions || []
  const hasPermission = permissions.includes('submit_seller_operate_manager')
  const tableColumns = computed<ThContent[]>(() => [{
    title: '店铺ID',
    dataIndex: 'sellerId',
    minWidth: 230,
    render: ({ rowData }) => <Text>{rowData?.sellerId}</Text>
  },
  {
    title: '店铺名称',
    dataIndex: 'shopName',
    minWidth: 150,
    render: ({ rowData }) => <Text>{rowData?.shopName}</Text>
  },
  {
    title: '店铺入驻日期',
    dataIndex: 'settlementTime',
    minWidth: 120,
    render: ({ rowData }) => <Text>{rowData?.settlementTime}</Text>
  },
  {
    title: '入驻类目',
    dataIndex: 'categoryName',
    minWidth: 200,
    render: ({ rowData }) => <Text>{rowData?.categoryName}</Text>
  },
  {
    title: '门店认领状态',
    dataIndex: 'poiClaimStatus',
    minWidth: 120,
    render: ({ rowData }) => <Text>{poiClaimStatusMap[rowData?.poiClaimStatus as 'NOT_CLAIM' | 'CLAIMED']}</Text>
  },
  {
    title: '门店认领数',
    dataIndex: 'totalClaimPoiCnt',
    minWidth: 100,
    render: ({ rowData }) => <Text>{rowData?.totalClaimPoiCnt}</Text>
  },
  {
    title: '商品上架状态',
    dataIndex: 'productOnShelfStatus',
    minWidth: 120,
    render: ({ rowData }) => <Text>{productOnShelfStatusMap[rowData?.productOnShelfStatus as 'OFF_SHELF' | 'ON_SHELF']}</Text>
  },
  {
    title: '7d dgtv',
    dataIndex: 'dgtv7d',
    minWidth: 80,
    render: ({ rowData }) => <Text>{rowData?.dgtv7d}</Text>
  },
  {
    title: '30d dgtv',
    dataIndex: 'dgtv30d',
    minWidth: 110,
    render: ({ rowData }) => <Text>{rowData?.dgtv30d}</Text>
  },
  {
    title: '代运营服务商',
    dataIndex: 'providerName',
    minWidth: 110,
    render: ({ rowData }) => <Text>{rowData?.providerName}</Text>
  },
  {
    title: '门店所在城市',
    dataIndex: 'coverCity',
    minWidth: 110,
    render: ({ rowData }) => <Text>{rowData?.coverCity}</Text>
  },
  {
    title: '负责区域',
    dataIndex: 'operateManagerRegionName',
    minWidth: 80,
    render: ({ rowData }) => <Text>{rowData?.operateManagerRegionName}</Text>
  },
  {
    title: '店铺运营负责人',
    dataIndex: 'operateManagerName',
    minWidth: 130,
    render: ({ rowData }) => <Text>{rowData?.operateManagerName}</Text>
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    fixed: 'right',
    minWidth: 100,
    render: ({ rowData }) => <TableCell
      type="action"
      action-props={{
        actionOptions: [{
          text: '分配',
          disabled: !hasPermission,
          tooltip: !hasPermission ? '暂无权限' : '',
          onClick: () => handleShowModal(rowData)
        }],
      }} />,
  }])
  return {
    tableColumns,
  }
}
