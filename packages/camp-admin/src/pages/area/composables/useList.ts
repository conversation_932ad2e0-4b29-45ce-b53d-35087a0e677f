import { ref } from 'vue'

import { queryArea, AreaItem, QueryAreaParams } from '@/services/area'

export const useList = () => {
  const listSource = ref<AreaItem[]>(
    // process.env.NODE_ENV === 'development' ? [{ poiGroupId: '1', status: 1 } as AreaItem] : []
    []
  ) // 列表数据
  const loading = ref(false) // 列表加载标识
  const filterParam = ref<QueryAreaParams>({ // 搜索参数
    sellerId: '',
    sellerName: '',
    poiGroupId: '',
    poiGroupName: '',
    pageNo: 1,
    pageSize: 10,
  })
  const total = ref(0)

  const fetchList = async (isResetPageNum = true) => { // 获取区域列表
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }
    try {
      const res = await queryArea(filterParam.value)
      loading.value = false
      total.value = res.total
      listSource.value = res?.poiGroupList || []
    } finally {
      loading.value = false
    }
  }
  fetchList()

  return {
    listSource,
    loading,
    filterParam,
    fetchList,
    total,
  }
}
