import { computed, ref, nextTick } from 'vue'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

import { AreaItem } from '@/services/area'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: '资质管理',
  },
  {
    label: '刷资质',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: fetchList,
    filterItems: [
      {
        name: 'sellerId',
        label: '商家Id',
        component: {
          props: {
            placeholder: '请输入商家Id',
          },
        },
      },
      // {
      //   name: 'sellerName',
      //   label: '商家名称',
      //   component: {
      //     props: {
      //       placeholder: '请输入商家名称',
      //     },
      //   },
      // },
      {
        name: 'poiGroupId',
        label: '区域Id',
        component: {
          props: {
            placeholder: '请输入区域Id',
          },
        },
      },
      {
        name: 'poiGroupName',
        label: '区域名称',
        component: {
          props: {
            placeholder: '请输入区域名称',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const importVisible = ref(false)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '刷区域资质',
        onClick: () => {
          importVisible.value = true
        },
      },
    ],
  }))
  return {
    toolBarConfig,
    importVisible,
  }
}

export const useTableColumns = () => {
  const editItem = ref<AreaItem>()
  const shopVisible = ref(false)
  const areaVisible = ref(false)
  const showShop = (item: AreaItem) => {
    editItem.value = item
    nextTick().then(() => {
      shopVisible.value = true
    })
  }
  const showQual = (item:AreaItem) => {
    editItem.value = item
    nextTick().then(() => {
      areaVisible.value = true
    })
  }

  // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '商家sellerId',
      dataIndex: 'sellerId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as AreaItem)?.sellerId}</Text>),
    },
    {
      title: '商家sellerName',
      dataIndex: 'sellerName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as AreaItem)?.sellerName}</Text>),
    },
    {
      title: '区域Id',
      dataIndex: 'poiGroupId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as AreaItem)?.poiGroupId}</Text>),
    },
    {
      title: '区域名称',
      dataIndex: 'poiGroupName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as AreaItem)?.poiGroupName || '-'}</Text>),
    },
    {
      title: '是否已提交资质',
      dataIndex: 'statusText',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as AreaItem).status === 'QUAL_COMMITED' ? '是' : '否'}</Text>),
    },
    {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          // direction: 'vertical',
          actionOptions: [
            {
              text: '查看门店',
              onClick: () => showShop(rowData as AreaItem),
            },
            {
              text: '查看资质',
              visible: !!(rowData as AreaItem).status,
              onClick: () => showQual(rowData as AreaItem),
            },
          ],
        }} />,
    }]))
  return {
    tableColumns,
    editItem,
    shopVisible,
    areaVisible,
  }
}
