<template>
  <Modal
    v-model:visible="modalVisible"
    title="查看区域门店"
    :size="580"
    :with-footer="false"
    @cancel="modalVisible = false"
  >
    <Table
      size="large"
      :columns="columns"
      :data-source="data"
      :loading="loading"
    />
    <Pagination
      v-model="payload.pageNo"
      :total="total"
      style="margin-top: 24px"
      @change="fetchData"
    />
  </Modal>
</template>

<script lang="tsx" setup>
  import {
    watch, ref, computed, reactive
  } from 'vue'
  import {
    Table, Pagination, Text, Modal
  } from '@xhs/delight'

  import { queryShop, QueryShopParams, PoiItem } from '@/services/area'

  const props = defineProps<{
    areaId: string
    visible: boolean
    sellerId: string
  }>()

  const emit = defineEmits(['update:visible'])

  const modalVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const payload:QueryShopParams = reactive({
    poiGroupId: props.areaId,
    sellerId: props.sellerId,
    pageNo: 1,
    pageSize: 10,
  })

  watch(
    () => props.areaId,
    e => {
      payload.poiGroupId = e
    }
  )

  const loading = ref(false)
  const data = ref<PoiItem[]>([])
  const total = ref(0)

  const fetchData = async () => {
    loading.value = true
    try {
      const res = await queryShop(payload)
      data.value = res.poiList
      total.value = res.total
    } finally {
      loading.value = false
    }
  }

  watch(
    () => props.visible,
    () => {
      payload.pageNo = 1
      fetchData()
    }
  )

  const columns = [
    {
      title: '门店名称',
      dataIndex: 'shopName',
      render: ({ rowData }:any) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as PoiItem)?.shopName}</Text>),
    },
    {
      title: '门店Id',
      dataIndex: 'shopId',
      render: ({ rowData }:any) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{(rowData as PoiItem)?.shopId}</Text>),
    },
  ]

</script>

<style>
</style>
