<template>
  <Modal
    v-model:visible="editVisible"
    title="区域资质"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Form v-if="editVisible" label-width="73px">
      <FormItem label="模板下载">
        <Link target="_blank" href="https://fe-video-qc.xhscdn.com/fe-platform/f5d4a8127208dc57cd0d04a223939b163ae8f7a9/%E5%8C%BA%E5%9F%9F%E8%B5%84%E8%B4%A8%E4%BF%A1%E6%81%AF%E6%A8%A1%E6%9D%BF.xlsx">区域资质信息模板.xlsx</Link>
        <Text>（据模版，填写要刷的区域资质信息模板）</Text>
      </FormItem>
      <FormItem label="批量上传">
        <div v-if="!result" class="upload-container">
          <Button
            style="position: relative"
            :icon="Upload"
            :loading="loading"
            @click="upload"
          >
            {{ failed || result ? '重新' : '' }}上传 Excel 文件
          </Button>
          <div v-if="failed" class="message">
            <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
            <Text color="text-description">信息识别失败，请重新上传</Text>
          </div>
        </div>
        <UploadResult v-if="result" text="上传" :result="result" />
      </FormItem>
    </Form>

  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, ref, watch } from 'vue'
  import {
    Button,
    Modal,
    Form2 as Form,
    FormItem2 as FormItem,
    Text,
    Link,
  } from '@xhs/delight'
  import { Upload } from '@xhs/delight/icons'

  import { getFiles } from '@/utils/getFiles'
  import { ClaimResult } from '@/services/poiClaim'
  import { importAreaQualication } from '@/services/area'

  import UploadResult from '@/components/upload-result/index.vue'

  const props = defineProps<{
    visible: boolean
  }>()

  const emit = defineEmits(['update:visible'])

  const failed = ref(false)
  const loading = ref(false)

  const result = ref<ClaimResult | undefined>()

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const upload = async () => {
    const [file] = await getFiles()
    if (!file) return
    loading.value = true

    const f = new FormData()
    f.append('file', file)
    try {
      const res = await importAreaQualication(f)
      result.value = res
    } catch (e) {
      loading.value = false
      failed.value = true
    } finally {
      loading.value = false
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
</style>
