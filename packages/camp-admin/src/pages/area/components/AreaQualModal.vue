<template>
  <Modal
    v-model:visible="modalVisible"
    title="查看资质"
    :size="850"
    :with-footer="false"
    @cancel="modalVisible = false"
  >
    <Skeleton :loading="loading" :paragraph="paragraph">
      <div style="margin-bottom: 25px;">
        <Text type="h6" bold style="margin-bottom: 5px">《营业执照》</Text>
        <Table :columns="licenseColumns" :data-source="qual ? [qual] : []" />
      </div>
      <div style="margin-bottom: 25px;">
        <Text type="h6" bold style="margin-bottom: 5px">《身份证》</Text>
        <Table :columns="maintainerColumns" :data-source="qual ? [qual] : []" />
      </div>
    </Skeleton>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed, ref, watch } from 'vue'
  import {
    Modal, Text, Table, Skeleton
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'

  import { AreaQual, queryAreaQual } from '@/services/area'

  import BaseImage from '@/components/base-image/index.vue'

  const props = defineProps<{
    visible: boolean
    areaId: string
    sellerId: string
  }>()

  const emit = defineEmits(['update:visible'])

  const loading = ref(false)
  const paragraph:any = { rows: 6 }
  const qual = ref<AreaQual>()

  const modalVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  watch(
    () => props.visible,
    async () => {
      loading.value = true
      try {
        const res = await queryAreaQual(props.areaId, props.sellerId)
        qual.value = res
      } finally {
        loading.value = false
      }
    }
  )

  const licenseColumns:ThContent[] = [
    {
      title: '资质图片',
      dataIndex: 'companyLicenseUrl',
      render: ({ rowData }) => <BaseImage src={rowData?.companyLicenseUrl} />,
    },
    {
      title: '有效时间',
      dataIndex: 'qualificationUrl',
      render: ({ rowData }) => {
        const row = rowData as AreaQual
        return (
      <Text
        ellipsis
        tooltip
        style={{ maxWidth: '250px' }}
      >
        {rowData?.permanent ? '永久有效' : ([row?.companyLicenseStartTime, rowData?.companyLicenseEndTime].filter(Boolean).join(' ~ ') || '-')}
      </Text>
    )
      },
    },
    {
      title: '区域主体类型',
      dataIndex: 'qualificationUrl',
      render: ({ rowData }) => <Text>{(rowData as AreaQual)?.companySubjectType}</Text>
    },
    {
      title: '区域企业名称',
      dataIndex: 'poiGroupName',
      render: ({ rowData }) => <Text>{(rowData as AreaQual)?.poiGroupName}</Text>
    },
    {
      title: '区域统一社会信用代码',
      dataIndex: 'companyLicenseNo',
      render: ({ rowData }) => <Text>{(rowData as AreaQual)?.companyLicenseNo}</Text>
    },
  ]

  const maintainerColumns:ThContent[] = [
    {
      title: '资质图片',
      dataIndex: 'companyLicenseUrl',
      render: ({ rowData }) => (
      <div>
        <BaseImage src={rowData?.idCardFortUrl} />
        <BaseImage src={rowData?.idCardBackUrl} />
      </div>
    ),
    },
    {
      title: '有效时间',
      dataIndex: 'qualificationUrl',
      render: ({ rowData }) => {
        const row = rowData as AreaQual
        return (
      <Text
        ellipsis
        tooltip
        style={{ maxWidth: '250px' }}
      >
        {rowData?.permanent ? '永久有效' : ([row?.idCardStartTime, rowData?.idCardEndTime].filter(Boolean).join(' ~ ') || '-')}
      </Text>
    )
      },
    },
    {
      title: '姓名',
      dataIndex: 'maintainerName',
      render: ({ rowData }) => <Text>{(rowData as AreaQual)?.maintainerName}</Text>
    },
    {
      title: '证件号',
      dataIndex: 'maintainerIdentifyNo',
      render: ({ rowData }) => <Text>{(rowData as AreaQual)?.maintainerIdentifyNo}</Text>
    },
    {
      title: '联系电话',
      dataIndex: 'maintainerPhone',
      render: ({ rowData }) => <Text>{(rowData as AreaQual)?.maintainerPhone}</Text>
    },
  ]
</script>

<style>
</style>
