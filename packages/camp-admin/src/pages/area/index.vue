<template>
  <PageLayout :nav-list="navList">
    <Space
      class="poi-claim-container"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
      />

      <div class="list-content">
        <Toolbar :config="toolBarConfig" />
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <Pagination
          v-model="filterParam.pageNo"
          :total="total"
          style="margin-top: 24px"
          @change="fetchList(false)"
        />
      </div>
    </Space>
    <ImportAreaQualModal v-model:visible="importVisible" />
    <AreaShopModal v-if="editItem?.poiGroupId" v-model:visible="shopVisible" :area-id="editItem?.poiGroupId" :seller-id="editItem.sellerId" />
    <AreaQualModal v-if="editItem?.poiGroupId" v-model:visible="areaVisible" :area-id="editItem?.poiGroupId" :seller-id="editItem.sellerId" />
  </PageLayout>
</template>
<script setup lang="ts">
  import {
    Space, Table, Pagination
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import PageLayout from '@/components/page-layout/index.vue'

  import {
    spaceProps, useToolBar, useFilter, useTableColumns, navList,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  import AreaQualModal from './components/AreaQualModal.vue'
  import ImportAreaQualModal from './components/ImportAreaQualModal.vue'
  import AreaShopModal from './components/AreaShopModal.vue'

  const {
    listSource, // 列表数据
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
    total,
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const {
    importVisible,
    toolBarConfig
  } = useToolBar()
  const {
    editItem,
    tableColumns,
    shopVisible,
    areaVisible,
  } = useTableColumns()
</script>

<style lang="stylus" scoped>
.poi-claim-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 0 24px 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
