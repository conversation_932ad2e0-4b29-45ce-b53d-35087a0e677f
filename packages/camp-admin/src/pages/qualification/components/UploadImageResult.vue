<template>
  <div class="upload-bulk-result">
    <div class="result-title">图片链接<Link
      target="_blank"
      :href="result.imageUrlExcel"
    >
      门店认领资质图片.xlsx
    </Link>
    </div>
  </div>
</template>

  <script lang="ts" setup>
  import { Link } from '@xhs/delight'

  defineProps<{
    result: any
  }>()

  </script>

  <style lang="stylus" scoped>
  .upload-bulk-result
    flex 1
    display flex
    flex-direction column
    align-items flex-start
    background #F7F7F7
    border-radius 4px
    color #666666
    font-size 14px
    line-height 24px
    padding 16px
    margin-top 20px
    .result-title
      font-weight bold
      display flex
      align-items center
      gap 10px
      .text-link
        color #2db8ff
        margin-left 8px

  </style>
