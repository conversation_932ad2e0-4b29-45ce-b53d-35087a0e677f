<template>
  <Modal
    v-model:visible="editVisible"
    title="批量上传门店认领资质图片"
    :size="580"
    :with-footer="false"
    @cancel="editVisible = false"
  >
    <Text>请将所有资质图片命名后打包成.zip文件上传</Text>
    <Text>* 压缩包最多支持两层文件夹嵌套，否则可能上传失败</Text>
    <Text>* 压缩包大小不超过300M，图片支持PNG/JPG/JPEG格式</Text>
    <div v-if="!result" class="upload-container">
      <Spinner :spinning="loading">
        <FileUploader
          v-model="fileList"
          accept=".zip"
          :uploading="false"
          :percent="percent"
          :text="uploadBtnText"
          @update:modelValue="complete"
        />
      </Spinner>
      <Text v-if="finishedCount" color="text-description">当前处理成功：{{ finishedCount }}</Text>
      <div v-if="failed" class="message">
        <img class="error-icon" src="https://ci.xiaohongshu.com/5a7fd551-945b-4e72-9c46-93fa92f29a97" />
        <Text color="text-description">信息识别失败，请重新上传</Text>
      </div>
    </div>
    <UploadImageResult v-if="result" :result="result" />
  </Modal>
</template>

<script lang="tsx" setup>
  import {
    computed, ref, watch, onUnmounted
  } from 'vue'
  import {
    Spinner,
    Modal,
    Text,
    toast
  } from '@xhs/delight'
  import FileUploader from '@/components/upload-file/index.vue'
  import { isFinished, computePercent } from '@/utils/longTask'
  import { LONG_TASK_STATUS, LONG_TASK_NAME } from '@/constants/longTask'
  import { postLongTask, getLongTask, GetLongTaskRes } from '@/services/longTaskv2'
  import UploadImageResult from './UploadImageResult.vue'

  const props = defineProps<{
    visible: boolean
  }>()

  const emit = defineEmits(['update:visible'])

  const failed = ref(false)
  const loading = ref(false)

  const result = ref<any>()
  let timer:number
  const fileList = ref<any[]>([])
  const percent = ref(0)
  const finishedCount = ref(0)
  const uploadBtnText = ref('上传.zip资质图片')

  watch(
    () => props.visible,
    () => {
      if (!props.visible) {
        failed.value = false
        loading.value = false
        result.value = undefined
        finishedCount.value = 0
        window.clearTimeout(timer)
      }
    }
  )

  const editVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
      window.clearTimeout(timer)
    }
  })

  onUnmounted(() => {
    window.clearTimeout(timer)
  })

  const complete = async () => {
    loading.value = true
    try {
      const { taskId } = await postLongTask({
        input: {
          file_url: fileList.value[0]?.downloadUrl,
          extra: undefined // extra 需要以 map 的形式进行传递
        },
        task_name: LONG_TASK_NAME.LIFE_QUALIFICATION_FILE_TASK
      })
      // 心跳请求
      const loopQueryTaskStatus = async () => {
        const res = await getLongTask<GetLongTaskRes>(taskId)
        percent.value = computePercent(res?.finishedCount, res?.totalCount) || percent.value
        finishedCount.value = res?.finishedCount
        if (isFinished(res?.status)) {
          loading.value = false
          if (res?.status === LONG_TASK_STATUS.FAIL) {
            failed.value = true
            toast.danger('上传失败，请重新上传')
            uploadBtnText.value = '重新上传.zip资质图片'
            return
          }
          failed.value = false
          const { fileUrl = '' } = res?.result
          result.value = {
            imageUrlExcel: fileUrl,
          }
          return
        }
        timer = window.setTimeout(loopQueryTaskStatus, 1000)
      }
      loopQueryTaskStatus()
    } catch (error: any) {
      loading.value = false
      failed.value = true
      toast.danger(error?.message || '服务器异常，请重新操作')
      uploadBtnText.value = '重新上传.zip资质图片'
    } finally {
      failed.value = false
      finishedCount.value = 0
    }
  }

</script>

<style lang="stylus" scoped>
.upload-container
  flex 1
  min-height 158px
  background-color #f7f7f7
  display flex
  align-items center
  justify-content center
  border-radius 3px
  flex-direction column
  margin-top 20px
  .message
    display flex
    align-items center
    line-height 2
    padding-top 1em
  .error-icon
    width 16px
    height 16px
    margin-right 5px
</style>
