<template>
  <Modal
    v-model:visible="modalVisible"
    title="查看资质"
    :size="850"
    :with-footer="false"
    @cancel="modalVisible = false"
  >
    <div v-for="item in data" :key="item.type" style="margin-bottom: 25px;">
      <Text type="h6" bold style="margin-bottom: 5px">{{ item.qualTitle }}</Text>
      <Table :columns="getColumns(item)" :data-source="[item]" />
    </div>
  </Modal>
</template>

<script lang="tsx" setup>
  import { computed } from 'vue'
  import { Modal, Text, Table } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'

  import { LicenseQulDetail } from '@/services/qualification'

  import BaseImage from '@/components/base-image/index.vue'

  const props = defineProps<{
    visible: boolean
    data: LicenseQulDetail[]
  }>()

  const emit = defineEmits(['update:visible'])

  const modalVisible = computed({
    get: () => props.visible,
    set: e => {
      emit('update:visible', e)
    }
  })

  const getColumns = (license:LicenseQulDetail) => {
    const columns:ThContent[] = [
      {
        title: '资质图片',
        dataIndex: 'qualificationUrl',
        render: ({ rowData }) => <BaseImage src={rowData?.qualificationUrl} />,
      },
      {
        title: '有效时间',
        dataIndex: 'qualificationUrl',
        render: ({ rowData }) => (
        <Text
          ellipsis
          tooltip
          style={{ maxWidth: '250px' }}
        >
          {rowData?.permanent ? '永久有效' : ([rowData?.startTime, rowData?.endTime].filter(Boolean).join(' ~ ') || '-')}
        </Text>
      ),
      },
    ]

    const { qualDataList = [] } = license

    qualDataList.forEach(qualData => {
      columns.push({
        title: qualData.nameDesc,
        dataIndex: qualData.name,
        render: () => (
        <Text
          ellipsis
          tooltip
          style={{ maxWidth: '250px' }}
        >
          { qualData.valueDesc }
        </Text>
      )
      })
    })

    return columns
  }

</script>

<style>
</style>
