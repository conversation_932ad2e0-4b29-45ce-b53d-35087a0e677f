import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'

import { fetchQualification, QualificationItem } from '@/services/qualification'

export const useList = () => {
  const listSource = ref<QualificationItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const filterParam = ref({ // 搜索参数
    userId: '',
    poiId: process.env.NODE_ENV === 'development' ? '1575464002073763670' : ''
  })
  const detail = ref<QualificationItem>() // 资质详情

  const fetchList = async () => { // 获取商品列表的方法
    if (!filterParam.value.userId && !filterParam.value.poiId) {
      toast.info('请输入POI ID或者用户ID')
      listSource.value = []
      return
    }

    loading.value = true
    const res = await fetchQualification(filterParam.value)
    loading.value = false
    listSource.value = res?.poiQualInfoList || []
  }

  const handleEdit = (e:QualificationItem) => { // 处理编辑
    editVisible.value = true
    detail.value = e
  }

  return {
    listSource,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleEdit,
    detail,
  }
}
