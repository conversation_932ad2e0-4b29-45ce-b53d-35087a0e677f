import { computed, ref } from 'vue'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import { QualificationItem } from '@/services/qualification'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: '资质管理',
  },
  {
    label: '刷资质',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: fetchList,
    filterItems: [
      {
        name: 'poiId',
        label: 'POI ID',
        component: {
          props: {
            placeholder: '请输入POI ID',
          },
        },
      },
      // {
      //   name: 'userId',
      //   label: '用户ID',
      //   component: {
      //     props: {
      //       placeholder: '请输入用户ID',
      //     },
      //   },
      // },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = () => { // 工具栏的配置
  const poiVisible = ref(false)
  const persionVisible = ref(false)
  const qualImagesVisible = ref(false)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '刷门店资质',
        onClick: () => {
          poiVisible.value = true
        },
      },
      {
        text: '刷法人资质',
        onClick: () => {
          persionVisible.value = true
        },
      },
      {
        text: '上传资质图片',
        onClick: () => {
          qualImagesVisible.value = true
        },
      },
    ],
  }))
  return {
    toolBarConfig,
    poiVisible,
    persionVisible,
    qualImagesVisible
  }
}

export const useTableColumns = (
  {
    handleEdit
  }:
  {
    handleEdit: (e:QualificationItem) => void
  }
) => {
  // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: 'POI ID',
      dataIndex: 'poiId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiInfo?.poiId}</Text>),
    },
    {
      title: '门店名称',
      dataIndex: 'poiName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiInfo?.poiName || '-'}</Text>),
    },
    {
      title: '省市区',
      dataIndex: 'provinceName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>
        {[rowData?.poiInfo?.provinceName, rowData?.poiInfo?.cityName, rowData?.poiInfo?.districtName].filter(Boolean).join('') || '-'}
      </Text>),
    },
    {
      title: '门店地址',
      dataIndex: 'address',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>
        { rowData?.poiInfo?.address || '-' }
      </Text>),
    },
    {
      title: '操作',
      fixed: 'right',
      minWidth: 135,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [
            {
              text: '查看资质',
              disabled: !rowData?.qualDetailList,
              onClick: () => handleEdit(rowData as QualificationItem),
            },
          ],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
