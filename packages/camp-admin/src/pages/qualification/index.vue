<template>
  <PageLayout :nav-list="navList">
    <Space
      class="poi-claim-container"
      v-bind="spaceProps"
    >
      <OutlineFilter
        v-model="filterParam"
        :config="filterConfig"
      />

      <div class="list-content">
        <Toolbar :config="toolBarConfig" />
        <Table
          size="large"
          :columns="tableColumns"
          :data-source="listSource"
          :loading="loading"
        />
        <!-- <Pagination
          v-model="filterParam.pageNo"
          :total="filterParam.total"
          style="margin-top: 24px"
          @change="fetchList(false)"
        /> -->
      </div>
    </Space>
    <QualificationModal v-if="detail?.qualDetailList" v-model:visible="editVisible" :data="detail?.qualDetailList" />
    <ImportPoiQualication v-model:visible="poiVisible" />
    <ImportPoiPersionQualModal v-model:visible="persionVisible" />
    <ImportQualImagesModal v-model:visible="qualImagesVisible" />
  </PageLayout>
</template>
<script setup lang="ts">
  import {
    Space, Table,
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import PageLayout from '@/components/page-layout/index.vue'

  import {
    spaceProps, useToolBar, useFilter, useTableColumns, navList,
  } from './listProps'
  import { useList } from './listHooks'

  import QualificationModal from './components/QualificationModal.vue'
  import ImportPoiQualication from './components/ImportPoiQualModal.vue'
  import ImportPoiPersionQualModal from './components/ImportPoiPersionQualModal.vue'
  import ImportQualImagesModal from './components/ImportQualImagesModal.vue'

  const {
    listSource, // 列表数据
    loading, // 列表加载标识
    detail, // 资质详情
    filterParam, // 查询条件
    editVisible, // 编辑弹窗展示标识
    fetchList, // 获取列表方法
    handleEdit,
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const {
    poiVisible,
    persionVisible,
    qualImagesVisible,
    toolBarConfig
  } = useToolBar()
  const { tableColumns } = useTableColumns({
    handleEdit,
  })
</script>

<style lang="stylus" scoped>
.poi-claim-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 0 24px 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
