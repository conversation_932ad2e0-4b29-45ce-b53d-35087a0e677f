<template>
  <Form ref="formRef" label-width="120px" :rules="rules" :model="model">
    <FormItem label="大号userId" help="已开电商店的专业号" name="mainUserId">
      <UserInput v-model="model.mainUserId" />
    </FormItem>
    <FormItem label="小号userId" help="新开本地店的专业号" name="burnerUserId">
      <UserInput v-model="model.burnerUserId" />
    </FormItem>
  </Form>
</template>

<script setup lang="tsx">
  import { reactive, watch, ref } from 'vue'
  import { Form2 as Form, FormItem2 as FormItem, FormRule } from '@xhs/delight'

  import UserInput from '@/components/user-input/index.vue'

  const emit = defineEmits(['change'])

  const formRef = ref<any>()

  const model = reactive({
    mainUserId: '',
    burnerUserId: ''
  })

  const rules:FormRule = {
    mainUserId: { required: true, message: '必填项' },
    burnerUserId: { required: true, message: '必填项' },
  }

  watch(
    () => [model.burnerUserId, model.mainUserId],
    () => {
      emit('change', {
        ...model
      })
    }
  )

  defineExpose({
    validate: () => formRef.value?.validate()
  })

</script>
