<template>
  <Table
    :columns="columns"
    :data-source="dataSource"
  />
</template>

<script setup lang="tsx">
  import { Table, Text } from '@xhs/delight'
  import dayjs from 'dayjs'

  import { accountStatusTextMap } from '@/constants/accountRelation'
  import { RelationLogItem } from '@/services/accountRelation'

  defineProps<{
    dataSource: RelationLogItem[]
  }>()

  const columns = [
    {
      title: '操作人',
      dataIndex: 'operator',
    },
    {
      title: '操作类型',
      dataIndex: 'operateType',
      render: ({ rowData }:any) => <Text color={rowData?.operateType === 'EFFECTIVE' ? 'text-title' : 'text-description'}>{accountStatusTextMap[rowData?.operateType as keyof typeof accountStatusTextMap]}</Text>
    },
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      render: ({ rowData }:any) => <Text>{rowData?.operateTime ? dayjs(rowData?.operateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
    },
  ]
</script>
