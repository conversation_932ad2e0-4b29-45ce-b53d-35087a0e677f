<template>
  <Space
    class="list-container"
    v-bind="spaceProps"
  >
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfig"
    />

    <div class="list-content">
      <Toolbar :config="toolBarConfig" />
      <Table
        size="large"
        :columns="tableColumns"
        :data-source="listSource"
        :loading="loading"
      >
        <template #td="{ data }">
          <Text ellipsis>{{ data }}</Text>
        </template>
      </Table>
      <Pagination
        v-model="filterParam.pageNum"
        v-model:pageSize="filterParam.pageSize"
        :total="total"
        style="margin-top: 24px"
        @change="fetchList(false)"
      />
    </div>
  </Space>
</template>
<script setup lang="ts">
  import {
    Space, Table, Pagination, Text,
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'

  import {
    spaceProps, useToolBar, useFilter, useTableColumns,
  } from './composables/useProps'
  import { useList } from './composables/useList'

  const {
    listSource, // 列表数据
    total, // 数据总数
    loading, // 列表加载标识
    filterParam, // 查询条件
    fetchList, // 获取列表方法
    handleDelete, // 删除方法
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const { toolBarConfig } = useToolBar({
    refresh: () => fetchList(false)
  })
  const { tableColumns } = useTableColumns({
    handleDelete,
  })

  fetchList()
</script>

<style lang="stylus" scoped>
.list-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 0 24px 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
