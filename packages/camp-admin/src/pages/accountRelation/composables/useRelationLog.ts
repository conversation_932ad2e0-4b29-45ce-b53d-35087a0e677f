import { h } from 'vue'
import { Modal } from '@xhs/delight'

import { fetchAccountRelationLog } from '@/services/accountRelation'

import RelationLogTable from '../components/RelationLogTable.vue'

export default function useRelationLog() {
  const showLog = async (id:string) => {
    const res = await fetchAccountRelationLog(id)

    Modal.confirm({
      title: '操作日志',
      size: '550px',
      content: h(RelationLogTable, { dataSource: res.accountRelationshipLogList }),
      withFooter: false
    })
  }

  return {
    showLog
  }
}
