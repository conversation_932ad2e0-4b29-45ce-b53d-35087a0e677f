import { computed } from 'vue'
import TableCell from '@xhs/delight-material-ultra-table-cell'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

import useRelationLog from './useRelationLog'
import useRelationForm from './useRelationForm'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'mainUserId',
        label: '大号userId',
        component: {
          props: {
            placeholder: '请输入',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = ({
  refresh
}:{
  refresh: () => void
}) => { // 工具栏的配置
  const { showRelationForm } = useRelationForm(refresh)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新增关联',
        onClick: async () => {
          await showRelationForm()
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = ({
  handleDelete,
}: {
  handleDelete: (id: string) => void
}) => {
  const { showLog } = useRelationLog()

  // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '大号专业号名称',
      tooltip: '已开电商店的专业号',
      dataIndex: 'mainAccountName',
      render: ({ rowData }) => <Text>{ rowData?.mainAccountName || '-' }</Text>
    },
    {
      title: '大号userId',
      dataIndex: 'mainAccountUserId',
      render: ({ rowData }) => <Text>{ rowData?.mainAccountUserId || '-' }</Text>
    },
    {
      title: '电商sellerId',
      dataIndex: 'mainAccountSellerId',
      render: ({ rowData }) => <Text>{ rowData?.mainAccountSellerId || '-' }</Text>
    },
    {
      title: '小号专业号名称',
      tooltip: '新开本地店的专业号',
      dataIndex: 'burnerAccountName',
      render: ({ rowData }) => <Text>{ rowData?.burnerAccountName || '-' }</Text>
    },
    {
      title: '小号userId',
      dataIndex: 'burnerAccountUserId',
      render: ({ rowData }) => <Text>{ rowData?.burnerAccountUserId || '-' }</Text>
    },
    {
      title: '本地店sellerId',
      dataIndex: 'burnerAccountSellerId',
      render: ({ rowData }) => <Text>{ rowData?.burnerAccountSellerId || '-' }</Text>
    },
    {
      title: '状态',
      dataIndex: 'relationshipStatus',
      render: ({ rowData }) => <Text>{ rowData?.relationshipStatus === 'EFFECTIVE'
        ? <Text color="text-title">关联成功</Text>
        : <Text color="text-description">已解除关联</Text>
      }</Text>
    },
    {
      title: '操作',
      fixed: 'right',
      width: 160,
      render: ({ rowData }) => <TableCell
        type="action"
        action-props={{
          actionOptions: [
            {
              text: '操作日志',
              onClick: () => showLog(rowData?.id),
            },
            {
              text: '解除关联',
              visible: rowData?.relationshipStatus === 'EFFECTIVE',
              popConfirmProps: {
                outsideCloseable: true,
                placement: 'top-end',
                title: '确认解除关联',
                description: '你正在解除帐号关联，解除后关联关系将立即失效，无法实现同账号同时关联本地店和电商店，请谨慎确认',
                width: 250,
                onConfirm: () => handleDelete(rowData?.id),
              },
            }
          ],
        }} />,
    }]))
  return {
    tableColumns,
  }
}
