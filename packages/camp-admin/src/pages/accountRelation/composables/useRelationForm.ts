import { h, ref } from 'vue'
import { Modal } from '@xhs/delight'

import { AccountData, createAccountRelation } from '@/services/accountRelation'

import RelationForm from '../components/RelationForm.vue'

export default function useRelationForm(refresh: () => void) {
  const showRelationForm = () => {
    const form = ref({} as AccountData)

    const modal = Modal.confirm({
      title: '新增关联',
      width: 500,
      confirmButtonProps: {
        disabled: true,
      },
      content: h(RelationForm, {
        onChange: (f:AccountData) => {
          form.value = f
          modal.update({
            confirmButtonProps: {
              disabled: !(f.burnerUserId && f.mainUserId)
            }
          })
        }
      }),
      async onConfirm() {
        modal.update({
          loading: true
        })
        try {
          await createAccountRelation(form.value)
          refresh()
          modal.destroy?.()
        } finally {
          modal.update({
            loading: false
          })
        }
      }
    })
  }

  return {
    showRelationForm
  }
}
