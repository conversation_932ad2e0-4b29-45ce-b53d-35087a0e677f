import { ref } from 'vue'
import { toast2 as toast } from '@xhs/delight'

import { delAccountRelation, fetchAccountRelation, RelationItem } from '@/services/accountRelation'

export const useList = () => {
  const listSource = ref<RelationItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const total = ref(0)
  const filterParam = ref({ // 搜索参数
    pageSize: 10,
    pageNum: 1,
    mainUserId: ''
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNum = 1
    }
    try {
      const res = await fetchAccountRelation({
        pageSize: filterParam.value.pageSize,
        pageNo: filterParam.value.pageNum,
        mainUserId: filterParam.value.mainUserId,
      })
      total.value = res.total
      listSource.value = res.accountRelationshipList || []
    } finally {
      loading.value = false
    }
  }

  const handleDelete = async (id: string) => { // 删除方法
    await delAccountRelation(id)
    toast.success({ // 规范Toast交互，要求带背景色
      content: '操作成功',
      strong: true,
    })
    fetchList(false)
  }

  return {
    listSource,
    total,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleDelete,
  }
}
