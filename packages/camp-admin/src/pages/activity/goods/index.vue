<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card title="商品创建活动">
      <GoodsSearch :payload="payload" @search="search" />

      <ActivityTable :data="data" />

      <Pagination v-model="payload" :total="total" @change="fetchData" />
    </Card>
  </PageLayout>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'

  import PageLayout from '@/components/page-layout/index.vue'
  import Card from '@/components/base-card/index.vue'
  import Pagination from '@/components/base-pagination/index.vue'

  import { GoodsParams, Goods } from '@/types/activity'
  import * as Api from '@/services/activity'

  import ActivityTable from './goods-table.vue'
  import GoodsSearch from './goods-search.vue'

  const navList = [
    {
      label: '活动管理',
    },
    {
      label: '活动信息',
    },
    {
      label: '商品创建活动'
    }
  ]

  const payload = ref<GoodsParams>({
    pageNum: 1,
    pageSize: 20,
    sellerId: ''
  })

  const loading = ref<boolean>(true)
  const total = ref<number>(0)
  const data = ref<Goods[]>([])

  const fetchData = async () => {
    loading.value = true
    try {
      const res = await Api.goods(payload.value)
      data.value = res.list
      total.value = res.total
      loading.value = false
    } catch (e: any) {
      loading.value = false
    }
  }
  fetchData()

  const search = (p:GoodsParams) => {
    payload.value = p
    fetchData()
  }

</script>

<style>
</style>
