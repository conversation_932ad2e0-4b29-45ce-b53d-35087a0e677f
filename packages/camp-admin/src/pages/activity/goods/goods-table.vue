<template>
  <BeerTable border-style="full" :columns="columns" :data="data" />
  <GoodsModal v-model="goodsVisible" :goods="goods" />
</template>

<script lang="tsx" setup>
  import { defineProps, PropType, ref } from 'vue'
  import { RouterLink } from 'vue-router'
  import { BeerTable } from '@xhs/yam-beer'

  import { GoodsParams } from '@/types/activity'
  import { IGoodsInfo } from '@/types/goods'

  import BaseImage from '@/components/base-image/index.vue'
  import GoodsModal from '@/components//goods-modal/index.vue'

  import GoodsStatus from './components/goods-status.vue'

  const goodsVisible = ref<boolean>(false)
  const goods = ref<IGoodsInfo>()

  defineProps({
    data: {
      type: Array,
      default: () => []
    },
    payload: {
      type: Object as PropType<GoodsParams>,
      default: () => ({})
    }
  })

  const columns = [
    {
      title: '商品名称',
      key: 'name',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          const baseInfo = row.baseInfo
          const src = baseInfo?.images[0]?.path ? (`//qimg.xiaohongshu.com/${baseInfo?.images[0].path}`) : ''
          return (
          <div class="goods">
            <BaseImage src={src} />
            <div class="info">
              <div class="goods-name r-ellipsis-2">{baseInfo?.name}</div>
              <div class="goods-desc">商品ID：{baseInfo?.id}</div>
            </div>
          </div>
        )
        }
      }
    },
    {
      title: '商家',
      key: 'seller',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
          <span>{row.baseInfo?.sellerName || '-'}</span>
        )
        }
      }
    },
    {
      title: '商品类目',
      key: 'title3',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
          <span>{row.baseInfo?.categoryName || '-'}</span>
        )
        }
      }
    },
    {
      title: '商品状态',
      key: 'state',
      td: {
        bs: {},
        is: (data: any) => {
          const { rowData: { baseInfo } } = data.props
          return baseInfo.state === 0 ? <span style="color: red">已下架</span> : <span>在架上</span>
        },
      },
    },
    {
      title: '审核状态',
      key: 'status',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
            row.auditStatus ? <GoodsStatus status={row.auditStatus} auditList={row.auditResultList} /> : '--'
          )
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData

          const showDetail = () => {
            goods.value = row
            goodsVisible.value = true
          }

          return row.hadToActivity ? (
          <span style="color: 666">绑定活动Id: {[...new Set(row.activityParentIds)]?.join('、')}</span>
            ) : (
          <div class="r-actions">
            <RouterLink class="primary" to={{
              name: 'activityForm',
              query: {
                goodsId: row.baseInfo?.id,
                goodsName: row.baseInfo?.name || '-',
                sellerId: row.baseInfo?.sellerId,
                sellerName: row.baseInfo?.sellerName || '-',
                type: row.type || ''
              }
            }}>创建活动</RouterLink>
            <span class="primary" onClick={showDetail}>查看商品</span>
          </div>
        )
        },
      }
    },
  ]

</script>

<style scoepd lang="stylus">
.goods
  display flex
  align-items center
  .info
    height 54px
    padding-left: 1em
    max-width 210px
    display flex
    flex-direction column
    justify-content space-between
  .goods-name
    font-size 14px
    line-height 16px
    color rgba(0,0,0,0.85)
    height 32px
  .goods-desc
    color rgba(0,0,0,0.45)
    font-size 12px
    line-height 1
    padding-top .5em
    white-space nowrap
</style>
