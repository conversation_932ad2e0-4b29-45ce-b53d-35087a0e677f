<template>
  <div style="display: flex; align-items: center; white-space: nowrap;">
    <BeerTag
      variant="fill"
      :bs="{
        ...statusStyle,
        fontWeight: 500,
      }"
    >{{ statusValue }}</BeerTag>
    <Tooltip v-if="status === EProductAuditStatus.FAIL && auditList.length > 0" placement="top">
      <BeerIcon :bs="{
        ml: 2,
      }" size="14" icon="details"
      />
      <template #content>
        <NewBsBox
          :bs="{
            maxWidth: 800,
            maxHeight: 300,
            overflowY: 'auto',
          }"
        >
          <NewBsBox v-for="(v, idx) of auditList" :key="idx">
            <div>审核失败内容：{{ v.content }}</div>
            <div>审核失败理由：{{ v.comment }}</div>
          </NewBsBox>
        </NewBsBox>
      </template>
    </Tooltip>
  </div>
</template>

<script lang="ts">
  import { defineComponent, computed, PropType } from 'vue'
  import {
    BeerTag, Tooltip, BeerIcon, NewBsBox,
  } from '@xhs/yam-beer'
  import { EProductAuditStatus, PRODUCT_AUDIT_STATUS } from '@/constants/goods'

  export default defineComponent({
    components: {
      BeerTag, Tooltip, BeerIcon, NewBsBox,
    },
    props: {
      status: {
        type: Number,
        required: true,
      },
      auditList: {
        type: Array as PropType<{ content: string; comment: string }[]>,
        default: () => ([]),
      },
    },
    setup(props) {
      const statusStyles = {
        [EProductAuditStatus.AUDITING]: {
          backgroundColor: 'rgba(58, 100, 255, 0.1)',
          color: '#3A64FF',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(58, 100, 255, 0.2)',
          },
        },
        [EProductAuditStatus.PASS]: {
          backgroundColor: 'rgba(48, 218, 106, 0.1)',
          color: '#42C9A0',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(48, 218, 106, 0.2)',
          },
        },
        [EProductAuditStatus.FAIL]: {
          backgroundColor: 'rgba(255, 46, 77, 0.1)',
          color: '#FF2442',
          "&:hover:not([data-disable='true'])": {
            backgroundColor: 'rgba(255, 46, 77, 0.2)',
          },
        },
      }

      return {
        EProductAuditStatus,
        // @ts-ignore
        statusStyle: computed(() => statusStyles[props.status]),
        // @ts-ignore
        statusValue: computed(() => PRODUCT_AUDIT_STATUS[props.status] || ' - '),
      }
    },
  })
</script>

<style lang="stylus" scoped>
</style>
