<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card>
      <h2 class="r-part-title">活动设置</h2>
      <BaseForm
        v-if="type"
        ok-text="提交"
        cancel-text="取消"
        :payload="payload"
        :fields="fields"
        @submit="submit"
        @cancel="cancel"
      />
      <div v-else-if="!id" class="center-flex">
        <span>页面参数发生错误</span>
      </div>

      <GoodsModal v-model="visible" :goods="goods" />
    </Card>
  </PageLayout>
</template>

<script lang="tsx" setup>
  import { ref, computed } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { BeerInput, BeerSingleSelect } from '@xhs/yam-beer'
  import PageLayout from '@/components/page-layout/index.vue'
  import Card from '@/components/base-card/index.vue'
  import UploadImage from '@/components/upload-image/index.vue'
  import BaseForm from '@/components/base-form/index.vue'
  import PoiSelect from '@/components/poi-select/index.vue'
  import GoodsModal from '@/components/goods-modal/index.vue'

  import { FormFields } from '@/types'
  import { PoiType } from '@/types/poi'
  import { IGoodsInfo } from '@/types/goods'
  import { ActivityCreatePayload as Payload } from '@/types/activity'
  import * as Api from '@/services/activity'
  import * as GoodsApi from '@/services/goods'
  import confirm from '@/utils/confirm'
  import { refresh } from '@/composables/use-cross-refresh'

  const navList = [
    {
      label: '活动管理'
    },
    {
      label: '编辑活动'
    }
  ]

  const router = useRouter()
  const route = useRoute()
  const visible = ref<boolean>(false)
  const goods = ref<IGoodsInfo>()
  const loading = ref<boolean>(false)

  const {
    id = '',
    goodsId: queryGoodsId = '',
    goodsName: queryGoodsName = '',
    sellerId = '',
    sellerName = '',
    type: queryType = ''
  } = route.query as { [key: string]: string }

  const goodsId = ref(queryGoodsId)
  const goodsName = ref(queryGoodsName)
  const selectedPoiOptions = ref<{ name: string; value: string }[]>([])
  const type = ref(queryType as PoiType)

  const payload = ref<Payload<string>>({
    images: [],
    name: goodsName.value,
    pois: [],
    organizerId: sellerId,
    organizerName: sellerName,
    items: goodsId.value ? [{ itemId: goodsId.value }] : []
  })

  const fetchData = async () => {
    if (id) {
      loading.value = true
      const res = await Api.detail(id)
      payload.value.images = res.images.map(item => ({
        link: item.url,
        width: item.width,
        height: item.height
      }))
      selectedPoiOptions.value = res.pois.map(item => ({ name: item.poiName, value: item.poiId }))
      payload.value.name = res.name || ''
      payload.value.pois = res.pois.map(poi => poi.poiId)
      payload.value.organizerId = res.organizerId
      payload.value.organizerName = res.organizerName
      payload.value.items = res.items
      goodsId.value = res.items[0]?.itemId
      goodsName.value = res.items[0]?.itemName
      loading.value = false
      type.value = res.type
    }
  }
  fetchData()

  const showGoods = async (nextVisible = true) => {
    if (goods?.value?.baseInfo) {
      visible.value = nextVisible
      return
    }
    loading.value = true
    try {
      const res = await GoodsApi.detail(goodsId.value)
      visible.value = nextVisible
      goods.value = res
      if (goodsId.value) {
        payload.value.images = res.baseInfo?.images?.map(img => ({
          width: img.width, height: img.height, link: `https://qimg.xiaohongshu.com/${img.path}`
        })) || []
      }
    } catch (err) {
    // @ts-ignore
    }
    loading.value = false
  }

  if (goodsId.value) {
    showGoods(false)
  }

  const merchantForm = computed(() => (goodsId.value ? [
    {
      label: '商家名称',
      name: 'organizerName',
      value: payload.value.organizerName,
      required: true,
      is: BeerSingleSelect,
      props: { disabled: true, options: [payload.value.organizerName] },
    },
    {
      label: '绑定商品',
      name: 'productionName',
      value: goodsName.value,
      required: true,
      is: BeerSingleSelect,
      props: { disabled: true, options: [goodsName.value] },
      suffix: () => <span class="btn-text" onClick={() => showGoods()}>查看商品</span>
    },
  ] : []))

  // 基础表单
  const fields = computed<FormFields>(() => [
    {
      label: '活动名称',
      name: 'name',
      value: payload.value.name,
      required: true,
      error: '',
      is: BeerInput,
      validate: (value: string) => (value?.length > 12 ? Promise.resolve('活动名称已超过12字') : Promise.resolve(true)),
      suffix: () => <span style="color: #666; font-size: 12px">突出活动特色，建议10-12字以内</span>
    },
    ...merchantForm.value,
    {
      label: '活动图片',
      name: 'images',
      value: payload.value.images,
      required: true,
      is: UploadImage,
      props: {
        aspectRatio: [1 / 1],
        maxCount: 10,
        maxSize: 1024 * 1024 * 5, // 5M
        tip: '最多上传10张，支持 png / jpg /jpeg 尺寸，尺寸为1:1，大小不超过5M；可拖拽排序，活动缩略图取首图'
      },
      validate: (value: any) => {
        if (!value?.length) return Promise.reject('请上传活动图片')
        const error = value?.find((img: any) => img.error)?.error
        return error ? Promise.reject(error) : Promise.resolve()
      }
    },
    {
      label: '活动地点',
      name: 'pois',
      value: payload.value.pois,
      required: true,
      is: PoiSelect,
      props: {
        selectedOptions: selectedPoiOptions.value,
        type: type.value,
        key: type.value,
        placeholder: '输入关键词模糊搜索',
      },
      validate: (value: any) => (value?.length ? Promise.resolve(true) : Promise.reject('请选择活动地点'))
    }
  ])

  const cancel = () => {
    if (window.history.length > 1) {
      router.back()
    } else {
      window.close()
      refresh()
    }
  }

  const submit = async (p: Payload<string>) => {
    await confirm('是否确认提交？')
    loading.value = true
    try {
      const images = p.images
        .filter(item => !item.error)
        .map(item => ({
          width: item.width,
          height: item.height,
          url: item.link,
        }))
      if (id) {
        await Api.update({
          ...p,
          activityId: id,
          images,
          pois: p.pois.map(poiId => ({ poiId }))
        })
      } else {
        await Api.create({
          ...p,
          images,
          pois: p.pois.map(poiId => ({ poiId }))
        })
      }
      // 刷新列表数据
      refresh()
      window.close()
    } catch (e) {
    // @ts-ignore
    // toaster.danger(e.message)
    }
    loading.value = false
  }

</script>
