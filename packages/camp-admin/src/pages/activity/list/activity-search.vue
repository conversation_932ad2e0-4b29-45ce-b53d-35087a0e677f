<template>
  <BaseForm ok-text="查询" layout="inline" :payload="payload" :fields="fields" @submit="search" />
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'

  import BaseForm from '@/components/base-form/index.vue'
  import PoiSelect from '@/components/poi-single-select/index.vue'
  import MerchantSelect from '@/components/merchent-single-select/index.vue'

  import { ActivityListParams } from '@/types/activity'
  import { FormFields } from '@/types'

  const props = defineProps({
    payload: {
      type: Object as PropType<ActivityListParams>,
      default: () => ({}),
    },
  })

  // 基础表单
  const fields = computed<FormFields>(() => [
    {
      label: 'POI名称',
      name: 'poiId',
      value: props.payload.poiId,
      is: PoiSelect,
    },
    {
      label: '商家名称',
      name: 'sellerId',
      value: props.payload.sellerId,
      is: MerchantSelect,
    },
  ])

  const emit = defineEmits(['search'])

  // 搜索
  const search = (payload: ActivityListParams) => {
    emit('search', payload)
  }

</script>

<style>
</style>
