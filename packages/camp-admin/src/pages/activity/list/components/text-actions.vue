<template>
  <div class="r-actions">
    <router-link
      class="primary"
      target="_blank"
      :to="{ name: 'activityForm', query: { id: row.activityId } }"
    >编辑</router-link>
    <!-- 2代表上线 -->
    <Popover v-if="row.status !== 2 && row.auditStatus === 4" trigger="hover" placement="right">
      <span class="r-plain-text">上线</span>
      <template #popover>
        <span style="padding: 0 1em;">活动审核不通过，不允许上线</span>
      </template>
    </Popover>
    <span v-else class="primary" @click="changeStatus">{{ row.status === 2 ? '下线' : '上线' }}</span>
  </div>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import {
    Popover, showModal, BeerInput, toaster
  } from '@xhs/yam-beer'

  import { Activity } from '@/types/activity'
  import * as Api from '@/services/activity'

  // 1: '待处理',
  // 2: '已上线',
  // 3: '已下线'

  const props = defineProps({
    row: {
      type: Object as PropType<Activity>,
      default: () => ({})
    }
  })

  const emit = defineEmits(['refresh'])

  const changeStatus = () => {
    const row = props.row
    let value = ''
    const inputProps = {
      placeholder: '请填写活动下线原因',
      style: 'width: 352px',
      'onUpdate:modelValue': (newValue: string) => {
        value = newValue
      }
    }
    showModal({
      props: { // 支持 modal 的所有参数， 包括 type=dialog
        title: row.status === 2 ? '请填写活动下线原因' : '请确定是否上线',
        handleConfirm: async ({ close }: { close: any }) => {
          if (!value && row.status === 2) {
            toaster.danger('请填写活动下线原因')
            return
          }

          await Api.changeStatus({
            status: row.status === 2 ? 3 : 2,
            activityParentId: row.activityId,
            reason: value
          })

          emit('refresh')

          close()
        },
      },
      // 只有上线才会显示
      getScopedSlots: () => ({ // template slot 配置
        default: () => (row.status === 2 ? <BeerInput {...inputProps} /> as any : undefined)
      }),
    })
  }
</script>

<style>
</style>
