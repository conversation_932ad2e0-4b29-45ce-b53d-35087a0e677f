<template>
  <div class="middle-flex">
    <div class="tag" :style="style">{{ auditStatus[status] }}</div>
    <Popover v-if="status === 4 && message?.length" trigger="hover" placement="right">
      <BeerIcon style="cursor: pointer;margin-left: 5px;" icon="exclamation-marks" color="danger" />
      <template #popover>
        <div style="padding: 10px; max-width: 500px;">
          <div style="padding: 10px; max-width: 500px;">
            <div v-for="(item, key) in message" :key="key">
              <div>
                <p
                  v-if="item.content"
                  style="white-space: normal; word-break: break-all; color: #666; font-size: 14px"
                >审核失败内容：{{ item.content }}</p>
                <p
                  v-if="item.comment"
                  style="white-space: normal; word-break: break-all; padding-bottom: 10px"
                >审核失败理由：{{ item.comment }}</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Popover>
  </div>
</template>

<script lang="tsx" setup>
  import { computed, defineProps, PropType } from 'vue'
  import { Popover, BeerIcon } from '@xhs/yam-beer'
  import { auditStatus } from '@/constants/activity'

  const colorMap = {
    1: { color: '#999', backgroundColor: 'rgba(51, 51, 51, 0.05)' }, // 创建完成
    2: { color: '#3A64FF', backgroundColor: 'rgba(58, 100, 255, 0.1)' }, // 审核中
    3: { color: '#42C9A0', backgroundColor: 'rgba(48, 218, 106, 0.1)' }, // 审核通过
    4: { color: '#FF2442', backgroundColor: 'rgba(255, 46, 77, 0.1)' } // 审核通不通过
  }

  const props = defineProps({
    status: {
      type: Number as PropType<keyof typeof colorMap>,
      default: 0
    },
    message: {
      type: Array as PropType<{ comment: string; content: string }[]>,
      default: () => []
    }
  })

  const style = computed(() => colorMap[props.status] || colorMap[1])

</script>

<style scoped lang="stylus">
.tag
  display flex
  align-items center
  justify-content center
  padding 5px 8px
  font-size 14px
  border-radius 4px
  white-space nowrap
</style>
