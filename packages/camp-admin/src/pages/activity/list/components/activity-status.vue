<template>
  <div style="display: flex; align-items: center;">
    <div :class="{ gray: status !== 2 }" style="white-space: nowrap;">{{ statusMap[status] || '-' }}</div>
    <Popover v-if="status === 3 && message" trigger="hover" placement="right">
      <BeerIcon style="padding-left: .3em" icon="exclamation-marks" color="danger" />
      <template #popover>
        <div style="padding: 10px; max-width: 500px;">
          <span>{{ message }}</span>
        </div>
      </template>
    </Popover>
  </div>
</template>

<script lang="tsx" setup>
  import { defineProps, PropType } from 'vue'
  import { Popover, BeerIcon } from '@xhs/yam-beer'
  import { statusMap } from '@/constants/activity'

  defineProps({
    status: {
      type: Number as PropType<(1 | 2 | 3)>,
      default: 1
    },
    message: {
      type: String,
      default: ''
    }
  })

</script>

<style scoped>
.gray {
  color: #999;
}
</style>
