<template>
  <PageLayout :nav-list="navList" :loading="loading">
    <Card>
      <ActivitySearch :payload="payload" @search="search" />

      <div class="between-flex" style="margin-bottom: 20px">
        <BaseButtonGroup
          v-model="payload.activityStatus"
          :options="statusOptions"
          @change="search"
        />
        <div>
          <BeerButton variant="default" icon-before="add-m" @click="toChoose()">绑定商品创建活动</BeerButton>
          <!-- <BeerButton variant="default" icon-before="add-m" @click="toCreate()">不绑定商品创建活动</BeerButton> -->
        </div>
      </div>

      <ActivityTable :data="data" :payload="payload" @refresh="fetchData" />

      <BasePagination v-model="payload" :total="total" @change="saveAndFetch" />
    </Card>
  </PageLayout>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { BeerButton } from '@xhs/yam-beer'

  import Card from '@/components/base-card/index.vue'
  import PageLayout from '@/components/page-layout/index.vue'
  import BaseButtonGroup from '@/components/base-button-group/index.vue'
  import BasePagination from '@/components/base-pagination/index.vue'

  import { statusOptions } from '@/constants/activity'
  import { Activity, ActivityListParams } from '@/types/activity'
  import * as Api from '@/services/activity'
  import useSaveParams from '@/composables/use-save-params'
  import { useCrossRefresh } from '@/composables/use-cross-refresh'

  import ActivitySearch from './activity-search.vue'
  import ActivityTable from './activity-table.vue'

  const router = useRouter()
  const { saveParams, query } = useSaveParams()

  const navList = [
    {
      label: '活动管理'
    },
    {
      label: '活动信息'
    }
  ]

  const loading = ref<boolean>(true)

  const payload = ref<ActivityListParams>({
    pageNum: Number(query.pageNum || 1),
    pageSize: Number(query.pageSize || 20),
    poiId: `${query.poiId || ''}`,
    sellerId: `${query.sellerId || ''}`,
    activityStatus: `${query.activityStatus || ''}`,
  })

  const total = ref<number>(0)

  const data = ref<Activity[]>([])

  const fetchData = async () => {
    loading.value = true
    try {
      const res = await Api.list(payload.value)
      data.value = res.list
      total.value = res.total
      loading.value = false
    } catch (e: any) {
      loading.value = false
    }
  }
  fetchData()
  useCrossRefresh(fetchData)

  const saveAndFetch = () => {
    saveParams(payload.value)
    fetchData()
  }

  const search = (f: ActivityListParams) => {
    payload.value = { ...payload.value, ...f, pageNum: 1 }
    saveAndFetch()
  }

  const toChoose = () => {
    window.open(router.resolve({ name: 'activityGoods' }).href)
  }

// const toCreate = () => {
//   window.open(router.resolve({ name: 'activityForm' }).href)
// }

</script>

<style>
</style>
