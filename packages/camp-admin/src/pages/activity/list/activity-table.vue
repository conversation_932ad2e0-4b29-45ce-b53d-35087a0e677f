<template>
  <BeerTable border-style="full" :columns="columns" :data="data" />
</template>

<script lang="tsx" setup>
  import { defineProps, PropType } from 'vue'
  import { BeerTable } from '@xhs/yam-beer'

  import { ActivityListParams } from '@/types/activity'

  import BaseImage from '@/components/base-image/index.vue'
  import ActivityStatus from './components/activity-status.vue'
  import ApproveStatus from './components/approve-status.vue'
  import TextActions from './components/text-actions.vue'

  defineProps({
    data: {
      type: Array,
      default: () => []
    },
    payload: {
      type: Object as PropType<ActivityListParams>,
      default: () => ({})
    }
  })

  const emit = defineEmits(['refresh'])

  const columns = [
    {
      title: '活动名称',
      key: 'name',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          const url = row.images[0]?.url
          return (
          <div class="goods">
            <BaseImage src={url} />
            <div class="info">
              <div class="goods-name r-ellipsis-2">{row.name}</div>
              <div class="goods-desc r-ellipsis">活动Id：{row.activityId}</div>
            </div>
          </div>
        )
        }
      }
    },
    {
      title: '活动POI',
      key: 'pois',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
          <div>
            {
              row?.pois?.map((item: { poiName: string }) => item.poiName).filter(Boolean).map((name: string) => (
                <div>{name}</div>
              ))
            }
          </div>
        )
        }
      }
    },
    {
      title: '活动商家',
      key: 'organizerName',
    },
    {
      title: '绑定商品',
      key: 'items',
      td: {
        is: (data: any) => {
          const items = data.props.rowData?.items as { itemId: string; itemName: string }[]
          const uniqueItems = () => {
            const itemIds = [...new Set(items.map(item => item.itemId))]
            const itemsMap = items.reduce((acc, item) => {
              acc[item.itemId] = item.itemName
              return acc
            }, {} as Record<string, string>)
            return itemIds.map(id => ({ itemId: id, itemName: itemsMap[id] }))
          }

          return (
          <div>
            {
              uniqueItems()?.map(item => (
                <div class="goods-info">
                  <div class="goods-name r-ellipsis-2">{item.itemName}</div>
                  <div class="goods-desc">商品ID：{item.itemId}</div>
                </div>
              ))
            }
          </div>
        )
        }
      }
    },
    {
      title: '活动状态',
      key: 'status',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return <ActivityStatus status={row.status} message={row.offlineReason} />
        }
      }
    },
    {
      title: '审核状态',
      key: 'status',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return <ApproveStatus status={row.auditStatus} message={row.auditResultList} />
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData

          return <TextActions row={row} onRefresh={() => emit('refresh')} />
        },
      }
    },
  ]

</script>

<style scoepd lang="stylus">
.goods
  display flex
  align-items center
.info
  height 54px
  padding-left: 1em
  max-width 220px
  display flex
  flex-direction column
  justify-content space-between
.goods-info
  max-height 54px
  padding-left: 1em
  max-width 220px
  display flex
  flex-direction column
  &:not(:last-child)
    padding-bottom 10px
.goods-name
  font-size 14px
  line-height 16px
  color rgba(0,0,0,0.85)
  max-height 32px
.goods-desc
  color rgba(0,0,0,0.45)
  font-size 12px
  line-height 1
  padding-top .5em
  white-space nowrap
</style>
