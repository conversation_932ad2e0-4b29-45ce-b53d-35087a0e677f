import { computed, Ref } from 'vue'
import { Text, Button } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'
import { Delete } from '@xhs/delight/icons'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const navList = [
  {
    label: '博主带货',
  },
  {
    label: '博主绑定',
  },
]

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: () => {
      fetchList()
    },
    filterItems: [
      {
        name: 'kolUserId',
        label: '博主用户Id',
        component: {
          props: {
            placeholder: '请输入博主用户Id',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = ({ editVisible }:{
  editVisible: Ref<boolean>
}) => { // 工具栏的配置
  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '刷博主报名数据',
        onClick: () => {
          editVisible.value = true
        },
      },
    ],
  }))
  return {
    toolBarConfig,
  }
}

export const useTableColumns = (handleDelete: (itemid: any) => void) => { // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: '博主用户Id',
      dataIndex: 'kolUserId',
      render: ({ rowData }) => (<Text>{rowData?.kolUserId}</Text>),
    },
    {
      title: '商家sellerId',
      dataIndex: 'sellerId',
      render: ({ rowData }) => (<Text>{rowData?.sellerId}</Text>),
    },
    {
      title: '操作',
      render: ({ rowData }) => (<Button onClick={ () => {
        handleDelete(rowData)
      }} type='danger' icon={Delete}>删除</Button>),
      align: 'center'
    }
  ]))
  return {
    tableColumns,
  }
}
