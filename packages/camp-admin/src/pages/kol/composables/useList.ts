import { ref, h } from 'vue'
import { toast2 as toast, Modal, Text } from '@xhs/delight'
import {
  KolParams, fetchKol, KolItem, deleteBinding
} from '@/services/kol'

export const useList = () => {
  const listSource = ref<KolItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const editVisible = ref(false) // 编辑弹窗展示标识
  const editItem = ref() // 编辑的Item
  const total = ref(0)
  const filterParam = ref<KolParams>({ // 搜索参数
    pageSize: 10,
    pageNo: 1,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNo = 1
      filterParam.value.pageSize = 10
    }
    const res = await fetchKol(filterParam.value)
    loading.value = false
    total.value = res?.total || 0
    listSource.value = res?.kolSellerVOS || []
  }

  const handleDelete = (deleteItem:any) => { // 删除方法
    Modal.danger({
      title: '警告',
      content: h(Text, {}, {
        default: () => `确定要删除博主${deleteItem?.kolUserId}与商家${deleteItem?.sellerId}吗`
      }),
      async onConfirm(close:any) {
        try {
          await deleteBinding({
            kolUserId: deleteItem?.kolUserId,
            sellerId: deleteItem?.sellerId
          })
          toast.success('删除成功！')
          fetchList()
        } finally {
          close?.()
        }
      },
      onCancel(close:any) {
        close?.()
      }
    })
  }
  const handleModal = (item:any) => { // 处理编辑弹窗
    editVisible.value = true
    editItem.value = item
  }
  const handleEdit = () => { // 处理编辑
    toast.info({
      content: `编辑${editItem.value?.good}`,
      strong: true,
    })
    editVisible.value = false
  }

  return {
    listSource,
    total,
    loading,
    editVisible,
    filterParam,
    fetchList,
    handleDelete,
    handleEdit,
    handleModal,
  }
}
