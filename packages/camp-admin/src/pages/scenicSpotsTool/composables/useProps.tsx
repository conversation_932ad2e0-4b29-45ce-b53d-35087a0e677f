import { computed, ref, watch } from 'vue'
import { PageHeaderProps } from '@xhs/delight-material-ultra-page-header'
import dayjs from 'dayjs'
import { Text } from '@xhs/delight'
import { ThContent } from '@xhs/delight/components/Table/interface'
import { IFilter } from '@xhs/delight-material-ultra-outline-filter'

export const spaceProps = { // page容器的配置
  direction: 'vertical',
  size: 'large',
  align: 'unset',
} as const

export const pageHeaderProps: PageHeaderProps = {
  title: '景点POI打卡距离白名单配置',
}

export const useFilter = (fetchList: () => void) => { // 筛选器的配置
  const filterConfig = computed<IFilter>(() => ({
    handleFilter: fetchList,
    filterItems: [
      {
        name: 'poiId',
        label: 'POI ID',
        component: {
          props: {
            placeholder: '请输入POI ID',
          },
        },
      },
    ],
  }))
  return {
    filterConfig,
  }
}

export const useToolBar = fetchList => { // 工具栏的配置
  const addwhiteListVisible = ref(false)
  const delwhiteListVisible = ref(false)

  const toolBarConfig = computed(() => ({
    actions: [
      {
        text: '新增白名单',
        onClick: () => {
          addwhiteListVisible.value = true
        },
      },
      {
        text: '删除白名单',
        onClick: () => {
          delwhiteListVisible.value = true
        },
      },
    ],
  }))

  watch(() => addwhiteListVisible.value, val => {
    if (!val) {
      fetchList()
    }
  })
  watch(() => delwhiteListVisible.value, val => {
    if (!val) {
      fetchList()
    }
  })

  return {
    toolBarConfig,
    addwhiteListVisible,
    delwhiteListVisible
  }
}

export const useTableColumns = () => {
  // 列表的配置
  const tableColumns = computed<ThContent[]>(() => ([
    {
      title: 'POI ID',
      dataIndex: 'poiId',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiId}</Text>),
    },
    {
      title: '门店名称',
      dataIndex: 'poiName',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiName || '-'}</Text>),
    },
    {
      title: 'POI 品类',
      dataIndex: 'poiType',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.poiType || '-'}</Text>),
    },
    {
      title: '打卡距离',
      dataIndex: 'distance',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{`${rowData?.distance ? `${rowData.distance}km` : '-'}`}</Text>),
    },
    {
      title: '提报人',
      dataIndex: 'submitBy',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.submitBy || '-'}</Text>),
    },
    {
      title: '操作人',
      dataIndex: 'createBy',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{rowData?.createBy || '-'}</Text>),
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      render: ({ rowData }) => (<Text ellipsis tooltip style={{ maxWidth: '250px' }}>{dayjs(rowData?.createTime).format('YYYY-MM-DD HH:mm:ss')}</Text>),
    },
  ]))
  return {
    tableColumns,
  }
}
