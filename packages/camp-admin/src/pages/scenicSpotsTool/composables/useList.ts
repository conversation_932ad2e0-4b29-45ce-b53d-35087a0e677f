import { ref } from 'vue'
import { fetchScenicSpotsList, poiMessageVOLItem } from '@/services/scenicSpots'

export const useList = () => {
  const listSource = ref<poiMessageVOLItem[]>([]) // 列表数据
  const loading = ref(false) // 列表加载标识
  const total = ref(0)
  const filterParam = ref({ // 搜索参数
    pageSize: 50,
    pageNum: 1,
  })

  const fetchList = async (isResetPageNum:boolean = true) => { // 获取商品列表的方法
    loading.value = true
    if (isResetPageNum) { // 是否重置
      filterParam.value.pageNum = 1
    }
    try {
      const res = await fetchScenicSpotsList(filterParam.value)
      total.value = res.total
      listSource.value = res?.queryPoiMarkWhileVOList || []
    } finally {
      loading.value = false
    }
  }

  fetchList()

  return {
    listSource,
    loading,
    filterParam,
    total,
    fetchList,
  }
}
