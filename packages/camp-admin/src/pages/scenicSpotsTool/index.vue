<template>
  <Space
    class="poi-claim-container"
    v-bind="spaceProps"
  >
    <PageHeader v-bind="pageHeaderProps" />
    <OutlineFilter
      v-model="filterParam"
      :config="filterConfig"
    />

    <div class="list-content">
      <Toolbar :config="toolBarConfig" />
      <Table
        size="large"
        :columns="tableColumns"
        :data-source="listSource"
        :loading="loading"
      />
      <Pagination
        v-model="filterParam.pageNum"
        v-model:pageSize="filterParam.pageSize"
        :total="total"
        style="margin-top: 24px"
        @change="fetchList(false)"
      />
    </div>
  </Space>
  <ImportAddWhiteList v-model:visible="addwhiteListVisible" />
  <ImportDelWhiteList v-model:visible="delwhiteListVisible" />
</template>
<script setup lang="ts">
  import {
    Space, Table, Pagination
  } from '@xhs/delight'
  import Toolbar from '@xhs/delight-material-ultra-toolbar'
  import PageHeader from '@xhs/delight-material-ultra-page-header'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import {
    spaceProps, useToolBar, useFilter, useTableColumns, pageHeaderProps,
  } from './composables/useProps'
  import { useList } from './composables/useList'
  import ImportAddWhiteList from './components/ImportAddWhiteList.vue'
  import ImportDelWhiteList from './components/ImportDelWhiteList.vue'

  const {
    listSource, // 列表数据
    loading, // 列表加载标识
    filterParam, // 查询条件
    total, // 总条数
    fetchList, // 获取列表方法
  } = useList()

  const { filterConfig } = useFilter(fetchList)
  const {
    addwhiteListVisible,
    delwhiteListVisible,
    toolBarConfig
  } = useToolBar(fetchList)
  const { tableColumns } = useTableColumns()
</script>

<style lang="stylus" scoped>
.poi-claim-container
  width 100%
  .list-content
    background #FFFFFF
    border-radius 8px
    padding 0 24px 24px
  .affixed-bg .ultra-page-header
    background  #FFFFFF
    box-shadow 0px 1px 8px rgba(0, 0, 0, 0.09)
    border-radius 0px 0px 8px 8px
    padding 24px
    box-sizing content-box
</style>
