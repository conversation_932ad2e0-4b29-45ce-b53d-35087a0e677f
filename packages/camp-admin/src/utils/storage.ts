import { isObject } from './index'

/**
 * 根据账号id储存localStorage
 */
export class Storage {
  private userId: string

  constructor(userId = '') {
    this.userId = userId
  }

  private static getAll(key: string, defaultItem: any = null) {
    let result = defaultItem
    const jsonData = localStorage.getItem(key)
    if (jsonData) {
      try {
        result = JSON.parse(jsonData)
      } catch (error) {
        // 异常时清除该localStorage
        localStorage.removeItem(key)
      }
    }
    return result
  }

  private static update(key: string, result: any) {
    if (!isObject(result)) {
      localStorage.removeItem(key)
    }
  }

  getItem(key: string, defaultItem: any = null) {
    let data = defaultItem
    const result = Storage.getAll(key, defaultItem)
    if (result && this.userId) {
      data = result[this.userId] ?? defaultItem
    }
    Storage.update(key, result)
    return data
  }

  setItem(key: string, value: any) {
    const result = Storage.getAll(key, {})
    if (result && this.userId) {
      result[this.userId] = value
      localStorage.setItem(key, JSON.stringify(result))
    }
    Storage.update(key, result)
  }

  remoteItem(key: string) {
    const result = Storage.getAll(key, null)
    if (result) {
      delete result[this.userId]
      localStorage.setItem(key, JSON.stringify(result))
    }
    Storage.update(key, result)
  }
}
