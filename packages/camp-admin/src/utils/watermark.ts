import watermark from './watermark-dom' // 水印

export default function initWatermark(txt: string) {
  if (!txt) {
    return
  }

  try {
    const el = document.createElement('div') as HTMLElement
    const waterID = 'js-watermark'

    el.style.cssText = 'position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 100000;pointer-events: none;'
    el.id = waterID
    document.body.appendChild(el)

    watermark.init({
      watermark_txt: txt,
      watermark_fontsize: '14px',
      watermark_color: '#BCBCBC',
      watermark_width: 200,
      watermark_height: 60,
      watermark_angle: 30,
      watermark_parent_node: waterID,
      monitor: false,
    })
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error(e)
  }
}
