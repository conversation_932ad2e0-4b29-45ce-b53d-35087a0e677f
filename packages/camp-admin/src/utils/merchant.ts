import { NGetApiHawkUser } from '@xhs/logan-services-barley-iron'
import dayjs from 'dayjs'
import opener from '@xhs/ozone-opener'
import { toaster } from '@xhs/yam-beer'

export function getDefaultTimeRange() {
  return {
    startTime: dayjs().subtract(8, 'days').startOf('days'),
    endTime: dayjs().subtract(1, 'days').endOf('days'),
  }
}

export function disableDay(day: any, start: any) {
  return (
    day > dayjs().subtract(1, 'd') || (start && day > dayjs(start).add(61, 'd'))
  )
}

export function getAllOptions() {
  return [
    {
      name: '全部',
      value: '',
    },
  ]
}

function getUrl(ossImage: string = '', prefix = '', suffix = '') {
  if (ossImage) {
    if (/^https?/.test(ossImage)) {
      return ossImage
    }
    return `${prefix}${ossImage}?${suffix}`
  }
  return ''
}

export function getAvatarUrlByOssImage(ossImage: string = '') {
  return getUrl(
    ossImage,
    '//sns-avatar-qc.xhscdn.com/avatar/',
    'imageView2/2/w/540/format/jpg/q/75',
  )
}

export function getNoteCoverUrlByOssImage(ossImage: string = '') {
  return getUrl(
    ossImage,
    '//sns-img-qn.xhscdn.com/',
    'imageView2/2/w/540/format/jpg/q/75',
  )
}

export function showAmName(
  bindAmUser: NGetApiHawkUser.IBindAmUser,
  defaultShow: string = '',
): string {
  if (!bindAmUser) {
    return defaultShow
  }
  if (bindAmUser.redName && bindAmUser.name) {
    return `${bindAmUser.redName}（${bindAmUser.name}）`
  }
  return bindAmUser.redName || bindAmUser.name || defaultShow
}

export function goToNoteDetail(noteId: string = '', type: string = '') {
  if (!noteId) {
    toaster.danger('笔记ID不存在')
    return
  }
  if (type === '1') {
    // 图文
    opener.toNoteByType(noteId, '')
  } else if (type === '2') {
    // 视频
    opener.toNoteByType(noteId, 'video')
  } else {
    toaster.danger('Invalid noteType')
  }
}
