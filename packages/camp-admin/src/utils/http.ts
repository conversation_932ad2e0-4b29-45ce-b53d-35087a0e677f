import { http } from '@xhs/launcher'
import { HttpRequestConfig } from '@xhs/http'
import { toaster } from '@xhs/yam-beer'

import loading from '@/utils/loading'
import { baseURL, edithBaseURL } from '@/config/http.config'

// api/hera走当前域名、其他走edith接口
const getBaseURL = (url:string) => {
  if (/^(http)|(\/\/)/.test(url)) return ''
  return url.startsWith('/api/hera') ? baseURL : edithBaseURL
}

export const get = async <T>(url: string, config?: HttpRequestConfig, { shouldLoading = false, shouldAlert = true } = {}): Promise<T> => {
  if (shouldLoading) {
    loading.show()
  }
  try {
    const headers = { ...config?.headers }
    const res = await http.get<T>(url, {
      ...config,
      headers,
      baseURL: config?.baseURL || getBaseURL(url),
    })
    if (shouldLoading) {
      loading.close()
    }
    return res
  } catch (err: any) {
    // eslint-disable-next-line no-console
    console.error('err', err)
    if (shouldLoading) {
      loading.close()
    }

    if (shouldAlert && err?.message !== 'canceled') {
      // @ts-ignore
      toaster.danger(err.message)
    }
    return Promise.reject(err)
  }
}

export const del = async <T>(url: string, config?: HttpRequestConfig, { shouldLoading = false, shouldAlert = true } = {}): Promise<T> => {
  if (shouldLoading) {
    loading.show()
  }
  try {
    const headers = { ...config?.headers }
    const res = await http.del<T>(url, {
      ...config,
      headers,
      baseURL: config?.baseURL || getBaseURL(url),
    })
    if (shouldLoading) {
      loading.close()
    }
    return res
  } catch (err:any) {
    // eslint-disable-next-line no-console
    console.error('err', err)
    if (shouldLoading) {
      loading.close()
    }
    if (shouldAlert && err?.message !== 'canceled') {
      // @ts-ignore
      toaster.danger(err.message)
    }
    return Promise.reject(err)
  }
}

export const post = async <T>(url: string, data: any, config?: HttpRequestConfig, { shouldLoading = false, shouldAlert = true } = {}): Promise<T> => {
  if (shouldLoading) {
    loading.show()
  }
  try {
    const headers = { ...config?.headers }
    const res = await http.post<T>(url, data, {
      ...config,
      headers,
      baseURL: config?.baseURL || getBaseURL(url),
    })
    if (shouldLoading) {
      loading.close()
    }
    return res
  } catch (err:any) {
    // eslint-disable-next-line no-console
    console.error('err', err)
    if (shouldLoading) {
      loading.close()
    }
    if (shouldAlert && err?.message !== 'canceled') {
      // @ts-ignore
      toaster.danger(err.message)
    }
    return Promise.reject(err)
  }
}

export default {
  get,
  del,
  post,
}
