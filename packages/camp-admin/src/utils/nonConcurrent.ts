interface Action {
  (...args:any[]): Promise<any>
}
// 并发处理
const nonConcurrent = <T extends Action>(action: T) => {
  let cache: ReturnType<T> | undefined
  const nonConcurrented = function settle(...args: any[]):ReturnType<T> {
    if (cache) return cache
    cache = action(...args) as ReturnType<T>
    cache.then(res => {
      cache = undefined
      return res
    })
    cache.catch(err => {
      cache = undefined
      throw err
    })
    return cache
  }
  return nonConcurrented
}

export default nonConcurrent
