import Big from 'big.js'

// 金额分转换为元
export const fenToYuan = (price:number | string = 0) => +(+price / 100).toFixed(2)

// 金额元转换为分
export const yuanToFen = (price:number | string) => +(+price * 100).toFixed(0)

/** 价格单位从“元” 转换成 “分” */
export function priceToCentByYuan(val: number | string) {
  return new Big(val).times(100).toNumber()
}
/** 价格单位从“分” 转换成 “元” */
export function priceToYuanByCent(val: number | string) {
  return new Big(val).div(100).toNumber()
}
