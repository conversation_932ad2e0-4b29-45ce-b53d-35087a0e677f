export function getFiles(accept:string = '.xlsx', maxCount = 1): Promise<FileList> {
  const multiple = maxCount > 1
  const id = 'camp-admin-upload-input'
  let _input = document.getElementById(id) as HTMLInputElement
  if (!_input) {
    _input = document.createElement('input')
    _input.id = id
    _input.type = 'file'
    _input.style.display = 'none'
    document.body.appendChild(_input)
  }
  _input.value = ''
  _input.accept = accept
  _input.multiple = multiple

  _input.click()

  return new Promise((resolve, reject) => {
    _input.onchange = () => {
      const files = (_input as HTMLInputElement).files
      if (!files || !files.length) {
        reject('未选择文件')
      } else {
        resolve(files)
      }
    }
  })
}
