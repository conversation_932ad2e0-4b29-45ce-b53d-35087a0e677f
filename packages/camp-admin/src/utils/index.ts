import { withLoading } from '@xhs/yam-beer'
import parseUrl from 'url-parse'
import { env } from '../config/http.config'

// 判断类型
const isType = (type: string) => (o: unknown): boolean => Object.prototype.toString.call(o) === `[object ${type}]`
export const isNumber = isType('Number')
export const isFunction = isType('Function')
export const isObject = isType('Object')
export const isArray = isType('Array')

export const isNullObject = (obj: any) => isObject(obj) && Object.keys(obj).length === 0
// 全屏loading
export const globalLoading = withLoading({ showAtInit: false }) as {
  close(clearAll?: boolean): void
  clearAll(): void
  toggle(): void
  show(): void
}

export const replaceStr = (str: string = '', matchChar: RegExp = /，|,|(\s+)|(\n+)/g, replaceChar: string = ','): string => str.replace(matchChar, replaceChar)

export const formatData = {
  identifier: (num: any, invalidShow: string = '') => {
    if (num === undefined || num === null || Number.isNaN(Number(num))) {
      return invalidShow
    }
    const [str, others] = num.toString().split('.')
    return (
      str.replace(/(?=(\B)(\d{3})+$)/g, ',')
      + (others?.length ? `.${others[0]}` : '')
    )
  },
  percentage: (num: any, fixedLength: number = 2, invalidShow: string = '') => {
    if (num === undefined || num === null || Number.isNaN(Number(num))) {
      return invalidShow
    }
    return `${(num * 100).toFixed(fixedLength)}%`
  },
  toFixedCount: (num: number, count: number = 2) => num.toFixed(count),
}

export function tooltipFormatterPercentage(item: any) {
  return `<span style="display: inline-block;margin-right: 5px;border-radius: 10px;width: 10px;height: 10px;background-color: ${
    item.color
  };"></span>${
    item.name
  } <span style="font-weight: 600;">${formatData.percentage(item.value)}</span>`
}

/**
 * 判断数字是否有值
 * @param num 被判断数字
 * @param includeZero 0是否被认定为空，默认不包括
 * @returns true - 为空，false - 不为空
 */
export function checkNumIsEmpty(num: any, includeZero: boolean = false) {
  if (
    num === null
    || num === undefined
    || Number.isNaN(Number(num))
    || num === ''
  ) {
    return true
  }
  if (includeZero) {
    return Number(num) === 0
  }
  return false
}

/**
 * 使用 @xhs/launcher-plugin-logan 插件来设置 development 环境的 BASE_URL
 *
 * 文档地址: https://code.devops.xiaohongshu.com/formula/launcher-plugins/launcher-plugin-logan
 *
 * 使用方法：
 *
 * 1. 启动项目时，定义 process.evn.LOGAN_PROXY_KEY, 参考 app-goods/packages.json dev 命令
 * 或者
 * 2. 在浏览器地址，增加 ?proxy_key=[YOUR CUSTOM LOGAN KEY]
 *
 * 只在 非 production 环境下起作用
 */
export function getLoganUrlByQuery(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _defaultProxyKey = '',
  proxyKey = 'proxy_key',
): string {
  const hasWindow = typeof window !== 'undefined'
  if (typeof process === 'undefined') {
    return ''
  }
  if (hasWindow) {
    const url = parseUrl(window.location.href, true)
    // useProxy = !!(url.query[proxyKey] || defaultProxyKey) // 如果search中有proxy_key或者传了proxy_key，则启用
    // if (useProxy) {
    // let loganProxyKey = defaultProxyKey
    // @ts-ignore
    // const { LOGAN_PROXY_KEY } = process.env
    // if (LOGAN_PROXY_KEY && !loganProxyKey) {
    //   loganProxyKey = LOGAN_PROXY_KEY
    // }
    if (env !== 'production') {
      const { LOGAN_PROXY_KEY, LOGAN } = process.env
      let loganProxyKey = LOGAN || LOGAN_PROXY_KEY
      loganProxyKey = url.query[proxyKey] || loganProxyKey
      if (loganProxyKey) {
        return `//logan.devops.xiaohongshu.com/proxy/${loganProxyKey}`
      }
    }
    return ''
  }
  return ''
}
