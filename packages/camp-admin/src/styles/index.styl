a
  text-decoration none
.primary-color
  color #3a64ff
.pointer
  cursor pointer
.btn-text
  color #3a64ff
  cursor pointer
  padding 0.5em
.flex
  display flex
.middle-flex
  display flex
  align-items center
.between-flex
  display flex
  justify-content space-between
.center-flex
  display flex
  align-items center
  justify-content center
.r-part-title
  line-height 1.5
  font-size 16px
  padding-bottom 0.2em
.r-ellipsis
  width 100%
  white-space nowrap
  text-overflow ellipsis
  overflow hidden
.r-ellipsis-2
  overflow hidden
  text-overflow ellipsis
  display -webkit-box
  -webkit-box-orient vertical
  -webkit-line-clamp 2
  word-break break-all
.r-h2
  font-weight 500
  font-size 16px
  line-height 16px
  padding 10px 0
  color #2D2D2D
.r-plain-text
  font-size 14px
  line-height 1.5px
  color rgba(0, 0, 0, 0.45)
.r-pr
  padding-right 1em
.r-actions
  display flex
  align-items center
  white-space nowrap;
  span, a
    cursor pointer
    padding 0.5em
    text-decoration none
    &:active
      opacity 0.8
  span:not(:last-child)
    padding-right 1em
  .primary
    color #3a64ff
.table-head-row th, .table-body-row td
  border-left none
  border-right none
.plain-text
  font-size 12px
  color #999

/* 宽度和高度可根据需要进行调整 */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* 滑轨 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* 滑块 */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

/* 滑轨在滑块顶部和底部的边距 */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 当滚动条被点击时 */
::-webkit-scrollbar-thumb:active {
  background: #333;
}

.d-select-wrapper .d-select .d-select-main .d-select-content .d-select-tags
  flex-wrap: wrap