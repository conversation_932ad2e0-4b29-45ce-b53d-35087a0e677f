export interface GoodsImage {
  path: string
  extension: string
  width: number
  height: number
  size: number
  type: 0 | 1
}
// 商品基础信息
export interface IGoodsBaseInfo {
  id?: string // 商品ID
  name: string // 商品标题
  shortName: string // 商品短标题
  categoryId: string // 类目ID
  categoryName?: string // 类目名称
  state?: 1 | 0 // 上下架
  images: GoodsImage[]
  productType?: string // 商品类型
  description: string // 商品描述
}

// 商品图文信息
export interface IGoodsImgAndDescDetail {
  description: string // 商品描述
  images: GoodsImage[]
}

export interface ISku {
  productId: string // sku ID
  rowNum: number
  price: number
  stock: number
  status: number
  productType: string
  stockChangeValue: number
  variants: {
    id: string
    name: string
    value: string
  }[]
}
export interface IGoodsInfo {
  baseInfo: IGoodsBaseInfo
  imgAndDescDetail: IGoodsImgAndDescDetail
  suitId?: string
  fixedDays?: number
  variantData?: {
    id: string
    name: string
    selected: boolean
    values: {
      name: string
      selected: boolean
    }[]
  }[]
  auditStatus?: 1 | 2 | 3 // 审核状态 1 - 审核中 2 - 审核通过 3 - 审核不通过
  auditResultList?: {
    comment: string // 审核不通过理由
    content: string // 内容
    dataField: string // 字段
  }[]

  attributes: { // 商品属性
    capacity?: string
    [key: string]: any
  }
  priceAndStock: {
    skus?: ISku[]
    deleteSkus?: ISku[]
    updateSkus?: ISku[]
  }
  type: 'CAMP' | "SKI"
}
