import { ImageItem } from "."

export type PoiType = 'CAMP' | 'SKI'

export type ImageSource = 'upload' | 'suite'
export interface PoiForm<T = string> {
  type: PoiType | ''
  poiId: string
  poiName: string
  province: string
  city: string
  district: string
  address: string
  images: T[]
  imageSource?: ImageSource
  extend1: string[]
  extend2: string[]
  extend3: string[]
}

export interface PoiSuite {
  id: number
  poiId: string
  poiName: string
  address?: string
  city?: string
  district?: string
  province?: string
  poiType?: string
  productCount: number
  buyableProductCount: number
  relatedTime: string
  sellerId: string
  sellerName: string
  status: 'PASS' | 'PROCESSING' | 'REJECT'
  suiteAuditStatus: 'INIT' | 'PROCESSING' | 'SUCCEED' | 'FAILED'
  suiteId: string
  suiteName: string
  suiteStatus: "ACTIVE" | 'DISABLE'
}

export type POI = PoiForm & {
  isUpgrade: "Y" | "N"
  isShow: "Y" | "N"
  noteNum: number
  extend1: string[]
  extend2: string[]
  extend3: string[]
  passList: PoiSuite[]
  processingList: PoiSuite[]
  isTerminalShow: boolean
}
export interface PoiListParams {
  pageNum: string | number
  pageSize: string | number
  type?: PoiType | ''
  poiName?: string
  poiId?: string
  isUpgrade?: string
}

export interface TodoPoiListParams extends PoiListParams {
  suiteId?: string
  status?: 'PASS' | 'PROCESSING' | 'REJECT'
}

export interface TodoPoi {
  id: number
  poiId: string
  poiName: string
  poiType: string
  sellerName: string
  status: string
  suiteId: number
  suiteName: string
}

export interface PoiGoods {
  id: number
  poiId: string
  productId: string
  suiteId: string
  status: string
  productName: string
  poiName: string
  suiteName: string
  sellerName: string
  sellerId: string
  isShow: boolean
  isTerminalShow: boolean
  isProductShow: boolean
  images: {
    extension: string
    height: number
    imageType: number
    path: string
    size: number
    width: number
  }[]
}


export interface SuiteImageInfo {
  sellerName: string
  suiteId: string
  suiteName: string
  images: ImageItem[]
}
