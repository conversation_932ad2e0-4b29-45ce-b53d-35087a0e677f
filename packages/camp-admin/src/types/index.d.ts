export interface FormFieldItem {
  label: string
  name: string
  value: any
  is?: string | any
  required?: boolean
  width?: string
  validate?: (value?: any) => Promise<any>
  props?: any
  suffix?: any
  [key:string]: any
}


export type FormFields = FormFieldItem[]

export interface ImageItem {
  id?: string
  link: string
  path: string
  width?: number
  height?: number
  base64?: string
  file?: Blob | File
  error?: string
}

export type Images = ImageItem[]
