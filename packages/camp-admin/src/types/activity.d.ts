import { IGoodsInfo } from "./goods";
import { PoiType } from "./poi";

export interface Activity {
  id: number | string,
  activityId: number | string,
  images: { url: string, width: number, height: number }[],
  name: string
  pois: { poiId: string; poiName: string }[]
  organizerId: string
  organizerName: string
  items: { itemId: string; itemName: string; productInfo?: IGoodsInfo}[]
  status: number
  offlineReason: string
  auditStatus: number
  type: PoiType
}

export interface ActivityListParams {
  pageNum: number
  pageSize: number
  poiId?: string
  sellerId?: string
  activityStatus?: string
}

export interface ActivityCreatePayload<T = {poiId: string}> {
  id?: string
  activityId?: string
  images: { url?: string; link?: string; width: number; height: number, [key:string]: any }[]
  name: string
  pois: T[]
  organizerId: string
  organizerName: string
  items: { itemId: string }[]
}

export interface GoodsParams {
  pageNum: number
  pageSize: number
  sellerId?: string
}

export interface Goods {
  [key: string]: any
}
