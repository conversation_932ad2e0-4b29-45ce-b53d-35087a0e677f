export interface OrderListParams {
  pageNum: number
  pageSize: number
  sellerId?: string // 店铺ID
  sellerUserId?: string // 卖家账号ID
  buyerUserId?: string // 买家账号ID
  id?: string // 订单Id
}

export interface OrderItem {
  checkInTime: string
  createTime: number
  id: string
  skuId: string
  skuName: string
  sellerId: string
  sellerName: string
  sellerUserId: string
  status: number
  totalPrice: string
  userId: string
  userMobile: string
  userName: string
}

export interface OrderSku {
  attributeDTO: {
    advantageLabel: string
    bedSize: number
    cancellationPolicy: string
    skiingCancellationPolicy: string
    cancellationPolicyType: 0 | 1 | 2
    capacity: number
    fixedDays: number
    fullRefundDays: number
    suiteId: string
    tentArea: number
    tentType: number
    ladderCancelRules?: {day:number;percent:number}[]
  }
  checkInTime: string
  checkOutTime: string
  description: string
  image: string
  skuId: string
  skuName: string
  price: number
  quantity: number
  suiteId: number
  suiteName: string
  suiteType: string
  tag: string
  tentType: string
  variantData: {name:string;cname:string;value:string}[]
  sellerTel: string
}

export interface OrderSnapshot {
  id: string
  orderPrice: number
  skuList: OrderSku[]
  orderRefundInfo: {
    totalRefund: number
  }
  orderTimeInfo: {
    autoCancelTime: number
    createTime: number
    expectedFinishTime: number
    expireTime: number
    finishTime: number
    firstPackTime: number
    firstShipTime: number
    packTime: number
    payTime: number
    checkInTime: string
    checkOutTime: string
    realCheckInTime: number
    realCheckOutTime: number
    receivedTime: number
    shipTime: number
    time: number
    updateTime: number
  }
  packageId: string
  status: number
  userId: string
  userInfo: {
    userMobile: string
    userName: string
    _checkInPersonNumber: number
  }
}
