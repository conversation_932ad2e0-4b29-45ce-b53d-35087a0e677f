export interface IMenu {
  name: string

  title: string

  icon?: string

  children?: IMenu[]
}

export interface IPagination {
  pageNo: number
  pageSize: number
}


export interface IronPagination {
  pageNo: number
  pageSize: number
}
export interface IronSort {
  sortKey: string
  sortRule: string
}

export interface IResponse {
  errorCode: number
  result: number
  success: boolean
}

export interface IResponseList<T> extends IResponse {
  data: {
    total: number
    list: T[]
  }
}

export interface IResponseData<T> extends IResponse {
  data: T
}

export interface IOption<N, V> {
  name: N
  value: V
}
