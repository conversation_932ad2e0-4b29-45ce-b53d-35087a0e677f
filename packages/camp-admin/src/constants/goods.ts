// 价格类型
export enum EPriceType {
  SAME = 1, // 每日同价
  DIFF, // 每日不同价
}

// 帐篷类型
export enum ETentType {
  FURNISHED = 1, // 拎包入住
  BYOT = 2, // 其他
}
export const TENT_TYPE_VALUE = {
  [ETentType.FURNISHED]: '拎包入住',
  [ETentType.BYOT]: '其他',
}

// 库存类型
export enum EStockHavingType {
  BASE = 1, // 基准库存
  CALENDAR = 2, // 日历库存
}

// 商品审核状态
export enum EProductAuditStatus {
  // 审核中：机审等待，机器不确定待人审
  AUDITING = 1,
  // 审核通过：人审通过，机审通过
  PASS = 2,
  // 审核不通过：人审拒绝，机审拒绝
  FAIL = 3,
}
export const PRODUCT_AUDIT_STATUS = {
  [EProductAuditStatus.AUDITING]: '审核中',
  [EProductAuditStatus.PASS]: '审核通过',
  [EProductAuditStatus.FAIL]: '审核不通过',
}

export enum EChangeStockType {
  CUSTOM = 1,
  CLOSE = 2,
}

// -------------------营地-------------------

export enum EAuditStatus {
  INIT = 1, // 未审核
  PROCESSING = 2, // 审核中
  SUCCEED = 3, // 审核通过
  FAILED = 4, // 审核失败
}
export const AuditStatusValue = {
  [EAuditStatus.INIT]: '待审核',
  [EAuditStatus.PROCESSING]: '审核中',
  [EAuditStatus.SUCCEED]: '审核通过',
  [EAuditStatus.FAILED]: '审核失败',
}

export interface IAuditResult {
  comment: string // 审核不通过理由
  content: string // 内容
  dataField: string // 字段
}
