<template>
  <Spinner :spinning="loading">
    <div class="upload-card">
      <!-- @ts-ignore -->
      <Icon color="text-paragraph" size="default" :icon="Upload" />
      <span class="text">{{ text }}</span>
      <input
        ref="input"
        :multiple="maxCount > 1"
        :accept="accept"
        type="file"
        class="upload-input"
        @change="onUploadFile"
      />
    </div>
    <Progress v-show="uploading" :percent="percent || 0" />
  </Spinner>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { Icon, Spinner, Progress } from '@xhs/delight'
  import { Upload } from '@xhs/delight/icons'
  import { FileUploader } from '../../../node_modules/@xhs/uploader'
  import { findDuplicatePath, uniqueId, fillId } from '../upload-image/utils'
  import uploadProps from './props'
  import { DataList, DataItem } from '../upload-image/types'

  const props = defineProps(uploadProps)

  const emit = defineEmits(['update:modelValue'])

  const input = ref<HTMLInputElement>()
  const loading = ref(false)

  const fileList = ref<DataList>(fillId(props.modelValue))
  // eslint-disable-next-line no-return-assign
  watch(() => props.modelValue, (list: DataList) => {
    fileList.value = fillId(list)
  })

  const uploadJob = async ({ id, file }: { id: string; file: File | Blob }) => {
    const currentUpload: DataItem = {
      file, id, link: ''
    }

    const target = fileList.value.find(item => item.id === id)
    if (target && !target.error) {
      return currentUpload
    }

    const update = (item: DataItem) => {
      const itemIndex = fileList.value.findIndex(it => it.id === id)
      if (itemIndex === -1) {
        fileList.value.push(currentUpload)
      } else {
        fileList.value[itemIndex] = item
      }
    }

    update(currentUpload)

    const uploader = new FileUploader({
      bucket: props.bucket,
      bizKey: props.bizKey,
    })
    const onProgress = (event: { loaded: number; total: number }) => {
      let percent = Math.round((event.loaded / event.total) * 100)
      if (percent > 99) {
        percent = 99
      }
      const itemIndex = fileList.value.findIndex(item => item.id === id)
      if (itemIndex === -1) {
        return
      }
      fileList.value[itemIndex].uploadPercent = percent
    }

    const res = await uploader.post({ file, onProgress }).catch((err: any) => ({
      error: `网络错误: ${err.message}`,
    }))
    const { cdnUrl: link, ...rest } = res
    const successItem = {
      ...currentUpload, uploadPercent: 100, ...rest, link
    }
    update(successItem)
    return successItem
  }

  // 上传完成, 同步props
  const onComplete = () => {
    // 提示重复错误
    findDuplicatePath(fileList.value)
    // 同步props
    emit('update:modelValue', fileList.value)
  }
  // 接收文件
  const onUploadFile = async () => {
    // 保证最大上传数量
    loading.value = true

    try {
      const ipt = input.value
      if (ipt && input.value?.files) {
        // 每次都是重新上传
        fileList.value = []

        const files = [...input.value.files]
          .slice(0, props.maxCount - fileList.value.length)
          .map(file => ({ id: uniqueId(), file }))
        for (const item of files) {
          // eslint-disable-next-line no-await-in-loop
          await uploadJob(item)
        }
        input.value.value = ''
        onComplete()
      }
    } finally {
      loading.value = false
    }
  }

</script>

<style scoped lang="stylus">
.upload-card
  display flex
  align-items center
  padding: 0 16px
  min-height: 32px
  background-color var(--color-fill)
  cursor: pointer;
  border-radius: var(--size-radius-default);
  transition: 0.1s
  position relative
  gap 5px
  &:hover
    background-color: var(--color-fill-hover)
.upload-input
  position absolute
  left 0
  top 0
  width 100%
  height 100%
  opacity 0
  cursor pointer
.text
  font-weight: var(--size-text-font-weight-bold)
  font-size: var(--size-text-default)
  color var(--color-text-paragraph)
  white-space: nowrap
</style>
