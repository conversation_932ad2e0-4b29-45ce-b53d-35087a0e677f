import { PropType } from 'vue'

import { DataList } from '../upload-image/types'

export default {
  modelValue: {
    type: Array as PropType<DataList>,
    default: () => []
  },
  draggable: {
    type: Boolean,
    default: true
  },
  aspectRatio: {
    type: [Array, Number] as PropType<number[] | number>,
    required: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  bucket: {
    type: String,
    default: 'picasso',
  },
  bizKey: {
    type: String,
    default: 'hera',
  },
  accept: {
    type: String,
    default: '/*',
  },
  maxCount: {
    type: Number,
    default: 1
  },
  maxSize: {
    type: Number,
    default: null
  },
  text: {
    type: String,
    default: '上传 Excel 文件'
  },
  tip: {
    type: String,
    default: ''
  },
  uploading: {
    type: Boolean,
    default: false
  },
  percent: {
    type: Number,
    default: 0
  }
}
