<template>
  <SingleSelect
    placeholder="输入关键词模糊搜索"
    :model-value="props.modelValue"
    :get-list="search"
    :fixed="true"
    :filterable="true"
    @update:model-value="change"
  />
</template>

<script lang="tsx" setup>
  import { SingleSelect } from '@xhs/yam-beer'

  import * as Api from '../../services/merchant'

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    }
  })
  const emit = defineEmits(['update:modelValue'])

  const search = async (value: string) => {
    const data = await Api.list(value)
    const id = props.modelValue
    if (id) {
      const targetItem = (await Api.list('', id))[0]
      if (targetItem && data.some(item => item.value !== targetItem.value)) {
        data.unshift(targetItem)
      }
    }
    return {
      data
    }
  }
  const change = (value: string) => {
    emit('update:modelValue', value)
  }
</script>
