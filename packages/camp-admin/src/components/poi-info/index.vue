<template>
  <div class="poi-info">
    <BaseImage :src="poi.images?.[0]" />
    <div class="info">
      <div class="poi-name">
        <span>{{ poi.poiName || '-' }}</span>
        <Tag v-if="poi.isNew" class="tag" variant="color" size="s" modifier="success">NEW</Tag>
      </div>
      <div class="plain-text">poi_id: {{ poi.poiId || '-' }}</div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import { Tag } from '@xhs/yam-beer'

  import BaseImage from '@/components/base-image/index.vue'

  defineProps({
    poi: {
      type: Object as PropType<{
        poiId: string
        poiName: string
        images?: string[]
        isNew?: boolean
      }>,
      required: true
    }
  })

</script>

<style lang="stylus" scoped>
.poi-info
  display flex
  align-items center
.info
  padding 10px
.poi-name
  display flex
  align-items center
  padding-right 0.5em
.tag
  transform scale(0.8)
</style>
