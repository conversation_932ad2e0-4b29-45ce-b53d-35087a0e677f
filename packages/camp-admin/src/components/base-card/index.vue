<template>
  <div class="card">
    <div v-if="title" class="title">{{ title }}</div>
    <slot />
  </div>
</template>

<script lang="tsx" setup>
  defineProps({
    title: {
      type: String,
      default: ''
    }
  })
</script>

<style lang="stylus" scoped>
.card
  min-height 380px
  background-color #fff
  padding 24px
  border-radius 3px
.title
  font-weight 500
  font-size 16px
  line-height 16px
  padding-bottom 20px
</style>
