<template>
  <BeerButton :disabled="!images?.length" @click="showViewModal">预览</BeerButton>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'
  import { withModal, BeerButton } from '@xhs/yam-beer'
  import ViewGoodsImageText from './ImageText.vue'

  const props = defineProps({
    images: {
      type: Array as PropType<{ path?: string; url?: string }[]>,
    },
  })

  const showViewModal = () => {
    const showModal = withModal({
      title: '图文详情',
      handleConfirm: ({ close }: { close: any }) => {
        close?.()
      },
    // @ts-ignore
    }, () => ({
      // @ts-ignore
      default: () => <ViewGoodsImageText images={props.images} />,
    }))
    showModal()
  }

</script>

<style>
</style>
