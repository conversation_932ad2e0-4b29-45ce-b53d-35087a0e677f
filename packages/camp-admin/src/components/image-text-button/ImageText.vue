<template>
  <div :bs="{ pl: 24 }">
    <img
      v-for="(item, index) in images"
      :key="index"
      :src="item.url || (item.path ? '//qimg.xiaohongshu.com/' + item.path : '')"
      style="width: 100%;display: block;"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'

  defineProps({
    images: {
      type: Array as PropType<{ path?: string; url?: string }[]>,
    },
  })
</script>
