<template>
  <form @submit.prevent="submit">
    <div style="margin-right: -25px;" :class="{ 'inline-container': layout === 'inline' }">
      <FormGroup
        v-for="(item) of fields"
        :key="item.name"
        class="group-item"
        :layout="layout"
        :label="item.label"
        :error-text="errorMap[item.name]"
        :style="item.width ? { width: item.width } : {}"
        :required="item.required || false"
      >
        <component
          v-bind="item.props"
          :is="item.is || BeerInput"
          v-model="item.value"
          @update:modelValue="change(item)"
        />

        <component :is="item.suffix" v-if="item.suffix" style="padding-left: 20px;" />
      </FormGroup>

      <div v-if="actionVisible" class="actions">
        <div v-if="okText" @click="submit">
          <slot name="confirm">
            <BeerButton :disabled="okDisabled">{{ okText }}</BeerButton>
          </slot>
        </div>

        <div v-if="cancelText" @click.prevent="cancel">
          <slot name="cancel">
            <BeerButton variant="secondary">{{ cancelText }}</BeerButton>
          </slot>
        </div>
      </div>
    </div>
  </form>
</template>

<script lang="tsx" setup>
  import {
    defineExpose, PropType, ref,
  } from 'vue'
  import { FormGroup, BeerInput, BeerButton } from '@xhs/yam-beer'

  import { FormFieldItem as FiledItem } from '@/types'

  const errorMap = ref<Record<string, string>>({})

  const props = defineProps({
    fields: {
      type: Array as PropType<FiledItem[]>,
      default: () => [],
    },
    payload: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    okText: {
      type: String,
      default: '提交',
    },
    cancelText: {
      type: String,
      default: '',
    },
    okDisabled: {
      type: Boolean,
      default: false,
    },
    inline: {
      type: Boolean,
      default: false,
    },
    layout: {
      type: String as PropType<'inline' | 'block' | 'table'>,
      default: 'table',
    },
    actionVisible: {
      type: Boolean,
      default: true,
    }
  })

  const emit = defineEmits(['submit', 'cancel'])

  const isSubmitting = ref<boolean>(false)

  // 验证单个表单
  const validateItem = (field: FiledItem) => {
    if (!field.value && field.required) {
      errorMap.value[field.name] = `${field.label}必须填写`
      return Promise.reject(field.error)
    }
    return field.validate?.(field.value).then(res => {
      errorMap.value[field.name] = res && typeof res === 'string' ? res : ''
      return res
    }).catch(error => {
      errorMap.value[field.name] = error.message || error
      return Promise.reject(error)
    })
  }

  const change = (item: FiledItem) => {
    // 同步 props payload 引用
    const { payload } = props
    payload[item.name] = item.value
    // 验证
    validateItem(item)
  }

  // 表单验证
  const validate = () => Promise.all(props.fields.map(validateItem))

  // 提交表单
  const delay = (t = 500) => new Promise(resolve => setTimeout(resolve, t))
  const submit = async () => {
    if (isSubmitting.value) return
    isSubmitting.value = true
    try {
      await validate()
      const fromValue = props.fields.reduce((acc: Record<string, any>, item) => {
        acc[item.name] = item.value
        return acc
      }, {})
      emit('submit', { ...props.payload, ...fromValue })
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error)
    }
    await delay()
    isSubmitting.value = false
  }

  // 取消表单提交
  const cancel = () => {
    emit('cancel')
  }

  // 暴露变量
  defineExpose({
    validate,
  })

</script>

<style lang="stylus" scoped>
.flex
  display flex
  flex-wrap wrap
.actions
  display flex
  margin-bottom 20px
  padding-left 89px
.inline-container
  .actions
    padding-left 0
    display inline-flex
</style>
