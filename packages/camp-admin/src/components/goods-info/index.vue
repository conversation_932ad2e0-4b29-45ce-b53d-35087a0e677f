<template>
  <div class="goods-info">
    <BaseImage :src="fullPath(data.images[0])" />
    <div class="info">
      <div class="goods-name">
        <span>{{ data.productName || '-' }}</span>
      </div>
      <div class="plain-text">商品Id: {{ data.productId || '-' }}</div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { PropType } from 'vue'

  import { PoiGoods } from '@/types/poi'

  import { fullPath } from '@/utils/image'

  import BaseImage from '@/components/base-image/index.vue'

  defineProps({
    data: {
      type: Object as PropType<PoiGoods>,
      required: true
    }
  })

</script>

<style lang="stylus" scoped>
.goods-info
  display flex
  align-items center
.info
  padding 10px
.goods-name
  display flex
  align-items center
  padding-right 0.5em
.tag
  transform scale(0.8)
</style>
