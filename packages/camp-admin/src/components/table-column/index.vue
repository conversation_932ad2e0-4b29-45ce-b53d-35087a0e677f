<template>
  <Text v-if="!clickable" :ellipsis="ellipsis" :tooltip="tooltip">
    {{ noEmpty ? rowData[dataIndex] : '-' }}
    <Text v-if="noEmpty">{{ unit }}</Text>
  </Text>
  <Text v-else :ellipsis="ellipsis" :tooltip="tooltip">
    <Link>{{ rowData[dataIndex] + unit || '-' }}</Link>
  </Text>
</template>

<script lang="tsx" setup>
  import { PropType, computed } from 'vue'
  import { Link, Text } from '@xhs/delight'

  const props = defineProps({
    rowData: {
      type: Object,
      default() {
        return {}
      }
    },
    dataIndex: {
      type: String as PropType<string>,
      default: ''
    },
    ellipsis: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    tooltip: {
      type: Boolean as PropType<boolean>,
      default: true
    },
    unit: {
      type: String as PropType<string>,
      default: ''
    },
    clickable: {
      type: Boolean as PropType<boolean>,
      default: false
    },
  })

  const noEmpty = computed(() => props.rowData[props.dataIndex] !== undefined && props.rowData[props.dataIndex] !== null)
</script>
