<template>
  <SingleSelect
    placeholder="输入关键词模糊搜索"
    :model-value="props.modelValue"
    :get-list="search"
    :fixed="true"
    :filterable="true"
    @update:model-value="change"
  />
</template>

<script lang="tsx" setup>
  import { SingleSelect } from '@xhs/yam-beer'

  import * as Api from '../../services/poi'

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const change = (value: string) => {
    emit('update:modelValue', value)
  }

  const search = async (value: string) => {
    const { list } = await Api.list({
      poiName: value,
      pageSize: 10,
      pageNum: 1,
      type: '',
      poiId: '',
      isUpgrade: ''
    })
    const id = props.modelValue
    if (id) {
      const targetItem = (await Api.list({
        poiName: value,
        pageSize: 10,
        pageNum: 1,
        type: '',
        poiId: id,
        isUpgrade: ''
      })).list[0]
      if (targetItem && list.some(item => item.poiId !== targetItem.poiId)) {
        list.unshift(targetItem)
      }
    }
    const data = list.map((item: { poiName: any; poiId: any }) => ({
      name: item.poiName,
      value: item.poiId,
    }))
    return {
      data
    }
  }

</script>

<style>
</style>
