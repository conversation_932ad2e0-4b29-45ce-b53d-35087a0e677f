<template>
  <BeerSingleSelect :options="dataOptions" :value="value" @update:model-value="change" />
</template>

<script lang="tsx" setup>
  import { defineProps, PropType, computed } from 'vue'
  import { BeerSingleSelect } from '@xhs/yam-beer'

  interface Option {
    name: string
    value: string
  }

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    options: {
      type: Array as PropType<Option[]>,
      default: () => [],
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const value = computed(() => {
    const target = props.options.find(opt => opt.value === props.modelValue)
    return target?.name
  })

  const dataOptions = computed(() => props.options.map(item => item.name))

  const change = (name: string) => {
    emit('update:modelValue', props.options.find(opt => opt.name === name)?.value)
  }
</script>
