<template>
  <Select
    v-model="value"
    :options="options"
    filterable
    :filter="filter"
    :loading="loading"
    remote
    dense
    placeholder="请输入用户Id搜索"
  >
    <template #option="{ option, markText, active }">
      <Tag :color="active ? 'blue' : 'grey'">
        <div style="display: flex; align-items: center; gap: 3px">
          <!-- 插槽option没有完整信息 -->
          <Avatar size="extra-small" :src="avatarMap[option.value]" />
          <!-- <Avatar size="extra-small" src="https://code.devops.xiaohongshu.com/uploads/-/system/user/avatar/66/avatar.png"/> -->
          <component :is="markText(option.label)" />
        </div>
      </Tag>
    </template>
    <template #prefix>
      <Avatar v-if="selectedOption?.imageS" size="extra-small" :src="selectedOption.imageS" />
      <Icon v-else :icon="Search" />
    </template>
    <template #empty>
      <div style="padding: var(--size-space-large) 0;">
        <Result title="请输入用户Id搜索" />
      </div>
    </template>
  </Select>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import {
    Select, Result, useDebounce, OptionContent, Avatar, Icon, Tag
  } from '@xhs/delight'
  import { Search } from '@xhs/delight/icons'

  import { searchAccount } from '../../services/accountRelation'

  const props = defineProps<{
    modelValue: string
  }>()

  const emit = defineEmits(['update:modelValue'])

  const value = computed({
    get: () => props.modelValue,
    set: e => {
      emit('update:modelValue', e)
    }
  })
  const options = ref<(OptionContent)[]>([])

  const selectedOption = computed(() => options.value.find(o => o.value === props.modelValue) || {})
  const avatarMap = computed(() => {
    const map:Record<string, string> = {}
    options.value.forEach(o => {
      map[o.value as string] = o.imageS
    })
    return map
  })

  const loading = ref(false)

  const filter = useDebounce(
    async filterValue => {
      if (filterValue) {
        loading.value = true
        const res = await searchAccount(filterValue)
        options.value = res?.map(d => ({
          ...d,
          value: d.userId,
          label: d.userNickname,
        })) || []
        loading.value = false
      }
    },
    { delay: 300 },
  )
</script>
