<template>
  <NewBsBox class="beco-page-500-wrapper">
    <BecoException
      type="500"
      :callback="callback"
    />
  </NewBsBox>
</template>

<script setup lang="ts">
  import { NewBsBox } from '@xhs/yam-beer'
  import { BecoException } from '@xhs/becoui'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const callback = () => {
    router.push({
      name: 'Index',
    })
  }
</script>

<style lang="stylus" scoped>
.beco-page-500-wrapper{
  padding 24px
}
</style>
