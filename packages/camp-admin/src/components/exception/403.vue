<template>
  <NewBsBox class="beco-page-403-wrapper">
    <BecoException
      type="403"
      :callback="callback"
    />
  </NewBsBox>
</template>

<script setup lang="ts">
  import { NewBsBox } from '@xhs/yam-beer'
  import { BecoException } from '@xhs/becoui'
  import { globalExceptionOptions } from '@xhs/becoui/src/config'

  // 通过全局的方式修改异常页面的展示信息
  // globalExceptionOptions[403].img = 'https://picasso-static.xiaohongshu.com/fe-platform/41b11103b793d4d474ebafc18b165b5f6cfe1a0f.png'
  globalExceptionOptions[403].btnText = '前往OA申请'

  const callback = () => {
    const param: any = { applyList: [{ applySysCode: 'porch#####hera', applyRoleCode: '3562', expireTime: '-1' }] }
    window.open(`https://oa.xiaohongshu.com/billbuildermobile/create/RBACAU?initValue=${encodeURIComponent(JSON.stringify(param))}`)
  }
</script>
<style lang="stylus" scoped>
.beco-page-403-wrapper{
  padding 24px
}
</style>
