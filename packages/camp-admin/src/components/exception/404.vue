<template>
  <NewBsBox class="beco-page-404-wrapper">
    <BecoException
      type="404"
      :callback="callback"
    />
  </NewBsBox>
</template>

<script setup lang="ts">
  import { NewBsBox } from '@xhs/yam-beer'
  import { BecoException } from '@xhs/becoui'
  import { globalExceptionOptions } from '@xhs/becoui/src/config'
  import { useRouter } from 'vue-router'

  globalExceptionOptions[404].btnText = '回到首页'
  const router = useRouter()
  const callback = () => {
    router.push({
      name: 'Index',
    })
  }
</script>

<style lang="stylus" scoped>
.beco-page-404-wrapper{
  padding 24px
}
</style>
