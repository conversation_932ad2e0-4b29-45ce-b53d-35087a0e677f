<template>
  <img v-if="src" v-lazy="src" class="image" :style="style" @click="preview" />
  <img v-else src="https://ci.xiaohongshu.com/e25d01dd-e150-4003-9b8c-81ab3fb69dc1@r_640w_640h.jpg" class="image-placeholder" :style="style" />
</template>

<script lang="tsx" setup>
  import { defineProps, computed } from 'vue'

  import { viewImgs } from '@xhs/yam-beer/cmp/overlayers/Viewer/Viewer'

  const props = defineProps({
    src: {
      type: String,
      default: '',
    },
    width: {
      type: Number,
      default: 54,
    },
    height: {
      type: Number,
      default: 54
    }
  })

  const style = computed(() => ({
    width: `${props.width}px`,
    height: `${props.height}px`
  }))

  const preview = () => {
    if (props.src) {
      viewImgs([props.src])
    }
  }

</script>

<style scoepd>
.image {
  border-radius: 3px;
  object-fit: cover;
  object-position: center center;
  cursor: pointer;
}
.image-placeholder {
  background-color: #eee;
  border-radius: 3px;
}
</style>
