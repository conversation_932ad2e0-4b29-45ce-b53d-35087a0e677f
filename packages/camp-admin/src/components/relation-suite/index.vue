<template>
  <BeerModal
    :show="visible"
    :bs="{ width: 1000 }"
    title="添加关联关系"
    confirm-text="添加"
    :width="500"
    :footer="null"
    :confirm-props="{disabled: !selected.suiteId}"
    @confirm="confirm"
    @close="close"
  >
    <template #footer>
      <div></div>
    </template>
    <div class="content" style="min-height: 60vh">
      <BaseForm ok-text="查询" layout="inline" :ok-disabled="!payload.sellerId" :payload="payload" :fields="fields" @submit="search" />

      <BeerTable border-style="full" :columns="columns" :data="data" />
    </div>
  </BeerModal>
</template>

<script lang="tsx" setup>
  import {
    ref, computed, watch, PropType
  } from 'vue'
  import { BeerModal, BeerTable } from '@xhs/yam-beer'

  import { FormFields } from '@/types/index'
  import { SuiteParams, SimpleSuite } from '@/types/suite'
  import * as Api from '@/services/suite'

  import BaseForm from '@/components/base-form/index.vue'
  import MerchentSingSelect from '@/components/merchent-single-select/index.vue'
  import SuiteSingSelect from '@/components/suite-single-select/index.vue'

  import type { Action } from './getRelationSuite'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    action: {
      type: Function as PropType<Action>,
    }
  })

  const emit = defineEmits(['ok', 'cancel'])

  const selected = ref<SimpleSuite>({
    auditStatus: '',
    sellerId: '',
    sellerName: '',
    status: '',
    suiteId: '',
    suiteName: '',
  })

  const columns = [
    {
      title: '营地信息',
      key: 'suite',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as SimpleSuite
          return (
          <div>
            <div>{ row.suiteName || '-' }</div>
            <div class="plain-text">suite_id: { row.suiteId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '商家信息',
      key: 'seller',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as SimpleSuite
          return (
          <div>
            <div>{ row.sellerName || '-' }</div>
            <div class="plain-text">seller_id: { row.sellerId || '-' }</div>
          </div>
        )
        }
      }
    },
    {
      title: '营地状态',
      key: 'status',
    },
    {
      title: '审核状态',
      key: 'auditStatus',
    },
    {
      title: '操作',
      key: 'seller',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData as SimpleSuite

          // const toggleSelected = () => {
          //   selected.value = row
          // }
          const isSelected = selected.value.suiteId === row.suiteId

          const choose = () => {
            props.action?.(row)
          }
          return (
          <div class="r-actions" style="min-width: 80px">
            <span onClick={choose} class={isSelected ? 'primary bold' : 'primary'}>{ selected.value.suiteId === row.suiteId ? '已选择' : '选择' }</span>
          </div>
        )
        }
      }
    }
  ]

  const data = ref<SimpleSuite[]>([])
  const payload = ref<SuiteParams>({
    sellerId: process.env.NODE_ENV === 'development' ? '614aef1e03590e0001f566b2' : ''
  })

  // 基础表单
  const fields = computed<FormFields>(() => [
    {
      label: '商家名称',
      name: 'sellerId',
      value: payload.value.sellerId,
      is: MerchentSingSelect,
    },
    {
      label: '营地名称',
      name: 'suiteId',
      value: payload.value.suiteId,
      is: SuiteSingSelect,
      props: {
        sellerId: payload.value.sellerId,
        key: payload.value.sellerId,
      }
    },
  ])

  watch(
    () => payload.value.sellerId,
    () => {
      payload.value.suiteId = ''
    }
  )

  const search = async () => {
    const res = await Api.simpleList({
      suiteIds: payload.value.suiteId || undefined,
      sellerId: payload.value.sellerId
    })
    data.value = res.suiteSimples
  }

  const confirm = () => {
    emit('ok', selected.value)
  }

  const close = () => {
    emit('cancel')
  }

</script>

<style scoped>
.content :deep(.bold) {
  font-weight: bold;
}
</style>
