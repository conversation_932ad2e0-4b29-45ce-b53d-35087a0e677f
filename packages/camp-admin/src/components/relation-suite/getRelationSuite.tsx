import { SimpleSuite } from '@/types/suite'
import {
  defineComponent, createVNode, render, onUnmounted, onMounted, ref
} from 'vue'

import RelationSuite from './index.vue'

export type Action = (s:SimpleSuite) => Promise<void>

function getPoi(action: Action) {
  return new Promise<SimpleSuite>((resolve, reject) => {
    const div = document.createElement('div')
    document.body.appendChild(div)

    const Wrapper = defineComponent({
      setup() {
        const visible = ref(false)

        const onCancel = () => {
          render(null, div)
          if (div.parentNode) {
            div.parentNode.removeChild(div)
          }
          reject('取消选择')
        }

        const onOk = (suite:SimpleSuite) => {
          resolve(suite)
          onCancel()
        }

        const keyupCancel = (e: KeyboardEvent) => {
          if (e.code === 'Escape') {
            onCancel()
          }
        }

        onMounted(() => {
          visible.value = true
          window.addEventListener('keyup', keyupCancel)
        })

        onUnmounted(() => {
          window.removeEventListener('keyup', keyupCancel)
        })

        const props = {
          visible: true,
          action,
          onOk,
          onCancel,
        }

        // @ts-ignore
        return () => <RelationSuite {...props} />
      }
    })

    const vm = createVNode(Wrapper)
    render(vm, div)
  })
}

export default getPoi
