<template>
  <div class="upload-bulk-result">
    <div class="result-title">上传成功，{{ text }}结果如下：</div>
    <div class="info-row">
      {{ text }}成功：{{ result.successNum }}
    </div>
    <slot name="info-row"></slot>
    <div class="info-row">
      {{ text }}失败：
      <span class="error-message">
        {{ result.failNum }}
      </span>
    </div>
    <slot name="result">
      <div
        v-if="result.failNum > 0 && result.failReasonPath"
        class="info-row"
      >
        下载结果：
        <Link
          class="error-link"
          target="_blank"
          :href="result.failReasonPath"
        >
          校验.xlsx
        </Link>
        <span class="comment-message">（查看{{ text }}失败原因）</span>
      </div>
    </slot>
  </div>
</template>

<script lang="ts" setup>
  import { Link } from '@xhs/delight'

  import { ClaimResult } from '@/services/poiClaim'

  withDefaults(
    defineProps<{
      result: ClaimResult
      text?: string
    }>(),
    {
      text: '认领'
    }
  )

</script>

<style lang="stylus" scoped>
.upload-bulk-result
  flex 1
  display flex
  flex-direction column
  align-items flex-start
  background #F7F7F7
  border-radius 4px

  color #666666
  font-size 14px
  line-height 24px
  padding 16px
  .result-title
    margin-bottom 16px
    font-weight bold

  .info-row
    margin-bottom 8px
    display flex
    align-items flex-end
    &:last-child
      margin-bottom 0
    .text-link
      color #2db8ff
    .error-link
      color #FF4949
      text-decoration underline
    .error-message
      color #F24444
    .comment-message
      color #999999
      font-size 12px
      line-height 20px
      margin-left 8px
</style>
