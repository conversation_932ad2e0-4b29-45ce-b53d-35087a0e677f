import { PropType } from 'vue'

import { DataList } from './types'

export default {
  modelValue: {
    type: Array as PropType<DataList>,
    default: () => []
  },
  draggable: {
    type: Boolean,
    default: true
  },
  aspectRatio: {
    type: [Array, Number] as PropType<number[] | number>,
    required: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  bucket: {
    type: String,
    default: 'qimg',
  },
  bizKey: {
    type: String,
    default: 'camp-admin',
  },
  accept: {
    type: String,
    default: 'image/*',
  },
  maxCount: {
    type: Number,
    default: 1
  },
  maxSize: {
    type: Number,
    default: null
  },
  text: {
    type: String,
    default: '上传图片'
  },
  tip: {
    type: String,
    default: ''
  }
}
