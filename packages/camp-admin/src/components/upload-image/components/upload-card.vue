<template>
  <div class="upload-card">
    <!-- @ts-ignore -->
    <BeerIcon :size="38" color="mute" icon="add-m" />
    <span class="text">{{ text }}</span>
    <input
      ref="input"
      :multiple="maxCount > 1"
      :accept="accept"
      type="file"
      class="upload-input"
      @change="onUploadFile"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { BeerIcon } from '@xhs/yam-beer'

  defineProps({
    maxCount: {
      type: Number,
      default: 1
    },
    accept: {
      type: String,
      default: 'image/*'
    },
    text: {
      type: String,
      default: '上传图片'
    }
  })

  const emit = defineEmits(['change'])

  const input = ref<HTMLInputElement>()

  const onUploadFile = () => {
    const ipt = input.value
    if (ipt) {
      const files = input.value?.files
      emit('change', files)
      ipt.value = ''
    }
  }
</script>

<style scoped lang="stylus">
.upload-card
  position relative
  width 104px
  height 104px
  margin-bottom: 12px
  display flex
  align-items center
  justify-content center
  flex-direction column
  border-radius 4px
  border 1px dashed #D9D9D9;
  cursor pointer
.upload-input
  position absolute
  left 0
  top 0
  width 100%
  height 100%
  opacity 0
  cursor pointer
.text
  color rgba(0, 0, 0, 0.25);
  font-size 12px
  line-height 18px
</style>
