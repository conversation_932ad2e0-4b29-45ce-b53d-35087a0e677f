// eslint-disable-next-line object-curly-newline
import { defineComponent, createVNode, render, onUnmounted, onMounted } from 'vue'
import ImageCropper from './ImageCropper.vue'

interface Ret {
  file: Blob
  base64: string
  width: number
  height: number
  cropped: boolean
}

function imageCrop(src: string, ratio: number) {
  if (!src) return Promise.reject(new Error('src is required'))

  return new Promise<Ret | void>(resolve => {
    const div = document.createElement('div')
    document.body.appendChild(div)

    const Wrapper = defineComponent({
      setup() {
        const onCancel = () => {
          render(null, div)
          if (div.parentNode) {
            div.parentNode.removeChild(div)
          }
          resolve()
        }

        const onOk = (ret: Ret) => {
          resolve(ret)
          onCancel()
        }

        const keyupCancel = (e: KeyboardEvent) => {
          if (e.code === 'Escape') {
            onCancel()
          }
        }

        onMounted(() => {
          window.addEventListener('keyup', keyupCancel)
        })

        onUnmounted(() => {
          window.removeEventListener('keyup', keyupCancel)
        })

        // @ts-ignore
        return () => <ImageCropper src={src} ratio={ratio} onCancel={onCancel} onOk={onOk} />
      }
    })

    const vm = createVNode(Wrapper)
    render(vm, div)
  })
}

export default imageCrop
