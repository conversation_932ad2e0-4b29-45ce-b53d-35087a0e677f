<template>
  <div class="modal-container">
    <div class="content">
      <!-- 图片 -->
      <div ref="wrap" class="img-wrap">
        <img ref="image" class="img" :src="src" />
      </div>

      <!-- 操作符号 -->
      <div class="action">
        <div class="buttons">
          <BeerButton variant="secondary" @click="cancel">取消</BeerButton>
          <BeerButton variant="default" @click="confirm">确认</BeerButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, defineProps, onUnmounted, onMounted, PropType
  } from 'vue'
  import { BeerButton, toaster } from '@xhs/yam-beer'
  import Cropper from 'cropperjs'
  import 'cropperjs/dist/cropper.min.css'

  interface Ret {
    file: Blob
    base64: string
    width: number
    height: number
    cropped: boolean
  }

  const props = defineProps({
    src: {
      type: String,
      default: ''
    },
    ratio: {
      type: Number,
      default: 1
    },
    maxSize: {
      type: Number,
      default: 1800,
    },
    onCancel: {
      type: Function as PropType<() => void>,
      default: () => () => {}
    },
    onOk: {
      type: Function as PropType<(ret: Ret) => void>,
      default: () => () => {}
    }
  })

  const image = ref<HTMLImageElement>()
  const wrap = ref<HTMLDivElement>()
  let cropper: Cropper | null = null

  let timer: any = null

  const validateZoom = (ratio: number) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const style = getComputedStyle(wrap.value!)
      const width = style.getPropertyValue('width')
      const height = style.getPropertyValue('height')
      if (ratio < 0.01) {
        cropper?.zoomTo(0.01)
      } else if (ratio > 3) {
        cropper?.zoomTo(3, {
          x: parseInt(width, 10) / 2,
          y: parseInt(height, 10) / 2
        })
      }
    }, 100)
  }

  onMounted(() => {
    if (image.value) {
      cropper = new Cropper(image.value, {
        aspectRatio: props.ratio,
        viewMode: 1,
        zoom: e => {
          validateZoom(e.detail.ratio)
        },
        ready() {
          if (cropper) {
            const canvas = cropper.getImageData()
            const n = Math.max(canvas.naturalHeight, canvas.naturalWidth)
            const max = props.maxSize
            if (n > max) {
              const scale = max / n
              cropper.zoom((1 - scale) / scale) // 放大视图
              cropper.scale(scale, scale) // 缩小图片
            }
          }
        },
      })
    }
  })

  // 取消
  const cancel = () => {
    cropper?.destroy()
    props.onCancel()
  }

  onUnmounted(cancel)

  // 执行剪切
  const confirm = () => {
    if (cropper) {
      const canvas = cropper.getCroppedCanvas()
      const { width, height } = canvas
      canvas.toBlob(blob => {
        if (blob) {
          props.onOk?.({
            file: blob, width, height, base64: canvas.toDataURL('image/png') || '', cropped: true
          })
        } else {
          toaster.danger('图片剪切失败')
        }
      })
    }
  }
</script>

<style lang="stylus" scoped>
.modal-container
  position fixed
  left 0
  top 0
  z-index 1000
  width 100%
  height 100%
  padding 20px
  background-color rgba(0, 0, 0, 0.7)
  animation modalAni 1s ease
.content
  display flex
  flex-direction column
  width 100%
  height 100%
.img-wrap
  flex 1
  display flex
  align-items center
  justify-content center
  overflow hidden
.action
  display flex
  flex-direction column
.buttons
  padding 10px
  display flex
  align-items center
  justify-content center
  background-color #fff
.img
  max-width 100%
  max-height 100%
@keyframes modalAni
  from
    opacity 0
  to
    opacity 1
</style>
