import { DataList } from '../types'

export function getSrcByPath(path: string, forMedia = 'image') {
  if (!path) {
    return ''
  }
  const newpath = path.indexOf('/') === 0 ? path : `/${path}`
  const domain = forMedia === 'video' ? 'store' : 'qimg'
  return `https://${domain}.xiaohongshu.com${newpath}`
}

export function getGoodsImgSrcDto(dto: { path: string } | string) {
  if (!dto || typeof dto === 'string') {
    return dto
  }
  // placehoder image
  if (!dto.path) {
    return 'https://ci.xiaohongshu.com/e25d01dd-e150-4003-9b8c-81ab3fb69dc1@r_640w_640h.jpg'
  }
  return getSrcByPath(dto.path)
}

// 提示重复
export function findDuplicatePath(fileList: DataList) {
  const list = Array.isArray(fileList) ? fileList : [fileList]
  const pathMap: Record<string, boolean> = {}
  list.forEach(item => {
    const { path } = item
    if (!path) {
      return
    }
    if (!pathMap[path]) {
      pathMap[path] = true
    } else {
      item.error = '图片重复'
    }
  })
  return list
}

let id = 0
// 获取唯一Id
export function uniqueId() {
  // eslint-disable-next-line no-plusplus
  return `${Date.now()}-${id++}`
}

export function fillId(dataList: DataList) {
  dataList.forEach(item => {
    if (!item.id) {
      item.id = uniqueId()
    }
  })
  return dataList
}

export function getFileExt(filename: string) {
  return `.${filename.split('.').pop()}`
}

export function getVideoInfo(file: File) {
  return new Promise(resolve => {
    const video = document.createElement('video')
    const src = URL.createObjectURL(file)
    video.preload = 'metadata'
    video.onloadedmetadata = () => {
      URL.revokeObjectURL(src)
      resolve({
        duration: Math.ceil(video.duration),
        extension: getFileExt(file.name),
        size: file.size,
        file,
        width: video.videoWidth,
        height: video.videoHeight,
      })
    }
    video.src = src
  })
}

export function readImgToBase64(file: File | Blob): Promise<string> {
  return new Promise(resolve => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
  })
}

export async function getImgSize(imgBase64: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.onload = () => {
      resolve({
        width: image.width,
        height: image.height,
      })
    }
    image.onerror = () => {
      reject('图片格式错误')
    }
    image.src = imgBase64
  })
}
