<template>
  <div class="upload-wrapper">
    <Draggable
      v-model="fileList"
      handle=".image-item"
      :disabled="!draggable"
      name="acceptFiles"
      class="image-list"
      item-key="id"
      ghost-class="ghost"
      @start="isDragging = true"
      @end="onDragEnd"
    >
      <template #item="{ element: item }">
        <div :key="item.id" class="image-item">
          <div class="image-wrapper" :class="{ 'image-error': !readonly && item.error }">
            <img
              ref="img"
              v-lazy="item.base64 || item.link || item.url"
              :class="{ draggable: draggable && !item.error, 'error-image': item.error }"
              class="img"
              @click="imageClickHandler(item)"
            />

            <div v-if="item.uploadPercent < 100 && !item.error" class="image-overlay">
              <BeerProgress :size="36" :progress="item.uploadPercent" :indicator="true" />
            </div>

            <div v-if="!readonly" class="remove-icon pointer" @click="onRemoveImage(item)">
              <RemoveIcon />
            </div>
            <div v-if="item.error" class="error-icon pointer" @click="doCrop(item)">
              <RefreshIcon />
            </div>
            <!-- 比例错误提示 -->
            <div v-if="item.ratioError" class="ratio-error pointer" @click="doCrop(item)">
              <div>图片比例不匹配</div>
              <div>请点击图片截取</div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="fileList.length < maxCount && !readonly" #footer>
        <UploadCard :max-count="maxCount" :accept="accept" :text="text" @change="onUploadFile" />
      </template>
    </Draggable>
    <div class="tip">{{ tip }}</div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, computed, watch } from 'vue'
  import Draggable from 'vuedraggable'
  import { BeerProgress, toaster } from '@xhs/yam-beer'
  import { viewImgs } from '@xhs/yam-beer/cmp/overlayers/Viewer/Viewer'

  // @ts-ignore
  import { ImageUploader } from '../../../node_modules/@xhs/uploader'
  import RefreshIcon from './components/refresh-icon.vue'
  import RemoveIcon from './components/remove-icon.vue'
  import UploadCard from './components/upload-card.vue'

  import { DataList, DataItem } from './types'
  import {
    findDuplicatePath, uniqueId, fillId, getImgSize, readImgToBase64
  } from './utils'
  import imageCrop from './image-crop'

  import uploadProps from './props'

  function previewImage(img: DataItem) {
    const src = img.link || img.base64
    if (src) {
      viewImgs([src])
    }
  }

  const props = defineProps(uploadProps)

  const emit = defineEmits(['update:modelValue'])

  // 上传文件
  const fileList = ref<DataList>(fillId(props.modelValue))
  // 是否正在拖拽
  const isDragging = ref<boolean>(false)
  // 是否正在上传
  const isUploading = ref<boolean>(false)
  // 所有合适的比例数组
  const aspectRatios = computed(() => {
    let { aspectRatio } = props
    if (!Array.isArray(aspectRatio)) {
      aspectRatio = aspectRatio ? [aspectRatio] : []
    }
    return aspectRatio
  })
  // 第一个比例
  const firstAspectRatio = computed(() => (aspectRatios.value.length ? aspectRatios.value[0] : 0))

  // eslint-disable-next-line no-return-assign
  watch(() => props.modelValue, (list: DataList) => {
    fileList.value = fillId(list)
  })

  // 上传完成, 同步props
  const onComplete = () => {
    // 提示重复错误
    findDuplicatePath(fileList.value)
    // 同步props
    emit('update:modelValue', fileList.value)
  }

  // 拖拽结束
  const onDragEnd = () => {
    isDragging.value = false
    if (!isUploading.value) {
      onComplete()
    }
  }

  // 校验
  const validate = async (file: File | Blob, cropped = false) => {
    const base64 = await readImgToBase64(file)
    const { width, height } = await getImgSize(base64)
    const { maxSize } = props
    if (maxSize && file.size > maxSize) {
      return {
        base64, width, height, error: `文件不得大于 ${maxSize / 1024 / 1024}M`
      }
    }
    if (!cropped && aspectRatios.value.length && !aspectRatios.value.some(r => parseFloat(`${r}`).toFixed(2) === parseFloat(`${width / height}`).toFixed(2))) {
      return {
        base64, width, height, ratioError: true, error: '比例不匹配，请点击图片截取'
      }
    }
    return { base64, width, height }
  }

  // 开始上传图片
  const uploadImageJob = async ({ id, file, cropped }: { id: string; file: File | Blob; cropped: boolean }) => {
    const currentUpload: DataItem = {
      file, id, link: ''
    }

    // 人工校验校验失败
    const valid = await validate(file, cropped)
    currentUpload.error = valid.error
    currentUpload.base64 = valid.base64
    currentUpload.width = valid.width
    currentUpload.height = valid.height
    currentUpload.ratioError = valid.ratioError || false

    const target = fileList.value.find(item => item.id === id)
    if (target && !target.error) {
      return currentUpload
    }

    const update = (item: DataItem) => {
      const itemIndex = fileList.value.findIndex(it => it.id === id)
      if (itemIndex === -1) {
        fileList.value.push(currentUpload)
      } else {
        fileList.value[itemIndex] = item
      }
    }

    update(currentUpload)

    if (valid.error) {
      toaster.danger(valid.error)
      update(currentUpload)
      return currentUpload
    }

    const uploader = new ImageUploader({
      bucket: props.bucket,
      bizKey: props.bizKey,
    })
    const onProgress = (event: { loaded: number; total: number }) => {
      let percent = Math.round((event.loaded / event.total) * 100)
      if (percent > 99) {
        percent = 99
      }
      const itemIndex = fileList.value.findIndex(item => item.id === id)
      if (itemIndex === -1) {
        return
      }
      fileList.value[itemIndex].uploadPercent = percent
    }

    const res = await uploader.post({ file, onProgress }).catch((err: any) => ({
      error: `网络错误: ${err.message}`,
    }))
    const { cdnUrl: link, ...rest } = res
    const successItem = {
      ...currentUpload, uploadPercent: 100, ...rest, link
    }
    update(successItem)
    return successItem
  }

  // 图片剪切
  const doCrop = async (item: DataItem) => {
    if (props.readonly) return
    // if (!item.ratioError) return
    const cropRes = await imageCrop(item.base64 || item.link, firstAspectRatio.value)
    if (!cropRes) return
    const { file, cropped } = cropRes
    await uploadImageJob({
      id: item.id || uniqueId(),
      file,
      cropped
    })
    onComplete()
  }

  // 图片点击
  const imageClickHandler = (item: DataItem) => {
    const ratio = firstAspectRatio.value
    if (!ratio) {
      // 图片预览
      previewImage(item)
    } else if (!props.readonly) {
      // 图片裁剪
      const src = item.base64 || item.link
      imageCrop(src, ratio)
    }
  }

  // 删除图片
  const onRemoveImage = (item: DataItem) => {
    fileList.value = fileList.value.filter(f => f.id !== item.id)
    onComplete()
  }

  // 接收文件
  const onUploadFile = async (receiveFiles: FileList) => {
    // 保证最大上传数量
    const files = [...receiveFiles]
      .slice(0, props.maxCount - fileList.value.length)
      .map(file => ({ id: uniqueId(), file }))
    for (const item of files) {
      // @ts-ignore
      // eslint-disable-next-line no-await-in-loop
      await uploadImageJob(item)
    }
    onComplete()
  }

</script>

<style lang="stylus" scoped>
.uploader-wraper
  display flex
  flex-wrap wrap
  max-width 580px
.image-list
  display flex
  flex-wrap wrap
.image-item, .upload-input-item
  position relative
  width 104px
  height 104px
  margin-right: 12px
  margin-bottom: 12px

.image-overlay
  border-radius: 4px;
  position absolute
  height 100%
  width 100%
  background-color #c9c9c9
  opacity 0.7
  top 0
  left 0
.remove-icon
  position absolute
  top 0
  right 0
.img
  position relative
  height 100%
  width 100%
  border-radius 4px
  object-fit cover
.draggable
  cursor move
.remove
  position absolute
  right 0
  top 0
  cursor pointer
.error-icon
  position absolute
  top 50%
  left 50%
  transform translate(-50%, -50%)
.image-wrapper
  position relative
  width 104px
  height 104px
  background rgba(0, 0, 0, 0.15);
  border: 1px solid transparent;
  box-sizing: border-box;
  border-radius: 4px;
  &.image-error
    border-color #FF525D
.ghost
  opacity: 0.5;
  background: #c8ebfb;

.ratio-error
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  color: #ff525d;
  font-size: 12px;
  font-weight bold
  line-height: 16px;
  text-align: center
  transform scale(0.917)
.pointer
  cursor: pointer
.error-image
  filter grayscale(1)
  opacity 0.8
.tip
  color #8C8C8C
  font-size 12px
  line-height 1.5
</style>
