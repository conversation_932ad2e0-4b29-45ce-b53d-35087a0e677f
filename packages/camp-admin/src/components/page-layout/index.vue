<template>
  <div class="breadcrumb">
    <Breadcrumb :nav-list="navList" />
  </div>
  <div v-beering="loading" class="layout">
    <slot />
  </div>
</template>

<script lang="tsx" setup>
  import { computed, defineProps, PropType } from 'vue'
  import { useRouter } from 'vue-router'
  import { Breadcrumb } from '@xhs/yam-beer'

  const router = useRouter()

  interface NavItem {
    label: string
    to?: {
      name: string
      params: Record<string, string>
      query: Record<string, string>
    }
  }

  const props = defineProps({
    navList: {
      type: Array as PropType<NavItem[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false
    }
  })

  const navList = computed(() => props.navList.map(nav => ({
    label: nav.label,
    props: {
      handleClick: nav.to ? () => {
        if (nav.to) {
          router.push(nav.to)
        }
      } : undefined,
    },
  })))

</script>

<style lang="stylus" scoped>
.breadcrumb
  margin-top -24px
</style>
