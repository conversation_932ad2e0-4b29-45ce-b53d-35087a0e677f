<template>
  <BeerModal
    v-model:show="show"
    :bs="{ width: 800 }"
    title="选择POI"
    :width="500"
    :footer="null"
    @confirm="({ close }: { close: any }) => close()"
  >
    <template #footer>
      <div></div>
    </template>
    <div class="content">
      <div class="middle-flex" style="padding-bottom: 20px">
        <div class="form-item">
          <CityCascader v-model="cityValue" :change-on-select="false" @change="changeCity" />
        </div>
        <div class="form-item" style="width: 280px">
          <BeerInput
            v-model="filters.poiKeyword"
            :disabled="!filters.cityId"
            style="width: 270px"
            placeholder="输入完整名称，如小红书之家大悦城店"
            @keyup.enter="search"
          />
        </div>
        <BeerButton :disabled="!filters.cityId || !filters.poiKeyword" @click="search">搜索</BeerButton>
        <BeerButton variant="secondary" @click.prevent="reset">重置</BeerButton>
      </div>

      <p v-if="tipVisible" class="tip-text">输入以上信息，搜索要添加的POI</p>
      <div v-else>
        <div v-if="filters.cityId && !internalStoreList.length" class="tip">
          <p>无匹配结果：</p>
          <p>请检查您输入的省、市、区，店铺信息是否有误；</p>
        </div>
        <BeerTable
          v-if="internalStoreList.length"
          :columns="INTERNAL_STORE_COLUMN"
          :data="internalStoreList"
        />
      </div>
    </div>
  </BeerModal>
</template>

<script lang="tsx" setup>
// 这里了的逻辑是搬运自pro-enterprise，如果着急不要看这里。。。
  import { ref, computed, PropType } from 'vue'
  import {
    BeerModal, BeerInput, BeerButton, BeerTable
  } from '@xhs/yam-beer'

  import CityCascader from '@/components/city-cascader/index.vue'

  import { getPoiInternal } from '@/services/poi'
  import loading from '@/utils/loading'
  import { POI } from '@/types/poi'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    action: {
      type: Function as PropType<(poi:POI) => void>,
    },
    onOk: {
      type: Function as PropType<(poi: Partial<POI>) => void>,
    }
  })

  const emit = defineEmits(['update:visible', 'ok'])

  interface Payload {
    provinceId: string
    cityId: string
    districtId: string
    poiKeyword: string
    provinceName?: string
    cityName?: string
    districtName?: string
  }
  const filters = ref<Payload>({
    provinceId: '',
    cityId: '',
    districtId: '',
    poiKeyword: '',
    provinceName: '',
    cityName: '',
    districtName: ''
  })
  const internalStoreList = ref<any>([])
  const externalStoreList = ref<any>([])

  const tipVisible = ref<boolean>(true)

  const cityValue = computed({
    get: () => [filters.value.provinceId, filters.value.cityId, filters.value.districtId],
    set: ([provinceId, cityId, distinct]) => {
      filters.value.provinceId = provinceId
      filters.value.cityId = cityId
      filters.value.districtId = distinct
    }
  })

  const changeCity = (_: any, names: string[] = []) => {
    filters.value.provinceName = names[0]
    filters.value.cityName = names[1]
    filters.value.districtName = names[2]
  }

  const show = computed({
    get: () => props.visible,
    set: newVisible => {
      emit('update:visible', newVisible)
    }
  })

  const reset = () => {
    filters.value = {
      provinceId: '',
      cityId: '',
      districtId: '',
      poiKeyword: '',
      provinceName: '',
      cityName: '',
      districtName: ''
    }
    internalStoreList.value = []
    externalStoreList.value = []
  }

  const search = async () => {
    if (!filters.value.poiKeyword) return
    loading.show()
    await getPoiInternal(filters.value).then(res => {
      internalStoreList.value = res
      tipVisible.value = false
      loading.close()
    }).catch(() => {
      tipVisible.value = false
      loading.close()
    // this.$itemToast(e.detail.msg)
    })
  }
  const choose = async (item: POI) => {
    if (props.action) {
      await props.action(item)
    }
    props.onOk?.(item)
  }

  const INTERNAL_STORE_COLUMN = [
    {
      title: 'POI名称',
      key: 'poiName',
    }, {
      title: '地址',
      key: 'address',
    }, {
      title: '笔记数',
      key: 'noteNum',
      is: (cellData: any) => (!cellData ? '-' : cellData),
    }, {
      title: '操作',
      key: 'action',
      td: {
        is: (data: any) => {
          const row = data.props?.rowData
          const { provinceName = '', cityName = '', districtName = '' } = filters.value
          const handleClick = () => {
            choose({
              ...row,
              province: provinceName,
              city: cityName,
              district: districtName,
            })
          }
          return (
          <div class="r-actions">
            <span class='primary' onClick={handleClick}>选择</span>
          </div>
        )
        }
      }
    },
  ]

</script>

<style lang="stylus" scoped>
.content
  min-height 300px
.form-item
  padding-right 1em
.tip-text
  padding 60px 0
  text-align center
.tip
  padding 30px 1em
</style>
