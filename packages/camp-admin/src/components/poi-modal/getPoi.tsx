import {
  defineComponent, createVNode, render, onUnmounted, onMounted,
} from 'vue'

import { POI } from '@/types/poi'

import PoiModal from './index.vue'

function getPoi(action?:(poi:POI) => any) {
  return new Promise<POI | void>(resolve => {
    const div = document.createElement('div')
    document.body.appendChild(div)

    const Wrapper = defineComponent({
      setup() {
        const onCancel = () => {
          render(null, div)
          if (div.parentNode) {
            div.parentNode.removeChild(div)
          }
          resolve()
        }

        const onOk = (ret: POI) => {
          resolve(ret)
          onCancel()
        }

        const keyupCancel = (e: KeyboardEvent) => {
          if (e.code === 'Escape') {
            onCancel()
          }
        }

        onMounted(() => {
          window.addEventListener('keyup', keyupCancel)
        })

        onUnmounted(() => {
          window.removeEventListener('keyup', keyupCancel)
        })

        const props = {
          visible: true,
          action,
          onOk,
          'onUpdate:visible': onCancel,
        }

        // @ts-ignore
        return () => <PoiModal {...props} />
      }
    })

    const vm = createVNode(Wrapper)
    render(vm, div)
  })
}

export default getPoi
