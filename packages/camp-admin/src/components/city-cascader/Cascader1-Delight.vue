<template>
  <Cascader
    v-model="value"
    :options="options"
    hide-after-select
    placeholder="请选择城市"
    expand-trigger="hover"
    check-strictly
    change-on-select
    :load-data="loadData"
  />
</template>

<script lang="tsx" setup>
  import { ref, computed, watch } from 'vue'
  import { Cascader } from '@xhs/delight'

  import * as Api from './service'
  import { Citys } from './service'

  interface Option {
    label: string
    value: string
    isLeaf?: boolean
    level: 0 | 1 | 2
    children?: Option[]
    ids?: number[]
    names?: string[]
  }

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['update:modelValue', 'change'])
  const data: Record<string, Option> = {}
  const options = ref<Option[]>([])

  const format = (list: Citys, level: 0 | 1 | 2 = 0, ids: number[] = [], names: string[] = []) => list.map(item => {
    const option = {
      label: item.locationItemName,
      value: item.locationItemId,
      isLeaf: level >= 2,
      level,
      children: level < 2 ? [] : undefined,
      ids: [...ids, item.locationItemId],
      names: [...names, item.locationItemName],
    } as Option
    data[item.locationItemId] = option
    return option
  })

  const loadData = async (item: Option, resolve) => {
    if (item) {
      let citys
      if (item.level === 0) {
        try {
          citys = await Api.city(item.value)
        } finally {
          resolve(format(citys || [], 1, item.ids, item.names))
        }
      } else if (item.level === 1) {
        try {
          citys = await Api.distinct(item.value)
        } finally {
          resolve(format(citys || [], 2, item.ids, item.names))
        }
      }
    }
  }

  const value = computed({
    get: () => props.modelValue,
    set: (newValue: any) => {
      // const ids = data[newValue]?.ids
      const names = data[newValue]?.names
      emit('update:modelValue', newValue)
      emit('change', names || [])
    }
  })

  Api.province().then(res => {
    options.value = format(res)
  })

  // 重置搜索条件时无法触发value的set方法，所以使用watch置空（可能是bug？不知缘由）
  watch(props, newProps => {
    if (!newProps.modelValue) {
      emit('change', '')
    }
  })

</script>

<style>
</style>
