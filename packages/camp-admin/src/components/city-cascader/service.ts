import http from '@/utils/http'

interface City {
  locationItemId: string
  locationItemName: string
}

export type Citys = City[]

let provinceList: Promise<Citys>
export function province(): Promise<Citys> {
  if (provinceList) return provinceList
  provinceList = http.get('/api/hera/businesscenter/poi/getProvinceInfo')
  return provinceList
}

export function city(provinceId: string): Promise<Citys> {
  return http.get('/api/hera/businesscenter/poi/getCityInfoByProvinceId', {
    params: { provinceId },
  })
}

export function distinct(cityId: string): Promise<Citys> {
  return http.get('/api/hera/businesscenter/poi/getDistrictInfoByCityId', {
    params: { cityId },
  })
}
