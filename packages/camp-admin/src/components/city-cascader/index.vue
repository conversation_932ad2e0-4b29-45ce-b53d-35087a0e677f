<template>
  <Cascader
    v-model:treeData="options"
    v-model="value"
    show-search
    placeholder="请选择城市"
    expand-trigger="hover"
    :change-on-select="changeOnSelect"
    :load-data="loadData"
  />
</template>

<script lang="tsx" setup>
  import { ref, computed } from 'vue'
  import { Cascader } from '@xhs/yam-beer'

  import * as Api from './service'
  import { Citys } from './service'

  interface Option {
    key: string
    title: string
    level: 0 | 1 | 2
    isLeaf?: boolean
    children?: Option[]
    ids?: number[]
    names?: string[]
  }

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    changeOnSelect: {
      type: Boolean,
      default: false
    }
  })

  const data: Record<string, Option> = {}

  const emit = defineEmits(['update:modelValue', 'change'])

  const options = ref<Option[]>([])

  const format = (list: Citys, level: 0 | 1 | 2 = 0, ids: number[] = [], names: string[] = []) => list.map(item => {
    const option = {
      key: item.locationItemId,
      title: item.locationItemName,
      isLeaf: level >= 2,
      level,
      ids: [...ids, item.locationItemId],
      names: [...names, item.locationItemName],
    } as Option
    data[item.locationItemId] = option
    return option
  })

  const loadData = async (item: Option) => {
    item.isLeaf = true

    if (item.level === 0) {
      const citys = await Api.city(item.key)
      item.children = format(citys, 1, item.ids, item.names)
    } else if (item.level === 1) {
      const citys = await Api.distinct(item.key)
      item.children = format(citys, 2, item.ids, item.names)
    }
  }

  const value = computed(({
    get: () => {
      const ls = props.modelValue.filter(Boolean)
      return ls[ls.length - 1]
    },
    set: (newValue: any) => {
      const ids = data[newValue]?.ids
      const names = data[newValue]?.names
      emit('update:modelValue', ids, names)
      emit('change', ids, names)
      if (!data[newValue]?.children?.length) {
        loadData(data[newValue])
      }
    }
  }))

  Api.province().then(res => {
    options.value = format(res)
  })
</script>

<style>
</style>
