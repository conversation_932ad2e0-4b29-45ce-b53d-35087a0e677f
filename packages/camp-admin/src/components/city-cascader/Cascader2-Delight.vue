<template>
  <Cascader
    v-model="value"
    :options="options"
    hide-after-select
    placeholder="请选择城市"
    expand-trigger="hover"
    check-strictly
    :load-data="loadData"
  />
</template>

<script lang="tsx" setup>
  import { ref, computed, watch } from 'vue'
  import { Cascader2 as Cascader } from '@xhs/delight'

  import * as Api from './service'
  import { Citys } from './service'

  interface Option {
    label: string
    value: string
    isLeaf?: boolean
    level: 0 | 1 | 2
    children?: Option[]
    names?: string[]
  }

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['update:modelValue', 'change'])

  const options = ref<Option[]>([])

  const format = (list: Citys, level: 0 | 1 | 2 = 0, names: string[] = []) => list.map(item => {
    const option = {
      label: item.locationItemName,
      value: item.locationItemId,
      isLeaf: level >= 2,
      level,
      names: [...names, item.locationItemName],
    } as Option
    return option
  })

  const loadData = async (opts: Option[]) => {
    if (opts?.length) {
      const item = opts[opts.length - 1]
      if (item.level === 0) {
        const citys = await Api.city(item.value)
        item.children = format(citys, 1, item.names)
        options.value = [...options.value]
      } else if (item.level === 1) {
        const citys = await Api.distinct(item.value)
        item.children = format(citys, 2, item.names)
        options.value = [...options.value]
      }
    }
  }

  const getNames = (names: string[], ids: string[] = [], opts: Option[] = []) => {
    const idsTemp = [...ids]
    if (idsTemp[0]) {
      const tmp = idsTemp.shift()
      const opt = opts.find(item => item.value === tmp)
      if (opt) {
        names.push(opt?.label)
        if (opt.children) {
          getNames(names, idsTemp, opt.children)
        }
      }
    }
  }

  const value = computed({
    get: () => props.modelValue,
    set: (newValue: any) => {
      const names = []
      getNames(names, newValue, options.value)
      emit('update:modelValue', names)
      emit('change', names)
    }
  })

  Api.province().then(res => {
    options.value = format(res)
  })

  // 重置搜索条件时无法触发value的set方法，所以使用watch置空（可能是bug？不知缘由）
  watch(props, newProps => {
    if (!newProps.modelValue.length) {
      emit('change', [])
    }
  })

</script>

<style>
</style>
