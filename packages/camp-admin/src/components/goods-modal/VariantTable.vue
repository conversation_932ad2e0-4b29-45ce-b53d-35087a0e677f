<template>
  <BeerTable style="width: 400px;" :columns="columns" :data="data" />
</template>

<script lang="tsx" setup>
  import { } from 'vue'
  import { BeerTable } from '@xhs/yam-beer'

  defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  const columns = [
    {
      title: '库存',
      key: 'stock',
    },
    {
      title: '售价',
      key: 'price',
    },
    {
      title: '状态',
      key: 'status',
      td: {
        is: (data: any) => {
          const row = data.props.rowData
          return (
          <div>{row.status ? '在架上' : '已下架'}</div>
        )
        }
      }
    }
  ]

</script>

<style>
</style>
