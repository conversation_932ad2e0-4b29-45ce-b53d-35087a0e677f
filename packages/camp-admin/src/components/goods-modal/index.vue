<template>
  <Drawer
    :show="modelValue"
    :width="680"
    :footer-bs="{
      textAlign: 'right'
    }"
    @update:show="emit('update:modelValue', $event)"
  >
    <slot name="title">
      <div class="title-wrap">
        <span style="margin-right: 1em">商品详情</span>
        <Tag v-for="tag in tags" :key="tag.text" variant="color" :modifier="tag.type">{{ tag.text }}</Tag>
      </div>
    </slot>
    <Card>
      <FormGroup label="商品类目" layout="table">
        <div class="value">{{ goods?.baseInfo?.categoryName || '-' }}</div>
      </FormGroup>
      <FormGroup label="商品标题" layout="table">
        <div class="value">{{ goods?.baseInfo?.name || '-' }}</div>
      </FormGroup>
      <FormGroup label="商品短标题" layout="table">
        <div class="value">{{ goods.baseInfo?.shortName || '-' }}</div>
      </FormGroup>
      <FormGroup label="商品图片" layout="table">
        <div class="flex" style="max-width: 800">
          <BaseImage v-for="(img, key) in images" :key="key" :width="104" :height="104" :src="img" />
        </div>
      </FormGroup>
      <FormGroup label="商品描述" layout="table">
        <div class="value">{{ goods.baseInfo?.description || '-' }}</div>
      </FormGroup>
      <FormGroup v-if="goodsType === 'SKI'" label="商品图文描述" layout="table">
        <ImageTextButton :images="goods.imgAndDescDetail.images" />
      </FormGroup>
      <!-- 露营商品 -->
      <template v-else>
        <FormGroup label="商品类型" layout="table">
          <div class="value">{{ typeName }}</div>
        </FormGroup>
        <FormGroup label="所属营地" layout="table">
          <div class="value">{{ goods.attributes?.suiteId || '-' }}</div>
        </FormGroup>
        <FormGroup label="可住人数" layout="table">
          <div
            class="value"
          >{{ goods.attributes?.capacity ? `${goods.attributes?.capacity}人` : '-' }}</div>
        </FormGroup>
        <FormGroup label="固定天数商品" layout="table">
          <div class="value">{{ goods.attributes?.fixedDays == -1 ? '否' : '是' }}</div>
        </FormGroup>
      </template>
      <FormGroup v-if="goods.baseInfo?.state !== null" label="商品状态" layout="table">
        <div class="value">{{ goods.baseInfo?.state ? '在架上' : '已下架' }}</div>
      </FormGroup>
      <FormGroup v-if="goods.auditStatus !== null" label="审核状态" layout="table">
        <div class="value middle-flex">
          <span>{{ auditMap[goods.auditStatus!] || '-' }}</span>
          <Popover
            v-if="goods.auditStatus === 3 && goods.auditResultList?.length"
            trigger="hover"
            placement="right"
          >
            <BeerIcon
              style="cursor: pointer;margin-left: 5px;"
              icon="exclamation-marks"
              color="danger"
            />
            <template #popover>
              <div style="padding: 10px; max-width: 500px;">
                <div style="padding: 10px; max-width: 500px;">
                  <div v-for="(item, key) in goods.auditResultList" :key="key">
                    <div>
                      <p
                        v-if="item.content"
                        style="white-space: normal; word-break: break-all; color: #666; font-size: 14px"
                      >审核失败内容：{{ item.content }}</p>
                      <p
                        v-if="item.comment"
                        style="white-space: normal; word-break: break-all; padding-bottom: 10px"
                      >审核失败理由：{{ item.comment }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Popover>
        </div>
      </FormGroup>
    </Card>
    <template #footer>
      <div class="footer">
        <BeerButton @click="emit('update:modelValue', false)">返回</BeerButton>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
  import { PropType, computed } from 'vue'
  import {
    Drawer, Tag, BeerButton, FormGroup, Popover, BeerIcon
  } from '@xhs/yam-beer'

  import { IGoodsInfo } from '@/types/goods'
  import { typeMap } from '@/constants/POI'
  import { getGoodsType } from '@/services/goods'

  import BaseImage from '@/components/base-image/index.vue'
  import ImageTextButton from '@/components/image-text-button/index.vue'
  import Card from '@/components/base-card/index.vue'
  // import VariantTable from './VariantTable.vue'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    goods: {
      type: Object as PropType<IGoodsInfo>,
      default: () => ({})
    },
    tags: {
      type: Array as PropType<{text:string;type:string}[]>,
      default: () => []
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const goodsType = computed(() => getGoodsType(props.goods))

  const typeName = computed(() => typeMap[goodsType.value] || '-')

  const images = computed(() => (props.goods?.baseInfo?.images || []).map(item => `//qimg.xiaohongshu.com/${item.path}`))

  const auditMap = {
    1: '审核中',
    2: '审核通过',
    3: '审核不通过',
  }
</script>

<style lang="stylus" scoped>
.value
  padding-top 5px

.title-wrap
  background-color #fff
  height 70px
  margin -24px -24px 24px -24px
  display flex
  align-items center
  align-items center
  border-bottom 1px solid #f0f0f0
  padding-left 24px
</style>
