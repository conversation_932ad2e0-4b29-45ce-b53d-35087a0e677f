<template>
  <div>
    <div class="flex">
      <div>
        <Tooltip :content="suiteDisabled" placement="top">
          <RadioGroup
            v-model="dataSource"
            :disabled="!!suiteDisabled"
            :options="sourceOptions"
            @update:model-value="changeSource"
          />
        </Tooltip>
      </div>
    </div>

    <UploadImage v-if="source === 'upload'" v-model="dataValue" v-bind="dataUploadProps" />

    <Draggable
      v-else
      v-model="dataValue"
      handle=".image-item"
      :disabled="!draggable"
      name="acceptFiles"
      class="image-list"
      item-key="id"
      ghost-class="ghost"
      @start="isDragging = true"
      @end="onDragEnd"
    >
      <template #item="{ element: item }">
        <div :key="item.id" class="image-item">
          <div class="image-wrapper" :class="{ 'image-error': !readonly && item.error }">
            <img
              ref="img"
              v-lazy="item.base64 || item.link"
              :class="{ draggable: draggable && !item.error, 'error-image': item.error }"
              class="img"
              @click="imageClickHandler(item)"
            />

            <div v-if="!readonly" class="remove-icon pointer" @click="onRemoveImage(item)">
              <RemoveIcon />
            </div>
            <div v-if="item.error" class="error-icon pointer" @click="doCrop(item)">
              <RefreshIcon />
            </div>
            <!-- 比例错误提示 -->
            <div v-if="item.ratioError" class="ratio-error pointer" @click="doCrop(item)">
              <div>图片比例不匹配</div>
              <div>请点击图片截取</div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="props.modelValue.length < maxCount && !readonly" #footer>
        <div v-if="!props.modelValue.length" class="image-item">
          <div class="image-wrapper">
            <img class="img" src="https://ci.xiaohongshu.com/e25d01dd-e150-4003-9b8c-81ab3fb69dc1@r_640w_640h.jpg" />
          </div>
        </div>
        <div class="button-wrap">
          <div class="r-actions">
            <span class="primary" style="font-size: 14px" @click="chooseImage">选择商家营地图片</span>
          </div>
        </div>
      </template>
    </Draggable>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, computed, PropType } from 'vue'
  import { RadioGroup, Tooltip } from '@xhs/yam-beer'
  import Draggable from 'vuedraggable'
  import { viewImgs } from '@xhs/yam-beer/cmp/overlayers/Viewer/Viewer'

  import { ImageSource } from '@/types/poi'

  import RefreshIcon from '../upload-image/components/refresh-icon.vue'
  import RemoveIcon from '../upload-image/components/remove-icon.vue'
  import { DataItem, DataList } from '../upload-image/types'
  import uploadProps from '../upload-image/props'
  import getSuiteImages from './suite-images/getSuiteImages'

  import UploadImage from '../upload-image/index.vue'

  const props = defineProps({
    source: {
      type: String as PropType<ImageSource>,
      default: 'upload',
    },
    poiId: {
      type: String,
      default: '',
    },
    suiteDisabled: {
      type: String,
      default: '',
    },
    ...uploadProps
  })

  const emit = defineEmits(['update:source', 'update:modelValue'])

  // 是否正在拖拽
  const isDragging = ref(false)

  const dataUploadProps = computed(() => {
    const {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      source, modelValue, poiId, ...restProps
    } = props
    return restProps
  })

  const sourceOptions = [
    { name: '同步商家营地图片', value: 'suite' },
    { name: '手动上传', value: 'upload' },
  ]

  const cache:Record<ImageSource, DataList> = {
    suite: [],
    upload: []
  }

  const dataSource = computed({
    get: () => (props.suiteDisabled ? 'upload' : props.source),
    set: (source:ImageSource) => {
      emit('update:source', source)
    }
  })

  const dataValue = computed({
    get: () => props.modelValue,
    set: (data:DataList) => {
      emit('update:modelValue', data)
    }
  })

  // 拖拽结束
  const onDragEnd = () => {
    isDragging.value = false
  }

  // 图片点击
  const imageClickHandler = (img: DataItem) => {
    const src = img.link || img.base64
    if (src) {
      viewImgs([src])
    }
  }

  // 图片剪切
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const doCrop = async (item: DataItem) => {
  // if (props.readonly) return
  // if (!item.ratioError) return
  // const cropRes = await imageCrop(item.base64 || item.link, firstAspectRatio.value)
  // if (!cropRes) return
  // const { file } = cropRes
  // await uploadImageJob({
  //   id: item.id || uniqueId(),
  //   file
  // })
  // onComplete()
  }

  // 删除图片
  const onRemoveImage = (item: DataItem) => {
    dataValue.value = dataValue.value.filter(f => f.id !== item.id)
  }

  // 选择营地图片
  const chooseImage = async () => {
    const res = await getSuiteImages({ poiId: props.poiId, images: dataValue.value.map(item => item.link) })
    dataValue.value = res.map(item => ({ link: item }))
  }

  const changeSource = () => {
    const source = props.source
    const oldSource = source === 'suite' ? 'upload' : 'suite'
    cache[oldSource] = props.modelValue
    emit('update:modelValue', cache[source])
  // 自动弹出选择窗口
  // if (source === 'suite' && !cache[source].length) {
  //   chooseImage()
  // }
  }

</script>

<style lang="stylus" scoped>
.image-list
  display flex
  flex-wrap wrap
.image-item, .upload-input-item
  position relative
  width 104px
  height 104px
  margin-right: 12px
  margin-bottom: 12px
.remove-icon
  position absolute
  top 0
  right 0
.img
  position relative
  height 100%
  width 100%
  border-radius 4px
  object-fit cover
.draggable
  cursor move
.error-icon
  position absolute
  top 50%
  left 50%
  transform translate(-50%, -50%)
.image-wrapper
  position relative
  width 104px
  height 104px
  background rgba(0, 0, 0, 0.15);
  border: 1px solid transparent;
  box-sizing: border-box;
  border-radius: 4px;
  &.image-error
    border-color #FF525D
.button-wrap
  height 102px
  display flex
  align-items flex-end
</style>
