import {
  defineComponent, onUnmounted, onMounted, ref, createApp
} from 'vue'
import { Lazyload } from '@vant/lazyload'

import { SuiteImageInfo } from '@/types/poi'

import { getPoiSuiteImages } from '@/services/poi'
import loading from '@/utils/loading'
import SuiteImagesModal from './index.vue'

interface Options {
  poiId: string
  images: string[]
}

function getPoi(options:Options) {
  return new Promise<string[]>((resolve, reject) => {
    const div = document.createElement('div')
    document.body.appendChild(div)

    const Wrapper = defineComponent({
      setup() {
        const visible = ref(false)
        const suiteImageInfo = ref<SuiteImageInfo[]>([])

        const onCancel = () => {
          if (div.parentNode) {
            div.parentNode.removeChild(div)
          }
          reject('取消选择图片')
        }

        const onOk = (images: string[]) => {
          resolve(images)
          onCancel()
        }

        const keyupCancel = (e: KeyboardEvent) => {
          if (e.code === 'Escape') {
            onCancel()
          }
        }

        onMounted(async () => {
          window.addEventListener('keyup', keyupCancel)
          loading.show()
          try {
            suiteImageInfo.value = await getPoiSuiteImages(options.poiId)
            visible.value = true
          } catch (err) {
            // eslint-disable-next-line no-console
            console.error(err)
          }
          loading.close()
        })

        onUnmounted(() => {
          window.removeEventListener('keyup', keyupCancel)
        })

        const props = {
          ...options,
          onOk,
          'onUpdate:visible': onCancel,
        }

        // @ts-ignore
        return () => <SuiteImagesModal suiteImageInfo={suiteImageInfo.value} visible={visible.value} {...props} />
      }
    })

    const app = createApp(Wrapper)
    app.use(Lazyload, {
      error: 'https://ci.xiaohongshu.com/e25d01dd-e150-4003-9b8c-81ab3fb69dc1@r_640w_640h.jpg',
      loading: 'https://ci.xiaohongshu.com/e25d01dd-e150-4003-9b8c-81ab3fb69dc1@r_640w_640h.jpg',
    })
    app.mount(div)
  })
}

export default getPoi
