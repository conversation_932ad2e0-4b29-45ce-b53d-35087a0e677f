<template>
  <BeerModal
    v-model:show="show"
    :bs="{ width: 800 }"
    title="选择商家营地图片"
    :width="500"
    @confirm="confirm"
    @close="close"
  >
    <div v-for="suite in data" :key="suite.suiteId" class="suite-card">
      <h4 class="suite-name">{{ suite.suiteName }}</h4>
      <div class="plain-text">商家: {{ suite.sellerName }}</div>
      <div class="images">
        <div v-for="(img, key) in suite.images" :key="key" class="image-box" :class="{active: img.isActive}">
          <Image style="width: 104px;height: 104px" :src="img.path" />
          <div class="actions-wrap">
            <BeerCheckbox :model-value="img.isActive" @update:modelValue="toggleChoose(img.path)" />
            <!-- <span :class="{primary: img.isFirst}" class="btn" @click="setFirstImage(img.path)">主图</span> -->
            <!-- <span v-if="!img.isFirst" :class="{primary: img.isActive}" class="btn" @click="toggleChoose(img.path)">选择</span> -->
          </div>
        </div>
      </div>
    </div>
  </BeerModal>
</template>

<script lang="tsx" setup>
  import { computed, PropType, ref } from 'vue'
  import { BeerModal, BeerCheckbox } from '@xhs/yam-beer'

  import { SuiteImageInfo } from '@/types/poi'

  import Image from '@/components/base-image/index.vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    suiteImageInfo: {
      type: Array as PropType<SuiteImageInfo[]>,
      default: () => []
    },
    images: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    onCancel: {
      type: Function as PropType<() => void>
    },
    onOk: {
      type: Function as PropType<(images: string[]) => void>,
    },
  })

  const emit = defineEmits(['update:visible'])

  const show = computed({
    get: () => props.visible,
    set: (newShow:boolean) => {
      emit('update:visible', newShow)
    }
  })

  const newImages = ref(props.images)

  const data = computed(() => {
    const res = props.suiteImageInfo.map(suite => ({
      ...suite,
      images: suite.images.map(item => ({
        ...item,
        isFirst: item.path === newImages.value[0],
        isActive: newImages.value.includes(item.path)
      }))
    }))
    return res
  })

  // const setFirstImage = (link: string) => {
  //   if (newImages.value.includes(link)) {
  //     newImages.value = newImages.value.filter(img => img !== link)
  //   } else {
  //     newImages.value = [link, ...newImages.value.filter(img => img !== link)]
  //   }
  // }

  const toggleChoose = (link: string) => {
    if (newImages.value.includes(link)) {
      newImages.value = newImages.value.filter(img => img !== link)
    } else {
      newImages.value.push(link)
    }
  }

  const confirm = () => {
    props.onOk?.([...newImages.value])
  }

  const close = () => {
    props.onCancel?.()
  }

</script>

<style lang="stylus" scoped>
.suite-card
  padding-bottom 20px
.suite-name
  margin 0
  line-height 1.5
.plain-text
  padding 0.5em 0
.images
  display flex
  flex-wrap wrap
.image-box
  width 104px
  height 104px
  margin-right 8px
  position relative
  background-color #f6f6f6
  &:hover .actions-wrap, &.active .actions-wrap
    display flex
.actions-wrap
  position absolute
  top 0
  left 10px
  width 32px
  height 32px
.setting-btn
  position absolute
  left 50%
  top 0
  transform translate(-50%, 0)
.btn
  padding 8px
  cursor pointer
.primary
  color #3a64ff
</style>
