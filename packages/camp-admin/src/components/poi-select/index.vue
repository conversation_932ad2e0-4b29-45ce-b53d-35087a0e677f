<template>
  <MultiSelect
    :placeholder="placeholder"
    :model-value="props.modelValue"
    :get-list="search"
    :fixed="true"
    :filterable="true"
    @update:model-value="emit('update:modelValue', $event)"
  />
</template>

<script lang="tsx" setup>
//  :option-params="{
//       // @ts-ignore
//       itemFactory: v => v,
//       // @ts-ignore
//       itemToKey: v => v.name,
//       // @ts-ignore
//       itemToValue: v => v
//     }"
  import { PropType } from 'vue'
  import { MultiSelect } from '@xhs/yam-beer'

  import { PoiType } from '@/types/poi'
  import * as Api from '@/services/poi'

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    selectedOptions: {
      type: Array as PropType<{ name: string; value: string }[]>,
      default: () => []
    },
    type: {
      type: String as PropType<PoiType | ''>,
      default: ''
    },
    placeholder: {
      type: String,
      default: '输入关键词模糊搜索'
    }
  })

  const emit = defineEmits(['update:modelValue'])

  const search = async (value: string) => {
    const { list } = await Api.list({
      poiName: value,
      pageSize: 10,
      pageNum: 1,
      type: props.type,
      poiId: '',
      isUpgrade: ''
    })
    const selectedValues = props.selectedOptions.map(item => item.value)
    const newData = list.map((item: { poiName: any; poiId: any }) => ({
      name: item.poiName,
      value: item.poiId,
    })).filter(item => !selectedValues.includes(item.value))

    return {
      data: [...props.selectedOptions, ...newData]
    }
  }

</script>

<style>
</style>
