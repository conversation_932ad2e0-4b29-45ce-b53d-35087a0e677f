<template>
  <Pagination v-if="total" v-model="pageInfo" :show-pager-option="showPagerOption" style="margin-top: 15px" />
</template>

<script lang="tsx" setup>
  import { PropType, computed } from 'vue'
  import { Pagination } from '@xhs/yam-beer'

  interface Payload {
    pageNum: number | string
    pageSize: number | string
  }

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Payload>,
      default: () => ({ pageNum: 1, pageSize: 20 })
    },
    total: {
      type: Number,
      default: 0
    },
    showPagerOption: {
      type: Boolean,
      default: true
    }
  })

  const emit = defineEmits(['change', 'update:modelValue'])

  const pageInfo = computed({
    get: () => ({
      pageNo: Number(props.modelValue.pageNum),
      pageSize: Number(props.modelValue.pageSize),
      total: Number(props.total)
    }),
    set: ({ pageNo, pageSize }) => {
      const payload = { ...props.modelValue, pageNum: pageNo, pageSize }
      emit('update:modelValue', payload)
      emit('change', payload)
      window.scrollTo(0, 0)
      const scrollDiv = document.querySelector('#scroll-page-container')
      if (scrollDiv) {
        scrollDiv.scrollTop = 0
      }
    }
  })

</script>

<style>
</style>
