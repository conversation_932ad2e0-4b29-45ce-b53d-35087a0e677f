<template>
  <NewBsBox class="beco-page-layout-wrapper">
    <BecoLayoutHeader @logo-click="goIndex">
      <template #header-logo>
        <svg
          viewBox="0 0 24 24"
          role="img"
          class="beco-page-header-logo"
        >
          <path
            d="M16.892 11.588l.001-.005v-.008c0-.005 0-.01.002-.013v-.638a.127.127 0 00-.094-.118.214.214 0 00-.069-.009h-.42c-.02 0-.03 0-.035.005-.005.005-.005.015-.005.036l.005.717v.033a.013.013 0 00.014.013h.569l.02-.004.006-.001a.014.014 0 00.004-.003.013.013 0 00.002-.005z"
            clip-rule="evenodd"
            fill-rule="evenodd"
          />
          <path
            d="M7 7a5 5 0 000 10h10a5 5 0 000-10H7zm11.698 4.96c.************.139.336a1.2 1.2 0 01.015.22v1.139a.778.778 0 01-.558.755 1.179 1.179 0 01-.235.043c-.083.006-.294.006-.38.006h-.292a.025.025 0 01-.023-.015v-.006c-.055-.12-.322-.729-.323-.743 0-.015.003-.019.015-.019h.698c.126 0 .19-.062.19-.21v-.682a.264.264 0 00-.158-.235.367.367 0 00-.16-.034h-1.329c-.015 0-.019.008-.019.02v1.88c0 .025.002.037-.004.042-.005.005-.017.005-.042.005h-.822c-.025 0-.037 0-.042-.005-.006-.005-.005-.017-.005-.042V12.56c0-.025.001-.036-.004-.041-.005-.005-.016-.004-.04-.004h-.833c-.005 0-.01 0-.016-.002a.033.033 0 01-.02-.022.033.033 0 01-.001-.016v-.83s-.004-.048.034-.048h.85c.035 0 .035-.067.035-.067v-.665s.006-.074-.041-.074h-.523c-.023 0-.035.002-.04-.004-.006-.005-.006-.016-.006-.042v-.852c0-.013.003-.017.016-.017h.575a.013.013 0 00.014-.013v-.312a.016.016 0 01.005-.013c.004-.004.013-.004.013-.004h.875s.016 0 .02.004a.026.026 0 01.006.019v.283c0 .019 0 .028.005.033.004.005.014.005.034.005h.413c.2.003.394.054.57.148.17.091.305.235.384.41.045.097.074.2.088.306.01.075.015.15.014.227v.603c0 .023.002.023.024.023h.103c.177.002.35.045.506.127.11.056.204.138.276.238zM14.5 13.55l.001.005v.887c0 .015-.003.019-.02.019h-3.009c-.02 0-.023 0-.026-.005-.004-.006.004-.02.004-.02l.4-.874a.034.034 0 01.033-.017h.748c.026 0 .038.002.044-.004.005-.005.004-.017.004-.044v-2.663c0-.02 0-.03-.004-.035-.005-.005-.015-.005-.036-.005h-.49c-.023 0-.034 0-.04-.005-.005-.005-.005-.016-.005-.039v-.83c0-.043.005-.042.035-.042h2.013c.025 0 .036-.001.041.004.005.005.004.016.004.039v.831c0 .021 0 .032-.004.037-.005.006-.016.006-.039.006h-.494c-.021 0-.031 0-.036.004-.005.005-.004.015-.004.036v2.68c0 .028 0 .028.028.028h.84l.005.001c.002 0 .003.002.004.003a.014.014 0 01.003.004zM6.888 9.59h.866a.018.018 0 01.018.016v.474c.002.464.004 1.151.004 1.57v1.976a1.1 1.1 0 01-.043.307.696.696 0 01-.341.442.743.743 0 01-.253.076 5.705 5.705 0 01-.538.008.033.033 0 01-.03-.02l-.085-.193a9.274 9.274 0 01-.23-.532c-.002-.027 0-.027.023-.027h.424a.14.14 0 00.15-.09.14.14 0 00.01-.062v-.534c0-.733.003-1.96.004-2.726l.002-.668a.019.019 0 01.019-.018zm4.336 2.838h.498a.015.015 0 01.014.005.015.015 0 01.003.007l-.001.008-.334.736a.014.014 0 01-.013.009h-.949a.596.596 0 01-.194-.032.392.392 0 01-.078-.037.27.27 0 01-.131-.228.293.293 0 01.012-.1 2.3 2.3 0 01.08-.22l.134-.303c.121-.27.273-.611.264-.611h-.338a.573.573 0 01-.19-.033.397.397 0 01-.074-.035.266.266 0 01-.131-.217.322.322 0 01.012-.11c.02-.072.046-.142.076-.21a5.48 5.48 0 01.152-.33l.53-1.19.006-.006a.013.013 0 01.008-.002h.892a.015.015 0 01.012.007.015.015 0 010 .013l-.5 1.126a.085.085 0 00.079.12h.754l.007.002a.015.015 0 01.007.012v.007l-.67 1.51a.072.072 0 00.03.092c.01.006.021.01.033.01zm-1.79.176a.834.834 0 00.088.377c0 .007-.004.014-.016.035l-.165.367c-.116.26-.256.573-.287.637l-.003.006c0 .003-.001.004-.005.005-.005.001-.015-.012-.023-.023a3.002 3.002 0 01-.413-.912 3.54 3.54 0 01-.08-.421 140.685 140.685 0 01-.067-.837l-.015-.198a30.938 30.938 0 01-.022-.288 54.046 54.046 0 00-.042-.54c-.001-.016 0-.02.018-.02h.856c.015 0 .036 0 .036.005a22.29 22.29 0 01.014.165l.011.159a107.627 107.627 0 01.062.789l.013.177.007.1.02.252.005.049c.004.038.008.077.008.116zm-4.048-1.812h.85c.015 0 .017.003.016.02a15.7 15.7 0 00-.007.084l-.006.085a205.5 205.5 0 01-.006.07l-.024.301a31.349 31.349 0 00-.021.288l-.016.198-.015.198-.035.45-.003.041a2.746 2.746 0 01-.096.566 2.96 2.96 0 01-.198.56c-.058.123-.13.24-.213.348a.1.1 0 01-.027.025c-.003 0-.005-.002-.01-.012a42.41 42.41 0 01-.274-.608 30.656 30.656 0 00-.176-.392c-.016-.032-.015-.032-.007-.049l.007-.014a.822.822 0 00.078-.349l.009-.132a18.334 18.334 0 00.012-.148l.011-.138.01-.13.01-.146.01-.118.012-.154c0-.016.002-.032.003-.047a22.317 22.317 0 01.02-.269l.017-.201.01-.134.01-.143a.988.988 0 01.005-.046c0-.004.001-.004.006-.004h.038zm5.001 2.748h.965a.015.015 0 **************.015 0 *************.015 0 01-.004.009l-.408.885a.014.014 0 01-.013.009H9.814a.706.706 0 01-.283-.071.014.014 0 01-.006-.019l.406-.891a.014.014 0 01.007-.008.014.014 0 01.011 0c.*************.438.071zm7.567-2.755h-.005a.01.01 0 01-.007-.007v-.452a.457.457 0 11.842.246.458.458 0 01-.386.213h-.444z"
            clip-rule="evenodd"
            fill-rule="evenodd"
          />
        </svg>
        <NewBsBox class="beco-page-header-name">本地生活</NewBsBox>
        <NewBsBox class="beco-page-header-line" />
        <NewBsBox class="beco-page-header-desc">运营管理后台</NewBsBox>
      </template>
      <template #header-main>
        <NewBsBox class="left-header" />
      </template>
      <template #header-userinfo>
        <NewBsBox style="position: relative">
          <NewBsBox
            class="avatar"
            @click.stop="showlogout = true"
          >
            <img
              class="avatar-img"
              src="https://fe-img-qc.xhscdn.com/559215fe936e5294d1d9af18f29290f664825b84"
            />
            <span class="avatar-nickname">{{ userInfo?.name || '' }}</span>
            <img
              class="avatar-arr"
              src="https://picasso-static.xiaohongshu.com/fe-platform/41f82c42e5b07f8e13164a702ee8ee14a502b612.png"
            />
          </NewBsBox>

          <ul
            v-if="showlogout"
            class="operations"
          >
            <li
              class="operat-item"
              @click="handleLogout"
            >退出登录</li>
          </ul>
        </NewBsBox>
      </template>
    </BecoLayoutHeader>

    <NewBsBox class="beco-page-layout-main">
      <template v-if="menuConfig?.list?.length">
        <BecoLayoutMenu :config="menuConfig" />
        <NewBsBox id="scroll-page-container" class="source-type">
          <BecoLayoutMain :bs="{ p: 24 }" />
        </NewBsBox>
      </template>
      <template v-else>
        <NewBsBox class="content-type">
          <BecoLayoutMain :bs="{ pt: 24 }" />
        </NewBsBox>
      </template>
    </NewBsBox>
  </NewBsBox>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import { NewBsBox } from '@xhs/yam-beer'
  import { BecoLayoutHeader, BecoLayoutMenu, BecoLayoutMain } from '@xhs/becoui'
  import '@xhs/becoui/src/style/beco-page-layout.styl'
  import menus from '@/config/menus.config'
  import watermark from '@/utils/watermark'
  import app from '../index'
  import { logout } from '../services/user'

  const router = useRouter()
  const store = useStore()
  const userInfo = app?.auth?.userInfo

  /**
   * 是否展示菜单（在routes.config.ts的meta字段中配置showMenu，默认true
   */
  const layoutType = computed(() => {
    const currentRoute = router.currentRoute.value
    return currentRoute.meta.showMenu === false ? 2 : 1
  })

  // 根据角色权限设置左侧栏
  const roleCodes = userInfo?.roleCodes || []

  // 菜单配置
  const menuConfig = computed(() => {
    const permissions:string[] = store.state.Auth?.userInfo?.permissions || []
    const permissionsMap = permissions.reduce((acc, key) => {
      acc[key] = true
      return acc
    }, {} as Record<string, true>)
    const list = layoutType.value === 1 ? menus.map(m => ({ ...m, id: m.text })) : []
    return {
      list: list.filter(item => item.auth.some(auth => permissionsMap[auth])),
      mutualExclude: false,
      isCollapsible: false,
    }
  })

  if (userInfo) {
    watermark(`小红书-${userInfo?.name || ''}`)
    if (roleCodes?.indexOf('maintain') > -1) {
      // 管理员
      userInfo.isManager = true
    }
    store.commit('user/setUserInfo', userInfo)
  }
  const showlogout = ref(false)
  document.body.addEventListener('click', () => {
    showlogout.value = false
  })
  const handleLogout = () => {
    logout().finally(() => {
      router.push({
        name: 'Index',
      })
    })
  }

  // 回主页
  const goIndex = () => {
    router
      .push({
        name: 'Index',
      })
      .then(() => {
        window.location.reload()
      })
  }
</script>

<style lang="stylus" scoped>
.beco-page-layout-wrapper
  :deep(.beco-layout-menu)
    width 180px !important
.beco-page-header-logo
  width 72px
  height 72px
  fill rgb(255, 36, 66)
.beco-page-header-name
  font-family PingFang SC
  font-weight 600
  font-size 20px
  line-height 20px
  color #2D2D2D
.beco-page-header-line
  margin-left 8px
  width 1px
  height 12px
  opacity 0.5
  border-left 1px solid rgba(0, 0, 0, 0.45)
.beco-page-header-desc
  margin-left 8px
  height 20px
  font-family PingFang SC
  font-style normal
  font-weight 500
  font-size 16px
  line-height 20px
  color rgba(51, 51, 51, 0.6)
.beco-page-layout-wrapper
  .left-header
    display flex
    justify-content space-between
  .avatar
    font-size 14px
    display flex
    align-items center
  .avatar-img
    display block
    width 28px
    height 28px
    border-radius 50%
    margin-right 4px
    cursor pointer
  .avatar-nickname
    cursor pointer
    margin-right 4px
    font-family PingFang SC
    font-style normal
    font-weight normal
    font-size 14px
    line-height 14px
    color rgba(0, 0, 0, 0.85)
  .avatar-arr
    display block
    width 16px
    height 16px
    cursor pointer
  .operations
    position absolute
    top 100%
    right -10px
    z-index 100
    min-width 100px
    background-color white
    box-shadow 0px 2px 8px 0px rgba(0, 15, 78, 0.1)
    border-radius 4px
    padding 8px 0
    &::before
      content ''
      position absolute
      top 0
      right 32px
      transform translateY(-100%)
      border 7px solid #fff
      border-left-color transparent
      border-right-color transparent
      border-top-color transparent
    .operat-item
      height 30px
      display flex
      align-items center
      justify-content center
      font-size 15px
      cursor pointer
      &:hover
        color #409eff
        background-color #ecf5ff
  .beco-page-layout-main
    .source-type
      flex 1
      overflow auto
    .content-type
      display flex
      width 100%
      justify-content center
      min-width 1200px
      padding-top 0
      margin 0 120px
      @media (max-width 1440px)
        width 1200px
        margin 0 auto
</style>
