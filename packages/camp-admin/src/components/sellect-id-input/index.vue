<template>
  <div class="seller-input">
    <Input
      v-model="value"
      :disabled="disabled"
      :placeholder="placeholder || '请输入商家sellerId'"
      @blur="blur"
    />
    <Text :loading="loading">
      {{ loading ? '搜索...' : sellerName }}
    </Text>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { Input, Text, toast } from '@xhs/delight'

  import { fetchSellerList } from '@/services/poiClaim'

  const props = defineProps<{
    modelValue?: string
    placeholder?: string
    sellerName?: string
    disabled?: boolean
  }>()

  const emit = defineEmits(['update:modelValue', 'update:sellerName'])

  const sellerName = ref('')
  const loading = ref(false)
  const realSellerLength = 24

  const value = computed({
    get: () => props.modelValue,
    set: e => {
      emit('update:modelValue', e)
    }
  })

  const blur = () => {
    if (value.value?.length !== realSellerLength) {
      if (!props.disabled) {
        toast.info('请检查sellerId是否正确')
      }
      sellerName.value = ''
      emit('update:sellerName', '')
    }
  }

  const search = async () => {
    loading.value = true

    try {
      const res = await fetchSellerList(
        {
          pageNo: 1,
          pageSize: 1,
          sellerId: value.value,
        },
        true
      )
      sellerName.value = res.sellerInfoList?.[0]?.publicName
      emit('update:sellerName', sellerName.value)
    } finally {
      loading.value = false
    }
  }

  watch(
    () => props.modelValue,
    () => {
      if (value.value?.length === realSellerLength) {
        search()
      } else {
        sellerName.value = ''
        emit('update:sellerName', sellerName.value)
      }
    },
    {
      immediate: true,
    }
  )

</script>

<style lang="stylus" scoped>
.seller-input
  display flex
  flex-direction column
  gap 10px
</style>
