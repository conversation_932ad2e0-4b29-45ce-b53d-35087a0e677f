<template>
  <ButtonGroup v-model="value" class="button-group" must-choose :options="computedOptions" />
</template>

<script lang="tsx" setup>
  import { computed, PropType } from 'vue'
  import { ButtonGroup } from '@xhs/yam-beer'

  interface Option {
    name: string
    value: string
  }

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    options: {
      type: Array as PropType<Option[]>,
      default: () => []
    }
  })

  const emit = defineEmits(['update:modelValue', 'change'])

  const computedOptions = computed(() => props.options.map(item => ({ name: item.name } as any)))
  const value = computed({
    get: () => props.options.find(item => item.value === props.modelValue)?.name || '',
    set: name => {
      const newValue = props.options.find(item => item.name === name)?.value || ''
      emit('update:modelValue', newValue)
      emit('change', newValue)
    },
  })

</script>

<style lang="stylus" scoped>
.button-group
  border: 1px solid rgb(217, 217, 217)
  border-radius: 4px
  ::v-deep(button:not(:last-child))
    border-right: 1px solid rgb(217, 217, 217)
</style>
