import { ActionTree, MutationTree, Module } from 'vuex'
import { IRootState } from '@xhs/launcher-plugin-store'

import { IUserInfo } from '../../types/user'

export interface IUserState {
  userInfo: IUserInfo | null
}

const stateInit: IUserState = {
  userInfo: null,
}

const mutations: MutationTree<IUserState> = {
  setUserInfo(state, userInfo: IUserInfo) {
    if (userInfo) {
      state.userInfo = userInfo
    } else {
      state.userInfo = null
    }
  },
}

const actions: ActionTree<IUserState, IRootState> = {}

const userModule: Module<IUserState, IRootState> = {
  namespaced: true,
  state: stateInit,
  actions,
  mutations,
}

export default userModule
