// 时间选择器的值类型
export interface TimePickerValue {
  /** 有效期类型：permanent-永久有效，limited-期限有效 */
  type: 'permanent' | 'limited'
  /** 选择的日期，当type为limited时必填 */
  date?: string | number | Date
  /** 时间戳，当type为limited时自动生成 */
  timestamp?: number
}

// 时间选择器的属性类型
export interface TimePickerProps {
  /** 双向绑定的值 */
  modelValue?: TimePickerValue | null
  /** 是否只读 */
  readOnly?: boolean
  /** 占位符文本 */
  placeholder?: string
  /** 弹窗标题 */
  label?: string
  /** 颜色模式 */
  colorMode?: 'light' | 'dark'
  /** 日期格式化字符串 */
  dateFormat?: string
}

// 时间选择器的事件类型
export interface TimePickerEmits {
  /** 值更新事件 */
  'update:modelValue': [value: TimePickerValue | null]
  /** 值变化事件 */
  'change': [value: TimePickerValue | null]
}

// 有效期类型枚举
export enum ValidityType {
  /** 永久有效 */
  PERMANENT = 'permanent',
  /** 期限有效 */
  LIMITED = 'limited'
}

// 默认配置
export const DEFAULT_CONFIG = {
  placeholder: '请选择有效期',
  label: '选择有效期',
  colorMode: 'light' as const,
  dateFormat: 'YYYY-MM-DD'
}
