<template>
  <div class="example-page">
    <Header title="时间选择器使用示例" />
    
    <main class="example-content">
      <!-- 表单示例 -->
      <section class="example-section">
        <h3 class="section-title">表单集成示例</h3>
        <ConfigProvider :color-mode="colorMode">
          <Form :model="formData" :rules="formRules">
            <FormItem
              label="商品有效期"
              name="validityPeriod"
              required
              :required-style="{color: 'red'}"
            >
              <TimePicker
                v-model="formData.validityPeriod"
                placeholder="请选择商品有效期"
                @change="handleValidityChange"
              />
            </FormItem>
            
            <FormItem
              label="活动有效期"
              name="activityPeriod"
              required
              :required-style="{color: 'red'}"
            >
              <TimePicker
                v-model="formData.activityPeriod"
                placeholder="请选择活动有效期"
                date-format="YYYY年MM月DD日"
                label="选择活动有效期"
              />
            </FormItem>
            
            <FormItem
              label="查看已设置的期限"
              name="readOnlyPeriod"
            >
              <TimePicker
                v-model="formData.readOnlyPeriod"
                :read-only="true"
                placeholder="只读状态"
              />
            </FormItem>
          </Form>
        </ConfigProvider>
      </section>

      <!-- 自定义样式示例 -->
      <section class="example-section">
        <h3 class="section-title">自定义样式示例</h3>
        <div class="custom-style-demo">
          <TimePicker
            v-model="customStyleValue"
            placeholder="自定义样式"
            class="custom-time-picker"
          >
            <template #display="{ value, formatted }">
              <div class="custom-display-card">
                <div class="card-header">
                  <span class="card-icon">⏰</span>
                  <span class="card-title">有效期设置</span>
                </div>
                <div class="card-content">
                  <span v-if="formatted" class="card-value">{{ formatted }}</span>
                  <span v-else class="card-placeholder">点击设置有效期</span>
                </div>
                <div v-if="value && value.type === 'limited'" class="card-footer">
                  <span :class="['status-badge', isExpired(value) ? 'expired' : 'valid']">
                    {{ isExpired(value) ? '已过期' : '有效' }}
                  </span>
                </div>
              </div>
            </template>
          </TimePicker>
        </div>
      </section>

      <!-- 业务场景示例 -->
      <section class="example-section">
        <h3 class="section-title">业务场景示例</h3>
        <div class="business-demo">
          <div class="demo-item">
            <h4>优惠券有效期</h4>
            <TimePicker
              v-model="couponPeriod"
              placeholder="设置优惠券有效期"
              @change="handleCouponChange"
            />
            <div v-if="couponPeriod" class="demo-result">
              <p><strong>设置结果:</strong></p>
              <ul>
                <li>类型: {{ couponPeriod.type === 'permanent' ? '永久有效' : '期限有效' }}</li>
                <li v-if="couponPeriod.type === 'limited'">
                  到期时间: {{ formatDate(couponPeriod.date!) }}
                </li>
                <li v-if="couponPeriod.timestamp">
                  时间戳: {{ couponPeriod.timestamp }}
                </li>
                <li>状态: {{ isExpired(couponPeriod) ? '已过期' : '有效' }}</li>
              </ul>
            </div>
          </div>
          
          <div class="demo-item">
            <h4>会员权益有效期</h4>
            <TimePicker
              v-model="membershipPeriod"
              placeholder="设置会员权益有效期"
              date-format="YYYY/MM/DD"
            />
          </div>
        </div>
      </section>

      <!-- 操作按钮 -->
      <section class="example-section">
        <h3 class="section-title">快捷操作</h3>
        <div class="action-buttons">
          <Button @click="setAllPermanent">全部设为永久</Button>
          <Button @click="setAllLimited">全部设为期限（明天）</Button>
          <Button @click="clearAll">清空所有</Button>
          <Button @click="showFormData">查看表单数据</Button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import dayjs from 'dayjs'
  
  // 组件库
  import {
    Form,
    FormItem,
    Button,
    ConfigProvider
  } from '@xhs/reds-h5-next'
  
  // 本地组件
  import TimePicker from './index.vue'
  import Header from '~/components/header/index.vue'
  import { timePickerUtils } from './composable'
  import type { TimePickerValue } from './types'
  
  // 响应式数据
  const colorMode = ref<'light' | 'dark'>('light')
  
  const formData = reactive({
    validityPeriod: null as TimePickerValue | null,
    activityPeriod: null as TimePickerValue | null,
    readOnlyPeriod: timePickerUtils.createLimitedValue(dayjs().add(30, 'day').toDate())
  })
  
  const formRules = {
    validityPeriod: [
      { required: true, message: '请选择商品有效期' }
    ],
    activityPeriod: [
      { required: true, message: '请选择活动有效期' }
    ]
  }
  
  const customStyleValue = ref<TimePickerValue | null>(null)
  const couponPeriod = ref<TimePickerValue | null>(null)
  const membershipPeriod = ref<TimePickerValue | null>(null)
  
  // 工具函数
  const { isExpired, createPermanentValue, createLimitedValue, formatDisplayText } = timePickerUtils
  
  const formatDate = (date: string | number | Date) => {
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
  }
  
  // 事件处理
  const handleValidityChange = (value: TimePickerValue | null) => {
    console.log('商品有效期变化:', value)
    if (value && isExpired(value)) {
      alert('警告：选择的日期已过期！')
    }
  }
  
  const handleCouponChange = (value: TimePickerValue | null) => {
    console.log('优惠券有效期变化:', value)
  }
  
  // 快捷操作
  const setAllPermanent = () => {
    const permanentValue = createPermanentValue()
    formData.validityPeriod = permanentValue
    formData.activityPeriod = permanentValue
    customStyleValue.value = permanentValue
    couponPeriod.value = permanentValue
    membershipPeriod.value = permanentValue
  }
  
  const setAllLimited = () => {
    const tomorrow = dayjs().add(1, 'day').toDate()
    const limitedValue = createLimitedValue(tomorrow)
    formData.validityPeriod = limitedValue
    formData.activityPeriod = limitedValue
    customStyleValue.value = limitedValue
    couponPeriod.value = limitedValue
    membershipPeriod.value = limitedValue
  }
  
  const clearAll = () => {
    formData.validityPeriod = null
    formData.activityPeriod = null
    customStyleValue.value = null
    couponPeriod.value = null
    membershipPeriod.value = null
  }
  
  const showFormData = () => {
    console.log('表单数据:', formData)
    alert(`表单数据:\n${JSON.stringify(formData, null, 2)}`)
  }
</script>

<style lang="stylus" scoped>
.example-page
  min-height 100vh
  background #f5f5f5

.example-content
  padding 16px

.example-section
  margin-bottom 24px
  background #fff
  border-radius 12px
  padding 16px

.section-title
  color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
  font-size 18px
  font-weight 600
  margin 0 0 16px 0

.custom-style-demo
  .custom-time-picker
    margin-bottom 16px

.custom-display-card
  background linear-gradient(135deg, #667eea 0%, #764ba2 100%)
  border-radius 16px
  padding 20px
  color white
  box-shadow 0 8px 32px rgba(0, 0, 0, 0.1)

.card-header
  display flex
  align-items center
  margin-bottom 12px

.card-icon
  font-size 20px
  margin-right 8px

.card-title
  font-size 16px
  font-weight 600

.card-content
  margin-bottom 12px

.card-value
  font-size 18px
  font-weight 500

.card-placeholder
  font-size 16px
  opacity 0.8

.card-footer
  display flex
  justify-content flex-end

.status-badge
  padding 4px 12px
  border-radius 20px
  font-size 12px
  font-weight 500
  
  &.valid
    background rgba(76, 175, 80, 0.2)
    color #4CAF50
  
  &.expired
    background rgba(244, 67, 54, 0.2)
    color #F44336

.business-demo
  .demo-item
    margin-bottom 20px
    
    &:last-child
      margin-bottom 0
    
    h4
      color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
      font-size 16px
      font-weight 500
      margin 0 0 12px 0

.demo-result
  margin-top 12px
  padding 12px
  background #f8f8f8
  border-radius 8px
  
  p
    margin 0 0 8px 0
    font-weight 500
  
  ul
    margin 0
    padding-left 16px
    
    li
      margin-bottom 4px
      font-size 14px
      color var(--Light-Labels-Secondary, rgba(0, 0, 0, 0.60))

.action-buttons
  display flex
  gap 8px
  flex-wrap wrap
  
  .reds-button
    flex 1
    min-width 80px
</style>
