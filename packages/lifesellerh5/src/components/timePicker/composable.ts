import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import type { TimePickerValue, TimePickerProps } from './types'

/**
 * 时间选择器组合式函数
 * @param props 组件属性
 * @param emits 组件事件
 */
export function useTimePicker(
  props: TimePickerProps,
  emits: {
    (e: 'update:modelValue', value: TimePickerValue | null): void
    (e: 'change', value: TimePickerValue | null): void
  }
) {
  // 响应式状态
  const visible = ref(false)
  const datePickerVisible = ref(false)
  const validityType = ref<'permanent' | 'limited'>('permanent')
  const selectedDate = ref<string | number | Date | null>(null)

  // 计算属性
  const displayValue = computed(() => {
    return props.modelValue
  })

  const formattedValue = computed(() => {
    if (!props.modelValue) return ''
    
    if (props.modelValue.type === 'permanent') {
      return '永久有效'
    } else if (props.modelValue.type === 'limited' && props.modelValue.date) {
      return `有效期至 ${formatDate(props.modelValue.date)}`
    }
    
    return ''
  })

  const isValid = computed(() => {
    if (!props.modelValue) return false
    
    if (props.modelValue.type === 'permanent') return true
    
    return props.modelValue.type === 'limited' && !!props.modelValue.date
  })

  // 工具方法
  const formatDate = (date: string | number | Date) => {
    return dayjs(date).format(props.dateFormat || 'YYYY-MM-DD')
  }

  const createTimePickerValue = (): TimePickerValue | null => {
    if (validityType.value === 'permanent') {
      return {
        type: 'permanent'
      }
    } else if (validityType.value === 'limited' && selectedDate.value) {
      const timestamp = dayjs(selectedDate.value).valueOf()
      return {
        type: 'limited',
        date: selectedDate.value,
        timestamp
      }
    }
    
    return null
  }

  // 事件处理
  const handleDisplayClick = () => {
    if (props.readOnly) return
    
    visible.value = true
    
    // 初始化选择状态
    if (props.modelValue) {
      validityType.value = props.modelValue.type
      selectedDate.value = props.modelValue.date || null
    } else {
      validityType.value = 'permanent'
      selectedDate.value = null
    }
  }

  const handleCancel = () => {
    visible.value = false
    datePickerVisible.value = false
  }

  const handleConfirm = () => {
    const newValue = createTimePickerValue()
    
    if (newValue || validityType.value === 'permanent') {
      emits('update:modelValue', newValue)
      emits('change', newValue)
    }
    
    visible.value = false
  }

  const handleValidityTypeChange = (value: 'permanent' | 'limited') => {
    validityType.value = value
    if (value === 'permanent') {
      selectedDate.value = null
    }
  }

  const showDatePicker = () => {
    datePickerVisible.value = true
  }

  const handleDateConfirm = (date: string | number | Date) => {
    selectedDate.value = date
    datePickerVisible.value = false
  }

  const handleDateCancel = () => {
    datePickerVisible.value = false
  }

  const reset = () => {
    validityType.value = 'permanent'
    selectedDate.value = null
    visible.value = false
    datePickerVisible.value = false
  }

  // 监听器
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      validityType.value = newValue.type
      selectedDate.value = newValue.date || null
    } else {
      validityType.value = 'permanent'
      selectedDate.value = null
    }
  }, { immediate: true })

  return {
    // 状态
    visible,
    datePickerVisible,
    validityType,
    selectedDate,
    
    // 计算属性
    displayValue,
    formattedValue,
    isValid,
    
    // 方法
    formatDate,
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    showDatePicker,
    handleDateConfirm,
    handleDateCancel,
    reset
  }
}

/**
 * 时间选择器工具函数
 */
export const timePickerUtils = {
  /**
   * 验证时间选择器的值是否有效
   */
  isValidValue(value: TimePickerValue | null): boolean {
    if (!value) return false
    
    if (value.type === 'permanent') return true
    
    return value.type === 'limited' && !!value.date
  },

  /**
   * 格式化显示文本
   */
  formatDisplayText(value: TimePickerValue | null, dateFormat = 'YYYY-MM-DD'): string {
    if (!value) return ''
    
    if (value.type === 'permanent') {
      return '永久有效'
    } else if (value.type === 'limited' && value.date) {
      return `有效期至 ${dayjs(value.date).format(dateFormat)}`
    }
    
    return ''
  },

  /**
   * 创建永久有效的值
   */
  createPermanentValue(): TimePickerValue {
    return {
      type: 'permanent'
    }
  },

  /**
   * 创建期限有效的值
   */
  createLimitedValue(date: string | number | Date): TimePickerValue {
    const timestamp = dayjs(date).valueOf()
    return {
      type: 'limited',
      date,
      timestamp
    }
  },

  /**
   * 检查日期是否已过期
   */
  isExpired(value: TimePickerValue | null): boolean {
    if (!value || value.type === 'permanent') return false
    
    if (value.type === 'limited' && value.date) {
      return dayjs().isAfter(dayjs(value.date))
    }
    
    return false
  }
}
