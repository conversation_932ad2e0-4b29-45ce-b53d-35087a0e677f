<template>
  <div class="radio-test-page">
    <Header title="Radio 组件配置测试" />
    
    <main class="test-content">
      <!-- 浅色模式测试 -->
      <section class="test-section">
        <h3>浅色模式测试</h3>
        <div class="form-item-layout">
          <div class="form-item">
            <p class="title">有效期选择 (浅色模式)</p>
            <div class="form-item-box border-radius-default">
              <div class="item border-radius-default">
                <ValidityTimePicker
                  v-model="lightModeValue"
                  color-mode="light"
                  @change="handleLightModeChange"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div class="result-display">
          <h4>当前值:</h4>
          <pre>{{ JSON.stringify(lightModeValue, null, 2) }}</pre>
        </div>
      </section>

      <!-- 深色模式测试 -->
      <section class="test-section dark-mode">
        <h3>深色模式测试</h3>
        <div class="form-item-layout">
          <div class="form-item">
            <p class="title">有效期选择 (深色模式)</p>
            <div class="form-item-box border-radius-default">
              <div class="item border-radius-default">
                <ValidityTimePicker
                  v-model="darkModeValue"
                  color-mode="dark"
                  @change="handleDarkModeChange"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div class="result-display">
          <h4>当前值:</h4>
          <pre>{{ JSON.stringify(darkModeValue, null, 2) }}</pre>
        </div>
      </section>

      <!-- Radio 组件结构验证 -->
      <section class="test-section">
        <h3>Radio 组件结构验证</h3>
        <div class="verification-info">
          <div class="info-item">
            <span class="label">RadioGroup 布局:</span>
            <span class="value">垂直排布 (VERTICAL)</span>
          </div>
          <div class="info-item">
            <span class="label">第一个 Radio value:</span>
            <span class="value">true (永久有效)</span>
          </div>
          <div class="info-item">
            <span class="label">第二个 Radio value:</span>
            <span class="value">false (期限有效)</span>
          </div>
          <div class="info-item">
            <span class="label">Radio 间距:</span>
            <span class="value">24px margin-bottom</span>
          </div>
          <div class="info-item">
            <span class="label">无障碍支持:</span>
            <span class="value">✅ aria-describedby 属性</span>
          </div>
        </div>
      </section>

      <!-- 交互测试 -->
      <section class="test-section">
        <h3>交互测试</h3>
        <div class="interaction-test">
          <div class="test-buttons">
            <button @click="setAllPermanent">全部设为永久有效</button>
            <button @click="setAllLimited">全部设为期限有效</button>
            <button @click="clearAllValues">清空所有值</button>
          </div>
          
          <div class="test-status">
            <p>浅色模式状态: {{ getStatusText(lightModeValue) }}</p>
            <p>深色模式状态: {{ getStatusText(darkModeValue) }}</p>
          </div>
        </div>
      </section>

      <!-- 样式检查 -->
      <section class="test-section">
        <h3>样式检查</h3>
        <div class="style-check">
          <div class="check-item">
            <span class="label">触摸目标大小:</span>
            <span class="value">≥ 44px ✅</span>
          </div>
          <div class="check-item">
            <span class="label">触摸反馈:</span>
            <span class="value">scale(0.98) ✅</span>
          </div>
          <div class="check-item">
            <span class="label">背景色适配:</span>
            <span class="value">colorMode 动态调整 ✅</span>
          </div>
          <div class="check-item">
            <span class="label">无障碍支持:</span>
            <span class="value">ARIA 属性完整 ✅</span>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import dayjs from 'dayjs'
  
  // 组件
  import ValidityTimePicker from './index.vue'
  import Header from '~/components/header/index.vue'
  import { validityTimePickerUtils } from './composable'
  import type { ValidityTimePickerValue } from './types'
  
  // 响应式数据
  const lightModeValue = ref<ValidityTimePickerValue | null>(null)
  const darkModeValue = ref<ValidityTimePickerValue | null>(null)
  
  // 事件处理
  const handleLightModeChange = (value: ValidityTimePickerValue | null) => {
    console.log('浅色模式值变化:', value)
  }
  
  const handleDarkModeChange = (value: ValidityTimePickerValue | null) => {
    console.log('深色模式值变化:', value)
  }
  
  // 工具函数
  const getStatusText = (value: ValidityTimePickerValue | null): string => {
    if (!value) return '未选择'
    if (value.qualValidityPeriod === 0) return '永久有效'
    if (value.qualValidityPeriod === 1) {
      return value.endTime ? `期限有效至 ${dayjs(value.endTime).format('YYYY-MM-DD')}` : '期限有效(未设置日期)'
    }
    return '未知状态'
  }
  
  // 测试操作
  const setAllPermanent = () => {
    lightModeValue.value = validityTimePickerUtils.createPermanentValue()
    darkModeValue.value = validityTimePickerUtils.createPermanentValue()
  }
  
  const setAllLimited = () => {
    const tomorrow = dayjs().add(1, 'day').toDate()
    lightModeValue.value = validityTimePickerUtils.createLimitedValue(tomorrow)
    darkModeValue.value = validityTimePickerUtils.createLimitedValue(tomorrow)
  }
  
  const clearAllValues = () => {
    lightModeValue.value = null
    darkModeValue.value = null
  }
</script>

<style lang="stylus" scoped>
.radio-test-page
  min-height 100vh
  background #F5F5F5

.test-content
  padding 16px

.test-section
  margin-bottom 24px
  background #fff
  border-radius 12px
  padding 16px

  h3
    margin 0 0 16px 0
    color rgba(0, 0, 0, 0.80)
    font-size 18px
    font-weight 600

  &.dark-mode
    background #1C1C1E
    color rgba(255, 255, 255, 0.9)
    
    h3
      color rgba(255, 255, 255, 0.9)

.form-item-layout
  margin-bottom 16px

.form-item
  .title
    margin 0 0 12px 8px
    color rgba(0, 0, 0, 0.62)
    font-size 12px

  .form-item-box
    background #FFF
    padding-left 16px

    .item
      display flex
      padding 12px 16px 12px 0

.dark-mode .form-item
  .title
    color rgba(255, 255, 255, 0.6)
  
  .form-item-box
    background rgba(255, 255, 255, 0.1)

.border-radius-default
  border-radius 8px

.result-display
  h4
    margin 0 0 8px 0
    font-size 14px
    color rgba(0, 0, 0, 0.80)
  
  pre
    margin 0
    padding 12px
    background #f8f8f8
    border-radius 6px
    font-size 12px
    overflow auto

.dark-mode .result-display
  h4
    color rgba(255, 255, 255, 0.9)
  
  pre
    background rgba(255, 255, 255, 0.1)
    color rgba(255, 255, 255, 0.8)

.verification-info, .style-check
  display grid
  grid-template-columns 1fr
  gap 12px

.info-item, .check-item
  display flex
  justify-content space-between
  padding 8px 12px
  background #f8f8f8
  border-radius 6px
  
  .label
    color rgba(0, 0, 0, 0.60)
    font-size 14px
  
  .value
    color rgba(0, 0, 0, 0.80)
    font-size 14px
    font-weight 500

.dark-mode .info-item,
.dark-mode .check-item
  background rgba(255, 255, 255, 0.1)
  
  .label
    color rgba(255, 255, 255, 0.6)
  
  .value
    color rgba(255, 255, 255, 0.9)

.interaction-test
  .test-buttons
    display grid
    grid-template-columns repeat(auto-fit, minmax(120px, 1fr))
    gap 8px
    margin-bottom 16px
    
    button
      padding 10px 16px
      background #007AFF
      color white
      border none
      border-radius 6px
      font-size 14px
      cursor pointer
      
      &:active
        transform scale(0.95)

  .test-status
    p
      margin 0 0 8px 0
      font-size 14px
      color rgba(0, 0, 0, 0.80)

.dark-mode .test-status p
  color rgba(255, 255, 255, 0.9)

/* 移动端优化 */
@media (max-width: 480px)
  .test-content
    padding 12px
  
  .verification-info, .style-check
    grid-template-columns 1fr
  
  .interaction-test .test-buttons
    grid-template-columns 1fr
</style>
