<template>
  <div class="validity-time-picker-wrapper">
    <!-- 显示区域 -->
    <div
      class="validity-time-picker-display"
      :class="{ 'readonly': readOnly }"
      @click="handleDisplayClick"
    >
      <slot name="display" :value="displayValue" :formatted="formattedValue">
        <div class="display-content">
          <span v-if="displayValue" class="display-text">{{ formattedValue }}</span>
          <span v-else class="display-placeholder">{{ placeholder }}</span>
          <OnixIcon v-if="!readOnly" class="display-icon" icon="arrowRightRightM" />
        </div>
      </slot>
    </div>

    <!-- 有效期时间选择弹窗 -->
    <ConfigProvider :color-mode="colorMode">
      <Sheets
        :visible="visible"
        :label="label"
        :cancel="true"
        :close="true"
        :close-type="SheetsType.SheetsActionType.icon"
        :mask="true"
        :mask-mode="SheetsType.SheetsMaskMode.dark"
        :line="false"
        @cancel="handleCancel"
        @confirm="handleConfirm"
        @mask="handleCancel"
      >
        <div class="validity-time-picker-content">
          <!-- 有效期类型选择 -->
          <div class="validity-type-section">
            <RadioGroup
              v-model="checked"
              :layout="RadioGroupType.RadioGroupLayout.VERTICAL"
              @change="handleValidityTypeChange"
            >
              <Radio
                label="永久有效"
                :value="true"
                class="radio-item"
              />
              <Radio
                label="期限有效"
                :value="false"
                class="radio-item"
              />
            </RadioGroup>
          </div>

          <!-- 日期选择区域 -->
          <div v-if="!checked" class="date-section">
            <div class="date-label">选择有效期至</div>
            <div class="date-picker-container">
              <div class="date-display" @click="showDatePicker">
                <span v-if="value.endTime" class="date-text">{{ value.endTime }}</span>
                <span v-else class="date-placeholder">请选择日期</span>
                <OnixIcon class="date-icon" icon="arrowRightRightM" />
              </div>
            </div>
          </div>
        </div>

      </Sheets>

      <!-- 日期选择器 -->
      <DatePicker
        :value="value.endTime"
        :visible="datePickerVisible"
        label="选择日期"
        :mode="DatePickerType.DatePickerMode.Date"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
      />
    </ConfigProvider>
  </div>
</template>

<script setup lang="ts">
  // 组件库
  import {
    Sheets,
    SheetsType,
    RadioGroup,
    Radio,
    RadioGroupType,
    DatePicker,
    DatePickerType,
    ConfigProvider
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'

  // 本地模块
  import { useValidityTimePicker } from './composable'
  import type { ValidityTimePickerProps, ValidityTimePickerValue } from './types'

  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'

  // Props
  const props = withDefaults(defineProps<ValidityTimePickerProps>(), {
    modelValue: null,
    readOnly: false,
    placeholder: '请选择有效期',
    label: '选择有效期',
    colorMode: 'light',
    dateFormat: 'YYYY-MM-DD'
  })

  // Emits
  const emits = defineEmits<{
    'update:modelValue': [value: ValidityTimePickerValue | null]
    'change': [value: ValidityTimePickerValue | null]
  }>()

  // 使用组合式函数
  const {
    visible,
    datePickerVisible,
    value,
    checked,
    displayValue,
    formattedValue,
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    showDatePicker,
    handleDateConfirm,
    handleDateCancel
  } = useValidityTimePicker(props, emits)
</script>

<style lang="stylus" scoped>
.validity-time-picker-wrapper
  width 100%

.validity-time-picker-display
  cursor pointer

  &.readonly
    cursor default
    opacity 0.6

.display-content
  display flex
  align-items center
  justify-content space-between
  padding 12px 16px
  background #fff
  border-radius 8px
  border 1px solid #E5E5E5

.display-text
  color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
  font-size 16px
  font-weight 400

.display-placeholder
  color var(--Light-Labels-Tertiary, rgba(0, 0, 0, 0.40))
  font-size 16px
  font-weight 400

.display-icon
  width 16px
  height 16px
  color var(--Light-Labels-Secondary, rgba(0, 0, 0, 0.60))

.validity-time-picker-content
  padding 0 16px 20px 16px

.validity-type-section
  margin-bottom 20px

.radio-item
  margin-bottom 12px
  padding 16px
  background #F8F8F8
  border-radius 8px

  &:last-child
    margin-bottom 0

.date-section
  .date-label
    color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
    font-size 16px
    font-weight 500
    margin-bottom 12px

.date-picker-container
  .date-display
    display flex
    align-items center
    justify-content space-between
    padding 16px
    background #F8F8F8
    border-radius 8px
    cursor pointer

.date-text
  color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
  font-size 16px
  font-weight 400

.date-placeholder
  color var(--Light-Labels-Tertiary, rgba(0, 0, 0, 0.40))
  font-size 16px
  font-weight 400

.date-icon
  width 16px
  height 16px
  color var(--Light-Labels-Secondary, rgba(0, 0, 0, 0.60))
</style>
