<template>
  <div class="validity-time-picker-wrapper">
    <!-- 显示区域 -->
    <div
      class="validity-time-picker-display"
      :class="{ 'readonly': readOnly, 'disabled': disabled }"
      @click="handleDisplayClick"
    >
      <slot name="display" :value="displayValue" :formatted="formattedValue">
        <div class="display-content">
          <span class="display-label">有效期</span>
          <div class="display-right">
            <span v-if="formattedValue" class="display-text">
              {{ formattedValue }}
            </span>
            <span v-else class="display-placeholder">
              {{ placeholder }}
            </span>
            <OnixIcon
              v-if="!readOnly && !disabled"
              class="display-icon"
              icon="arrowRightRightM"
            />
          </div>
        </div>
      </slot>
    </div>

    <!-- 有效期时间选择弹窗 -->
    <ConfigProvider :color-mode="colorMode">
      <Sheets
        :visible="visible"
        :label="label"
        :cancel="true"
        :close="true"
        :close-type="SheetsType.SheetsActionType.icon"
        :mask="true"
        :mask-mode="SheetsType.SheetsMaskMode.dark"
        :line="false"
        @cancel="handleCancel"
        @confirm="handleConfirm"
        @mask="handleCancel"
      >
        <div class="validity-time-picker-content">
          <!-- 第一步：期限类型选择 -->
          <div v-if="!showDateRangeSelection" class="validity-type-section">
            <ConfigProvider :color-mode="colorMode">
              <RadioGroup
                v-model="checked"
                :layout="RadioGroupType.RadioGroupLayout.VERTICAL"
                @change="handleValidityTypeChange"
              >
                <Radio :value="true" class="radio-item">
                  永久有效
                </Radio>
                <Radio :value="false" class="radio-item">
                  期限有效
                </Radio>
              </RadioGroup>
            </ConfigProvider>
          </div>

          <!-- 第二步：日期范围选择界面 -->
          <div v-if="showDateRangeSelection" class="date-range-section">
            <!-- 返回按钮 -->
            <div class="date-range-header">
              <button type="button" class="back-button" @click="backToValidityTypeSelection">
                <OnixIcon class="back-icon" icon="arrowLeftLeftM" />
                <span class="back-text">返回</span>
              </button>
              <h3 class="date-range-title">设置有效期</h3>
            </div>

            <!-- 起始日期选择 -->
            <div class="date-item">
              <div class="date-label">起始日期</div>
              <div class="date-picker-container">
                <div class="date-display" @click="showStartDatePicker">
                  <span v-if="value.startTime" class="date-text">{{ value.startTime }}</span>
                  <span v-else class="date-placeholder">请选择起始日期</span>
                  <OnixIcon class="date-icon" icon="arrowRightRightM" />
                </div>
              </div>
            </div>

            <!-- 结束日期选择 -->
            <div class="date-item">
              <div class="date-label">结束日期</div>
              <div class="date-picker-container">
                <div class="date-display" @click="showEndDatePicker">
                  <span v-if="value.endTime" class="date-text">{{ value.endTime }}</span>
                  <span v-else class="date-placeholder">请选择结束日期</span>
                  <OnixIcon class="date-icon" icon="arrowRightRightM" />
                </div>
              </div>
            </div>
          </div>
        </div>

      </Sheets>

      <!-- 日期选择器 -->
      <DatePicker
        :value="currentDateType === 'start' ? value.startTime : value.endTime"
        :visible="datePickerVisible"
        :label="currentDateType === 'start' ? '选择起始日期' : '选择结束日期'"
        :mode="DatePickerType.DatePickerMode.Date"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
      />
    </ConfigProvider>
  </div>
</template>

<script setup lang="ts">
  // 组件库
  import {
    Sheets,
    SheetsType,
    RadioGroup,
    RadioGroupType,
    Radio,
    DatePicker,
    DatePickerType,
    ConfigProvider
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'

  // 本地模块
  import { useValidityTimePicker } from './composable'
  import type { ValidityTimePickerProps, ValidityTimePickerValue } from './types'

  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'

  // Props
  const props = withDefaults(defineProps<ValidityTimePickerProps>(), {
    modelValue: null,
    readOnly: false,
    disabled: false,
    placeholder: '请选择时间段',
    label: '选择有效期',
    colorMode: 'light',
    dateFormat: 'YYYY-MM-DD'
  })

  // Emits
  const emits = defineEmits<{
    'update:modelValue': [value: ValidityTimePickerValue | null]
    'change': [value: ValidityTimePickerValue | null]
  }>()

  // 使用组合式函数
  const {
    visible,
    datePickerVisible,
    value,
    checked,
    displayValue,
    formattedValue,
    // 界面状态
    showDateRangeSelection,
    currentDateType,
    // 事件处理
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    backToValidityTypeSelection,
    showDatePicker,
    showStartDatePicker,
    showEndDatePicker,
    handleDateConfirm,
    handleDateCancel
  } = useValidityTimePicker(props, emits)
</script>

<style lang="stylus" scoped>
.validity-time-picker-wrapper
  width 100%
  font-size: var(--b1-font-size);
.validity-time-picker-display
  background: var(--bg);

.display-content
  display flex
  align-items center
  justify-content space-between
  width 100%
  height 100%
  padding 0

.display-label
  color: var(--title);

  /* Body/B1 */
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: var(--b1-font-weight);
  line-height: var(--b1-line-height); /* 150% */

.display-right
  display flex
  align-items center
  justify-content flex-end
  flex 1
  gap 8px

.display-text
  color rgba(0, 0, 0, 0.80)
  font-family "PingFang SC"
  font-style normal
  font-weight 400
  line-height 24px
  text-align right

.display-placeholder
  color: var(--placeholder);
  text-align: right;

  /* Body/B1 */
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: var(--b1-font-weight);
  line-height: var(--b1-line-height); /* 150% */

.display-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0

/* 弹窗内容样式 */
.validity-time-picker-content
  padding 0 16px 20px 16px
  background: var(--bg0);

.validity-type-section
  margin-bottom 20px

.radio-item
  margin-bottom 24px /* 设置 24px 间距 */
  padding 16px
  border-radius 8px
  background #F8F8F8

  &:last-child
    margin-bottom 0

/* 日期范围选择界面样式 */
.date-range-section
  .date-range-header
    display flex
    align-items center
    margin-bottom 20px
    position relative

  .back-button
    display flex
    align-items center
    gap 4px
    background none
    border none
    cursor pointer
    padding 8px
    border-radius 4px
    color rgba(0, 0, 0, 0.80)
    font-size 14px

    &:hover
      background rgba(0, 0, 0, 0.05)

  .back-icon
    width 16px
    height 16px

  .back-text
    font-weight 400

  .date-range-title
    position absolute
    left 50%
    transform translateX(-50%)
    margin 0
    color rgba(0, 0, 0, 0.80)
    font-size 16px
    font-weight 500

.date-item
  margin-bottom 16px
  background: var(--bg);

  &:last-child
    margin-bottom 0

  .date-label
    color rgba(0, 0, 0, 0.80)
    font-size 14px
    font-weight 500
    margin 0 0 8px 0

.date-picker-container
  .date-display
    background #F8F8F8
    width 100%
    display flex
    align-items center
    justify-content space-between
    padding 16px
    border-radius 8px
    cursor pointer

.date-text
  color rgba(0, 0, 0, 0.80)
  font-weight 400
  line-height 24px

.date-placeholder
  color rgba(0, 0, 0, 0.40)
  font-weight 400
  line-height 24px

.date-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0
</style>
