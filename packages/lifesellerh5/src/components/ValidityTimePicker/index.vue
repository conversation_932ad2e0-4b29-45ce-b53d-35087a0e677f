<template>
  <div class="validity-time-picker-wrapper">
    <!-- 显示区域 -->
    <div
      class="validity-time-picker-display"
      :class="{ 'readonly': readOnly, 'disabled': disabled }"
      @click="handleDisplayClick"
    >
      <slot name="display" :value="displayValue" :formatted="formattedValue">
        <div class="display-content">
          <span class="display-label">有效期</span>
          <div class="display-right">
            <span v-if="formattedValue" class="display-text">
              {{ formattedValue }}
            </span>
            <span v-else class="display-placeholder">
              {{ placeholder }}
            </span>
            <OnixIcon
              v-if="!readOnly && !disabled"
              class="display-icon"
              icon="arrowRightRightM"
            />
          </div>
        </div>
      </slot>
    </div>

    <!-- 有效期时间选择弹窗 -->
    <ConfigProvider :color-mode="colorMode">
      <Sheets
        :visible="visible"
        :label="label"
        :cancel="true"
        :close="true"
        :close-type="SheetsType.SheetsActionType.icon"
        :mask="true"
        :mask-mode="SheetsType.SheetsMaskMode.dark"
        :line="false"
        @cancel="handleCancel"
        @confirm="handleConfirm"
        @mask="handleCancel"
      >
        <div class="validity-time-picker-content">
          <!-- 有效期类型选择 -->
          <div class="validity-type-section">
            <ConfigProvider :color-mode="colorMode">
              <RadioGroup
                v-model="checked"
                @change="handleValidityTypeChange"
              >
                <div class="radio-item">
                  <Radio :name="true">
                    永久有效
                  </Radio>
                  <Radio :name="false">
                    <div style="display: flex; align-items: center; justify-content: space-between;wid">
                      <Text>期限有效</Text>
                      <OnixIcon
                        class="display-icon"
                        icon="arrowRightRightM"
                      />
                    </div>
                  </Radio>
                </div>

              </RadioGroup>
            </ConfigProvider>
          </div>

          <!-- 日期选择区域 -->
          <div v-if="!checked" class="date-section">
            <div class="date-label">选择有效期至</div>
            <div class="date-picker-container">
              <div class="date-display" @click="showDatePicker">
                <span v-if="value.endTime" class="date-text">{{ value.endTime }}</span>
                <span v-else class="date-placeholder">请选择日期</span>
                <OnixIcon class="date-icon" icon="arrowRightRightM" />
              </div>
            </div>
          </div>
        </div>

      </Sheets>

      <!-- 日期选择器 -->
      <DatePicker
        :value="value.endTime"
        :visible="datePickerVisible"
        label="选择日期"
        :mode="DatePickerType.DatePickerMode.Date"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
      />
    </ConfigProvider>
  </div>
</template>

<script setup lang="ts">
  // 组件库
  import {
    Sheets,
    SheetsType,
    RadioGroup,
    Radio,
    DatePicker,
    DatePickerType,
    ConfigProvider
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'

  // 本地模块
  import { useValidityTimePicker } from './composable'
  import type { ValidityTimePickerProps, ValidityTimePickerValue } from './types'

  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'

  // Props
  const props = withDefaults(defineProps<ValidityTimePickerProps>(), {
    modelValue: null,
    readOnly: false,
    disabled: false,
    placeholder: '请选择时间段',
    label: '选择有效期',
    colorMode: 'light',
    dateFormat: 'YYYY-MM-DD'
  })

  // Emits
  const emits = defineEmits<{
    'update:modelValue': [value: ValidityTimePickerValue | null]
    'change': [value: ValidityTimePickerValue | null]
  }>()

  // 使用组合式函数
  const {
    visible,
    datePickerVisible,
    value,
    checked,
    displayValue,
    formattedValue,
    // 事件处理
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    showDatePicker,
    handleDateConfirm,
    handleDateCancel
  } = useValidityTimePicker(props, emits)
</script>

<style lang="stylus" scoped>
.validity-time-picker-wrapper
  width 100%
  font-size: var(--b1-font-size);
.validity-time-picker-display
  cursor pointer

.display-content
  display flex
  align-items center
  justify-content space-between
  width 100%
  height 100%
  padding 0

.display-label
  color: var(--title);

  /* Body/B1 */
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: var(--b1-font-weight);
  line-height: var(--b1-line-height); /* 150% */

.display-right
  display flex
  align-items center
  justify-content flex-end
  flex 1
  gap 8px

.display-text
  color rgba(0, 0, 0, 0.80)
  font-family "PingFang SC"
  font-style normal
  font-weight 400
  line-height 24px
  text-align right

.display-placeholder
  color: var(--placeholder);
  text-align: right;

  /* Body/B1 */
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: var(--b1-font-weight);
  line-height: var(--b1-line-height); /* 150% */

.display-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0

/* 弹窗内容样式 */
.validity-time-picker-content
  padding 0 16px 20px 16px

.validity-type-section
  margin-bottom 20px

.radio-item
  width 100%
  margin-bottom 24px /* 设置 24px 间距 */
  padding 16px
  border-radius 8px
  background #F8F8F8
  display flex
  flex-direction column
  gap 12px

.date-section
  .date-label
    color rgba(0, 0, 0, 0.80)
    font-family "PingFang SC"
    font-weight 500
    margin 0 0 12px 0

.date-picker-container
  .date-display
    background #F8F8F8
    width 100%
    display flex
    align-items center
    justify-content space-between
    padding 16px
    border-radius 8px
    cursor pointer

.date-text
  color rgba(0, 0, 0, 0.80)
  font-family "PingFang SC"
  font-weight 400
  line-height 24px

.date-placeholder
  color rgba(0, 0, 0, 0.40)
  font-family "PingFang SC"
  font-weight 400
  line-height 24px

.date-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0
</style>
