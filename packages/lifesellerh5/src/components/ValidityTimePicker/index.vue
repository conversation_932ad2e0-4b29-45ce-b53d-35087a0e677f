<template>
  <div class="validity-time-picker-wrapper">
    <!-- 显示区域 - 使用语义化标签和无障碍属性 -->
    <button
      type="button"
      class="validity-time-picker-display"
      :class="{ 'readonly': readOnly, 'disabled': disabled }"
      :disabled="readOnly || disabled"
      :aria-label="`有效期选择器，当前值：${formattedValue || placeholder}`"
      :aria-expanded="visible"
      :aria-haspopup="true"
      role="combobox"
      @click="handleDisplayClick"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
    >
      <slot name="display" :value="displayValue" :formatted="formattedValue">
        <div class="display-content">
          <span class="display-label" aria-hidden="true">有效期</span>
          <div class="display-right">
            <span
              v-if="formattedValue"
              class="display-text"
              :aria-label="`已选择：${formattedValue}`"
            >
              {{ formattedValue }}
            </span>
            <span
              v-else
              class="display-placeholder"
              :aria-label="placeholder"
            >
              {{ placeholder }}
            </span>
            <OnixIcon
              v-if="!readOnly && !disabled"
              class="display-icon"
              icon="arrowRightRightM"
              aria-hidden="true"
            />
          </div>
        </div>
      </slot>
    </button>

    <!-- 有效期时间选择弹窗 - 移动端优化 -->
    <ConfigProvider :color-mode="colorMode">
      <Sheets
        :visible="visible"
        :label="label"
        :cancel="true"
        :close="true"
        :close-type="SheetsType.SheetsActionType.icon"
        :mask="true"
        :mask-mode="SheetsType.SheetsMaskMode.dark"
        :line="false"
        :z-index="9999"
        role="dialog"
        :aria-modal="true"
        :aria-labelledby="sheetsTitleId"
        @cancel="handleCancel"
        @confirm="handleConfirm"
        @mask="handleCancel"
        @after-enter="handleSheetsEnter"
        @after-leave="handleSheetsLeave"
      >
        <div class="validity-time-picker-content">
          <!-- 标题 - 隐藏但用于无障碍 -->
          <h2 :id="sheetsTitleId" class="sr-only">{{ label }}</h2>

          <!-- 有效期类型选择 -->
          <fieldset class="validity-type-section">
            <legend class="validity-type-legend">选择有效期类型</legend>
            <ConfigProvider :color-mode="colorMode">
              <RadioGroup
                v-model="checked"
                :layout="RadioGroupType.RadioGroupLayout.VERTICAL"
                role="radiogroup"
                :aria-labelledby="validityTypeId"
                @change="handleValidityTypeChange"
              >
                <Radio
                  :value="true"
                  class="radio-item"
                  :aria-describedby="permanentDescId"
                >
                  永久有效
                </Radio>
                <Radio
                  :value="false"
                  class="radio-item"
                  :aria-describedby="limitedDescId"
                >
                  期限有效
                </Radio>
              </RadioGroup>
            </ConfigProvider>
          </fieldset>

          <!-- 日期选择区域 -->
          <div v-if="!checked" class="date-section" role="group" :aria-labelledby="dateSectionId">
            <h3 :id="dateSectionId" class="date-label">选择有效期至</h3>
            <div class="date-picker-container">
              <button
                type="button"
                class="date-display"
                :aria-label="`选择日期，当前选择：${value.endTime || '未选择'}`"
                :aria-expanded="datePickerVisible"
                @click="showDatePicker"
                @touchstart="handleDateTouchStart"
                @touchend="handleDateTouchEnd"
              >
                <span v-if="value.endTime" class="date-text">{{ value.endTime }}</span>
                <span v-else class="date-placeholder">请选择日期</span>
                <OnixIcon class="date-icon" icon="arrowRightRightM" aria-hidden="true" />
              </button>
            </div>
          </div>

          <!-- 隐藏的描述文本用于无障碍 -->
          <div class="sr-only">
            <div :id="permanentDescId">选择永久有效，该设置将不会过期</div>
            <div :id="limitedDescId">选择期限有效，需要设置具体的到期日期</div>
          </div>
        </div>

      </Sheets>

      <!-- 日期选择器 -->
      <DatePicker
        :value="value.endTime"
        :visible="datePickerVisible"
        label="选择日期"
        :mode="DatePickerType.DatePickerMode.Date"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
      />
    </ConfigProvider>
  </div>
</template>

<script setup lang="ts">
  // 组件库
  import {
    Sheets,
    SheetsType,
    RadioGroup,
    RadioGroupType,
    Radio,
    DatePicker,
    DatePickerType,
    ConfigProvider
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'

  // 本地模块
  import { useValidityTimePicker } from './composable'
  import type { ValidityTimePickerProps, ValidityTimePickerValue } from './types'

  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'

  // Props
  const props = withDefaults(defineProps<ValidityTimePickerProps>(), {
    modelValue: null,
    readOnly: false,
    disabled: false,
    placeholder: '请选择时间段',
    label: '选择有效期',
    colorMode: 'light',
    dateFormat: 'YYYY-MM-DD'
  })

  // Emits
  const emits = defineEmits<{
    'update:modelValue': [value: ValidityTimePickerValue | null]
    'change': [value: ValidityTimePickerValue | null]
  }>()

  // 使用组合式函数
  const {
    visible,
    datePickerVisible,
    value,
    checked,
    displayValue,
    formattedValue,
    // 无障碍ID
    sheetsTitleId,
    validityTypeId,
    dateSectionId,
    permanentDescId,
    limitedDescId,
    // 事件处理
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    showDatePicker,
    handleDateConfirm,
    handleDateCancel,
    // 移动端触摸事件
    handleTouchStart,
    handleTouchEnd,
    handleDateTouchStart,
    handleDateTouchEnd,
    handleSheetsEnter,
    handleSheetsLeave
  } = useValidityTimePicker(props, emits)
</script>

<style lang="stylus" scoped>
/* 移动端H5优化样式 */
.validity-time-picker-wrapper
  width 100%
  position relative

.validity-time-picker-display
  /* 移动端触摸优化 */
  appearance none
  border none
  background transparent
  width 100%
  min-height 44px /* 移动端最小触摸目标 */
  padding 0
  margin 0
  cursor pointer
  outline none

  /* 触摸反馈 */
  -webkit-tap-highlight-color transparent
  touch-action manipulation
  user-select none
  -webkit-user-select none

  /* 状态样式 */
  &:focus-visible
    outline 2px solid #007AFF
    outline-offset 2px
    border-radius 4px

  &.readonly, &.disabled
    cursor default
    opacity 0.6
    pointer-events none

  &:active:not(.readonly):not(.disabled)
    transform scale(0.98)
    transition transform 0.1s ease

.display-content
  display flex
  align-items center
  justify-content space-between
  width 100%
  height 100%
  min-height 44px
  padding 0

  /* 移动端文本渲染优化 */
  -webkit-font-smoothing antialiased
  -moz-osx-font-smoothing grayscale

.display-label
  color rgba(0, 0, 0, 0.80)
  font-family "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
  font-size 16px
  font-style normal
  font-weight 400
  line-height 24px
  flex-shrink 0

  /* 移动端文本优化 */
  text-rendering optimizeLegibility

.display-right
  display flex
  align-items center
  justify-content flex-end
  flex 1
  gap 8px
  min-height 44px

.display-text
  color rgba(0, 0, 0, 0.80)
  font-family "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
  font-size 16px
  font-style normal
  font-weight 400
  line-height 24px
  text-align right
  word-break break-word
  hyphens auto

.display-placeholder
  color rgba(0, 0, 0, 0.45)
  font-family "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
  font-size 16px
  font-style normal
  font-weight 400
  line-height 24px
  text-align right

.display-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0

  /* 图标渲染优化 */
  image-rendering -webkit-optimize-contrast
  image-rendering crisp-edges

/* 弹窗内容样式 - 移动端优化 */
.validity-time-picker-content
  padding 0 16px 20px 16px

  /* 安全区域适配 */
  padding-bottom calc(20px + env(safe-area-inset-bottom))
  padding-left calc(16px + env(safe-area-inset-left))
  padding-right calc(16px + env(safe-area-inset-right))

/* 无障碍隐藏类 */
.sr-only
  position absolute
  width 1px
  height 1px
  padding 0
  margin -1px
  overflow hidden
  clip rect(0, 0, 0, 0)
  white-space nowrap
  border 0

.validity-type-section
  margin-bottom 20px
  border none
  padding 0

.validity-type-legend
  @extend .sr-only

.radio-item
  margin-bottom 24px /* 设置 24px 间距 */
  padding 16px
  border-radius 8px
  min-height 44px /* 移动端触摸目标 */

  /* 浅色模式背景 */
  background #F8F8F8

  /* 触摸优化 */
  touch-action manipulation
  -webkit-tap-highlight-color transparent

  &:last-child
    margin-bottom 0

  /* 触摸反馈 */
  &:active
    transform scale(0.98)
    transition transform 0.1s ease

/* 深色模式支持 */
@media (prefers-color-scheme: dark)
  .radio-item
    background rgba(255, 255, 255, 0.1)

  /* 深色模式下的文本颜色调整 */
  .validity-time-picker-content
    color rgba(255, 255, 255, 0.9)

/* 通过 data 属性支持 colorMode */
[data-color-mode="dark"] .radio-item
  background rgba(255, 255, 255, 0.1)

[data-color-mode="light"] .radio-item
  background #F8F8F8

.date-section
  .date-label
    color rgba(0, 0, 0, 0.80)
    font-family "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
    font-size 16px
    font-weight 500
    margin 0 0 12px 0
    padding 0

.date-picker-container
  .date-display
    appearance none
    border none
    background #F8F8F8
    width 100%
    display flex
    align-items center
    justify-content space-between
    padding 16px
    border-radius 8px
    cursor pointer
    min-height 44px /* 移动端触摸目标 */
    outline none

    /* 触摸优化 */
    touch-action manipulation
    -webkit-tap-highlight-color transparent
    user-select none
    -webkit-user-select none

    /* 焦点样式 */
    &:focus-visible
      outline 2px solid #007AFF
      outline-offset 2px

    /* 触摸反馈 */
    &:active
      transform scale(0.98)
      transition transform 0.1s ease

.date-text
  color rgba(0, 0, 0, 0.80)
  font-family "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
  font-size 16px
  font-weight 400
  line-height 24px

.date-placeholder
  color rgba(0, 0, 0, 0.40)
  font-family "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
  font-size 16px
  font-weight 400
  line-height 24px

.date-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0

  /* 图标渲染优化 */
  image-rendering -webkit-optimize-contrast
  image-rendering crisp-edges

/* 移动端媒体查询优化 */
@media (max-width: 480px)
  .validity-time-picker-content
    padding 0 12px 16px 12px
    padding-bottom calc(16px + env(safe-area-inset-bottom))

  .radio-item, .date-display
    padding 12px
    min-height 40px

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)
  .display-icon, .date-icon
    image-rendering -webkit-optimize-contrast

/* 暗色模式支持 */
@media (prefers-color-scheme: dark)
  .validity-time-picker-display:focus-visible
    outline-color #0A84FF
</style>
