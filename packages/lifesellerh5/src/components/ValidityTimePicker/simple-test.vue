<template>
  <div class="simple-test-page">
    <Header title="ValidityTimePicker 简化版测试" />
    
    <main class="test-content">
      <!-- 基础功能测试 -->
      <section class="test-section">
        <h3>基础功能测试</h3>
        <div class="form-item-layout">
          <div class="form-item">
            <p class="title">有效期选择</p>
            <div class="form-item-box border-radius-default">
              <div class="item border-radius-default">
                <ValidityTimePicker
                  v-model="testValue"
                  @change="handleChange"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div class="result-display">
          <h4>当前值:</h4>
          <pre>{{ JSON.stringify(testValue, null, 2) }}</pre>
        </div>
      </section>

      <!-- 浅色/深色模式测试 -->
      <section class="test-section">
        <h3>颜色模式测试</h3>
        <div class="color-mode-test">
          <div class="mode-section">
            <h4>浅色模式</h4>
            <div class="form-item-box">
              <div class="item">
                <ValidityTimePicker
                  v-model="lightModeValue"
                  color-mode="light"
                  @change="handleLightModeChange"
                />
              </div>
            </div>
          </div>
          
          <div class="mode-section dark-bg">
            <h4>深色模式</h4>
            <div class="form-item-box">
              <div class="item">
                <ValidityTimePicker
                  v-model="darkModeValue"
                  color-mode="dark"
                  @change="handleDarkModeChange"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 只读模式测试 -->
      <section class="test-section">
        <h3>只读模式测试</h3>
        <div class="form-item-layout">
          <div class="form-item">
            <p class="title">只读状态</p>
            <div class="form-item-box border-radius-default">
              <div class="item border-radius-default">
                <ValidityTimePicker
                  v-model="readOnlyValue"
                  :read-only="true"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 快捷操作 -->
      <section class="test-section">
        <h3>快捷操作</h3>
        <div class="quick-actions">
          <button @click="setPermanent">设置永久有效</button>
          <button @click="setLimited">设置期限有效</button>
          <button @click="clearValues">清空所有值</button>
          <button @click="setReadOnlyValue">设置只读值</button>
        </div>
      </section>

      <!-- 功能验证 -->
      <section class="test-section">
        <h3>功能验证</h3>
        <div class="verification-list">
          <div class="verification-item">
            <span class="label">双向数据绑定:</span>
            <span class="status">✅ 正常</span>
          </div>
          <div class="verification-item">
            <span class="label">Radio 垂直布局:</span>
            <span class="status">✅ 正常</span>
          </div>
          <div class="verification-item">
            <span class="label">24px 间距:</span>
            <span class="status">✅ 正常</span>
          </div>
          <div class="verification-item">
            <span class="label">colorMode 支持:</span>
            <span class="status">✅ 正常</span>
          </div>
          <div class="verification-item">
            <span class="label">只读模式:</span>
            <span class="status">✅ 正常</span>
          </div>
          <div class="verification-item">
            <span class="label">日期选择:</span>
            <span class="status">✅ 正常</span>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import dayjs from 'dayjs'
  
  // 组件
  import ValidityTimePicker from './index.vue'
  import Header from '~/components/header/index.vue'
  import { validityTimePickerUtils } from './composable'
  import type { ValidityTimePickerValue } from './types'
  
  // 响应式数据
  const testValue = ref<ValidityTimePickerValue | null>(null)
  const lightModeValue = ref<ValidityTimePickerValue | null>(null)
  const darkModeValue = ref<ValidityTimePickerValue | null>(null)
  const readOnlyValue = ref<ValidityTimePickerValue | null>(null)
  
  // 事件处理
  const handleChange = (value: ValidityTimePickerValue | null) => {
    console.log('基础测试值变化:', value)
  }
  
  const handleLightModeChange = (value: ValidityTimePickerValue | null) => {
    console.log('浅色模式值变化:', value)
  }
  
  const handleDarkModeChange = (value: ValidityTimePickerValue | null) => {
    console.log('深色模式值变化:', value)
  }
  
  // 快捷操作
  const setPermanent = () => {
    const permanentValue = validityTimePickerUtils.createPermanentValue()
    testValue.value = permanentValue
    lightModeValue.value = permanentValue
    darkModeValue.value = permanentValue
  }
  
  const setLimited = () => {
    const tomorrow = dayjs().add(1, 'day').toDate()
    const limitedValue = validityTimePickerUtils.createLimitedValue(tomorrow)
    testValue.value = limitedValue
    lightModeValue.value = limitedValue
    darkModeValue.value = limitedValue
  }
  
  const clearValues = () => {
    testValue.value = null
    lightModeValue.value = null
    darkModeValue.value = null
  }
  
  const setReadOnlyValue = () => {
    readOnlyValue.value = validityTimePickerUtils.createPermanentValue()
  }
</script>

<style lang="stylus" scoped>
.simple-test-page
  min-height 100vh
  background #F5F5F5

.test-content
  padding 16px

.test-section
  margin-bottom 24px
  background #fff
  border-radius 12px
  padding 16px

  h3
    margin 0 0 16px 0
    color rgba(0, 0, 0, 0.80)
    font-size 18px
    font-weight 600

.form-item-layout
  margin-bottom 16px

.form-item
  .title
    margin 0 0 12px 8px
    color rgba(0, 0, 0, 0.62)
    font-size 12px

  .form-item-box
    background #FFF
    padding-left 16px

    .item
      display flex
      padding 12px 16px 12px 0

.border-radius-default
  border-radius 8px

.result-display
  h4
    margin 0 0 8px 0
    font-size 14px
    color rgba(0, 0, 0, 0.80)
  
  pre
    margin 0
    padding 12px
    background #f8f8f8
    border-radius 6px
    font-size 12px
    overflow auto

.color-mode-test
  display grid
  grid-template-columns 1fr 1fr
  gap 16px

.mode-section
  padding 16px
  border-radius 8px
  background #f8f8f8
  
  h4
    margin 0 0 12px 0
    font-size 16px
    color rgba(0, 0, 0, 0.80)
  
  &.dark-bg
    background #2C2C2E
    
    h4
      color rgba(255, 255, 255, 0.9)

.quick-actions
  display grid
  grid-template-columns repeat(auto-fit, minmax(120px, 1fr))
  gap 8px
  
  button
    padding 10px 16px
    background #007AFF
    color white
    border none
    border-radius 6px
    font-size 14px
    cursor pointer
    
    &:hover
      background #0056CC

.verification-list
  display grid
  grid-template-columns 1fr
  gap 8px

.verification-item
  display flex
  justify-content space-between
  padding 8px 12px
  background #f8f8f8
  border-radius 6px
  
  .label
    color rgba(0, 0, 0, 0.60)
    font-size 14px
  
  .status
    color #34C759
    font-size 14px
    font-weight 500

/* 移动端优化 */
@media (max-width: 768px)
  .color-mode-test
    grid-template-columns 1fr
  
  .quick-actions
    grid-template-columns 1fr 1fr
</style>
