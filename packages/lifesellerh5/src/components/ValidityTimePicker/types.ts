// 有效期类型枚举 - 与参考代码保持一致
// 0 永久有效，1 期限有效
export enum Period {
  PERMANENT = 0,
  NON_PERMANENT = 1
}

// 有效期时间选择器的值类型 - 与参考代码数据结构保持一致
export interface ValidityTimePickerValue {
  /** 有效期类型：0-永久有效，1-期限有效 */
  qualValidityPeriod: Period
  /** 开始时间（期限有效时设为0） */
  startTime?: number
  /** 结束时间（时间戳或日期字符串） */
  endTime?: string | number
}

// 有效期时间选择器的属性类型
export interface ValidityTimePickerProps {
  /** 双向绑定的值 */
  modelValue?: ValidityTimePickerValue | null
  /** 是否只读 */
  readOnly?: boolean
  /** 占位符文本 */
  placeholder?: string
  /** 弹窗标题 */
  label?: string
  /** 颜色模式 */
  colorMode?: 'light' | 'dark'
  /** 日期格式化字符串 */
  dateFormat?: string
  /** 是否禁用 */
  disabled?: boolean
}

// 有效期时间选择器的事件类型
export interface ValidityTimePickerEmits {
  /** 值更新事件 */
  'update:modelValue': [value: ValidityTimePickerValue | null]
  /** 值变化事件 */
  'change': [value: ValidityTimePickerValue | null]
}

// 向后兼容性类型别名
export type TimePickerValue = ValidityTimePickerValue
export type TimePickerProps = ValidityTimePickerProps
export type TimePickerEmits = ValidityTimePickerEmits

// 默认配置
export const DEFAULT_CONFIG = {
  placeholder: '请选择有效期',
  label: '选择有效期',
  colorMode: 'light' as const,
  dateFormat: 'YYYY-MM-DD'
}
