import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import dayjs from 'dayjs'
import type { ValidityTimePickerValue, ValidityTimePickerProps } from './types'
import {
  isMobile,
  isTouchDevice,
  mobileDebounce,
  detectKeyboard,
  performanceMonitor,
  handleMobileError
} from './mobile-utils'

// 生成唯一ID的工具函数
let idCounter = 0
const generateId = (prefix: string) => `${prefix}-${++idCounter}-${Date.now()}`

/**
 * 数据转换函数 - 与参考代码保持一致
 * @param v 原始值
 */
const transformValue = (v?: ValidityTimePickerValue | null) => {
  if (!v) {
    return {
      qualValidityPeriod: 0, // 默认为永久有效
      endTime: undefined,
    }
  }
  return {
    qualValidityPeriod: v.qualValidityPeriod ?? 0,
    endTime: v.endTime ? dayjs(v.endTime).format('YYYY-MM-DD') : undefined,
  }
}

/**
 * 有效期时间选择器组合式函数
 * @param props 组件属性
 * @param emits 组件事件
 */
export function useValidityTimePicker(
  props: ValidityTimePickerProps,
  emits: {
    (e: 'update:modelValue', value: ValidityTimePickerValue | null): void
    (e: 'change', value: ValidityTimePickerValue | null): void
  }
) {
  // 响应式状态
  const visible = ref(false)
  const datePickerVisible = ref(false)

  // 移动端触摸状态
  const isTouching = ref(false)
  const touchStartTime = ref(0)

  // 移动端环境检测
  const isMobileDevice = ref(isMobile())
  const supportTouch = ref(isTouchDevice())

  // 键盘状态
  const isKeyboardOpen = ref(false)

  // 无障碍ID生成
  const sheetsTitleId = ref(generateId('sheets-title'))
  const validityTypeId = ref(generateId('validity-type'))
  const dateSectionId = ref(generateId('date-section'))
  const permanentDescId = ref(generateId('permanent-desc'))
  const limitedDescId = ref(generateId('limited-desc'))

  // 内部状态管理 - 与参考代码保持一致
  const value = ref(transformValue(props.modelValue))

  // 计算属性 - 永久有效状态
  const checked = computed({
    get() {
      return value.value.qualValidityPeriod === 0 // Period.PERMANENT
    },
    set(newVal) {
      value.value.qualValidityPeriod = newVal ? 0 : 1 // Period.PERMANENT : Period.NON_PERMANENT
      value.value.endTime = undefined
      syncChange()
    }
  })

  // 显示值计算
  const displayValue = computed(() => props.modelValue)

  // 格式化显示文本
  const formattedValue = computed(() => {
    const data = props.modelValue
    if (!data) return ''

    if (data.qualValidityPeriod === 0) { // Period.PERMANENT
      return '永久有效'
    }
    if (data.qualValidityPeriod === 1 && data.endTime) { // Period.NON_PERMANENT
      return `有效期至 ${dayjs(data.endTime).format(props.dateFormat || 'YYYY-MM-DD')}`
    }
    return ''
  })

  // 验证有效性
  const isValid = computed(() => {
    if (!props.modelValue) return false

    if (props.modelValue.qualValidityPeriod === 0) return true // Period.PERMANENT

    return props.modelValue.qualValidityPeriod === 1 && !!props.modelValue.endTime // Period.NON_PERMANENT
  })

  // 工具方法
  const formatDate = (date: string | number | Date) => dayjs(date).format(props.dateFormat || 'YYYY-MM-DD')

  // 防抖同步变更 - 移动端优化版本
  const syncChange = mobileDebounce(
    () => {
      try {
        performanceMonitor.start('syncChange')

        const v = value.value
        const newValue: ValidityTimePickerValue = {
          qualValidityPeriod: v.qualValidityPeriod,
          startTime: v.qualValidityPeriod === 1 ? 0 : undefined, // Period.NON_PERMANENT
          endTime: v.endTime ? dayjs(v.endTime).valueOf() : undefined,
        }
        emits('update:modelValue', newValue)
        emits('change', newValue)

        performanceMonitor.end('syncChange')
      } catch (error) {
        handleMobileError(error as Error, 'syncChange')
      }
    },
    isMobileDevice.value ? 200 : 300 // 移动端更快的响应
  )

  // 日期变更处理 - 与参考代码保持一致
  const changeDate = (date: string) => {
    if (date) {
      value.value.qualValidityPeriod = 1 // Period.NON_PERMANENT
    }
    value.value.endTime = date
    syncChange()
  }

  // 移动端触摸事件处理
  const handleTouchStart = (event: TouchEvent) => {
    isTouching.value = true
    touchStartTime.value = Date.now()

    // 添加触摸反馈
    const target = event.currentTarget as HTMLElement
    target.style.transform = 'scale(0.98)'
    target.style.transition = 'transform 0.1s ease'
  }

  const handleTouchEnd = (event: TouchEvent) => {
    const target = event.currentTarget as HTMLElement
    target.style.transform = 'scale(1)'

    setTimeout(() => {
      isTouching.value = false
      target.style.transition = ''
    }, 100)
  }

  const handleDateTouchStart = (event: TouchEvent) => {
    const target = event.currentTarget as HTMLElement
    target.style.transform = 'scale(0.98)'
    target.style.transition = 'transform 0.1s ease'
  }

  const handleDateTouchEnd = (event: TouchEvent) => {
    const target = event.currentTarget as HTMLElement
    target.style.transform = 'scale(1)'

    setTimeout(() => {
      target.style.transition = ''
    }, 100)
  }

  // 弹窗生命周期事件
  const handleSheetsEnter = () => {
    // 弹窗打开时的处理
    document.body.style.overflow = 'hidden'
  }

  const handleSheetsLeave = () => {
    // 弹窗关闭时的处理
    document.body.style.overflow = ''
  }

  // 事件处理
  const handleDisplayClick = () => {
    if (props.readOnly || props.disabled) return

    visible.value = true
  }

  const handleCancel = () => {
    visible.value = false
    datePickerVisible.value = false
  }

  const handleConfirm = () => {
    visible.value = false
  }

  const handleValidityTypeChange = (newVal: boolean) => {
    checked.value = newVal
  }

  const showDatePicker = () => {
    datePickerVisible.value = true
  }

  const handleDateConfirm = (date: string | number | Date) => {
    const dateStr = dayjs(date).format('YYYY-MM-DD')
    changeDate(dateStr)
    datePickerVisible.value = false
  }

  const handleDateCancel = () => {
    datePickerVisible.value = false
  }

  const reset = () => {
    value.value = transformValue(null)
    visible.value = false
    datePickerVisible.value = false
  }

  // 生命周期钩子 - 移动端优化
  let keyboardCleanup: (() => void) | null = null

  onMounted(() => {
    try {
      performanceMonitor.start('component-mount')

      // 键盘检测
      if (isMobileDevice.value) {
        keyboardCleanup = detectKeyboard((isOpen) => {
          isKeyboardOpen.value = isOpen

          // 键盘弹出时调整弹窗位置
          if (visible.value && isOpen) {
            nextTick(() => {
              // 可以在这里添加弹窗位置调整逻辑
            })
          }
        })
      }

      performanceMonitor.end('component-mount')
    } catch (error) {
      handleMobileError(error as Error, 'component-mount')
    }
  })

  onUnmounted(() => {
    try {
      // 清理键盘监听
      if (keyboardCleanup) {
        keyboardCleanup()
      }

      // 恢复body滚动
      document.body.style.overflow = ''
    } catch (error) {
      handleMobileError(error as Error, 'component-unmount')
    }
  })

  // 监听器 - 与参考代码保持一致
  watch(
    () => props.modelValue,
    () => {
      value.value = transformValue(props.modelValue)
    }
  )

  return {
    // 状态
    visible,
    datePickerVisible,
    value,
    checked,

    // 移动端状态
    isMobileDevice,
    supportTouch,
    isKeyboardOpen,

    // 计算属性
    displayValue,
    formattedValue,
    isValid,

    // 无障碍ID
    sheetsTitleId,
    validityTypeId,
    dateSectionId,
    permanentDescId,
    limitedDescId,

    // 方法
    formatDate,
    changeDate,
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    showDatePicker,
    handleDateConfirm,
    handleDateCancel,
    reset,

    // 移动端触摸事件
    handleTouchStart,
    handleTouchEnd,
    handleDateTouchStart,
    handleDateTouchEnd,
    handleSheetsEnter,
    handleSheetsLeave
  }
}

/**
 * 有效期时间选择器工具函数 - 与参考代码保持一致
 */
export const validityTimePickerUtils = {
  /**
   * 验证有效期时间选择器的值是否有效
   */
  isValidValue(value: ValidityTimePickerValue | null): boolean {
    if (!value) return false

    if (value.qualValidityPeriod === 0) return true // Period.PERMANENT

    return value.qualValidityPeriod === 1 && !!value.endTime // Period.NON_PERMANENT
  },

  /**
   * 格式化显示文本
   */
  formatDisplayText(value: ValidityTimePickerValue | null, dateFormat = 'YYYY-MM-DD'): string {
    if (!value) return ''

    if (value.qualValidityPeriod === 0) { // Period.PERMANENT
      return '永久有效'
    }
    if (value.qualValidityPeriod === 1 && value.endTime) { // Period.NON_PERMANENT
      return `有效期至 ${dayjs(value.endTime).format(dateFormat)}`
    }

    return ''
  },

  /**
   * 创建永久有效的值
   */
  createPermanentValue(): ValidityTimePickerValue {
    return {
      qualValidityPeriod: 0, // Period.PERMANENT
      startTime: undefined,
      endTime: undefined
    }
  },

  /**
   * 创建期限有效的值
   */
  createLimitedValue(date: string | number | Date): ValidityTimePickerValue {
    return {
      qualValidityPeriod: 1, // Period.NON_PERMANENT
      startTime: 0,
      endTime: dayjs(date).valueOf()
    }
  },

  /**
   * 检查日期是否已过期
   */
  isExpired(value: ValidityTimePickerValue | null): boolean {
    if (!value || value.qualValidityPeriod === 0) return false // Period.PERMANENT

    if (value.qualValidityPeriod === 1 && value.endTime) { // Period.NON_PERMANENT
      return dayjs().isAfter(dayjs(value.endTime))
    }

    return false
  },

  /**
   * 检查是否有有效的有效期设置
   */
  hasValid(value?: ValidityTimePickerValue): boolean {
    const period = value?.qualValidityPeriod
    if (period === 0) return true // Period.PERMANENT
    return period === 1 && !!value?.endTime // Period.NON_PERMANENT
  }
}

// 向后兼容性别名
export const timePickerUtils = validityTimePickerUtils

// 向后兼容性函数别名
export const useTimePicker = useValidityTimePicker
