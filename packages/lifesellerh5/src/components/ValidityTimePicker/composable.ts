import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import { debounce } from 'lodash'
import type { ValidityTimePickerValue, ValidityTimePickerProps, Period } from './types'

/**
 * 数据转换函数 - 与参考代码保持一致
 * @param v 原始值
 */
const transformValue = (v: ValidityTimePickerValue = {} as ValidityTimePickerValue) => ({
  qualValidityPeriod: v.qualValidityPeriod,
  endTime: v.endTime ? dayjs(v.endTime).format('YYYY-MM-DD') : undefined,
})

/**
 * 有效期时间选择器组合式函数
 * @param props 组件属性
 * @param emits 组件事件
 */
export function useValidityTimePicker(
  props: ValidityTimePickerProps,
  emits: {
    (e: 'update:modelValue', value: ValidityTimePickerValue | null): void
    (e: 'change', value: ValidityTimePickerValue | null): void
  }
) {
  // 响应式状态
  const visible = ref(false)
  const datePickerVisible = ref(false)

  // 内部状态管理 - 与参考代码保持一致
  const value = ref(transformValue(props.modelValue))

  // 计算属性 - 永久有效状态
  const checked = computed({
    get() {
      return value.value.qualValidityPeriod === 0 // Period.PERMANENT
    },
    set(newVal) {
      value.value.qualValidityPeriod = newVal ? 0 : 1 // Period.PERMANENT : Period.NON_PERMANENT
      value.value.endTime = undefined
      syncChange()
    }
  })

  // 显示值计算
  const displayValue = computed(() => {
    return props.modelValue
  })

  // 格式化显示文本
  const formattedValue = computed(() => {
    const data = props.modelValue || {}
    if (data.qualValidityPeriod === 0) { // Period.PERMANENT
      return '永久有效'
    }
    return data.endTime ? dayjs(data.endTime).format(props.dateFormat || 'YYYY-MM-DD') : ''
  })

  // 验证有效性
  const isValid = computed(() => {
    if (!props.modelValue) return false

    if (props.modelValue.qualValidityPeriod === 0) return true // Period.PERMANENT

    return props.modelValue.qualValidityPeriod === 1 && !!props.modelValue.endTime // Period.NON_PERMANENT
  })

  // 工具方法
  const formatDate = (date: string | number | Date) => {
    return dayjs(date).format(props.dateFormat || 'YYYY-MM-DD')
  }

  // 防抖同步变更 - 与参考代码保持一致
  const syncChange = debounce(
    () => {
      const v = value.value
      const newValue: ValidityTimePickerValue = {
        qualValidityPeriod: v.qualValidityPeriod,
        startTime: v.qualValidityPeriod === 1 ? 0 : undefined, // Period.NON_PERMANENT
        endTime: v.endTime ? dayjs(v.endTime).valueOf() : undefined,
      }
      emits('update:modelValue', newValue)
      emits('change', newValue)
    },
    300
  )

  // 日期变更处理 - 与参考代码保持一致
  const changeDate = (date: string) => {
    if (date) {
      value.value.qualValidityPeriod = 1 // Period.NON_PERMANENT
    }
    value.value.endTime = date
    syncChange()
  }

  // 事件处理
  const handleDisplayClick = () => {
    if (props.readOnly || props.disabled) return

    visible.value = true
  }

  const handleCancel = () => {
    visible.value = false
    datePickerVisible.value = false
  }

  const handleConfirm = () => {
    visible.value = false
  }

  const handleValidityTypeChange = (newVal: boolean) => {
    checked.value = newVal
  }

  const showDatePicker = () => {
    datePickerVisible.value = true
  }

  const handleDateConfirm = (date: string | number | Date) => {
    const dateStr = dayjs(date).format('YYYY-MM-DD')
    changeDate(dateStr)
    datePickerVisible.value = false
  }

  const handleDateCancel = () => {
    datePickerVisible.value = false
  }

  const reset = () => {
    value.value = transformValue({} as ValidityTimePickerValue)
    visible.value = false
    datePickerVisible.value = false
  }

  // 监听器 - 与参考代码保持一致
  watch(
    () => props.modelValue,
    () => {
      value.value = transformValue(props.modelValue)
    }
  )

  return {
    // 状态
    visible,
    datePickerVisible,
    value,
    checked,

    // 计算属性
    displayValue,
    formattedValue,
    isValid,

    // 方法
    formatDate,
    changeDate,
    handleDisplayClick,
    handleCancel,
    handleConfirm,
    handleValidityTypeChange,
    showDatePicker,
    handleDateConfirm,
    handleDateCancel,
    reset
  }
}

/**
 * 有效期时间选择器工具函数 - 与参考代码保持一致
 */
export const validityTimePickerUtils = {
  /**
   * 验证有效期时间选择器的值是否有效
   */
  isValidValue(value: ValidityTimePickerValue | null): boolean {
    if (!value) return false

    if (value.qualValidityPeriod === 0) return true // Period.PERMANENT

    return value.qualValidityPeriod === 1 && !!value.endTime // Period.NON_PERMANENT
  },

  /**
   * 格式化显示文本
   */
  formatDisplayText(value: ValidityTimePickerValue | null, dateFormat = 'YYYY-MM-DD'): string {
    if (!value) return ''

    if (value.qualValidityPeriod === 0) { // Period.PERMANENT
      return '永久有效'
    } else if (value.qualValidityPeriod === 1 && value.endTime) { // Period.NON_PERMANENT
      return `有效期至 ${dayjs(value.endTime).format(dateFormat)}`
    }

    return ''
  },

  /**
   * 创建永久有效的值
   */
  createPermanentValue(): ValidityTimePickerValue {
    return {
      qualValidityPeriod: 0, // Period.PERMANENT
      startTime: undefined,
      endTime: undefined
    }
  },

  /**
   * 创建期限有效的值
   */
  createLimitedValue(date: string | number | Date): ValidityTimePickerValue {
    return {
      qualValidityPeriod: 1, // Period.NON_PERMANENT
      startTime: 0,
      endTime: dayjs(date).valueOf()
    }
  },

  /**
   * 检查日期是否已过期
   */
  isExpired(value: ValidityTimePickerValue | null): boolean {
    if (!value || value.qualValidityPeriod === 0) return false // Period.PERMANENT

    if (value.qualValidityPeriod === 1 && value.endTime) { // Period.NON_PERMANENT
      return dayjs().isAfter(dayjs(value.endTime))
    }

    return false
  },

  /**
   * 检查是否有有效的有效期设置
   */
  hasValid(value?: ValidityTimePickerValue): boolean {
    const period = value?.qualValidityPeriod
    if (period === 0) return true // Period.PERMANENT
    return period === 1 && !!value?.endTime // Period.NON_PERMANENT
  }
}

// 向后兼容性别名
export const timePickerUtils = validityTimePickerUtils

// 向后兼容性函数别名
export const useTimePicker = useValidityTimePicker
