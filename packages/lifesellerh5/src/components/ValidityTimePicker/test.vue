<template>
  <div class="test-page">
    <h2>ValidityTimePicker 组件测试</h2>

    <div class="test-case">
      <h3>基础测试</h3>
      <ValidityTimePicker
        v-model="testValue"
        placeholder="测试有效期时间选择器"
        @change="handleChange"
      />
      <p>当前值: {{ JSON.stringify(testValue) }}</p>
    </div>

    <div class="test-case">
      <h3>只读测试</h3>
      <ValidityTimePicker
        v-model="readOnlyTestValue"
        :read-only="true"
        placeholder="只读模式"
      />
    </div>

    <div class="test-buttons">
      <button @click="setPermanent">设置永久</button>
      <button @click="setLimited">设置期限</button>
      <button @click="clearValue">清空</button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import ValidityTimePicker from './index.vue'
  import { validityTimePickerUtils } from './composable'
  import type { ValidityTimePickerValue } from './types'

  const testValue = ref<ValidityTimePickerValue | null>(null)
  const readOnlyTestValue = ref<ValidityTimePickerValue | null>(validityTimePickerUtils.createPermanentValue())

  const handleChange = (value: ValidityTimePickerValue | null) => {
    console.log('值变化:', value)
  }

  const setPermanent = () => {
    testValue.value = validityTimePickerUtils.createPermanentValue()
  }

  const setLimited = () => {
    testValue.value = validityTimePickerUtils.createLimitedValue(new Date())
  }

  const clearValue = () => {
    testValue.value = null
  }
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-case {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-buttons {
  display: flex;
  gap: 10px;
}

.test-buttons button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
}
</style>
