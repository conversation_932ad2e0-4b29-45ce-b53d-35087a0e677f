<template>
  <div class="preview-page">
    <Header title="有效期时间选择器预览" />
    
    <main class="preview-content">
      <!-- 模拟页面样式的容器 -->
      <div class="form-item-layout">
        <div class="form-item">
          <p class="title">有效期</p>
          <div class="form-item-box border-radius-default">
            <div class="item border-radius-default">
              <ValidityTimePicker
                v-model="timeValue"
                @change="handleTimeChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 显示当前值 -->
      <div class="debug-info">
        <h3>当前值:</h3>
        <pre>{{ JSON.stringify(timeValue, null, 2) }}</pre>
        
        <div class="test-buttons">
          <button @click="setPermanent">设置永久有效</button>
          <button @click="setLimited">设置期限有效</button>
          <button @click="clearValue">清空值</button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import dayjs from 'dayjs'
  
  // 组件
  import ValidityTimePicker from './index.vue'
  import Header from '~/components/header/index.vue'
  import { validityTimePickerUtils } from './composable'
  import type { ValidityTimePickerValue } from './types'
  
  // 响应式数据
  const timeValue = ref<ValidityTimePickerValue | null>(null)
  
  // 事件处理
  const handleTimeChange = (value: ValidityTimePickerValue | null) => {
    console.log('时间选择变化:', value)
  }
  
  // 测试方法
  const setPermanent = () => {
    timeValue.value = validityTimePickerUtils.createPermanentValue()
  }
  
  const setLimited = () => {
    const tomorrow = dayjs().add(1, 'day').toDate()
    timeValue.value = validityTimePickerUtils.createLimitedValue(tomorrow)
  }
  
  const clearValue = () => {
    timeValue.value = null
  }
</script>

<style lang="stylus" scoped>
.preview-page
  min-height 100vh
  background #F5F5F5

.preview-content
  padding 16px

// 模拟页面样式
.form-item-layout
  margin-bottom 20px

.form-item
  width 100%

  .title
    margin 0 0 12px 8px
    color rgba(0, 0, 0, 0.62)
    font-family "PingFang SC"
    font-size 12px
    font-style normal
    font-weight 400
    line-height 18px

  .form-item-box
    width 100%
    height 100%
    padding-left 16px
    background #FFF

    .item
      display flex
      width 100%
      padding 12px 16px 12px 0
      margin-bottom 0
      flex-direction column
      align-items flex-start

.border-radius-default
  border-radius 8px

// 调试信息
.debug-info
  margin-top 20px
  padding 16px
  background #fff
  border-radius 8px
  
  h3
    margin 0 0 12px 0
    color rgba(0, 0, 0, 0.80)
    font-size 16px
    font-weight 500
  
  pre
    margin 0 0 16px 0
    padding 12px
    background #f5f5f5
    border-radius 4px
    font-size 12px
    color rgba(0, 0, 0, 0.60)
    overflow auto

.test-buttons
  display flex
  gap 8px
  flex-wrap wrap
  
  button
    padding 8px 16px
    border 1px solid #ddd
    border-radius 4px
    background #fff
    cursor pointer
    font-size 14px
    
    &:hover
      background #f5f5f5
</style>
