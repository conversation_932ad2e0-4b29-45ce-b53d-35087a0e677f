<template>
  <div class="validity-time-picker-demo">
    <Header title="有效期时间选择器示例" />

    <main class="demo-content">
      <!-- 基础用法 -->
      <section class="demo-section">
        <h3 class="section-title">基础用法</h3>
        <div class="demo-item">
          <ValidityTimePicker
            v-model="basicValue"
            placeholder="请选择有效期"
            @change="handleBasicChange"
          />
          <div class="result">
            <p><strong>当前值:</strong></p>
            <pre>{{ JSON.stringify(basicValue, null, 2) }}</pre>
          </div>
        </div>
      </section>

      <!-- 只读模式 -->
      <section class="demo-section">
        <h3 class="section-title">只读模式</h3>
        <div class="demo-item">
          <ValidityTimePicker
            v-model="readOnlyValue"
            :read-only="true"
            placeholder="只读状态"
          />
          <div class="result">
            <p><strong>只读值:</strong> {{ formatDisplayText(readOnlyValue) }}</p>
          </div>
        </div>
      </section>

      <!-- 自定义格式 -->
      <section class="demo-section">
        <h3 class="section-title">自定义日期格式</h3>
        <div class="demo-item">
          <ValidityTimePicker
            v-model="customFormatValue"
            date-format="YYYY年MM月DD日"
            placeholder="自定义格式"
            label="选择有效期（自定义格式）"
          />
          <div class="result">
            <p><strong>格式化显示:</strong> {{ formatDisplayText(customFormatValue, 'YYYY年MM月DD日') }}</p>
          </div>
        </div>
      </section>

      <!-- 自定义显示 -->
      <section class="demo-section">
        <h3 class="section-title">自定义显示</h3>
        <div class="demo-item">
          <ValidityTimePicker v-model="customDisplayValue">
            <template #display="{ value, formatted }">
              <div class="custom-display">
                <div class="custom-label">📅 有效期设置</div>
                <div class="custom-value">
                  {{ formatted || '点击设置有效期' }}
                </div>
                <div v-if="value && value.type === 'limited'" class="custom-status">
                  {{ isExpired(value) ? '⚠️ 已过期' : '✅ 有效' }}
                </div>
              </div>
            </template>
          </ValidityTimePicker>
        </div>
      </section>

      <!-- 工具函数演示 -->
      <section class="demo-section">
        <h3 class="section-title">工具函数演示</h3>
        <div class="demo-item">
          <div class="utils-demo">
            <Button @click="setPermanent">设置永久有效</Button>
            <Button @click="setLimited">设置期限有效（明天）</Button>
            <Button @click="setExpired">设置已过期日期</Button>
            <Button @click="clearValue">清空值</Button>
          </div>
          <ValidityTimePicker
            v-model="utilsValue"
            placeholder="工具函数演示"
          />
          <div class="result">
            <p><strong>值状态:</strong></p>
            <ul>
              <li>是否有效: {{ isValidValue(utilsValue) ? '✅' : '❌' }}</li>
              <li>是否过期: {{ isExpired(utilsValue) ? '⚠️ 是' : '✅ 否' }}</li>
              <li>显示文本: {{ formatDisplayText(utilsValue) || '无' }}</li>
            </ul>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import dayjs from 'dayjs'

  // 组件
  import ValidityTimePicker from './index.vue'
  import Header from '~/components/header/index.vue'
  import { Button } from '@xhs/reds-h5-next'

  // 工具函数
  import { validityTimePickerUtils } from './composable'
  import type { ValidityTimePickerValue } from './types'

  // 响应式数据
  const basicValue = ref<ValidityTimePickerValue | null>(null)
  const readOnlyValue = ref<ValidityTimePickerValue | null>(validityTimePickerUtils.createPermanentValue())
  const customFormatValue = ref<ValidityTimePickerValue | null>(null)
  const customDisplayValue = ref<ValidityTimePickerValue | null>(null)
  const utilsValue = ref<ValidityTimePickerValue | null>(null)

  // 工具函数解构
  const {
    isValidValue,
    formatDisplayText,
    createPermanentValue,
    createLimitedValue,
    isExpired
  } = validityTimePickerUtils

  // 事件处理
  const handleBasicChange = (value: ValidityTimePickerValue | null) => {
    console.log('基础用法值变化:', value)

    if (isExpired(value)) {
      console.warn('选择的日期已过期')
    }
  }

  // 工具函数演示
  const setPermanent = () => {
    utilsValue.value = createPermanentValue()
  }

  const setLimited = () => {
    const tomorrow = dayjs().add(1, 'day').toDate()
    utilsValue.value = createLimitedValue(tomorrow)
  }

  const setExpired = () => {
    const yesterday = dayjs().subtract(1, 'day').toDate()
    utilsValue.value = createLimitedValue(yesterday)
  }

  const clearValue = () => {
    utilsValue.value = null
  }
</script>

<style lang="stylus" scoped>
.validity-time-picker-demo
  min-height 100vh
  background #f5f5f5

.demo-content
  padding 16px

.demo-section
  margin-bottom 24px
  background #fff
  border-radius 12px
  padding 16px

.section-title
  color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
  font-size 18px
  font-weight 600
  margin 0 0 16px 0

.demo-item
  margin-bottom 16px

  &:last-child
    margin-bottom 0

.result
  margin-top 12px
  padding 12px
  background #f8f8f8
  border-radius 8px

  p
    margin 0 0 8px 0
    color var(--Light-Labels-Title, rgba(0, 0, 0, 0.80))
    font-size 14px
    font-weight 500

  pre
    margin 0
    color var(--Light-Labels-Secondary, rgba(0, 0, 0, 0.60))
    font-size 12px
    font-family 'Monaco', 'Menlo', 'Ubuntu Mono', monospace
    white-space pre-wrap
    word-break break-all

  ul
    margin 0
    padding-left 16px

    li
      color var(--Light-Labels-Secondary, rgba(0, 0, 0, 0.60))
      font-size 14px
      margin-bottom 4px

.custom-display
  display flex
  flex-direction column
  padding 16px
  background linear-gradient(135deg, #667eea 0%, #764ba2 100%)
  border-radius 12px
  color white

.custom-label
  font-size 14px
  opacity 0.9
  margin-bottom 4px

.custom-value
  font-size 16px
  font-weight 500
  margin-bottom 4px

.custom-status
  font-size 12px
  opacity 0.8

.utils-demo
  display flex
  gap 8px
  margin-bottom 16px
  flex-wrap wrap

  .reds-button
    flex 1
    min-width 80px
    font-size 12px
</style>
