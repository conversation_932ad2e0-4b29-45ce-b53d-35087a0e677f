<template>
  <div class="mobile-test-page">
    <Header title="移动端H5优化测试" />
    
    <main class="test-content">
      <!-- 移动端环境信息 -->
      <section class="env-info">
        <h3>移动端环境检测</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">设备类型:</span>
            <span class="value">{{ deviceInfo.isMobile ? '移动设备' : '桌面设备' }}</span>
          </div>
          <div class="info-item">
            <span class="label">触摸支持:</span>
            <span class="value">{{ deviceInfo.hasTouch ? '支持' : '不支持' }}</span>
          </div>
          <div class="info-item">
            <span class="label">安全区域:</span>
            <span class="value">{{ deviceInfo.hasSafeArea ? '支持' : '不支持' }}</span>
          </div>
          <div class="info-item">
            <span class="label">视觉视口:</span>
            <span class="value">{{ deviceInfo.hasVisualViewport ? '支持' : '不支持' }}</span>
          </div>
        </div>
      </section>

      <!-- 基础功能测试 -->
      <section class="test-section">
        <h3>基础功能测试</h3>
        <div class="form-item-layout">
          <div class="form-item">
            <p class="title">有效期选择</p>
            <div class="form-item-box border-radius-default">
              <div class="item border-radius-default">
                <ValidityTimePicker
                  v-model="testValue"
                  @change="handleChange"
                />
              </div>
            </div>
          </div>
        </div>
        
        <div class="result-display">
          <h4>当前值:</h4>
          <pre>{{ JSON.stringify(testValue, null, 2) }}</pre>
        </div>
      </section>

      <!-- 触摸反馈测试 -->
      <section class="test-section">
        <h3>触摸反馈测试</h3>
        <div class="touch-test-grid">
          <button 
            class="touch-test-btn"
            @touchstart="handleTouchFeedback"
            @touchend="handleTouchEnd"
          >
            触摸反馈按钮
          </button>
          <div class="touch-info">
            <p>触摸状态: {{ touchState }}</p>
            <p>触摸次数: {{ touchCount }}</p>
          </div>
        </div>
      </section>

      <!-- 性能测试 -->
      <section class="test-section">
        <h3>性能测试</h3>
        <div class="performance-test">
          <button @click="runPerformanceTest">运行性能测试</button>
          <div v-if="performanceResults.length" class="performance-results">
            <h4>测试结果:</h4>
            <ul>
              <li v-for="result in performanceResults" :key="result.name">
                {{ result.name }}: {{ result.duration.toFixed(2) }}ms
              </li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 键盘适配测试 -->
      <section class="test-section">
        <h3>键盘适配测试</h3>
        <div class="keyboard-test">
          <input 
            v-model="testInput"
            type="text" 
            placeholder="点击输入测试键盘弹出"
            class="test-input"
          />
          <p>键盘状态: {{ keyboardStatus }}</p>
          <p>视口高度: {{ viewportHeight }}px</p>
        </div>
      </section>

      <!-- 快捷操作 -->
      <section class="test-section">
        <h3>快捷操作</h3>
        <div class="quick-actions">
          <button @click="setPermanent">设置永久有效</button>
          <button @click="setLimited">设置期限有效</button>
          <button @click="clearValue">清空值</button>
          <button @click="toggleReadOnly">切换只读: {{ isReadOnly ? '是' : '否' }}</button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from 'vue'
  import dayjs from 'dayjs'
  
  // 组件
  import ValidityTimePicker from './index.vue'
  import Header from '~/components/header/index.vue'
  import { validityTimePickerUtils } from './composable'
  import type { ValidityTimePickerValue } from './types'
  
  // 移动端工具
  import { 
    isMobile, 
    isTouchDevice, 
    checkMobileCompatibility,
    getSafeAreaInsets,
    getViewportHeight,
    detectKeyboard,
    performanceMonitor
  } from './mobile-utils'
  
  // 响应式数据
  const testValue = ref<ValidityTimePickerValue | null>(null)
  const testInput = ref('')
  const isReadOnly = ref(false)
  const touchState = ref('未触摸')
  const touchCount = ref(0)
  const keyboardStatus = ref('未知')
  const viewportHeight = ref(getViewportHeight())
  
  // 设备信息
  const deviceInfo = reactive({
    isMobile: isMobile(),
    hasTouch: isTouchDevice(),
    hasSafeArea: checkMobileCompatibility().safeArea,
    hasVisualViewport: checkMobileCompatibility().visualViewport
  })
  
  // 性能测试结果
  const performanceResults = ref<Array<{ name: string; duration: number }>>([])
  
  // 事件处理
  const handleChange = (value: ValidityTimePickerValue | null) => {
    console.log('值变化:', value)
  }
  
  const handleTouchFeedback = () => {
    touchState.value = '触摸中'
    touchCount.value++
  }
  
  const handleTouchEnd = () => {
    touchState.value = '触摸结束'
    setTimeout(() => {
      touchState.value = '未触摸'
    }, 1000)
  }
  
  // 性能测试
  const runPerformanceTest = () => {
    performanceResults.value = []
    
    // 测试组件渲染性能
    performanceMonitor.start('component-render')
    // 模拟组件操作
    setTimeout(() => {
      performanceMonitor.end('component-render')
      
      // 获取性能结果
      const measures = performance.getEntriesByType('measure')
      performanceResults.value = measures.map(measure => ({
        name: measure.name,
        duration: measure.duration
      }))
    }, 100)
  }
  
  // 快捷操作
  const setPermanent = () => {
    testValue.value = validityTimePickerUtils.createPermanentValue()
  }
  
  const setLimited = () => {
    const tomorrow = dayjs().add(1, 'day').toDate()
    testValue.value = validityTimePickerUtils.createLimitedValue(tomorrow)
  }
  
  const clearValue = () => {
    testValue.value = null
  }
  
  const toggleReadOnly = () => {
    isReadOnly.value = !isReadOnly.value
  }
  
  // 生命周期
  let keyboardCleanup: (() => void) | null = null
  
  onMounted(() => {
    // 键盘检测
    if (deviceInfo.isMobile) {
      keyboardCleanup = detectKeyboard((isOpen) => {
        keyboardStatus.value = isOpen ? '键盘已弹出' : '键盘已收起'
        viewportHeight.value = getViewportHeight()
      })
    }
    
    // 视口变化监听
    const handleResize = () => {
      viewportHeight.value = getViewportHeight()
    }
    
    window.addEventListener('resize', handleResize)
    
    // 清理函数
    onUnmounted(() => {
      if (keyboardCleanup) {
        keyboardCleanup()
      }
      window.removeEventListener('resize', handleResize)
    })
  })
</script>

<style lang="stylus" scoped>
.mobile-test-page
  min-height 100vh
  background #F5F5F5
  padding-bottom env(safe-area-inset-bottom)

.test-content
  padding 16px
  padding-bottom calc(16px + env(safe-area-inset-bottom))

.test-section
  margin-bottom 24px
  background #fff
  border-radius 12px
  padding 16px

  h3
    margin 0 0 16px 0
    color rgba(0, 0, 0, 0.80)
    font-size 18px
    font-weight 600

.env-info
  .info-grid
    display grid
    grid-template-columns 1fr 1fr
    gap 12px
  
  .info-item
    display flex
    justify-content space-between
    padding 8px 12px
    background #f8f8f8
    border-radius 6px
    
    .label
      color rgba(0, 0, 0, 0.60)
      font-size 14px
    
    .value
      color rgba(0, 0, 0, 0.80)
      font-size 14px
      font-weight 500

.form-item-layout
  margin-bottom 16px

.form-item
  .title
    margin 0 0 12px 8px
    color rgba(0, 0, 0, 0.62)
    font-size 12px

  .form-item-box
    background #FFF
    padding-left 16px

    .item
      display flex
      padding 12px 16px 12px 0

.border-radius-default
  border-radius 8px

.result-display
  h4
    margin 0 0 8px 0
    font-size 14px
    color rgba(0, 0, 0, 0.80)
  
  pre
    margin 0
    padding 12px
    background #f8f8f8
    border-radius 6px
    font-size 12px
    overflow auto

.touch-test-grid
  display grid
  grid-template-columns 1fr 1fr
  gap 16px
  align-items center

.touch-test-btn
  padding 16px
  background #007AFF
  color white
  border none
  border-radius 8px
  font-size 16px
  min-height 44px
  cursor pointer
  
  &:active
    transform scale(0.95)

.touch-info
  p
    margin 0 0 8px 0
    font-size 14px
    color rgba(0, 0, 0, 0.80)

.performance-test
  button
    padding 12px 24px
    background #34C759
    color white
    border none
    border-radius 6px
    cursor pointer
    margin-bottom 16px

.performance-results
  h4
    margin 0 0 8px 0
    font-size 14px
  
  ul
    margin 0
    padding-left 16px
    
    li
      margin-bottom 4px
      font-size 12px
      color rgba(0, 0, 0, 0.60)

.keyboard-test
  .test-input
    width 100%
    padding 12px
    border 1px solid #ddd
    border-radius 6px
    font-size 16px
    margin-bottom 12px
  
  p
    margin 0 0 8px 0
    font-size 14px
    color rgba(0, 0, 0, 0.80)

.quick-actions
  display grid
  grid-template-columns 1fr 1fr
  gap 8px
  
  button
    padding 10px 16px
    background #FF9500
    color white
    border none
    border-radius 6px
    font-size 14px
    cursor pointer
    
    &:active
      transform scale(0.95)

/* 移动端优化 */
@media (max-width: 480px)
  .test-content
    padding 12px
  
  .info-grid
    grid-template-columns 1fr !important
  
  .touch-test-grid
    grid-template-columns 1fr !important
  
  .quick-actions
    grid-template-columns 1fr !important
</style>
