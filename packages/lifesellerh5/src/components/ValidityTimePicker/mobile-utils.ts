/**
 * 移动端H5优化工具函数
 */

// 检测移动设备
export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 检测iOS设备
export const isIOS = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

// 检测Android设备
export const isAndroid = (): boolean => {
  return /Android/.test(navigator.userAgent)
}

// 检测是否支持触摸
export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// 防抖函数 - 移动端优化版本
export const mobileDebounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number = 300
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func.apply(null, args)
      timeout = null
    }, wait)
  }
}

// 节流函数 - 移动端优化版本
export const mobileThrottle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number = 100
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

// 触摸反馈工具
export const addTouchFeedback = (element: HTMLElement, options?: {
  scale?: number
  duration?: number
}) => {
  const { scale = 0.98, duration = 100 } = options || {}
  
  const handleTouchStart = () => {
    element.style.transform = `scale(${scale})`
    element.style.transition = `transform ${duration}ms ease`
  }
  
  const handleTouchEnd = () => {
    element.style.transform = 'scale(1)'
    setTimeout(() => {
      element.style.transition = ''
    }, duration)
  }
  
  element.addEventListener('touchstart', handleTouchStart, { passive: true })
  element.addEventListener('touchend', handleTouchEnd, { passive: true })
  element.addEventListener('touchcancel', handleTouchEnd, { passive: true })
  
  // 返回清理函数
  return () => {
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchend', handleTouchEnd)
    element.removeEventListener('touchcancel', handleTouchEnd)
  }
}

// 安全区域检测
export const getSafeAreaInsets = () => {
  const style = getComputedStyle(document.documentElement)
  
  return {
    top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0', 10),
    right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0', 10),
    bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0', 10),
    left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0', 10)
  }
}

// 视口高度获取（考虑移动端键盘弹出）
export const getViewportHeight = (): number => {
  return window.visualViewport?.height || window.innerHeight
}

// 键盘状态检测
export const detectKeyboard = (callback: (isOpen: boolean) => void) => {
  if (!window.visualViewport) {
    return () => {} // 不支持的浏览器返回空清理函数
  }
  
  let initialHeight = window.visualViewport.height
  
  const handleResize = () => {
    const currentHeight = window.visualViewport!.height
    const heightDiff = initialHeight - currentHeight
    const isKeyboardOpen = heightDiff > 150 // 键盘高度阈值
    
    callback(isKeyboardOpen)
  }
  
  window.visualViewport.addEventListener('resize', handleResize)
  
  // 返回清理函数
  return () => {
    window.visualViewport?.removeEventListener('resize', handleResize)
  }
}

// 性能优化：requestAnimationFrame包装
export const raf = (callback: () => void): number => {
  return requestAnimationFrame(callback)
}

// 性能优化：取消requestAnimationFrame
export const cancelRaf = (id: number): void => {
  cancelAnimationFrame(id)
}

// 移动端滚动优化
export const optimizeScroll = (element: HTMLElement) => {
  element.style.webkitOverflowScrolling = 'touch'
  element.style.overflowScrolling = 'touch'
}

// 移动端点击延迟消除
export const fastClick = (element: HTMLElement, callback: () => void) => {
  let touchStartTime = 0
  let touchMoved = false
  
  const handleTouchStart = (e: TouchEvent) => {
    touchStartTime = Date.now()
    touchMoved = false
  }
  
  const handleTouchMove = () => {
    touchMoved = true
  }
  
  const handleTouchEnd = (e: TouchEvent) => {
    const touchDuration = Date.now() - touchStartTime
    
    if (!touchMoved && touchDuration < 300) {
      e.preventDefault()
      callback()
    }
  }
  
  element.addEventListener('touchstart', handleTouchStart, { passive: true })
  element.addEventListener('touchmove', handleTouchMove, { passive: true })
  element.addEventListener('touchend', handleTouchEnd)
  
  // 返回清理函数
  return () => {
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
  }
}

// 移动端兼容性检查
export const checkMobileCompatibility = () => {
  const features = {
    touch: isTouchDevice(),
    visualViewport: !!window.visualViewport,
    safeArea: CSS.supports('padding-top', 'env(safe-area-inset-top)'),
    webkitOverflowScrolling: CSS.supports('-webkit-overflow-scrolling', 'touch'),
    transform3d: CSS.supports('transform', 'translate3d(0,0,0)')
  }
  
  return features
}

// 移动端错误处理
export const handleMobileError = (error: Error, context: string) => {
  console.error(`[ValidityTimePicker Mobile Error] ${context}:`, error)
  
  // 可以在这里添加错误上报逻辑
  if (typeof window !== 'undefined' && window.console) {
    console.warn('移动端组件出现错误，请检查浏览器兼容性')
  }
}

// 移动端性能监控
export const performanceMonitor = {
  start: (name: string) => {
    if (performance.mark) {
      performance.mark(`${name}-start`)
    }
  },
  
  end: (name: string) => {
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
      
      const measure = performance.getEntriesByName(name)[0]
      if (measure && measure.duration > 16) { // 超过一帧的时间
        console.warn(`[Performance] ${name} took ${measure.duration.toFixed(2)}ms`)
      }
    }
  }
}
