# ValidityTimePicker 有效期时间选择组件

一个用于选择有效期的移动端组件，支持永久有效和期限有效两种模式。

## 特性

- 🕒 支持永久有效和期限有效两种模式
- 📅 集成日期选择器
- 📱 移动端优化的半浮层设计
- 🔄 完整的双向数据绑定支持
- 🔒 支持只读模式
- 🎨 支持自定义样式和主题
- ♿ 支持无障碍访问

## 使用方法

### 基础用法

```vue
<template>
  <ValidityTimePicker
    v-model="timeValue"
    @change="handleTimeChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import ValidityTimePicker from '~/components/ValidityTimePicker/index.vue'

const timeValue = ref(null)

const handleTimeChange = (value) => {
  console.log('时间选择变化:', value)
}
</script>
```

### 只读模式

```vue
<template>
  <ValidityTimePicker
    v-model="timeValue"
    :read-only="true"
    placeholder="查看有效期"
  />
</template>
```

### 自定义显示

```vue
<template>
  <ValidityTimePicker v-model="timeValue">
    <template #display="{ value, formatted }">
      <div class="custom-display">
        <span class="label">有效期：</span>
        <span class="value">{{ formatted || '未设置' }}</span>
      </div>
    </template>
  </ValidityTimePicker>
</template>
```

### 完整示例

```vue
<template>
  <div class="demo">
    <h3>时间选择器示例</h3>
    
    <!-- 基础用法 -->
    <div class="demo-item">
      <h4>基础用法</h4>
      <ValidityTimePicker
        v-model="basicValue"
        placeholder="请选择有效期"
        @change="handleBasicChange"
      />
      <p>当前值: {{ JSON.stringify(basicValue) }}</p>
    </div>

    <!-- 只读模式 -->
    <div class="demo-item">
      <h4>只读模式</h4>
      <ValidityTimePicker
        v-model="readOnlyValue"
        :read-only="true"
        placeholder="只读状态"
      />
    </div>

    <!-- 自定义格式 -->
    <div class="demo-item">
      <h4>自定义日期格式</h4>
      <ValidityTimePicker
        v-model="customFormatValue"
        date-format="YYYY年MM月DD日"
        placeholder="自定义格式"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ValidityTimePicker from '~/components/ValidityTimePicker/index.vue'
import { validityTimePickerUtils } from '~/components/ValidityTimePicker/composable'

const basicValue = ref(null)
const readOnlyValue = ref(timePickerUtils.createPermanentValue())
const customFormatValue = ref(null)

const handleBasicChange = (value) => {
  console.log('基础用法值变化:', value)
  
  // 检查是否过期
  if (timePickerUtils.isExpired(value)) {
    console.warn('选择的日期已过期')
  }
}
</script>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | `TimePickerValue \| null` | `null` | 双向绑定的值 |
| readOnly | `boolean` | `false` | 是否只读 |
| placeholder | `string` | `'请选择有效期'` | 占位符文本 |
| label | `string` | `'选择有效期'` | 弹窗标题 |
| colorMode | `'light' \| 'dark'` | `'light'` | 颜色模式 |
| dateFormat | `string` | `'YYYY-MM-DD'` | 日期格式化字符串 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(value: TimePickerValue \| null)` | 值更新事件 |
| change | `(value: TimePickerValue \| null)` | 值变化事件 |

### Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| display | `{ value: TimePickerValue \| null, formatted: string }` | 自定义显示区域 |

### 类型定义

```typescript
// 有效期类型枚举 - 与参考代码保持一致
enum Period {
  PERMANENT = 0,    // 永久有效
  NON_PERMANENT = 1 // 期限有效
}

interface TimePickerValue {
  /** 有效期类型：0-永久有效，1-期限有效 */
  qualValidityPeriod: Period
  /** 开始时间（期限有效时设为0） */
  startTime?: number
  /** 结束时间（时间戳或日期字符串） */
  endTime?: string | number
}
```

## 工具函数

组件提供了一系列工具函数来帮助处理时间选择器的值：

```typescript
import { validityTimePickerUtils } from '~/components/ValidityTimePicker/composable'

// 验证值是否有效
validityTimePickerUtils.isValidValue(value)

// 格式化显示文本
validityTimePickerUtils.formatDisplayText(value, 'YYYY-MM-DD')

// 创建永久有效的值
validityTimePickerUtils.createPermanentValue()
// 返回: { qualValidityPeriod: 0, startTime: undefined, endTime: undefined }

// 创建期限有效的值
validityTimePickerUtils.createLimitedValue(new Date())
// 返回: { qualValidityPeriod: 1, startTime: 0, endTime: timestamp }

// 检查是否已过期
validityTimePickerUtils.isExpired(value)

// 检查是否有有效的有效期设置
validityTimePickerUtils.hasValid(value)
```

## 样式特性

- 使用 Stylus 预处理器
- 响应式设计，适配移动端
- 遵循设计规范的颜色和尺寸
- 支持暗色模式

## 依赖

- Vue 3
- @xhs/reds-h5-next
- @xhs/onix-icon
- dayjs
- TypeScript
- Stylus

## 注意事项

1. 组件使用了 reds-h5-next 组件库，请确保已正确安装和配置
2. 组件采用 Composition API 编写，支持 TypeScript
3. 样式使用 scoped 作用域，避免样式冲突
4. 日期选择器仅支持日期选择，不包含时间选择
5. 组件会自动生成时间戳，方便后端处理
