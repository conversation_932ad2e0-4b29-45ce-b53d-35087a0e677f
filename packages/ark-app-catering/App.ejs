<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <% htmlWebpackPlugin.options.meta.forEach(function(item){ %>
      <meta
        <% for (var key in item) { %>
          <%= key %>="<%= item.name === 'viewport' && key === 'content' ? '' : item[key] %>"
        <% } %>
      >
    <% }) %>
    <title><%= htmlWebpackPlugin.options.title %></title>
    <script>
      (function() {
        var PaintTiming=function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return!(e instanceof HTMLHeadElement||e instanceof HTMLMetaElement||e instanceof HTMLLinkElement||e instanceof HTMLStyleElement||e instanceof HTMLScriptElement||e instanceof SVGElement||"none"===getComputedStyle(e).display)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.observe=function(){if(!window.MutationObserver||!window.performance||!performance.timing)return;window.__FP__=0,window.__FCP__=0,window.__FMP_OBSERVED_POINTS__=[],window.__FULLY_LOADED__=0;var e=void 0,t=0,n={},u=void 0,a=void 0;function _(t){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(0===window.__FULLY_LOADED__)if(r){n[t].end=Date.now();var o=Object.keys(n).some(function(e){return!n[e].end});o||(a=t,u=setTimeout(function(){e.disconnect();if(!window.__ROUTE_FMP_START__){var t=n[a].end;window.__FULLY_LOADED__=t-performance.timing.navigationStart,window.__FMP_OBSERVED_POINTS__=window.__FMP_OBSERVED_POINTS__.filter(function(e){return e.t<=window.__FULLY_LOADED__});var r=new CustomEvent("__fullyloaded__",{detail:{firstPaint:window.__FP__,firstContentfulPaint:window.__FCP__,fullyLoaded:window.__FULLY_LOADED__,observedPoints:window.__FMP_OBSERVED_POINTS__}});window.dispatchEvent(r)}},2e3))}else{var i=Date.now();n[t]={start:i};var _=u&&i<n[a].end+2e3;_&&clearTimeout(u)}}(e=new MutationObserver(function(e){e.forEach(function(e){"childList"===e.type?function e(n){var o=function(o){var u=n[o];0===window.__FP__&&u instanceof HTMLBodyElement&&(window.__FP__=Date.now()-performance.timing.navigationStart),0===window.__FCP__&&(0,i.default)(u)&&(window.__FCP__=Date.now()-performance.timing.navigationStart),u instanceof HTMLElement&&(0,r.default)(u)&&(_(t,!1),function(e){function t(){_(e)}u.addEventListener("load",t),u.addEventListener("error",t)}(t++)),u.childNodes&&e(u.childNodes)};for(var u=0;u<n.length;u++)o(u)}(e.addedNodes):"attributes"===e.type&&function(e){(0,r.default)(e)&&(_(t,!1),function(t){function n(){_(t)}e.addEventListener("load",n),e.addEventListener("error",n)}(t++))}(e.target)}),function(){if(!document.body||window.__ROUTE_FMP_START__)return;var e=document.body.clientHeight,t=window.innerHeight,n=(0,o.default)(document.body),r=window.__FMP_OBSERVED_POINTS__.length,i=window.__FMP_OBSERVED_POINTS__[r-1],u=i?n-i.allElementsNumber:n;window.__FMP_OBSERVED_POINTS__.push({t:Date.now()-performance.timing.navigationStart,layoutSignificance:u/Math.max(1,e/t),allElementsNumber:n})}()})).observe(document,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["src"]}),function(){var e=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.send=function(){var n=this;_(t,!1),function(e){n.addEventListener("readystatechange",function(){4===n.readyState&&_(e)})}(t++);for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return e.apply(this,o)}}()};var r=u(n(2)),o=u(n(3)),i=u(n(4));function u(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return"IMG"===e.tagName&&e.src}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t){if(!(0,r.default)(t))return 0;var n=1;if(t.children)for(var o=0;o<t.children.length;o++)n+=e(t.children[o]);return n};var r=function(e){return e&&e.__esModule?e:{default:e}}(n(0))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(e instanceof Text&&e.parentNode&&(0,r.default)(e.parentNode)&&e.textContent&&e.textContent.trim())return!0;return!1};var r=function(e){return e&&e.__esModule?e:{default:e}}(n(0))}]);
        PaintTiming.observe();
      })();
    </script>
    <% for (var key in htmlWebpackPlugin.options.preconnect) { %>
    <link rel="dns-prefetch" href="<%= htmlWebpackPlugin.options.preconnect[key] %>">
    <% } %>
    <% for (var key in htmlWebpackPlugin.options.preconnect) { %>
    <link rel="preconnect" href="<%= htmlWebpackPlugin.options.preconnect[key] %>" crossorigin>
    <% } %>
    <!-- error-tracker -->
    <% if (htmlWebpackPlugin.options.errorTracker) { %>
      <% var errorTracker = htmlWebpackPlugin.options.errorTracker; %>
      <script>window.__GLOBAL_SENTRY_CONFIG__=<%- JSON.stringify(errorTracker) %></script>
      <script src="<%= errorTracker.filepath %>" crossorigin async></script>
    <% } %>
    <!-- owl -->
    <% if (htmlWebpackPlugin.options.owl) { %>
      <% var owl = htmlWebpackPlugin.options.owl; %>
      <script src="<%= owl.filepath %>" app-id="<%= owl.appId %>" app-version="<%= owl.appVer %>" namespace="<%= owl.namespace %>" disable-auto-pv="<%= owl.disableAutoPv %>" need-cookie="<%= owl.needCookie %>" env="<%= owl.env %>" add-label-in-action="<%= owl.addLabelInAction %>" <% if (owl.plugins.length > 0) { %> plugins="<%= owl.plugins %>" <% } %> crossOrigin></script>
    <% } %>
    <!-- all css files would be injected here -->
    <% for (var key in compilation.externalStylesheets) { %>
      <%- compilation.externalStylesheets[key] %>
    <% } %>
    <% if (!htmlWebpackPlugin.options.inlineCss) { %>
      <% for (var key in htmlWebpackPlugin.files.css) { %>
        <link href="<%= htmlWebpackPlugin.files.css[key] %>" rel="stylesheet" crossorigin>
      <% } %>
    <% } %>
    <!-- all js files would be injected here -->
    <% for (var key in compilation.externalScripts) { %>
      <%- compilation.externalScripts[key] %>
    <% } %>
    <% for (key in htmlWebpackPlugin.files.js) { %>
    <script type="text/javascript" src="<%= htmlWebpackPlugin.files.js[key] %>" defer crossOrigin xhs-integrity="<%= htmlWebpackPlugin.files.jsIntegrity && htmlWebpackPlugin.files.jsIntegrity[key] %>"></script>
    <% } %>
  </head>
  <body>
    <div id="ark-app-catering-mount-container">
      <% if (htmlWebpackPlugin.options.sketch) { %>
        <%- include(htmlWebpackPlugin.options.sketch); %>
      <% } %>
    </div>
  </body>
</html>
