{
  "compilerOptions": {
    "module": "ESNext",
    "moduleResolution": "Node",
    "target": "ES2019",
    "jsx": "preserve",
    "resolveJsonModule": true,
    "strictNullChecks": true,
    "strictFunctionTypes": false,
    "baseUrl": "./",
    "strict": true,
    "esModuleInterop": true,
    // 允许从没有设置默认导出的模块中默认导入
    "allowSyntheticDefaultImports": true,
    "noImplicitAny": false,
    "noUnusedLocals": false,
    "paths": {
      "~/*": ["./src/*"],
      "@edith/*": ["../../edith/*"]
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost",
      "webworker",
      "ES2019",
      "ES2020"
    ],
    "types": [
      "node"
    ]
  },
  "include": [
    "./src/components/**/*",
    "./src/composables/**/*",
    "./src/config/**/*",
    "./src/constant/**/*",
    "./src/containers/**/*",
    "./src/Layout/**/*",
    "./src/services/**/*",
    "./src/store/**/*",
    "./src/types/**/*",
    "./src/utils/**/*",
    "../../module.d.ts",
    "../../node_modules/@xhs/apm-insight/src/types/"
  ],
  "exclude": [
    "node_modules",
    "**/node_modules/*",
    "formula.config.js",
  ]
}