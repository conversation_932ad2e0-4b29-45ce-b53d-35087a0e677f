/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34297
  * @name: 本地商家入驻-搜索城市
  * @identifier: api.redlife.merchant.search_coutryies.get
  * @version: undefined
  * @path: /api/redlife/merchant/search_coutryies
  * @method: get
  * @description: 本地商家入驻-搜索城市
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetSearchCoutryiesPayload {
	/** 地区 */
	lang?: string
}

export interface ICountrie {
	/** 城市code */
	code?: string
	/** 城市名称 */
	name?: string
	/** mogon code */
	mongoCode?: string
	/** 中文名 */
	cnname?: string
	/** 英文名 */
	enname?: string
}

export interface IData {
	/** 城市集合 */
	countries?: ICountrie[]
}

export interface IGetSearchCoutryiesResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getSearchCoutryies(params: IGetSearchCoutryiesPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/search_coutryies', { params, transform: false, ...{"needToast":true}, ...options })
}
