/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34293
  * @name: 独立入驻-对公打款搜索支行所在地
  * @identifier: api.redlife.merchant.bankarea.query.get
  * @version: undefined
  * @path: /api/redlife/merchant/bankarea/query
  * @method: get
  * @description: 独立入驻流程中，用户选择对公打款验证方式，搜索支行所在地
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetBankareaQueryPayload {
	/** 地区类型，PROVINCE：省、CITY：市 */
	areaType?: string
	/** 上级地区代码，areaType为CITY时必填 */
	parentAreaCode?: string
}

export interface IAreaList {
	/** 地区代码 */
	areaCode?: string
	/** 地区名称 */
	areaName?: string
	/** 地区类型，PROVINCE：省、CITY：市 */
	areaType?: string
	/** 上级地区代码 */
	parentAreaCode?: string
	/** 父级地区名称 */
	parentAreaName?: string
}

export interface IData {
	/** 区域列表 */
	areaList?: IAreaList[]
}

export interface IGetBankareaQueryResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getBankareaQuery(params: IGetBankareaQueryPayload, options = {}): Promise<IData> {
	return http.get('/api/redlife/merchant/bankarea/query', { params, transform: false, ...{ "needToast": true }, ...options })
}
