/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 35120
  * @name: 独立入驻-查询打款验证进度
  * @identifier: api.redlife.merchant.paymentstatus.query.get
  * @version: undefined
  * @path: /api/redlife/merchant/paymentstatus/query
  * @method: get
  * @description: 在独立入驻过程中，使用打款验证方式验证法人时，查询打款验证进度
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IBindBankAccountStatusDTO {
	/** 打款状态 */
	bindBankAccountStatus?: string
	/** 发起验证的时间 */
	bindTime?: number
}

export interface IData {
	/** 打款状态结构体 */
	bindBankAccountStatusDTO?: IBindBankAccountStatusDTO
}

export interface IGetPaymentstatusQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getPaymentstatusQuery(options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/paymentstatus/query', { transform: false, ...{"needToast":true}, ...options })
}
