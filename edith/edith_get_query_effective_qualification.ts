/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	* 
	* @id: 31194
	* @name: 本地生活 - 查询门店当前有效的资质信息
	* @identifier: api.redlife.life_service.poi.query_effective_qualification.get
	* @version: undefined
	* @path: /api/redlife/life_service/poi/query_effective_qualification
	* @method: get
	* @description: 本地生活 - 查询门店当前有效的资质信息
	* 
	* @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQueryEffectiveQualificationPayload {
	/** 商家id */
	sellerId?: string
	/** 门店id */
	poiId: string
	/** userId */
	userId?: string
}

export interface IBusinessLicenseImage {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IBusinessLicenseValid {
	/** 资质有效期 */
	qualValidityPeriod?: number
	/** startTime */
	startTime?: number
	/** endTime */
	endTime?: number
}

export interface IIndustryLicenseImage {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface ISupplementaryMaterial {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IQualificationValidity {
	/** 资质有效期 */
	qualValidityPeriod?: number
	/** 开始时间 */
	startTime?: number
	/** 结束时间 */
	endTime?: number
}

export interface IMediaInfoList {
	/** 资质图片列表 */
	url?: string
	/** width */
	width?: number
	/** height */
	height?: number
	/** urlBackup */
	urlBackup?: string
	/** urlBackupWidth */
	urlBackupWidth?: number
	/** urlBackupHeight */
	urlBackupHeight?: number
}

export interface IValue {
	/** 资质code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质有效性 */
	qualificationValidity?: IQualificationValidity
	/** 资质图片列表 */
	mediaInfoList?: IMediaInfoList[]
}

export interface IEffectiveQualification {
	/** 营业执照类别 */
	businessLicenseType?: number
	/** 媒体信息 图片、视频 */
	businessLicenseImage?: IBusinessLicenseImage
	/** 统一社会信用代码 */
	usci?: string
	/** 营业执照有效期 */
	businessLicenseValid?: IBusinessLicenseValid
	/** 行业资质类别 */
	industryLicenseType?: string
	/** 媒体信息 图片、视频 */
	industryLicenseImage?: IIndustryLicenseImage
	/** 行业资质有效期 */
	industryLicenseValid?: string
	/** 营业执照名称 */
	companyName?: string
	/** 补充材料 */
	supplementaryMaterial?: ISupplementaryMaterial[]
	/** 上次通过审核时间 */
	lastReviewTime?: number
	/** 行业资质MAP */
	qualificationMap?: Record<string, IValue>
}

export interface IData {
	/** 生效的资质 */
	effectiveQualification?: IEffectiveQualification
}

export interface IGetQueryEffectiveQualificationResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getQueryEffectiveQualification(params: IGetQueryEffectiveQualificationPayload, options = {}): Promise<IData> {
	return http.get('/api/redlife/life_service/poi/query_effective_qualification', { params, transform: false, ...{ "needToast": true }, ...options })
}
