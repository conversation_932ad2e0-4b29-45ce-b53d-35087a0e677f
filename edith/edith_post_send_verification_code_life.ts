/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 50471
  * @name: 验证码发送
  * @identifier: api.redlife.common.send_verification_code.post
  * @version: undefined
  * @path: /api/redlife/common/send_verification_code
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IVerificationCodeInfo {
	/** 用户id */
	userId?: string
	/** 验证类型 phone，email */
	verificationType?: string
	/** 手机号码/邮箱 */
	contactInfo?: string
	/** 设备ip */
	clientIp?: string
	/** 区号 */
	zoneCode?: string
	/** 验证码 */
	verificationCode?: string
}

export interface IPostSendVerificationCodePayload {
	/** 验证码信息 */
	verificationCodeInfo?: IVerificationCodeInfo
}

export interface IData {
	/** 风控参数 */
	antispamVerifyCtrlResp?: string
}

export interface IPostSendVerificationCodeResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function postSendVerificationCode(payload: IPostSendVerificationCodePayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/common/send_verification_code', payload, { transform: false, ...{"needToast":true}, ...options })
}
