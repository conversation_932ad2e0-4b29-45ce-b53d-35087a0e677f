/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33970
  * @name: 商家入驻-门店认领弹出框可认领门店查询
  * @identifier: api.redlife.merchant.poi.query_claimable_poi.post
  * @version: undefined
  * @path: /api/redlife/merchant/poi/query_claimable_poi
  * @method: post
  * @description: 门店认领弹出框可认领门店查询
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostQueryClaimablePoiPayload {
	/** 当前页 */
	pageNo: number
	/** 页面大小 */
	pageSize: number
	/** 省名称 */
	province: string
	/** 市名称 */
	city?: string
	/** 区名称 */
	district?: string
	/** poi名称 */
	poiName: string
	/** 商家ID */
	sellerId?: string
}

export interface IClaimablePoiInfo {
	/** poiId */
	poiId?: string
	/** gaodeId */
	gaodeId?: string
	/** poi名称 */
	poiName?: string
	/** 经度 */
	longitude?: string
	/** 纬度 */
	latitude?: string
	/** 省名称 */
	provinceName?: string
	/** 市名称 */
	cityName?: string
	/** 区名称 */
	districtName?: string
	/** 详细地址 */
	addressDetail?: string
	/** 认领状态 */
	claimStatus?: string
}

export interface IData {
	/** 可认领门店列表 */
	claimablePoiInfo?: IClaimablePoiInfo[]
	/** 总数据量 */
	total?: number
}

export interface IPostQueryClaimablePoiResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postQueryClaimablePoi(payload: IPostQueryClaimablePoiPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/poi/query_claimable_poi', payload, { transform: false, ...{"needToast":true}, ...options })
}
