/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33776
  * @name: 本地生活 - 省市区接口
  * @identifier: api.redlife.common.query_region.get
  * @version: undefined
  * @path: /api/redlife/common/query_region
  * @method: get
  * @description: 本地生活 - 省市区接口
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQueryRegionPayload {
	/** 地区编码 查省时使用1 */
	upperCode: string
	/** 是否从该节点构造完整子树 */
	needAllChildren?: boolean
}

export interface IChildren {
	/** 名称 */
	name?: string
	/** 编码 */
	code?: string
	/** 叶子节点 */
	leaf?: boolean
	/** 子节点 */
	children?: IChildren[]
	/** 父节点编码 */
	upper?: string
	/** level */
	level?: number
}

export interface IRegionList {
	/** 名称 */
	name?: string
	/** 编码 */
	code?: string
	/** 叶子节点 */
	leaf?: boolean
	/** 子节点 */
	children?: IChildren[]
	/** 父节点编码 */
	upper?: string
	/** level */
	level?: number
}

export interface IData {
	/** regionList */
	regionList?: IRegionList[]
}

export interface IGetQueryRegionResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getQueryRegion(params: IGetQueryRegionPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/common/query_region', { params, transform: false, ...{"needToast":true}, ...options })
}
