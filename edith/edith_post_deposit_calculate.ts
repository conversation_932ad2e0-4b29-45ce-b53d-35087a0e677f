/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34268
  * @name: 本地商家入驻-获取类目保证金与佣金率
  * @identifier: api.redlife.merchant.deposit_calculate.post
  * @version: undefined
  * @path: /api/redlife/merchant/deposit_calculate
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface ICategoryList {
	/** 类目id */
	categoryId?: string
	/** 全选，半选 */
	checkStatus?: string
}

export interface IPostDepositCalculatePayload {
	/** 类目信息 */
	categoryList?: ICategoryList[]
	/** 店铺类型-INDIVIDUAL_BUSINESS:个体工商主体,ENTERPRISE_PRINCIPAL:企业主体 */
	sellerType?: string
	/** 商家身份 */
	businessIdentityCode?: string
}

export interface IChildCategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目层级 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父ID */
	parentCategoryId?: string
	/** 是否可选 */
	visible?: boolean
	/** 业务类型 */
	businessId?: string
	/** 是否需要资质 */
	needQualification?: boolean
	/** 类目支持的店铺类型 */
	sellerTypeList?: string[]
	/** 全选半选标记 */
	checkStatus?: string
	/** 子类目 */
	childCategoryList?: IChildCategoryList[]
	/** 是否存在子节点 */
	hasSub?: boolean
	/** 类目业务类型配置 for美妆个护、露营特殊处理 */
	categoryBusinessType?: string
	/** 不可选原因 */
	msg?: string
}

export interface ICategoryList1 {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目层级 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父ID */
	parentCategoryId?: string
	/** 是否可选 */
	visible?: boolean
	/** 业务类型 */
	businessId?: string
	/** 是否需要资质 */
	needQualification?: boolean
	/** 类目支持的店铺类型 */
	sellerTypeList?: string[]
	/** 全选半选标记 */
	checkStatus?: string
	/** 子类目 */
	childCategoryList?: IChildCategoryList[]
	/** 是否存在子节点 */
	hasSub?: boolean
	/** 类目业务类型配置 for美妆个护、露营特殊处理 */
	categoryBusinessType?: string
	/** 不可选原因 */
	msg?: string
}

export interface IDepositInfo {
	/** 币种 */
	currency?: string
	/** 金额 */
	amount?: string
}

export interface IDepositConfigItemList {
	/** 类目列表 */
	categoryList?: ICategoryList1[]
	/** 保证金 */
	depositInfo?: IDepositInfo
	/** 佣金率 */
	commissionRate?: string
}

export interface IHighestDepositInfo {
	/** 币种 */
	currency?: string
	/** 金额 */
	amount?: string
}

export interface IData {
	/** 类目保证金明细 */
	depositConfigItemList?: IDepositConfigItemList[]
	/** 保证金 */
	highestDepositInfo?: IHighestDepositInfo
	/** 佣金率 */
	commissionRate?: string
	/** 最低佣金率 */
	lowestCommission?: string
	/** 最高佣金率 */
	highestCommission?: string
}

export interface IPostDepositCalculateResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data?: IData
}

export function postDepositCalculate(payload: IPostDepositCalculatePayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/deposit_calculate', payload, { transform: false, ...{"needToast":true}, ...options })
}
