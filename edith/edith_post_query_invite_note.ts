/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 50774
  * @name: lifemarketing-service-default
  * @identifier: api.edith.life_service.bk.campaign.query_invite_note.post
  * @version: undefined
  * @path: /api/edith/life_service/bk/campaign/query_invite_note
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostQueryInviteNotePayload {
	/** 商家id */
	dtm: string
	/** pageNo */
	pageNo?: number
	/** pageSize */
	pageSize?: number
	/** bizScene */
	bizScene?: string
	/** 页大小 */
	inviteStatus?: string
}

export interface INoteInfo {
	/** 1 */
	noteId: string
	/** 笔记ID", */
	noteImage: string
	/** 笔记首图", */
	noteTitle: string
	/** 笔记记标题, */
	noteLikes: string
	/** 点赞数 */
	noteCollects: string
	/** 收藏 */
	noteComments: string
	/** 评论 */
	noteReleaseTime: string
}

export interface IAuthorInfo {
	/** 1 */
	authorName: string
	/** "笔记作者", */
	authorAvatar: string
	/** "作者头像链接", */
	userd: string
	/** "作者UserId" */
	authorArea?: string
	/** "作者地址" */
	fansCount?: string
}

export interface INoteToInvite {
	/** 1 */
	noteInfo: INoteInfo
	/** 笔记 */
	authorInfo: IAuthorInfo
	/** 笔记作者信息 */
	inviteStatus: string
	/** 邀请状态 */
	compaingnId: string
}

export interface IData {
	/** 1 */
	noteToInvites?: INoteToInvite[]
}

export interface IPostQueryInviteNoteResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postQueryInviteNote(payload: IPostQueryInviteNotePayload, options = {}): Promise<IData> {
  return http.post('/api/edith/life_service/bk/campaign/query_invite_note', payload, { transform: false, ...{"needToast":true}, ...options })
}
