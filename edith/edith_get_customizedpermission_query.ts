/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 37872
  * @name: 检查商家定制化权限
  * @identifier: api.redlife.customizedpermission.query.get
  * @version: undefined
  * @path: /api/redlife/customizedpermission/query
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IData {
	/** 是否有免费送权限 */
	hasFreebiePermission?: boolean
}

export interface IGetCustomizedpermissionQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data?: IData
}

export function getCustomizedpermissionQuery(options = {}): Promise<IData> {
  return http.get('/api/redlife/customizedpermission/query', { transform: false, ...{"needToast":true}, ...options })
}
