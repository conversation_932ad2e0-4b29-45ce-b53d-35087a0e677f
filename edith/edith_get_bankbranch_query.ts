/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34292
  * @name: 独立入驻-对公打款搜索支行
  * @identifier: api.redlife.merchant.bankbranch.query.get
  * @version: undefined
  * @path: /api/redlife/merchant/bankbranch/query
  * @method: get
  * @description: 独立入驻过程中用户选择对公打款验证方式，搜索支行
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetBankbranchQueryPayload {
	/** 清算行号，必填 */
	settleBankCode?: string
	/** 城市编码，必填 */
	cityCode?: string
	/** 支行名称，可为空 */
	bankName?: string
}

export interface IBankBranchArea {
	/** 地区代码 */
	areaCode?: string
	/** 地区名称 */
	areaName?: string
	/** 地区类型，PROVINCE：省、CITY：市 */
	areaType?: string
	/** 上级地区代码 */
	parentAreaCode?: string
	/** 父级地区名称 */
	parentAreaName?: string
}

export interface IBankBranchList {
	/** 联行号 */
	cnapsBankCode?: string
	/** 支行名称 */
	bankName?: string
	/** 超级网银号 */
	superBankCode?: string
	/** 支行所在地 */
	bankBranchArea?: IBankBranchArea
}

export interface IData {
	/** 支行列表 */
	bankBranchList?: IBankBranchList[]
}

export interface IGetBankbranchQueryResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getBankbranchQuery(params: IGetBankbranchQueryPayload, options = {}): Promise<IData> {
	return http.get('/api/redlife/merchant/bankbranch/query', { params, transform: false, ...{ "needToast": true }, ...options })
}
