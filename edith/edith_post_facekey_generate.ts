/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 38002
  * @name: 独立入驻人脸方式扩展-生成faceKey
  * @identifier: api.redlife.merchant.facekey.generate.post
  * @version: undefined
  * @path: /api/redlife/merchant/facekey/generate
  * @method: post
  * @description: 生成faceKey
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostFacekeyGeneratePayload {
	/** 社区bizId，默认传local_qr_face */
	bizId: string
	/** 用户c端id */
	userId?: string
	/** 姓名 */
	name: string
	/** 身份证号 */
	idNumber: string
}

export interface IData {
	/** 生成的faceKey */
	faceKey?: string
}

export interface IPostFacekeyGenerateResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postFacekeyGenerate(payload: IPostFacekeyGeneratePayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/facekey/generate', payload, { transform: false, ...{"needToast":true}, ...options })
}
