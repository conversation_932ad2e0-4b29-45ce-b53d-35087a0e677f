/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 32113
  * @name: 本地生活 - 门店 = 认领/修改门店
  * @identifier: api.redlife.poi.upsert_claim_poi.post
  * @version: undefined
  * @path: /api/redlife/poi/upsert_claim_poi
  * @method: post
  * @description: 本地生活 - 门店 = 认领/修改门店
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IBusinessLicenseImage {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IBusinessLicenseValidity {
	/** 资质有效性{"NON_PERMANENT":1非永久,"PERMANENT":0永久有效} */
	qualValidityPeriod?: number
	/** 开始时间 */
	startTime?: number
	/** 结束时间 */
	endTime?: number
}

export interface IQualificationValidity {
	/** 资质有效性{"NON_PERMANENT":1非永久,"PERMANENT":0永久有效} */
	qualValidityPeriod?: number
	/** 开始时间 */
	startTime?: number
	/** 结束时间 */
	endTime?: number
}

export interface IMediaInfoList {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IValue {
	/** 资质code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质有效性 */
	qualificationValidity?: IQualificationValidity
	/** 资质图片等 */
	mediaInfoList?: IMediaInfoList[]
}

export interface IPostUpsertClaimPoiPayload {
	/** id */
	id?: number
	/** poiId */
	shopId: string
	/** 高德ID */
	gaodeId?: string
	/** 门店名称 */
	poiName?: string
	/** 经度 */
	longitude?: string
	/** 纬度 */
	latitude?: string
	/** 省份 */
	provinceName?: string
	/** 城市 */
	cityName?: string
	/** 区 */
	districtName?: string
	/** 详细地址 */
	addressDetail?: string
	/** 营业执照类型{"INDIVIDUAL_BUSINESS":0个体,"ORDINARY_BUSINESS":1普企,"OTHER":2} */
	businessLicenseType: number
	/** 媒体信息 图片、视频 */
	businessLicenseImage: IBusinessLicenseImage
	/** 统一社会信用代码 */
	usci: string
	/** 公司名称 */
	companyName: string
	/** 营业执照有效期 */
	businessLicenseValidity: IBusinessLicenseValidity
	/** 行业资质Map */
	qualificationMap: Record<string, IValue>
	/** userId */
	userId?: string
	/** 来源 */
	sourceFrom?: string
	/** 来源ID */
	sourceIdentity?: string
	/** 类目ID */
	categoryId?: string
}

export interface IPostUpsertClaimPoiResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: Record<string, any>
}

export function postUpsertClaimPoi(payload: IPostUpsertClaimPoiPayload, options = {}): Promise<void> {
  return http.post('/api/redlife/poi/upsert_claim_poi', payload, { transform: false, ...{"needToast":true}, ...options })
}
