/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34092
  * @name: 门店入驻-省市区搜索
  * @identifier: api.redlife.merchant.queryregion.get
  * @version: undefined
  * @path: /api/redlife/merchant/queryregion
  * @method: get
  * @description: 门店入驻-省市区搜索
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetMerchantQueryregionPayload {
	/** 父地区编码 */
	upperCode: string
	/** 用户id */
	userId?: number
}

export interface IChildren {
	/** 名称 */
	name?: string
	/** 编码 */
	code?: string
	/** 叶子节点 */
	leaf?: boolean
	/** 子节点 */
	children?: IChildren[]
}

export interface IRegionList {
	/** 名称 */
	name?: string
	/** 编码 */
	code?: string
	/** 叶子节点 */
	leaf?: boolean
	/** 子节点 */
	children?: IChildren[]
}

export interface IData {
	/** 响应数据 */
	regionList?: IRegionList[]
}

export interface IGetMerchantQueryregionResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMerchantQueryregion(params: IGetMerchantQueryregionPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/queryregion', { params, transform: false, ...{"needToast":true}, ...options })
}
