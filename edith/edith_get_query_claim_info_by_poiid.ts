/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 24661
  * @name: 本地生活-门店认领查询详情页
  * @identifier: api.edith.life_service.poi.query_claim_info_by_poiid.get
  * @version: undefined
  * @path: /api/edith/life_service/poi/query_claim_info_by_poiid
  * @method: get
  * @description: 门店认领查询详情页
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQueryClaimInfoByPoiidPayload {
	/** sellerId */
	sellerId?: string
	/** userId */
	userId?: string
	/** shopId */
	shopId?: string
}

export interface IUserInfo {
	/** sellerId */
	sellerId?: string
	/** 用户ID */
	userId?: string
	/** 用户名 */
	userName?: string
}

export interface IBusinessLicenseImage {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url 视频存放主图 */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IBusinessLicenseValidity {
	/** 资质有效性 */
	qualValidityPeriod?: number
	/** 开始时间 */
	startTime?: number
	/** 结束时间 */
	endTime?: number
}

export interface ISupplementaryMaterial {
	/** url */
	url?: string
	/** 宽 */
	width?: number
	/** 高 */
	height?: number
	/** 备用url 视频存放主图 */
	urlBackup?: string
	/** 备用url 宽 */
	urlBackupWidth?: number
	/** 备用url 高 */
	urlBackupHeight?: number
}

export interface IQualificationValidity {
	/** 资质有效期 */
	qualValidityPeriod?: number
	/** 开始时间 */
	startTime?: number
	/** 结束时间 */
	endTime?: number
}

export interface IMediaInfoList {
	/** url */
	url?: string
	/** width */
	width?: number
	/** height */
	height?: number
	/** urlBackup */
	urlBackup?: string
	/** urlBackupWidth */
	urlBackupWidth?: number
	/** urlBackupHeight */
	urlBackupHeight?: number
}

export interface IValue {
	/** 资质code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质有效性 */
	qualificationValidity?: IQualificationValidity
	/** 资质图片 */
	mediaInfoList?: IMediaInfoList[]
	/** 是否必填资质组url */
	need?: boolean
}

export interface IPoiClaimDetailInfo {
	/** id */
	id?: number
	/** shop ID */
	shopId: string
	/** 高德ID */
	gaodeId?: string
	/** POI名称 */
	poiName?: string
	/** 经度 */
	longitude?: string
	/** 纬度 */
	latitude?: string
	/** 省份 */
	provinceName?: string
	/** 城市 */
	cityName?: string
	/** 地区 */
	districtName?: string
	/** 详细地址 */
	addressDetail?: string
	/** 门店类型：INDIVIDUAL_BUSINESS：个体商户，INDIVIDUAL_BUSINESS:普通企业 */
	businessLicenseType: number
	/** 营业执照 */
	businessLicenseImage: IBusinessLicenseImage
	/** 统一社会信用代码 */
	usci: string
	/** 营业执照有效期 */
	businessLicenseValidity: IBusinessLicenseValidity
	/** 认领状态 */
	claimStatus?: string
	/** 审核意见 */
	auditRemark?: string
	/** 公司名称 */
	companyName?: string
	/** 补充材料 */
	supplementaryMaterial?: ISupplementaryMaterial[]
	/** 行业资质map */
	qualificationMap?: Record<string, IValue>
	/** 类目ID */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
}

export interface IPoiClaimApplyInfo {
	/** 用户信息 */
	userInfo?: IUserInfo
	/** 详情 */
	poiClaimDetailInfo?: IPoiClaimDetailInfo
	/** 资质信息 */
	qualificationMsg?: string
	/** banner Color */
	color?: string
	/** 申请状态 */
	applyRecordStatus?: string
	/** 是否认领成功过 */
	claimSuccessed?: boolean
}

export interface IData {
	/** 认领申请信息 */
	poiClaimApplyInfo?: IPoiClaimApplyInfo
}

export interface IGetQueryClaimInfoByPoiidResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getQueryClaimInfoByPoiid(params: IGetQueryClaimInfoByPoiidPayload, options = {}): Promise<IData> {
  return http.get('/api/edith/life_service/poi/query_claim_info_by_poiid', { params, transform: false, ...{"needToast":true}, ...options })
}
