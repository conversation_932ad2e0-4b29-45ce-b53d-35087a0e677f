/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34302
  * @name: 独立入驻-获取专业号经营范围
  * @identifier: api.redlife.merchant.professionalscope.query.get
  * @version: undefined
  * @path: /api/redlife/merchant/professionalscope/query
  * @method: get
  * @description: 独立入驻过程中，获取专业号经营范围
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetProfessionalscopeQueryPayload {
	/** 身份code */
	professionalCode?: string
	/** 主体类型 */
	principalType?: string
}

export interface IQualificationRuleList {
	/** 规则code */
	ruleCode?: string
	/** 规则名称 */
	ruleName?: string
}

export interface IData {
	/** 专业号经营范围 */
	qualificationRuleList?: IQualificationRuleList[]
}

export interface IGetProfessionalscopeQueryResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getProfessionalscopeQuery(params: IGetProfessionalscopeQueryPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/professionalscope/query', { params, transform: false, ...{"needToast":true}, ...options })
}
