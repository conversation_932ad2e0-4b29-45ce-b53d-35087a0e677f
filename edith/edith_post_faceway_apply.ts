/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 38003
  * @name: 独立入驻人脸方式扩展-申请人脸识别
  * @identifier: api.redlife.merchant.faceway.apply.post
  * @version: undefined
  * @path: /api/redlife/merchant/faceway/apply
  * @method: post
  * @description: 独立入驻人脸方式扩展-申请人脸识别
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostFacewayApplyPayload {
	/** 后端之前生成的faceKey */
	key: string
	/** 平台，WECHAT或XHS */
	platform: string
}

export interface IFaceToken {
	/** deeplink跳转 */
	deeplink: string
	/** token，用于之后的查询 */
	identityToken: string
}

export interface IFaceOrder {
	/** h5跳转链接 */
	identificationUrl?: string
	/** 订单号，用于之后的查询 */
	orderNo?: string
	/** 信息 */
	msg?: string
}

export interface IData {
	/** rn用结构体 */
	faceToken?: IFaceToken
	/** h5用结构体 */
	faceOrder?: IFaceOrder
}

export interface IPostFacewayApplyResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postFacewayApply(payload: IPostFacewayApplyPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/faceway/apply', payload, { transform: false, ...{"needToast":true}, ...options })
}
