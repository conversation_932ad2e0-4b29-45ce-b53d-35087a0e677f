/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 31153
  * @name: SSO - WEB - 列举手机区号
  * @identifier: api.cas.customer.web.zones.get
  * @version: undefined
  * @path: /api/cas/customer/web/zones
  * @method: get
  * @description: SSO - WEB - 列举手机区号
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetWebZonesPayload {
	/** 服务跳转 URL */
	service: string
	/** 渠道 */
	channel?: string
}

export interface IData1 {
	/** 区号值，用于传参 */
	code: string
	/** 区号名称，用于展示 */
	name: string
	/** 地区 */
	area: string
}

export interface IData {
	/** 数据 */
	data: IData1[]
}

export interface IGetWebZonesResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data?: IData
}

export function getWebZones(params: IGetWebZonesPayload, options = {}): Promise<IData> {
  return http.get('/api/cas/customer/web/zones', { params, transform: false, ...{"needToast":true}, ...options })
}
