/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 22388
  * @name: 分页获取餐饮商品
  * @identifier: api.edith.life_service.campaign.product_list.get
  * @version: undefined
  * @path: /api/edith/life_service/campaign/product_list
  * @method: get
  * @description: 分页获取餐饮商品
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetProductListPayload {
	/** pageNum */
	pageNum?: number
	/** pageSize */
	pageSize?: number
	/** keyword */
	keyword?: string
	/** sellerId */
	sellerId?: string
	/** state */
	state?: number
	/** 是否需要查询sku详情 */
	needSkuInfo?: boolean
}

export interface ICny {
	/** 是否展示 */
	displayAble?: boolean
	/** 活动类型 */
	cnyTypes?: number[]
}

export interface IPermission {
	/** cny */
	cny?: ICny
}

export interface IBookDailyStock {
	/** limitType */
	limitType?: number
	/** stockQty */
	stockQty?: number
}

export interface IStock {
	/** limit_type */
	limitType?: number
	/** stock_qty */
	stockQty?: number
}

export interface ILimitRule {
	/** limitTag */
	isLimit?: boolean
	/** totalBuyNum */
	totalBuyNum?: number
}

export interface IDiffPriceConfig {
	/** week */
	week?: number
	/** fromDate */
	fromDate?: string
	/** toDate */
	toDate?: string
	/** holiday */
	holiday?: number
	/** price */
	price?: number
	/** configType */
	configType?: number
}

export interface ISkuInfo {
	/** skuid */
	skuId?: string
	/** skuName */
	skuName?: string
	/** origin_amount */
	originAmount?: number
	/** bookDailyStock */
	bookDailyStock?: IBookDailyStock
	/** stock */
	stock?: IStock
	/** soldStartTime */
	soldStartTime?: number
	/** soldEndTime */
	soldEndTime?: number
	/** limitRule */
	limitRule?: ILimitRule
	/** priceDiff */
	diffPriceConfig?: IDiffPriceConfig
	/** available */
	available?: boolean
	/** actual_amount */
	actualAmount?: number
}

export interface IItemInfo {
	/** 商品id */
	itemId?: string
	/** 商品名称 */
	itemName?: string
	/** 商品图片 */
	image?: string
	/** 商品状态 */
	state?: number
	/** 商品价格 */
	price?: number
	/** 库存 */
	stock?: number
	/** 开始售卖时间 */
	soldStartTime?: number
	/** 结束售卖时间 */
	soldEndTime?: number
	/** 1 */
	planId?: string
	/** 1 */
	distributorRate?: string
	/** 商品类型 */
	productType?: string
	/** 商品审核状态 */
	productAuditStatus?: number
	/** 商品类型描述 */
	productTypeText?: string
	/** 商品审核状态描述 */
	productAuditStatusText?: string
	/** skuId */
	skuId?: string
	/** 创建方式 */
	createWay?: string
	/** 审批拒绝原因 */
	auditRefuseCause?: string
	/** 不限售卖日期标记 */
	saleUnLimitedTag?: boolean
	/** 不限库存标识 */
	unLimitedStockTag?: boolean
	/** 服务商佣金率 */
	commissionRate?: string
	/** 券码类型 */
	couponFulfilmentType?: number
	/** 售卖方式 */
	saleMode?: number
	/** 权限 */
	permission?: IPermission
	/** sku信息 */
	skuInfos?: ISkuInfo[]
	/** variant */
	variant?: number
	/** limitRuleInfo */
	limitRuleInfo?: string
	/** priceInfo */
	priceInfo?: string
	/** stockInfo */
	stockInfo?: string
	/** bookDailyStockInfo */
	bookDailyStockInfo?: string
	/** originPriceInfo */
	originPriceInfo?: string
}

export interface IData {
	/** 商品列表 */
	itemInfos?: IItemInfo[]
	/** 总数 */
	total?: number
	/** 1 */
	relationType?: string
	/** 是否存在合作订单 */
	providerCooperation?: boolean
}

export interface IGetProductListResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getProductList(params: IGetProductListPayload, options = {}): Promise<IData> {
  return http.get('/api/edith/life_service/campaign/product_list', { params, transform: true, ...{"needToast":true}, ...options })
}
