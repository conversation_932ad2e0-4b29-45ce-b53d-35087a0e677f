/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 52841
  * @name: 本地移动端数据获取
  * @identifier: api.edith.app.common.businessdata.post
  * @version: undefined
  * @path: /api/edith/app/common/businessdata
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IOrderBy {
	/** field */
	field?: string
	/** asc/desc */
	orderBy?: string
}

export interface IBlockElement {
	/** blockKey */
	blockKey?: string
	/** filterMap */
	filterMap?: Record<string, string>
	/** page */
	page?: number
	/** size */
	size?: number
	/** 排序 */
	orderBy?: IOrderBy[]
}

export interface IRequestBody {
	/** 请求块列表 */
	blockElements?: IBlockElement[]
	/** filterMap */
	filterMap?: Record<string, string>
	/** conditions */
	conditions?: Record<string, string>
}

export interface IPostCommonBusinessdataPayload {
	/** 用户 id */
	userId?: string
	/** sellerId */
	sellerId?: string
	/** 统一的request body */
	requestBody?: IRequestBody
	/** 链路上网关生成的唯一id,用于追踪debug trace */
	requestId?: string
	/** request json */
	requestJson?: string
}

export interface IData {
	/** responseJson */
	responseJson: string
}

export interface IPostCommonBusinessdataResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postCommonBusinessdata(payload: IPostCommonBusinessdataPayload, options = {}): Promise<IData> {
  return http.post('/api/edith/app/common/businessdata', payload, { transform: false, ...{"needToast":true}, ...options })
}
