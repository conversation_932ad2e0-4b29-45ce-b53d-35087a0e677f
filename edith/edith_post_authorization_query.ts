/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 35160
  * @name: 独立入驻-二要素查询认证结果
  * @identifier: api.redlife.merchant.authorization.query.post
  * @version: undefined
  * @path: /api/redlife/merchant/authorization/query
  * @method: post
  * @description: 独立入驻-二要素查询认证结果
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface ITwoElement {
	/** 姓名 */
	name?: string
	/** 身份证号 */
	idNumber?: string
}

export interface IPostAuthorizationQueryPayload {
	/** 二要素结构体 */
	twoElements: ITwoElement
}

export interface IQrFaceRecognitionResult {
	/** 人脸状态 */
	authStatus?: string
	/** 失败信息 */
	failMsg?: string
	/** token是否合法 */
	validIdentificationToken?: boolean
	/** 是否过期 */
	noExpire?: boolean
	/** 人脸单号 */
	orderNo?: string
}

export interface IData {
	/** 人脸结构体 */
	qrFaceRecognitionResult?: IQrFaceRecognitionResult
}

export interface IPostAuthorizationQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postAuthorizationQuery(payload: IPostAuthorizationQueryPayload, options = {}): Promise<IData> {
	return http.post('/api/redlife/merchant/authorization/query', payload, { transform: false, ...{ "needToast": true }, ...options })
}
