/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34053
  * @name: 本地商家入驻-校验手机号验证码
  * @identifier: api.redlife.merchant.check.verificationcode.post
  * @version: undefined
  * @path: /api/redlife/merchant/check/verificationcode
  * @method: post
  * @description: 本地商家入驻-校验手机号验证码
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IVerificationCodeInfo {
	/** 用户id */
	userId?: string
	/** 验证类型 phone，email */
	verificationType?: string
	/** 手机号码/邮箱 */
	contactInfo?: string
	/** 设备ip */
	clientIp?: string
	/** 区号 */
	zoneCode?: string
	/** 验证码 */
	verificationCode?: string
}

export interface IPostCheckVerificationcodePayload {
	/** =========================END========================================/=========================MERCHANT_BASE START========================================/ */
	verificationCodeInfo?: IVerificationCodeInfo
}

export interface ICheckResult {
	/** 校验成功与否 */
	checkSuccess?: boolean
	/** 失败原因 */
	errorMsg?: string
}

export interface IData {
	/** 校验结果 */
	checkResult?: ICheckResult
}

export interface IPostCheckVerificationcodeResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postCheckVerificationcode(payload: IPostCheckVerificationcodePayload, options = {}): Promise<IData> {
	return http.post('/api/redlife/merchant/check/verificationcode', payload, { transform: false, ...{ "needToast": true }, ...options })
}
