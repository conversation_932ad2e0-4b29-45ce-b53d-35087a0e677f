/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 40614
  * @name: 本地生活-官方服务商列表查询
  * @identifier: api.life.official_provider.list.get
  * @version: undefined
  * @path: /api/life/official_provider/list
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetOfficialProviderListPayload {
	/** 页码 */
	pageNo?: number
	/** 页大小 */
	pageSize?: number
	/** 搜索词 */
	searchWord?: string
}

export interface IProviderList {
	/** 服务商id */
	providerId?: string
	/** 服务商名称 */
	providerName?: string
	/** 服务商uid */
	providerUid?: string
	/** 头像 */
	avatar?: string
	/** 主体名称 */
	companyName?: string
}

export interface IData {
	/** 列表 */
	providerList?: IProviderList[]
	/** 总计 */
	total?: number
}

export interface IGetOfficialProviderListResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getOfficialProviderList(params: IGetOfficialProviderListPayload, options = {}): Promise<IData> {
  return http.get('/api/life/official_provider/list', { params, transform: false, ...{"needToast":true}, ...options })
}
