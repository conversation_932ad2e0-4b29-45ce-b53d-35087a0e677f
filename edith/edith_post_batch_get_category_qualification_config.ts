/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	* 
	* @id: 34262
	* @name: 本地商家入驻-查询类目资质和门店资质
	* @identifier: api.redlife.merchant.batch_get_category_qualification_config.post
	* @version: undefined
	* @path: /api/redlife/merchant/batch_get_category_qualification_config
	* @method: post
	* @description: 
	* 
	* @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface ICategoryIdList {
	/** 类目id */
	categoryId: string
	/** 全选半选标记-CHECK_ALL：全选；CHECK_SINGLE：单选 */
	checkStatus: string
}

export interface IPostBatchGetCategoryQualificationConfigPayload {
	/** 类目列表 */
	categoryIdList: ICategoryIdList[]
	/** 店铺类型-INDIVIDUAL_BUSINESS：个体工商户；COMMON_MERCHANT：普通企业店； */
	sellerType: string
	/** 商家身份 */
	businessIdentityCode: string
	/** 连锁类型 */
	chainType?: string
}

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IQualificationTemplate {
	/** 文件名称 */
	fileName?: string
	/** 文件类型，image,pdf,word,text */
	fileType?: string
	/** 文件高度 */
	height?: number
	/** 文件长度 */
	width?: number
	/** 资质文件 */
	uploaderInfoModel?: IUploaderInfoModel
}

export interface IQualificationExtInfo {
	/** 资质描述信息 */
	qualificationContent?: string
	/** 资质描述信息 */
	qualificationTemplate?: IQualificationTemplate[]
}

export interface IQualificationConfigList {
	/** 资质编码 */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质模板 */
	qualificationTemplate?: string
	/** 资质描述 */
	qualificationDesc?: string
	/** 示例图 */
	qualificationImage?: string
	/** 资质格式 图片，文本 */
	qualificationFormat?: string
	/** 资质分组 */
	qualificationGroup?: number
	/** 示例模板-多条 */
	qualificationImageList?: string[]
	/** 资质有效期要求 */
	qualificationValidate?: string
	/** 资质描述/示例等扩展信息 */
	qualificationExtInfo?: IQualificationExtInfo
}

export interface ICategoryPathList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目级别 */
	categoryLevel?: string
	/** 父类目 */
	parentCategoryId?: string
	/** 类目路径 */
	categoryPathList?: ICategoryPathList[]
}

export interface ICategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目级别 */
	categoryLevel?: string
	/** 父类目 */
	parentCategoryId?: string
	/** 类目路径 */
	categoryPathList?: ICategoryPathList[]
}

export interface ICategoryqualificationList {
	/** 资质列表 */
	qualificationConfigList?: IQualificationConfigList[]
	/** 相同资质要求的类目集合 */
	categoryList?: ICategoryList[]
	/** 配置所属的类目id */
	qualificationGroupFunc?: string
}

export interface IData {
	/** 类目资质 */
	categoryqualificationList?: ICategoryqualificationList[]
}

export interface IPostBatchGetCategoryQualificationConfigResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postBatchGetCategoryQualificationConfig(payload: IPostBatchGetCategoryQualificationConfigPayload, options = {}): Promise<IData> {
	return http.post('/api/redlife/merchant/batch_get_category_qualification_config', payload, { transform: false, ...{ "needToast": true }, ...options })
}
