/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33959
  * @name: 店铺名称校验-输入
  * @identifier: api.redlife.merchant.checksellernamevalidv2.post
  * @version: undefined
  * @path: /api/redlife/merchant/checksellernamevalidv2
  * @method: post
  * @description: 店铺名称校验-输入
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostMerchantChecksellernamevalidv2Payload {
	/** 用户id */
	userId?: string
	/** 店铺名称 */
	sellerName?: string
	/** 店铺类型 */
	sellerType?: string
	/** 品牌授权类型 */
	authorizationType?: string
}

export interface ICheckResult {
	/** 是否通过校验 */
	passCheck: boolean
	/** 原因 */
	remark?: string
}

export interface IData {
	/** 校验结果 */
	checkResult?: ICheckResult
}

export interface IPostMerchantChecksellernamevalidv2Response {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postMerchantChecksellernamevalidv2(payload: IPostMerchantChecksellernamevalidv2Payload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/checksellernamevalidv2', payload, { transform: false, ...{"needToast":true}, ...options })
}
