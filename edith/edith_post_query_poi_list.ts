/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 26585
  * @name: app查询本账号的门店数据
  * @identifier: api.redlife.account.app.query_poi_list.post
  * @version: undefined
  * @path: /api/redlife/account/app/query_poi_list
  * @method: post
  * @description: app查询本账号的门店数据
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostQueryPoiListPayload {
	/** 门店名称 */
	poiName?: string
	/** 经度 */
	lng?: number
	/** 纬度 */
	lat?: number
	/** 省份名称 */
	province?: string
	/** 页码 */
	pageNum: number
	/** 每页数量 */
	pageSize: number
	/** cityName */
	cityName?: string
	/** sysCode */
	syscode?: number
	/** 业务系统, 代运营: 1,平台码：2 */
	shopName?: string
}

export interface IPoiList {
	/** 门店ID */
	poiId?: string
	/** 门店名称 */
	poiName?: string
	/** 地址 */
	addressDetail?: string
	/** 门店地址 */
	claimStatus?: string
}

export interface IShopList {
	/** 门店ID */
	shopId?: string
	/** 门店名称 */
	shopName?: string
	/** 门店地址 */
	addressDetail?: string
	/** 门店状态 */
	claimStatus?: string
	/** poiId */
	poiId?: string
}

export interface IData {
	/** 总数 */
	total?: number
	/** 门店列表 */
	poiList?: IPoiList[]
	/** shopList */
	shopList?: IShopList[]
}

export interface IPostQueryPoiListResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postQueryPoiList(payload: IPostQueryPoiListPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/account/app/query_poi_list', payload, { transform: false, ...{"needToast":true}, ...options })
}
