/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34087
  * @name: 本地生活 - 省市区接口
  * @identifier: api.redlife.merchant.query_region.get
  * @version: undefined
  * @path: /api/redlife/merchant/query_region
  * @method: get
  * @description: 本地生活 - 省市区接口
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQueryRegionPayload {
	/** 父地区编码 */
	upperCode: string
}

export interface IChildren {
	/** 名称 */
	name?: string
	/** 编码 */
	code?: string
	/** 叶子节点 */
	leaf?: boolean
	/** 子节点 */
	children?: IChildren[]
}

export interface IRegionList {
	/** 名称 */
	name?: string
	/** 编码 */
	code?: string
	/** 叶子节点 */
	leaf?: boolean
	/** 子节点 */
	children?: IChildren[]
}

export interface IData {
	/** 省市区层级目录接口信息 */
	regionList?: IRegionList[]
}

export interface IGetQueryRegionResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getQueryRegion(params: IGetQueryRegionPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/query_region', { params, transform: false, ...{"needToast":true}, ...options })
}
