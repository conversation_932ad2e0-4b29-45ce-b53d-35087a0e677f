/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34707
  * @name: 本地商家入驻-CDN调度服务
  * @identifier: api.redlife.merchant.get.cdn.resource.post
  * @version: undefined
  * @path: /api/redlife/merchant/get/cdn/resource
  * @method: post
  * @description: 本地商家入驻-CDN调度服务
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostCdnResourcePayload {
	/** 业务名称 */
	business: string
	/** 场景名称 */
	scene: string
	/** 用户ip */
	userIp?: string
	/** 文件列表 */
	fileKeys?: string[]
	/** tags */
	tags?: Record<string, string>
}

export interface IDomain {
	/** master */
	master: string
	/** slave */
	slave?: string
}

export interface IValue {
	/** master */
	master: string
	/** slave */
	slave?: string
}

export interface IResource {
	/** domain */
	domain?: IDomain
	/** urls */
	urls?: Record<string, IValue>
}

export interface IData {
	/** resource */
	resource: IResource
}

export interface IPostCdnResourceResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postCdnResource(payload: IPostCdnResourcePayload, options = {}): Promise<IData> {
	return http.post('/api/redlife/merchant/get/cdn/resource', payload, { transform: false, ...{ "needToast": true }, ...options })
}
