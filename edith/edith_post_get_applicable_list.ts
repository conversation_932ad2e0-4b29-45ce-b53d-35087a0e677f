/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33998
  * @name: 本地商家入驻-查询用户可申请的类目
  * @identifier: api.redlife.merchant.category.get_applicable_list.post
  * @version: undefined
  * @path: /api/redlife/merchant/category/get_applicable_list
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostGetApplicableListPayload {
	/** 商家身份编码 */
	businessIdentityCode?: string
	/** 商家类型 */
	sellerType?: string
}

export interface IChildCategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目层级 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父ID */
	parentCategoryId?: string
	/** 是否可选 */
	visible?: boolean
	/** 业务类型 */
	businessId?: string
	/** 是否需要资质 */
	needQualification?: boolean
	/** 类目支持的店铺类型 */
	sellerTypeList?: string[]
	/** 全选半选标记 */
	checkStatus?: string
	/** 子类目 */
	childCategoryList?: IChildCategoryList[]
	/** 是否存在子节点 */
	hasSub?: boolean
	/** 类目业务类型配置 for美妆个护、露营特殊处理 */
	categoryBusinessType?: string
	/** 不可选原因 */
	msg?: string
}

export interface ICategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目层级 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父ID */
	parentCategoryId?: string
	/** 是否可选 */
	visible?: boolean
	/** 业务类型 */
	businessId?: string
	/** 是否需要资质 */
	needQualification?: boolean
	/** 类目支持的店铺类型 */
	sellerTypeList?: string[]
	/** 全选半选标记 */
	checkStatus?: string
	/** 子类目 */
	childCategoryList?: IChildCategoryList[]
	/** 是否存在子节点 */
	hasSub?: boolean
	/** 类目业务类型配置 for美妆个护、露营特殊处理 */
	categoryBusinessType?: string
	/** 不可选原因 */
	msg?: string
}

export interface IData {
	/** 商家可申请类目 */
	categoryList?: ICategoryList[]
}

export interface IPostGetApplicableListResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postGetApplicableList(payload: IPostGetApplicableListPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/category/get_applicable_list', payload, { transform: false, ...{"needToast":true}, ...options })
}
