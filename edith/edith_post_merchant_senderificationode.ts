/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33950
  * @name: 商家入驻-发送验证码
  * @identifier: api.redlife.merchant.senderificationode.post
  * @version: undefined
  * @path: /api/redlife/merchant/senderificationode
  * @method: post
  * @description: 商家入驻-发送验证码
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IVerificationCodeInfo {
	/** 用户id */
	userId?: string
	/** 验证类型 phone，email */
	verificationType?: string
	/** 手机号码/邮箱 */
	contactInfo?: string
	/** 设备ip */
	clientIp?: string
	/** 区号 */
	zoneCode?: string
}

export interface IPostMerchantSenderificationodePayload {
	/** 请求参数 */
	verificationCodeInfo?: IVerificationCodeInfo
}

export interface IPostMerchantSenderificationodeResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
}

export function postMerchantSenderificationode(payload: IPostMerchantSenderificationodePayload, options = {}): Promise<void> {
	return http.post('/api/redlife/merchant/senderificationode', payload, { transform: false, ...{ "needToast": true }, ...options })
}
