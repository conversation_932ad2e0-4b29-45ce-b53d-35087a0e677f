/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 35762
  * @name: 解绑门店二次确认
  * @identifier: api.redlife.poi.unbind.confirm.post
  * @version: undefined
  * @path: /api/redlife/poi/unbind/confirm
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostUnbindConfirmPayload {
	/** 门店id列表 */
	poiIds?: string[]
	/** {"CANCEL":2,"UNBIND":1} */
	operationType?: number
	/** 操作人id */
	operatorId?: string
	/** 解绑原因 */
	reason?: string
}

export interface IData {
	/** 申请记录id列表 */
	applyRecordIds?: number[]
}

export interface IPostUnbindConfirmResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function postUnbindConfirm(payload: IPostUnbindConfirmPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/poi/unbind/confirm', payload, { transform: false, ...{"needToast":true}, ...options })
}
