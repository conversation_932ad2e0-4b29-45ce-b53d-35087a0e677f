/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 49949
  * @name: 商家后台服务商市场
  * @identifier: api.redlife.provider_market.page.get
  * @version: undefined
  * @path: /api/redlife/provider_market/page
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetProviderMarketPagePayload {
	/** 页码 */
	pageNo: number
	/** 页大小 */
	pageSize: number
	/** 地区 */
	region?: string[]
	/** 行业 */
	industry?: string[]
}

export interface ISettlement {
	/** 结算方式{"SALES_COMMISSION":2,"SERVICE_FEE":1,"SERVICE_FEE_AND_SALES_COMMISSION":3} */
	providerSettlement?: number
	/** 描述 */
	desc?: string
}

export interface IProviderBaseInfoList {
	/** 服务商ID */
	providerId?: string
	/** 服务商头像 */
	providerAvatar?: string
	/** 人员规模 */
	staffSize?: string
	/** 服务地域 */
	region?: string[]
	/** 主营行业 */
	mainIndustry?: string[]
	/** 联系方式。商家填写表单  FORM：0     电话：1    PHONE */
	contact?: number[]
	/** 联系方式-手机号 */
	contactPhone?: string
	/** 成功案例 */
	cases?: string
	/** 案例图片 */
	casePhoto?: string[]
	/** 结算方式 */
	settlement?: ISettlement[]
	/** 申请状态{"APPROVED":3,"PROCESSING":1,"REJECT":2} */
	applyStatus?: number
	/** applyId */
	applyId?: string
	/** 驳回原因 */
	reason?: string
	/** 历史服务过的商家数 */
	historySellerCount?: number
	/** 服务商name */
	providerName?: string
	/** 公司简介 */
	desc?: string
	/** 入驻时间 */
	createTime?: number
}

export interface IData {
	/** 总量 */
	total?: number
	/** 服务商基本信息 */
	providerBaseInfoList?: IProviderBaseInfoList[]
}

export interface IGetProviderMarketPageResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getProviderMarketPage(params: IGetProviderMarketPagePayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/provider_market/page', { params, transform: false, ...{"needToast":true}, ...options })
}
