/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 38416
  * @name: 入驻/认领开放城市查询
  * @identifier: api.redlife.merchant.query_open_city.get
  * @version: undefined
  * @path: /api/redlife/merchant/query_open_city
  * @method: get
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQueryOpenCityPayload {
	/** 类目ID */
	categoryId?: string
}

export interface IData {
	/** 1:城市白名单逻辑   2:全国可见  3:酒旅行业，不在范围内 */
	status?: number
	/** 开放城市列表 */
	openCityList?: string[]
}

export interface IGetQueryOpenCityResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getQueryOpenCity(params: IGetQueryOpenCityPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/query_open_city', { params, transform: false, ...{"needToast":true}, ...options })
}
