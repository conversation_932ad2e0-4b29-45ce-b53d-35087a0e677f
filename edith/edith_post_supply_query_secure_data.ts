/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 38614
  * @name: lifesupply-查询明文数据-商家查自己
  * @identifier: api.redlife.supply.query_secure_data.post
  * @version: undefined
  * @path: /api/redlife/supply/query_secure_data
  * @method: post
  * @description: lifesupply-查询明文数据-商家查自己
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostQuerySecureDataPayload {
	/** 业务类型 */
	bizType?: string
	/** 唯一id */
	bizId?: string
	/** 明文字段列表 */
	secureFields?: string[]
}

export interface ISecureData {
	/** 业务类型 */
	bizType?: string
	/** 唯一id */
	bizId?: string
	/** 敏感字段明文详情 */
	secureFieldDetail?: Record<string, string>
}

export interface IData {
	/** 数据 */
	secureData?: ISecureData
}

export interface IPostQuerySecureDataResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postQuerySecureData(payload: IPostQuerySecureDataPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/supply/query_secure_data', payload, { transform: false, ...{"needToast":true}, ...options })
}
