/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 36811
  * @name: 增加可售类目-查询行业类目树
  * @identifier: api.redlife.merchant.categorytree.get.get
  * @version: undefined
  * @path: /api/redlife/merchant/categorytree/get
  * @method: get
  * @description: 增加可售类目-查询行业类目树
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetMerchantCategorytreePayload {
	/** 行业信息 */
	businessType: string
}

export interface IChildCategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目层级 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父ID */
	parentCategoryId?: string
	/** 是否可选 */
	visible?: boolean
	/** 业务类型 */
	businessId?: string
	/** 是否需要资质 */
	needQualification?: boolean
	/** 类目支持的店铺类型 */
	sellerTypeList?: string[]
	/** 全选半选标记 */
	checkStatus?: string
	/** 子类目 */
	childCategoryList?: IChildCategoryList[]
	/** 是否存在子节点 */
	hasSub?: boolean
	/** 类目业务类型配置 for美妆个护、露营特殊处理 */
	categoryBusinessType?: string
	/** 不可选原因 */
	msg?: string
}

export interface ICategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目层级 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父ID */
	parentCategoryId?: string
	/** 是否可选 */
	visible?: boolean
	/** 业务类型 */
	businessId?: string
	/** 是否需要资质 */
	needQualification?: boolean
	/** 类目支持的店铺类型 */
	sellerTypeList?: string[]
	/** 全选半选标记 */
	checkStatus?: string
	/** 子类目 */
	childCategoryList?: IChildCategoryList[]
	/** 是否存在子节点 */
	hasSub?: boolean
	/** 类目业务类型配置 for美妆个护、露营特殊处理 */
	categoryBusinessType?: string
	/** 不可选原因 */
	msg?: string
}

export interface IData {
	/** 类目树 */
	categoryList?: ICategoryList[]
}

export interface IGetMerchantCategorytreeResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMerchantCategorytree(params: IGetMerchantCategorytreePayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/categorytree/get', { params, transform: false, ...{"needToast":true}, ...options })
}
