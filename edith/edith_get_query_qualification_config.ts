/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
	* @XHS_API_KIT-INFO
	* 
	* @id: 32012
	* @name: 本地生活 - 门店 - 动态获取资质配置
	* @identifier: api.redlife.poi.query_qualification_config.get
	* @version: undefined
	* @path: /api/redlife/poi/query_qualification_config
	* @method: get
	* @description: 本地生活 - 门店 - 动态获取资质配置
	* 
	* @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQueryQualificationConfigPayload {
	/** 类目ID */
	categoryId: string
}

export interface IQualificationConfig {
	/** 资质编码 */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质模板 */
	qualificationTemplate?: string
	/** 资质描述 */
	qualificationDesc?: string
	/** 业务线 */
	businessTypeList?: string[]
	/** 示例图 */
	qualificationImage?: string
	/** 资质类型经营类，生产类 */
	qualificationType?: string
	/** 资质分类品牌资质，品类资质,company,brand */
	qualificationClassCode?: string
	/** 资质格式 图片，文本 */
	qualificationFormat?: string
	/** 属地 是否国内 0：国内，1-国外，2-国内和国外 */
	isDomestic?: number
	/** 是否必填 */
	necessary?: number
	/** 有效期要求 entire:完整有效期，withoutExpire：没有截止日期，permanent：永久有效，withoutStart：没有开始日期 */
	qualificationValidate?: string
	/** 拓展信息 */
	qualificationExtInfo?: string
	/** 是否删除 */
	isDelete?: number
	/** 创建时间 */
	createTime?: number
	/** 更新时间 */
	updateTime?: number
	/** 资质分组 */
	qualificationGroup?: number
	/** 扩展字段key */
	qualificationExtInfoKey?: string
	/** 业务线 */
	businessType?: string
	/** 类目名称 */
	qualificationClassCodeName?: string
	/** 示例模板-多条 */
	qualificationImageList?: string[]
}

export interface IQualificationElement {
	/** 具体的资质类型 */
	qualificationConfig?: IQualificationConfig
	/** 与或，因为只支持或能力，所以通过groupId进行分组后，可以不理解这个字段，因为都为OR */
	func?: number
}

export interface IQualificationGroupList {
	/** 资质列表 */
	qualificationElements?: IQualificationElement[]
	/** 与或条件，需要先根据与或条件进行分组 */
	func?: number
}

export interface IData {
	/** 资质group */
	qualificationGroupList?: IQualificationGroupList[]
}

export interface IGetQueryQualificationConfigResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getQueryQualificationConfig(params: IGetQueryQualificationConfigPayload, options = {}): Promise<IData> {
	return http.get('/api/redlife/poi/query_qualification_config', { params, transform: false, ...{ "needToast": true }, ...options })
}
