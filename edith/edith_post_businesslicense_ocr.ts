/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33994
  * @name: 本地商家入驻-营业执照识别
  * @identifier: api.redlife.merchant.query.businesslicense.ocr.post
  * @version: undefined
  * @path: /api/redlife/merchant/query/businesslicense/ocr
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IPostBusinesslicenseOcrPayload {
	/** 文件信息 */
	uploaderInfoModel?: IUploaderInfoModel
}

export interface IBusinessLicense {
	/** 资质唯一code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质文件有效期-开始时间 */
	startTime?: number
	/** 资质文件有效期-结束时间 */
	endTime?: number
	/** 是否长期有效 */
	permanent?: boolean
	/** 授权链路, FIRST, SECOND, THIRD, OTHER */
	authorizationLevel?: string
}

export interface ICompanyDTO {
	/** 统一社会信用码 */
	uniformCreditCode?: string
	/** 公司注册地址，由省市详细地址生成，只读不写 */
	companyRegisteredAddress?: string
	/** 公司名称 */
	companyName?: string
	/** 公司资质 */
	businessLicense?: IBusinessLicense
	/** 贸易模式，需要枚举，写入lifemerchant_base_draft表 */
	tradeMode?: string
	/** 公司所属国家 */
	companyCountry?: string
	/** 公司所属国家英文简写 */
	companyCountryEn?: string
	/** 注册地址省份 */
	registeredAddressProvince?: string
	/** 注册地址市 */
	registeredAddressCity?: string
	/** 注册地址详细地址 */
	registeredAddressStreet?: string
	/** 结算币种 */
	currency?: string
	/** 主体类型 */
	principalType?: string
}

export interface ICheckResult {
	/** 企查查校验是否通过 */
	checkPass?: boolean
	/** 企查查校验不通过的错误码 */
	failCode?: number
	/** 企查查校验不通过的业务错误信息 */
	failMessage?: string
}

export interface IBusinessLicenseOcrDTO {
	/** ocr识别的公司信息 */
	companyDTO?: ICompanyDTO
	/** 法人信息 */
	authorizationName?: string
	/** 企查查校验结果 */
	checkResult?: ICheckResult
}

export interface IData {
	/** ocr结果 */
	businessLicenseOcrDTO?: IBusinessLicenseOcrDTO
}

export interface IPostBusinesslicenseOcrResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postBusinesslicenseOcr(payload: IPostBusinesslicenseOcrPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/query/businesslicense/ocr', payload, { transform: false, ...{"needToast":true}, ...options })
}
