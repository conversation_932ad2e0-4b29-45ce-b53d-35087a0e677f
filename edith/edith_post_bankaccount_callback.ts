/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34295
  * @name: 独立入驻-对公打款校验打款结果
  * @identifier: api.redlife.merchant.bankaccount.callback.post
  * @version: undefined
  * @path: /api/redlife/merchant/bankaccount/callback
  * @method: post
  * @description: 独立入驻-校验对公打款结果
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostBankaccountCallbackPayload {
	/** 打款金额 */
	paymentAmount?: string
	/** 银行验证码 */
	checkCode?: string
}

export interface IData {
	/** 对公打款是否成功 */
	bindSuccess?: boolean
}

export interface IPostBankaccountCallbackResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postBankaccountCallback(payload: IPostBankaccountCallbackPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/bankaccount/callback', payload, { transform: false, ...{"needToast":true}, ...options })
}
