/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 38602
  * @name: 本地生活-交易-查询敏感信息
  * @identifier: api.redlife.trade.query_secure_data.post
  * @version: undefined
  * @path: /api/redlife/trade/query_secure_data
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostQuerySecureDataPayload {
	/** 业务类型 */
	bizType?: string
	/** 业务ID */
	bizId?: string
	/** fields */
	secureFields?: string[]
}

export interface ISecureData {
	/** 业务类型 */
	bizType?: string
	/** 业务ID */
	bizId?: string
	/** 业务具体信息 */
	secureFieldDetail?: Record<string, string>
}

export interface IData {
	/** 安全信息 */
	secureData?: ISecureData
}

export interface IPostQuerySecureDataResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postQuerySecureData(payload: IPostQuerySecureDataPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/trade/query_secure_data', payload, { transform: true, ...{"needToast":true}, ...options })
}
