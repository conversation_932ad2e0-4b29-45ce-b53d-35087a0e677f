/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 38004
  * @name: 独立入驻人脸方式扩展-查结果
  * @identifier: api.redlife.merchant.face.result.post
  * @version: undefined
  * @path: /api/redlife/merchant/face/result
  * @method: post
  * @description: 独立入驻人脸方式扩展-查结果
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostFaceResultPayload {
	/** 后端生成的faceKey */
	key: string
	/** c端id */
	userId?: string
}

export interface IFaceVerifyResult {
	/** 人脸状态 */
	authStatus?: string
	/** 失败信息 */
	failMsg?: string
	/** token是否合法 */
	validIdentificationToken?: boolean
	/** 是否过期 */
	noExpire?: boolean
	/** 人脸单号 */
	orderNo?: string
}

export interface IData {
	/** 结构体 */
	faceVerifyResult?: IFaceVerifyResult
	/** 平台 */
	platform?: string
}

export interface IPostFaceResultResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postFaceResult(payload: IPostFaceResultPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/face/result', payload, { transform: false, ...{"needToast":true}, ...options })
}
