/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 31204
  * @name: 查询申请记录状态
  * @identifier: api.redlife.common.apply_record_status.get
  * @version: undefined
  * @path: /api/redlife/common/apply_record_status
  * @method: get
  * @description: 查询申请记录状态
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetApplyRecordStatusPayload {
	/** 申请记录id */
	applyRecordId: number
}

export interface IData {
	/** 申请记录id */
	applyRecordId?: number
	/** 申请记录状态 */
	status?: number
	/** 申请记录状态 */
	auditRemark?: string
}

export interface IGetApplyRecordStatusResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getApplyRecordStatus(params: IGetApplyRecordStatusPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/common/apply_record_status', { params, transform: false, ...{"needToast":true}, ...options })
}
