/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33991
  * @name: 本地商家入驻-OCR识别
  * @identifier: api.redlife.merchant.query.ocr.post
  * @version: undefined
  * @path: /api/redlife/merchant/query/ocr
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName: string
	/** 场景 */
	scene: string
	/** 文件临时fileId */
	fileId: string
	/** 文件url */
	url: string
	/** 是否加解密 */
	isSecret: boolean
	/** 上传类型 */
	cloudType: number
}

export interface IOcrImage {
	/** 图片类型 */
	ocrImageType: string
	/** 文件列表 */
	uploaderInfoModel: IUploaderInfoModel
}

export interface IPostQueryOcrPayload {
	/** 图片信息 */
	ocrImage: IOcrImage
}

export interface IQualificationOCRInfo {
	/** ocr识别接口，k-v结构 */
	ocrResult?: Record<string, string>
}

export interface IData {
	/** ocr结果 */
	qualificationOCRInfo?: IQualificationOCRInfo
}

export interface IPostQueryOcrResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postQueryOcr(payload: IPostQueryOcrPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/query/ocr', payload, { transform: false, ...{"needToast":true}, ...options })
}
