/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34426
  * @name: 本地商家入驻-品牌类目
  * @identifier: api.redlife.merchant.get_brand_category.get
  * @version: undefined
  * @path: /api/redlife/merchant/get_brand_category
  * @method: get
  * @description: 本地商家入驻-品牌类目
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IData {
	/** 品牌查询结果 */
	brandCategory?: string[]
}

export interface IGetBrandCategoryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getBrandCategory(options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/get_brand_category', { transform: false, ...{"needToast":true}, ...options })
}
