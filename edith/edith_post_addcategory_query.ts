/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 36999
  * @name: 增加可售类目-编辑页查询接口
  * @identifier: api.redlife.merchant.addcategory.query.post
  * @version: undefined
  * @path: /api/redlife/merchant/addcategory/query
  * @method: post
  * @description: 增加可售类目-编辑页查询接口
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostAddcategoryQueryPayload {
	/** 商家id */
	sellerId: string
	/** 申请id */
	applyId: number
}

export interface IDepositInfo {
	/** 币种 */
	currency?: string
	/** 金额 */
	amount?: string
}

export interface ICategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目名称 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父类目id */
	parentCategoryId?: string
	/** 增加可售类目场景区分可售和待审状态 */
	status?: string
	/** 保证金信息 */
	depositInfo?: IDepositInfo
	/** 佣金信息 */
	commissionRate?: string
}

export interface IHighestDepositInfo {
	/** 币种 */
	currency?: string
	/** 金额 */
	amount?: string
}

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IFileAttachmentList {
	/** 文件名称 */
	fileName?: string
	/** 文件类型，image,pdf,word,text */
	fileType?: string
	/** 文件高度 */
	height?: number
	/** 文件长度 */
	width?: number
	/** 资质文件 */
	uploaderInfoModel?: IUploaderInfoModel
}

export interface IValue {
	/** 资质唯一code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质文件有效期-开始时间 */
	startTime?: number
	/** 资质文件有效期-结束时间 */
	endTime?: number
	/** 是否长期有效 */
	permanent?: boolean
	/** 资质文件 */
	fileAttachmentList?: IFileAttachmentList[]
	/** indexId,用于前端定位 */
	indexId?: string
}

export interface ISellerAddCategoryInfoDTO {
	/** 商家经营类目 */
	categoryList?: ICategoryList[]
	/** 最高保证金 */
	highestDepositInfo?: IHighestDepositInfo
	/** 佣金率 */
	commissionRate?: string
	/** 类目资质,key类目id,value资质列表 */
	categoryQualificationMap?: Record<string, IValue[]>
}

export interface IData {
	/** 结构体 */
	sellerAddCategoryInfoDTO?: ISellerAddCategoryInfoDTO
}

export interface IPostAddcategoryQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postAddcategoryQuery(payload: IPostAddcategoryQueryPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/addcategory/query', payload, { transform: false, ...{"needToast":true}, ...options })
}
