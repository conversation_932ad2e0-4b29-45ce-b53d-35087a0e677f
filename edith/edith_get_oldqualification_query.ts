/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 37000
  * @name: 增加可售类目-查询商家上传过的资质
  * @identifier: api.redlife.merchant.oldqualification.query.get
  * @version: undefined
  * @path: /api/redlife/merchant/oldqualification/query
  * @method: get
  * @description: 增加可售类目-查询商家上传过的资质
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetOldqualificationQueryPayload {
	/** 商家id */
	sellerId: string
}

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IFileAttachmentList {
	/** 文件名称 */
	fileName?: string
	/** 文件类型，image,pdf,word,text */
	fileType?: string
	/** 文件高度 */
	height?: number
	/** 文件长度 */
	width?: number
	/** 资质文件 */
	uploaderInfoModel?: IUploaderInfoModel
}

export interface IQualificationList {
	/** 资质唯一code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质文件有效期-开始时间 */
	startTime?: number
	/** 资质文件有效期-结束时间 */
	endTime?: number
	/** 是否长期有效 */
	permanent?: boolean
	/** 资质文件 */
	fileAttachmentList?: IFileAttachmentList[]
	/** indexId,用于前端定位 */
	indexId?: string
}

export interface IData {
	/** 资质列表 */
	qualificationList?: IQualificationList[]
}

export interface IGetOldqualificationQueryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getOldqualificationQuery(params: IGetOldqualificationQueryPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/oldqualification/query', { params, transform: false, ...{"needToast":true}, ...options })
}
