/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34290
  * @name: 独立入驻-对公打款查询总行
  * @identifier: api.redlife.merchant.bankhq.query.get
  * @version: undefined
  * @path: /api/redlife/merchant/bankhq/query
  * @method: get
  * @description: 本地独立入驻过程中，采用对公打款方式验证时根据总行名模糊查询总行
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetBankhqQueryPayload {
	/** 总行名称，可为空 */
	bankName?: string
}

export interface IBankHqList {
	/** 超级网银号 */
	superBankCode?: string
	/** 行别代码：同一性质银行（例如农信社）使用同一银行代码 */
	bankCode?: string
	/** 总行名称 */
	bankName?: string
	/** 清算行号：同一地区同一性质银行使用同一清算行号 */
	settleBankCode?: string
}

export interface IData {
	/** 总行列表 */
	bankHqList?: IBankHqList[]
}

export interface IGetBankhqQueryResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getBankhqQuery(params: IGetBankhqQueryPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/bankhq/query', { params, transform: false, ...{"needToast":true}, ...options })
}
