/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 49947
  * @name: 商家提交代运营咨询
  * @identifier: api.redlife.provider.consult.post
  * @version: undefined
  * @path: /api/redlife/provider/consult
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface ISellerConsult {
	/** 服务商ID */
	providerId?: string
	/** 商家名称 */
	sellerName?: string
	/** 类型 */
	type?: string
	/** 行业 */
	industry?: string[]
	/** 城市 */
	city?: string
	/** 联系人 */
	contactPerson?: string
	/** 联系电话 */
	contactPhone?: string
	/** 希望服务商做什么 */
	hope?: string
	/** {"FOLLOWING":2,"INVALID":3,"PROCESSING":1,"SUCCEED":4} */
	consultStatus?: number
}

export interface IPostProviderConsultPayload {
	/** 咨询 */
	sellerConsult: ISellerConsult
}

export interface IPostProviderConsultResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
}

export function postProviderConsult(payload: IPostProviderConsultPayload, options = {}): Promise<void> {
  return http.post('/api/redlife/provider/consult', payload, { transform: false, ...{"needToast":true}, ...options })
}
