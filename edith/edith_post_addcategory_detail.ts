/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 36997
  * @name: 增加可售类目-查看详情接口
  * @identifier: api.redlife.merchant.addcategory.detail.post
  * @version: undefined
  * @path: /api/redlife/merchant/addcategory/detail
  * @method: post
  * @description: 增加可售类目-查看详情接口
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostAddcategoryDetailPayload {
	/** 二级类目id */
	secondCategoryId?: string
	/** 申请中或者拒绝的需要给申请id */
	applyId?: number
}

export interface IFirstAndSecondCategoryInfoDTO {
	/** 主营一级类目 */
	firstCategoryId?: string
	/** 一级类目名称 */
	firstCategoryName?: string
	/** 主营二级类目 */
	secondCategoryId?: string
	/** 二级类目名称 */
	secondCategoryName?: string
}

export interface IDepositInfo {
	/** 币种 */
	currency?: string
	/** 金额 */
	amount?: string
}

export interface ILeafCategoryList {
	/** 类目 */
	categoryId?: string
	/** 类目名称 */
	categoryName?: string
	/** 类目名称 */
	categoryLevel?: string
	/** 是否叶子 */
	leaf?: boolean
	/** 父类目id */
	parentCategoryId?: string
	/** 增加可售类目场景区分可售和待审状态 */
	status?: string
	/** 保证金信息 */
	depositInfo?: IDepositInfo
	/** 佣金信息 */
	commissionRate?: string
}

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IFileAttachmentList {
	/** 文件名称 */
	fileName?: string
	/** 文件类型，image,pdf,word,text */
	fileType?: string
	/** 文件高度 */
	height?: number
	/** 文件长度 */
	width?: number
	/** 资质文件 */
	uploaderInfoModel?: IUploaderInfoModel
}

export interface ICategoryQualificationList {
	/** 资质唯一code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质文件有效期-开始时间 */
	startTime?: number
	/** 资质文件有效期-结束时间 */
	endTime?: number
	/** 是否长期有效 */
	permanent?: boolean
	/** 资质文件 */
	fileAttachmentList?: IFileAttachmentList[]
	/** indexId,用于前端定位 */
	indexId?: string
}

export interface IRejectDetailList {
	/** 字段编码 */
	fieldCode?: string
	/** 拒绝原因 */
	rejectContent?: string
	/** 字段名 */
	fieldName?: string
	/** 字段id */
	fielId?: string
	/** 模块id */
	moduleCode?: string
}

export interface ICategoryDetail {
	/** 一级和二级类目的信息 */
	firstAndSecondCategoryInfoDTO?: IFirstAndSecondCategoryInfoDTO
	/** 末级类目列表 */
	leafCategoryList?: ILeafCategoryList[]
	/** 资质信息 */
	categoryQualificationList?: ICategoryQualificationList[]
	/** 状态，可售，审核中，审核拒绝 */
	status?: string
	/** 拒绝原因详情 */
	rejectDetailList?: IRejectDetailList[]
	/** 上次更新时间 */
	lastUpdateTime?: number
}

export interface IData {
	/** 详情结构体 */
	categoryDetail?: ICategoryDetail
}

export interface IPostAddcategoryDetailResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postAddcategoryDetail(payload: IPostAddcategoryDetailPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/addcategory/detail', payload, { transform: false, ...{"needToast":true}, ...options })
}
