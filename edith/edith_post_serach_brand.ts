/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34298
  * @name: 本地商家入驻-品牌搜索
  * @identifier: api.redlife.merchant.serach_brand.post
  * @version: undefined
  * @path: /api/redlife/merchant/serach_brand
  * @method: post
  * @description: 本地商家入驻-品牌搜索
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostSerachBrandPayload {
	/** 品牌关键字 */
	keyword: string
}

export interface IBrandSearchDTO {
	/** 品牌ID */
	id?: string
	/** 品牌中文名称 */
	name?: string
	/** 品牌行业 */
	brandCategory?: string
	/** 品牌图片 */
	logoBanner?: string
}

export interface IData {
	/** 品牌搜索结果 */
	brandSearchDTO?: IBrandSearchDTO[]
}

export interface IPostSerachBrandResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postSerachBrand(payload: IPostSerachBrandPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/merchant/serach_brand', payload, { transform: false, ...{"needToast":true}, ...options })
}
