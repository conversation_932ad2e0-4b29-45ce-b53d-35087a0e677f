/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34294
  * @name: 独立入驻-对公打款发起验证
  * @identifier: api.redlife.merchant.bankaccoun.bind.post
  * @version: undefined
  * @path: /api/redlife/merchant/bankaccoun/bind
  * @method: post
  * @description: 独立入驻过程中，用户选择对公打款验证方式，发起对公打款验证
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IUploaderInfoModel {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IFileAttachmentList {
	/** 文件名称 */
	fileName?: string
	/** 文件类型，image,pdf,word,text */
	fileType?: string
	/** 文件高度 */
	height?: number
	/** 文件长度 */
	width?: number
	/** 资质文件 */
	uploaderInfoModel?: IUploaderInfoModel
}

export interface IBusinessLicense {
	/** 资质唯一code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质文件有效期-开始时间 */
	startTime?: number
	/** 资质文件有效期-结束时间 */
	endTime?: number
	/** 是否长期有效 */
	permanent?: boolean
	/** 资质文件 */
	fileAttachmentList?: IFileAttachmentList[]
	/** 授权链路, FIRST, SECOND, THIRD, OTHER */
	authorizationLevel?: string
}

export interface IMerchantCompanyInfo {
	/** 统一信用码 */
	uniformCreditCode: string
	/** 公司注册地址，由省市详细地址生成，只读不写 */
	companyRegisteredAddress?: string
	/** 公司名称 */
	companyName: string
	/** 公司资质 */
	businessLicense?: IBusinessLicense
	/** 贸易模式，需要枚举，写入lifemerchant_base_draft表 */
	tradeMode?: string
	/** 公司所属国家 */
	companyCountry?: string
	/** 公司所属国家英文简写 */
	companyCountryEn?: string
	/** 注册地址省份 */
	registeredAddressProvince?: string
	/** 注册地址市 */
	registeredAddressCity?: string
	/** 注册地址详细地址 */
	registeredAddressStreet?: string
	/** 结算币种 */
	currency?: string
	/** 主体类型 */
	principalType?: string
	/** 法人姓名 */
	authorizationName: string
}

export interface IUploaderInfoModel1 {
	/** 业务线 */
	bizName?: string
	/** 场景 */
	scene?: string
	/** 文件临时fileId */
	fileId?: string
	/** 文件url */
	url?: string
	/** 是否加解密 */
	isSecret?: boolean
	/** 上传类型 */
	cloudType?: number
}

export interface IFileAttachmentList1 {
	/** 文件名称 */
	fileName?: string
	/** 文件类型，image,pdf,word,text */
	fileType?: string
	/** 文件高度 */
	height?: number
	/** 文件长度 */
	width?: number
	/** 资质文件 */
	uploaderInfoModel?: IUploaderInfoModel1
}

export interface IQualificationList {
	/** 资质唯一code */
	qualificationCode?: string
	/** 资质名称 */
	qualificationName?: string
	/** 资质文件有效期-开始时间 */
	startTime?: number
	/** 资质文件有效期-结束时间 */
	endTime?: number
	/** 是否长期有效 */
	permanent?: boolean
	/** 资质文件 */
	fileAttachmentList?: IFileAttachmentList1[]
	/** 授权链路, FIRST, SECOND, THIRD, OTHER */
	authorizationLevel?: string
}

export interface IMerchantAuthorization {
	/** 证件类型，身份证，护照... */
	certificateType?: string
	/** 证件号码 */
	certificateNumber: string
	/** 法人姓名 */
	authorizationName: string
	/** 是否通过人脸识别 */
	faceIdentification?: boolean
	/** 法人证件资质 */
	qualificationList?: IQualificationList[]
	/** 认证类型,实名认证，对公打款 */
	authorizationType: string
	/** 证件地址 */
	certificateAddress?: string
	/** 人脸识别单号 */
	faceIdentificationOrder?: string
	/** 法人证件归属地 */
	locationType?: string
}

export interface IBankHeadQuarter {
	/** 超级网银号 */
	superBankCode?: string
	/** 行别代码：同一性质银行（例如农信社）使用同一银行代码 */
	bankCode?: string
	/** 总行名称 */
	bankName?: string
	/** 清算行号：同一地区同一性质银行使用同一清算行号 */
	settleBankCode?: string
}

export interface IBankBranchArea {
	/** 地区代码 */
	areaCode?: string
	/** 地区名称 */
	areaName?: string
	/** 地区类型，PROVINCE：省、CITY：市 */
	areaType?: string
	/** 上级地区代码 */
	parentAreaCode?: string
	/** 父级地区名称 */
	parentAreaName?: string
}

export interface IBankBranch {
	/** 联行号 */
	cnapsBankCode?: string
	/** 支行名称 */
	bankName?: string
	/** 超级网银号 */
	superBankCode?: string
	/** 支行所在地 */
	bankBranchArea?: IBankBranchArea
}

export interface IMerchantBankAccount {
	/** 只能传对公打款和对私打款两种枚举 */
	authorizationType?: string
	/** 公司信息 */
	merchantCompanyInfo?: IMerchantCompanyInfo
	/** 法人信息 */
	merchantAuthorization?: IMerchantAuthorization
	/** 银行卡号 */
	bankCardNo: string
	/** 总行 */
	bankHeadQuarters: IBankHeadQuarter
	/** 支行信息 */
	bankBranch: IBankBranch
	/** 银行预留手机号 */
	phoneNumber: string
	/** 是否已绑卡成功 */
	bindSuccess?: boolean
	/** 发起绑卡时间 */
	bindTime?: number
}

export interface IPostBankaccounBindPayload {
	/** 银行卡绑卡信息 */
	merchantBankAccount?: IMerchantBankAccount
}

export interface IPostBankaccounBindResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
}

export function postBankaccounBind(payload: IPostBankaccounBindPayload, options = {}): Promise<void> {
	return http.post('/api/redlife/merchant/bankaccoun/bind', payload, { transform: false, ...{ "needToast": true }, ...options })
}
