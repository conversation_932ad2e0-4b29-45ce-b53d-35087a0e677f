/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34164
  * @name: 修改主数据类型
  * @identifier: rfmultidata.operate.updatemasterdatatype.post
  * @version: undefined
  * @path: /rfmultidata/operate/updatemasterdatatype
  * @method: post
  * @description: 修改主数据类型
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostOperateUpdatemasterdatatypePayload {
	/** 主数据类型id */
	id: string
	/** 主数据类型名称 */
	name?: string
	/** 特征码 */
	code?: string
	/** 数据类型 */
	type?: string
	/** 领域 */
	domain?: string
	/** 数据来源 */
	dataSource?: string
	/** 维护方式 */
	mode?: string
	/** 归属人id */
	ownerId?: string
	/** 操作人名称 */
	operatorName?: string
	/** 状态 */
	status?: string
	/** 操作人ID */
	userId?: string
	/** 备注 */
	remark?: string
}

export interface IPostOperateUpdatemasterdatatypeResponse {
	/** 返回主数据类型列表 */
	data?: boolean[]
	/** 是否调用成功 */
	success?: boolean
	/** 状态码 */
	statusCode?: number
	/** 错误码 */
	errorCode?: number
	/** 提醒消息 */
	alertMsg?: string
	/** 错误消息 */
	errorMsg?: string
}

export function postOperateUpdatemasterdatatype(payload: IPostOperateUpdatemasterdatatypePayload, options = {}): Promise<void> {
  return http.post('/rfmultidata/operate/updatemasterdatatype', payload, { transform: false, ...{"needToast":true}, ...options })
}
