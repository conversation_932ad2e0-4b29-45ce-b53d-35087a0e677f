/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 36812
  * @name: 增加可售类目-查询商家占用类目
  * @identifier: api.redlife.merchant.usercategorylist.get.get
  * @version: undefined
  * @path: /api/redlife/merchant/usercategorylist/get
  * @method: get
  * @description: 增加可售类目-查询商家占用类目
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetMerchantUsercategorylistPayload {
	/** sellerId */
	sellerId?: string
}

export interface IData {
	/** 已经占用的类目id列表 */
	categoryIdList?: string[]
	/** 类目id和其对应的状态 */
	categoryId2StatusMap?: Record<string, string>
}

export interface IGetMerchantUsercategorylistResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getMerchantUsercategorylist(params: IGetMerchantUsercategorylistPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/usercategorylist/get', { params, transform: false, ...{"needToast":true}, ...options })
}
