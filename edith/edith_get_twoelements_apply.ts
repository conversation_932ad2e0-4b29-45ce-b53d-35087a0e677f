/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 33862
  * @name: 本地独立入驻-申请二要素校验
  * @identifier: api.redlife.merchant.twoelements.apply.get
  * @version: undefined
  * @path: /api/redlife/merchant/twoelements/apply
  * @method: get
  * @description: 在本地独立入驻过程中，申请二要素校验并返回校验结果是否通过
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetTwoelementsApplyPayload {
	/** 业务id */
	bizId?: string
	/** 名字 */
	name: string
	/** 身份证 */
	idNumber: string
	/** 电话号码 */
	phone?: string
}

export interface IData {
	/** 二要素核验是否通过 */
	verifyPass: boolean
}

export interface IGetTwoelementsApplyResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function getTwoelementsApply(params: IGetTwoelementsApplyPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/merchant/twoelements/apply', { params, transform: false, ...{"needToast":true}, ...options })
}
