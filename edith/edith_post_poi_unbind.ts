/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 31198
  * @name: 申请解绑门店
  * @identifier: api.redlife.poi.unbind.post
  * @version: undefined
  * @path: /api/redlife/poi/unbind
  * @method: post
  * @description: 申请解绑门店
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IPostPoiUnbindPayload {
	/** 门店id列表 */
	poiIds?: string[]
	/** 解绑原因 */
	reason?: string
}

export interface IData {
	/** 申请记录id列表 */
	applyRecordIds?: number[]
}

export interface IPostPoiUnbindResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function postPoiUnbind(payload: IPostPoiUnbindPayload, options = {}): Promise<IData> {
  return http.post('/api/redlife/poi/unbind', payload, { transform: false, ...{"needToast":true}, ...options })
}
