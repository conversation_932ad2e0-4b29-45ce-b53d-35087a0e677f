/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */ 
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 31192
  * @name: 本地生活 - 门店认领列表页顶部提示
  * @identifier: api.redlife.poi.qualification_summary.get
  * @version: undefined
  * @path: /api/redlife/poi/qualification_summary
  * @method: get
  * @description: 本地生活 - 门店认领列表页顶部提示
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface IGetQualificationSummaryPayload {
	/** 商家ID */
	sellerId?: string
}

export interface IClaimInfoOverview {
	/** 失败的 */
	failed?: number
	/** 过期的 */
	expired?: number
	/** 临期的 */
	advent?: number
}

export interface IData {
	/** claimInfoOverview */
	claimInfoOverview?: IClaimInfoOverview
}

export interface IGetQualificationSummaryResponse {
	/** 返回值code */
	code?: number
	/** 请求是否成功 */
	success?: boolean
	/** 信息 */
	msg?: string
	/** 数据体 */
	data?: IData
}

export function getQualificationSummary(params: IGetQualificationSummaryPayload, options = {}): Promise<IData> {
  return http.get('/api/redlife/poi/qualification_summary', { params, transform: false, ...{"needToast":true}, ...options })
}
