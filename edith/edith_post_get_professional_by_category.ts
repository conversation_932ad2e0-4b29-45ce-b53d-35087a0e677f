/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  * 
  * @id: 34305
  * @name: 本地商家入驻-根据类目查询专业号可选身份
  * @identifier: api.redlife.merchant.get_professional_by_category.post
  * @version: undefined
  * @path: /api/redlife/merchant/get_professional_by_category
  * @method: post
  * @description: 
  * 
  * @XHS_API_KIT-INFO
*/

import http from 'shared/http'

export interface ICategoryInfoList {
	/** 类目 */
	categoryId: string
	/** 类目选取状态 */
	checkStatus: string
	/** 是否叶子节点 */
	leaf: boolean
}

export interface IBrandTradeLevelRelation {
	/** 店铺类型 */
	sellerType: string
	/** 所选类目 */
	categoryInfoList: ICategoryInfoList[]
	/** 商家身份 */
	businessIdentityCode: string
}

export interface IPostGetProfessionalByCategoryPayload {
	/** 类目id */
	brandTradeLevelRelation?: IBrandTradeLevelRelation[]
}

export interface IProfessionalConfigList {
	/** 专业号身份code */
	professionalCode?: string
	/** 专业号身份name */
	professionalName?: string
	/** 是否需要品牌 */
	needBrand?: boolean
}

export interface IData {
	/** 专业号身份选择列表 */
	professionalConfigList?: IProfessionalConfigList[]
}

export interface IPostGetProfessionalByCategoryResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data?: IData
}

export function postGetProfessionalByCategory(payload: IPostGetProfessionalByCategoryPayload, options = {}): Promise<IData> {
	return http.post('/api/redlife/merchant/get_professional_by_category', payload, { transform: false, ...{ "needToast": true }, ...options })
}
