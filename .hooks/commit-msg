#!/bin/sh

namespace="\033[34mformula\033[0m"


parent_path=$( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )
TEMPLATE="$parent_path/template"

first_line=$(sed -n -e '/^[[:space:]]*#/!{
  /./p
}' $1 | head -n 1)

if [[ "$first_line" =~ ^$ ]]; then
  # no message, give it to git
  exit 0
fi

if [[ "$first_line" =~ ^[Mm]erge ]]; then
  # message came from git merge action
  exit 0
elif [[ "$first_line" =~ ^([0-9]+\.){2,3}[0-9]+(-[0-9]+)? ]]; then
  # message came from npm version
  exit 0
fi


# validate message format
if ! [[ "$first_line" =~ ^(feat|ci|fix|revert|improvement|dep|ci|style|test|docs)(\(.+\))?:[[:space:]].+ ]]; then

  echo >&2 "\n[$namespace] Commit \033[31mrejected\033[0m for message not following the rules below:\n"
  cat >&2 $TEMPLATE
  exit 1
fi

# append formula sign
sed -i.bak -n -e '/commit supervised by formula/!p' $1

echo '\ncommit supervised by formula' >> $1

